
!---------------------------------------------
!
!---------------------------------------------
module mod_TurbSST_ParmIndx
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    implicit none
    ! 129~256 are used to store turb constants
    integer,parameter:: IP_TurbSST_SigmaK1  = 129
    integer,parameter:: IP_TurbSST_SigmaK2  = 130
    integer,parameter:: IP_TurbSST_SigmaW1  = 131
    integer,parameter:: IP_TurbSST_SigmaW2  = 132
    integer,parameter:: IP_TurbSST_Belta1   = 133
    integer,parameter:: IP_TurbSST_Belta2   = 134
    integer,parameter:: IP_TurbSST_CW1      = 135
    integer,parameter:: IP_TurbSST_CW2      = 136
    integer,parameter:: IP_TurbSST_Gamma1   = 137
    integer,parameter:: IP_TurbSST_Gamma2   = 138
    integer,parameter:: IP_TurbSST_BStar    = 139
    integer,parameter:: IP_TurbSST_Kappa    = 140
    integer,parameter:: IP_TurbSST_A1       = 141
    integer,parameter:: IP_TurbSST_Cmu      = 142
    !
    integer,parameter:: IP_TurbSST_kInf     = 161
    integer,parameter:: IP_TurbSST_wInf     = 162
    integer,parameter:: IP_TurbSST_kWall    = 163
    integer,parameter:: IP_TurbSST_wWall    = 164
    integer,parameter:: IP_TurbSST_MutInf   = 165
    integer,parameter:: IP_TurbSST_DY1      = 166
    integer,parameter:: IP_TurbSST_DomLen   = 167
    !
    ! ==== array index of turbAuxv on node ====
    integer,parameter:: IP_SST_A1       = 1
    integer,parameter:: IP_SST_F1       = 2
    integer,parameter:: IP_SST_F2       = 3
    integer,parameter:: IP_SST_OMEGA    = 4
    integer,parameter:: IP_SST_SIGMA    = 5
    ! ==== array index of turbAuxv on sate ====
    ! ==== array index of turbAuxv on belv ====
endmodule
!
module mod_TurbSST_Def
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    implicit none
    !
    integer,parameter:: nTurbEx = 2
    integer,parameter:: nConsEx = nDim + 2
    integer,parameter:: nPrimEx = nDim + 4 !+mul+mut
    integer,parameter:: nSoluEx = nDim + 4 !nCons+nTurb
    !
endmodule
!
