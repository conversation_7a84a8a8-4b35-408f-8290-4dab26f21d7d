
!
!对5阶以下矩阵快速求逆
module mod_MatrixInversion
    implicit none 
    !
    interface getMatrixInv
        MODULE PROCEDURE getMatrixInv_R4
        MODULE PROCEDURE getMatrixInv_R8
    endinterface
    !
    contains
    !
    subroutine getMatrixInv_R4(val_A,val_AInv,val_size)
        integer,intent(in ):: val_size
        real*4 ,intent(in ):: val_A(val_size,val_size)
        real*4 ,intent(out):: val_AInv(val_size,val_size)
        !
        integer:: i,j
        real*8:: A(val_size,val_size)
        real*8:: B(val_size,val_size)
        !
        do i=1,val_size
            do j=1,val_size
                A(i,j) = val_A(i,j)
            enddo
        enddo
        
        call getMatrixInv_R8(A,B,val_size)
        
        do i=1,val_size
            do j=1,val_size
                val_AInv(i,j) = B(i,j)
            enddo
        enddo
        
    endsubroutine
    !
    subroutine getMatrixInv_R8(val_A,val_AInv,val_size)
        integer,intent(in ):: val_size
        real*8 ,intent(in ):: val_A(val_size,val_size)
        real*8 ,intent(out):: val_AInv(val_size,val_size)
        !
        integer:: i,j,k,s,IP,JP
        real*8 :: AAdj(val_size-1,val_size-1),detA,detAS,detAInv
        real*8 :: AInv(val_size,val_size),BI(val_size,val_size)
        
        
        if((val_size < 1).or.(val_size > 4)) then
            pause "nDim not support in getMatrixInv_R8!!"
            stop
        endif
        
        call getMatrixDetValue(val_A,val_size,detA)
        
        if(abs(detA) < 1.0E-300) then
            pause "abs(detA) < 1.0E-300 in getMatrixInv_R8!!"
            stop
        endif
        
        detAInv = 1.0/detA
        
        if(val_size == 1) then
            val_AInv(1,1) = detAInv
            return
        endif
        
        do i=1,val_size; do j=1,val_size
            do k=i,i+val_size-2; do s=j,j+val_size-2
                IP = mod(k,val_size) + 1
                JP = mod(s,val_size) + 1
                
                AAdj(k-i+1,s-j+1) = val_A(IP,JP)
            enddo; enddo
            
            call getMatrixDetValue(AAdj,val_size-1,detAS)
            
            AInv(j,i) = detAS*detAInv
            val_AInv(j,i) = detAS*detAInv
        enddo; enddo
    
        do i=1,val_size
            do j=1,val_size
                BI(i,j) = 0.0
                do k=1,val_size
                    BI(i,j) = BI(i,j) + val_A(i,k)*AInv(k,j)
                enddo
            enddo
        enddo
        
    endsubroutine
    !
    subroutine getMatrixDetValue(val_A,val_size,val_value)
        integer,intent(in ):: val_size
        real*8 ,intent(in ):: val_A(val_size,val_size)
        real*8 ,intent(out):: val_value
        !
        if(val_size == 1) then
            val_value = val_A(1,1)
        elseif(val_size == 2) then
            call getMatrixDetValue2D(val_A,val_value)
        elseif(val_size == 3) then
            call getMatrixDetValue3D(val_A,val_value)
        elseif(val_size == 4) then
            call getMatrixDetValue4D(val_A,val_value)
        else
            pause "nDim not support in getMatrixDetValue!!"
            stop
        endif
        
    endsubroutine
    !
    subroutine getMatrixDetValue2D(val_A,val_value)
        real*8 ,intent(in ):: val_A(2,2)
        real*8 ,intent(out):: val_value
        !
        val_value = val_A(1,1)*val_A(2,2)-val_A(1,2)*val_A(2,1)
        !
    endsubroutine
    !
    subroutine getMatrixDetValue3D(val_A,val_value)
        real*8 ,intent(in ):: val_A(3,3)
        real*8 ,intent(out):: val_value
        !
        integer:: i,j,k
        real*8 :: iData(6),iTemp
        
        do i=1,3
            j = mod(i  ,3) + 1
            k = mod(i+1,3) + 1
            !
            iData(2*(i-1)+1) =  val_A(1,i)*val_A(2,j)*val_A(3,k)
            iData(2*(i-1)+2) = -val_A(1,i)*val_A(2,k)*val_A(3,j)
        enddo
        
        !call getVectorSum(iData, 6, iTemp)
        call getVectorOrderedSum(iData, 6, iTemp)
        
        val_value = iTemp
        
    endsubroutine
    !
    subroutine getMatrixDetValue4D(val_A,val_value)
        real*8 ,intent(in ):: val_A(4,4)
        real*8 ,intent(out):: val_value
        !
        integer:: i,j,k,s
        real*8 :: iData(24),iTemp
        
        do i=1,4
            j = mod(i  ,4) + 1
            k = mod(i+1,4) + 1
            s = mod(i+2,4) + 1
            !
            iData(6*(i-1)+1) =  val_A(1,i)*val_A(2,j)*val_A(3,k)*val_A(4,s)
            iData(6*(i-1)+2) =  val_A(1,i)*val_A(2,s)*val_A(3,j)*val_A(4,k)
            iData(6*(i-1)+3) =  val_A(1,i)*val_A(2,k)*val_A(3,s)*val_A(4,j)
            iData(6*(i-1)+4) = -val_A(1,i)*val_A(2,j)*val_A(3,s)*val_A(4,k)
            iData(6*(i-1)+5) = -val_A(1,i)*val_A(2,k)*val_A(3,j)*val_A(4,s)
            iData(6*(i-1)+6) = -val_A(1,i)*val_A(2,s)*val_A(3,k)*val_A(4,j)
        enddo
        
        !call getVectorSum(iData, 24, iTemp)
        call getVectorOrderedSum(iData, 24, iTemp)
        
        val_value = iTemp
        
    endsubroutine
    !
    subroutine getVectorSum(val_A,val_size,val_value)
        integer,intent(in ):: val_size
        real*8 ,intent(in ):: val_A(val_size)
        real*8 ,intent(out):: val_value
        !
        integer:: i
        real*8 :: iTemp
        
        iTemp = 0.0
        do i=1,val_size
            iTemp = iTemp + val_A(i)
        enddo
        
        val_value = iTemp
        
    endsubroutine
    !
    subroutine getVectorOrderedSum(val_A,val_size,val_value)
        integer,intent(in ):: val_size
        real*8 ,intent(in ):: val_A(val_size)
        real*8 ,intent(out):: val_value
        !
        integer:: i,j,k,iCount,iSize,iOrder(val_size)
        real*8 :: iData(val_size),jData(val_Size),iTemp
        
        
        do i=1,val_size
            iData(i) = val_A(i)
        enddo
        
        do i=1,val_size
            do j=1,val_size,2
                if(j == val_size) exit
                
                if(abs(iData(j)) < abs(iData(j+1))) then
                    iTemp      = iData(j  )
                    iData(j  ) = iData(j+1)
                    iData(j+1) = iTemp
                endif
            enddo
            
            do j=2,val_size,2
                if(j == val_size) exit
                
                if(abs(iData(j)) < abs(iData(j+1))) then
                    iTemp      = iData(j  )
                    iData(j  ) = iData(j+1)
                    iData(j+1) = iTemp
                endif
            enddo
        enddo
        
        do i=1,val_size
            if(abs(iData(i)) < 1.0E-300) then
                iOrder(i) = 0
            else
                iOrder(i) = int(log10(abs(iData(i))))
            endif
        enddo
        
        iSize = val_size
        do i=1,30
            jData(1) = iData(1)
            k = iOrder(1)
            iCount = 1
            
            do j=2,iSize
                if(abs(iOrder(j) - k) < 3) then
                    jData(iCount) = jData(iCount) + iData(j)
                else
                    iCount = iCount + 1
                    jData(iCount) = iData(j)
                    k = iOrder(iCount)
                endif
            enddo
            
            iSize = iCount
            
            if(iSize == 1) exit
            
            do j=1,iSize
                iData(j) = jData(j)
                
                if(abs(iData(j)) < 1.0E-300) then
                    iOrder(j) = 0
                else
                    iOrder(j) = int(log10(abs(iData(j))))
                endif
            enddo
        enddo
        
        iTemp = 0.0
        do i=1,iSize
            iTemp = iTemp + jData(i)
        enddo
        
        val_value = iTemp
        
    endsubroutine
    !
endmodule
!