!
subroutine testIO_GlobalMesh_Valid(val_iMesh,val_strFlag)
    use mod_TypeDef_Mesh
    use mod_PreDefine_IOPort
    use mod_PreDefine_Flag
    use mod_WorkPath
    use mod_strOfNumber
    implicit none
    type(typ_GlobalMesh),intent(in):: val_iMesh
    character(len=*),intent(in):: val_strFlag
    !
    integer,save:: nSave = 0
    integer:: iDoma,i,j,iCount,jCount
    integer:: poinID,elemID,icci(4*nDim-4)
    integer:: iTempFlag(val_iMesh%nElem,2),nElem_Doma(val_iMesh%nDoma,2)
    
    
    if(nDim == 3) then
        write(ioPort_Out,*) 'not support nDim=3 in testIO_ValidMesh.f90'
        return
    endif
    
    call calITempFlag
    
    call getNElem_Doma
    
    
    open(ioPort_FULL,file=trim(testFullPath)//'/GlobalValA_'//trim(val_strFlag)         &
                //trim(strSix_Number(nSave))//'.plt',asynchronous='yes',status='unknown')
       
    do iDoma = 1,val_iMesh%nDoma
        if(nElem_Doma(iDoma,1) <= 0) cycle
        
        write(ioPort_FULL,*)'VARIABLES="X","Y","iHostFlag" '
            
        if(iDoma == 1) then
            write(ioPort_FULL,*)'ZONE DATAPACKING = POINT,',            &
                                    ', NODES =', val_iMesh%nPoin,       &
                                    ', ELEMENTS =', nElem_Doma(iDoma,1),&
                                    ', ZONETYPE = FEQUADRILATERAL'
        else
            write(ioPort_FULL,*)'ZONE DATAPACKING = POINT,',            &
                                    ', NODES =', val_iMesh%nPoin,       &
                                    ', ELEMENTS =', nElem_Doma(iDoma,1),&
                                    ', ZONETYPE = FEQUADRILATERAL, VARSHARELIST=([1-3]=1)'
        endif
    
        if(iDoma == 1) then
            do i=1,val_iMesh%nPoin
                write(ioPort_FULL,*) val_iMesh%iCoor_Poin(:,i),val_iMesh%iHost_Poin(i)
            enddo
        endif
        
        do i=1,val_iMesh%iDoma(iDoma)%nElem
            elemID = val_iMesh%iDoma(iDoma)%iElem(i)
                
            if(iTempFlag(elemID,1) == 1) then
                write(ioPort_FULL,*) val_iMesh%iElem(:,elemID)
            endif
        enddo
    enddo 
    
    if(val_iMesh%iExteRegnSANO%nSate > 0) then
        write(ioPort_FULL,*)'ZONE DATAPACKING = POINT,',            &
                                ', NODES =', val_iMesh%nPoin,       &
                                ', ELEMENTS =', val_iMesh%iExteRegnSANO%nSate,&
                                ', ZONETYPE = FELINESEG, VARSHARELIST=([1-3]=1)'
        
        do i=1,val_iMesh%nPoin
            do j=val_iMesh%iExteRegnSANO%kSate_Poin(i)+1,val_iMesh%iExteRegnSANO%kSate_Poin(i+1)
                write(ioPort_FULL,*) i,val_iMesh%iExteRegnSANO%iSate_Poin(j,1)
            enddo
        enddo
        
    endif
        
    close(ioPort_FULL)
    
    
    open(ioPort_FULL,file=trim(testFullPath)//'/GlobalValB_'//trim(val_strFlag)         &
                //trim(strSix_Number(nSave))//'.plt',asynchronous='yes',status='unknown')
       
    do iDoma = 1,val_iMesh%nDoma
        if(nElem_Doma(iDoma,2) <= 0) cycle
        
        write(ioPort_FULL,*)'VARIABLES="X","Y","iHostFlag" '
            
        if(iDoma == 1) then
            write(ioPort_FULL,*)'ZONE DATAPACKING = POINT,',            &
                                    ', NODES =', val_iMesh%nPoin,       &
                                    ', ELEMENTS =', nElem_Doma(iDoma,2),&
                                    ', ZONETYPE = FEQUADRILATERAL'
        else
            write(ioPort_FULL,*)'ZONE DATAPACKING = POINT,',            &
                                    ', NODES =', val_iMesh%nPoin,       &
                                    ', ELEMENTS =', nElem_Doma(iDoma,2),&
                                    ', ZONETYPE = FEQUADRILATERAL, VARSHARELIST=([1-3]=1)'
        endif
    
        if(iDoma == 1) then
            do i=1,val_iMesh%nPoin
                write(ioPort_FULL,*) val_iMesh%iCoor_Poin(:,i),val_iMesh%iHost_Poin(i)
            enddo
        endif
        
        do i=1,val_iMesh%iDoma(iDoma)%nElem
            elemID = val_iMesh%iDoma(iDoma)%iElem(i)
                
            if(iTempFlag(elemID,2) == 1) then
                write(ioPort_FULL,*) val_iMesh%iElem(:,elemID)
            endif
        enddo
    enddo 
        
    if(val_iMesh%iExteRegnSANO%nSate > 0) then
        write(ioPort_FULL,*)'ZONE DATAPACKING = POINT,',            &
                                ', NODES =', val_iMesh%nPoin,       &
                                ', ELEMENTS =', val_iMesh%iExteRegnSANO%nSate,&
                                ', ZONETYPE = FELINESEG, VARSHARELIST=([1-3]=1)'
        
        do i=1,val_iMesh%nPoin
            do j=val_iMesh%iExteRegnSANO%kSate_Poin(i)+1,val_iMesh%iExteRegnSANO%kSate_Poin(i+1)
                write(ioPort_FULL,*) i,val_iMesh%iExteRegnSANO%iSate_Poin(j,1)
            enddo
        enddo
        
    endif
    
    close(ioPort_FULL)
    
    nSave = nSave + 1
    return
    !
contains
    !
    subroutine calITempFlag
        implicit none
        
        iTempFlag(:,:) = 0
        do i=1,val_iMesh%nElem
            icci(:) = val_iMesh%iElem(:,i)
        
            do j=1,4*nDim-4
                if(val_iMesh%iHost_Poin(icci(j)) == FLAG_VALID) then
                    iTempFlag(i,1) = 1
                    exit
                endif
            enddo
        
            iCount = 0
            do j=1,4*nDim-4
                if(val_iMesh%iHost_Poin(icci(j)) == FLAG_VALID) then
                    iCount = iCount + 1
                endif
            enddo
            if(iCount == 4*nDim-4) then
                iTempFlag(i,2) = 1
            endif
        enddo
        
    endsubroutine
    !
    subroutine getNElem_Doma
        implicit none
        
        nElem_Doma(:,:) = 0
        do iDoma=1,val_iMesh%nDoma
            iCount = 0
            jCount = 0
            do i=1,val_iMesh%iDoma(iDoma)%nElem
                elemID = val_iMesh%iDoma(iDoma)%iElem(i)
            
                if(iTempFlag(elemID,1) == 1) then
                    iCount = iCount + 1
                endif
                if(iTempFlag(elemID,2) == 1) then
                    jCount = jCount + 1
                endif
            enddo
            nElem_Doma(iDoma,1) = iCount
            nElem_Doma(iDoma,2) = jCount
        enddo
        
    endsubroutine
    !
endsubroutine
!
