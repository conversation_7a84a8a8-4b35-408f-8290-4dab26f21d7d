!
subroutine testIO_DivRegin(val_iDivRegin,val_flagString,val_iFlag)
    use mod_PreDefine_Dimension
    use mod_PreDefine_IOPort
    use mod_TypeDef_Division
    use mod_WorkPath
    use mod_strOfNumber
    use mod_Interface_AllocateArray
    implicit none
    type(typ_DivRegin),intent(in):: val_iDivRegin
    character(len=*),intent(in):: val_flagString
    integer,optional:: val_iFlag
    
    !
    if(nDim == 2) then
        call testIO_DivRegin_2D
    elseif(nDim == 3) then
        call testIO_DivRegin_3D
    endif
    
    !
contains
    !
    subroutine testIO_DivRegin_2D
        integer:: i
        integer:: nPoin,nBlock
        real(kind=REALLEN):: minBnd(nDim),maxBnd(nDim)
        real(kind=REALLEN),allocatable:: iCoorX(:,:)
        character(len=STRLEN):: fileName
    
        if(nDim /= 2) return
    
    
        nBlock = 0
        do i=1,val_iDivRegin%nBlock
            if((val_iDivRegin%iBlock(i)%nPoin > 0).and.(.not.(val_iDivRegin%iBlock(i)%isNeedDiv))) then
                nBlock = nBlock + 1
            endif
        enddo
    
        nPoin = nBlock*4
    
        call allocateArray(iCoorX, nDim,nPoin);
    
    
        nBlock = 0
        do i=1,val_iDivRegin%nBlock
            if((val_iDivRegin%iBlock(i)%nPoin > 0).and.(.not.(val_iDivRegin%iBlock(i)%isNeedDiv))) then
                nBlock = nBlock + 1
            
                minBnd(:) = val_iDivRegin%iBlock(i)%minBnd(:)
                maxBnd(:) = val_iDivRegin%iBlock(i)%maxBnd(:)
            
                iCoorX(1,4*nBlock-3) = minBnd(1)
                iCoorX(2,4*nBlock-3) = minBnd(2)
            
                iCoorX(1,4*nBlock-2) = minBnd(1)
                iCoorX(2,4*nBlock-2) = maxBnd(2)
            
                iCoorX(1,4*nBlock-1) = maxBnd(1)
                iCoorX(2,4*nBlock-1) = maxBnd(2)
            
                iCoorX(1,4*nBlock  ) = maxBnd(1)
                iCoorX(2,4*nBlock  ) = minBnd(2)
            endif
        enddo
    
    
        if(present(val_iFlag)) then
            fileName = 'DivRegin_'//trim(val_flagString)//trim(strSix_Number(val_iFlag))//'.plt'
        else
            fileName = 'DivRegin_'//trim(val_flagString)//'.plt'
        endif
    
    
        open(ioPort_FULL,file=trim(testFullPath)//'/'//trim(fileName),asynchronous='yes',status='unknown')
            write(ioPort_FULL,*)'VARIABLES="X","Y" '
            write(ioPort_FULL,*)'ZONE DATAPACKING = POINT,',    &
                                    ' NODES =', nPoin,          &
                                    ' ELEMENTS =', nBlock,      &
                                    ' ZONETYPE = FEQUADRILATERAL'
    
            do i=1,nPoin
                write(ioPort_FULL,*) iCoorX(:,i)
            enddo
    
            do i=1,nBlock
                write(ioPort_FULL,*) 4*i-3, 4*i-2, 4*i-1, 4*i
            enddo
        
        close(ioPort_FULL)
    
    endsubroutine
    !
    subroutine testIO_DivRegin_3D
        integer:: i
        integer:: nPoin,nBlock
        real(kind=REALLEN):: minBnd(nDim),maxBnd(nDim)
        real(kind=REALLEN),allocatable:: iCoorX(:,:)
        character(len=STRLEN):: fileName
    
        if(nDim /= 3) return
    
    
        nBlock = 0
        do i=1,val_iDivRegin%nBlock
            if((val_iDivRegin%iBlock(i)%nPoin > 0).and.(.not.(val_iDivRegin%iBlock(i)%isNeedDiv))) then
                nBlock = nBlock + 1
            endif
        enddo
    
        nPoin = nBlock*8
    
        call allocateArray(iCoorX, nDim,nPoin);
    
    
        nBlock = 0
        do i=1,val_iDivRegin%nBlock
            if((val_iDivRegin%iBlock(i)%nPoin > 0).and.(.not.(val_iDivRegin%iBlock(i)%isNeedDiv))) then
                nBlock = nBlock + 1
            
                minBnd(:) = val_iDivRegin%iBlock(i)%minBnd(:)
                maxBnd(:) = val_iDivRegin%iBlock(i)%maxBnd(:)
            
                iCoorX(1,8*nBlock-7) = minBnd(1)
                iCoorX(2,8*nBlock-7) = minBnd(2)
                iCoorX(3,8*nBlock-7) = minBnd(3)
            
                iCoorX(1,8*nBlock-6) = maxBnd(1)
                iCoorX(2,8*nBlock-6) = minBnd(2)
                iCoorX(3,8*nBlock-6) = minBnd(3)
            
                iCoorX(1,8*nBlock-5) = maxBnd(1)
                iCoorX(2,8*nBlock-5) = maxBnd(2)
                iCoorX(3,8*nBlock-5) = minBnd(3)
            
                iCoorX(1,8*nBlock-4) = minBnd(1)
                iCoorX(2,8*nBlock-4) = maxBnd(2)
                iCoorX(3,8*nBlock-4) = minBnd(3)
                
                iCoorX(1,8*nBlock-3) = minBnd(1)
                iCoorX(2,8*nBlock-3) = minBnd(2)
                iCoorX(3,8*nBlock-3) = maxBnd(3)
            
                iCoorX(1,8*nBlock-2) = maxBnd(1)
                iCoorX(2,8*nBlock-2) = minBnd(2)
                iCoorX(3,8*nBlock-2) = maxBnd(3)
            
                iCoorX(1,8*nBlock-1) = maxBnd(1)
                iCoorX(2,8*nBlock-1) = maxBnd(2)
                iCoorX(3,8*nBlock-1) = maxBnd(3)
            
                iCoorX(1,8*nBlock  ) = minBnd(1)
                iCoorX(2,8*nBlock  ) = maxBnd(2)
                iCoorX(3,8*nBlock  ) = maxBnd(3)
            endif
        enddo
    
    
        if(present(val_iFlag)) then
            fileName = 'DivRegin_'//trim(val_flagString)//trim(strSix_Number(val_iFlag))//'.plt'
        else
            fileName = 'DivRegin_'//trim(val_flagString)//'.plt'
        endif
    
    
        open(ioPort_FULL,file=trim(testFullPath)//'/'//trim(fileName),asynchronous='yes',status='unknown')
            write(ioPort_FULL,*)'VARIABLES="X","Y","Z" '
            write(ioPort_FULL,*)'ZONE DATAPACKING = POINT,',    &
                                    ' NODES =', nPoin,          &
                                    ' ELEMENTS =', nBlock,      &
                                    ' ZONETYPE = FEBRICK'
    
            do i=1,nPoin
                write(ioPort_FULL,*) iCoorX(:,i)
            enddo
    
            do i=1,nBlock
                write(ioPort_FULL,*) 8*i-7, 8*i-6, 8*i-5, 8*i-4, 8*i-3, 8*i-2, 8*i-1, 8*i
            enddo
        
        close(ioPort_FULL)
    
    endsubroutine
    !
endsubroutine
    
