module mod_SoluMalloc
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    implicit none
contains
    !
    subroutine SoluMalloc(val_iParm,val_iMesh,val_iSolu)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_Interface_AllocateArray
        use mod_GetSoluParm, only: getSize_SoluPrim
        use mod_Config, only: KIND_HARDWARE, CONV_NUM_METHOD_FLOW, UNSTEADY_SIMULATION, IS_INCOMPRESSIBLE
        use mod_Options
        use mod_SoluAdapter, only: soluMalloc_TurbModel
        implicit none
        type(typ_Parm),intent(in   ):: val_iParm
        type(typ_Mesh),intent(in   ):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        !
        integer:: nPoin,nBelv,nSate,nMark
        integer:: nSoluX,nPrimX

        
        !---------------------------------------------
        nPoin = val_iMesh%nPoin
        nBelv = val_iMesh%nBelv
        nSate = val_iMesh%nSate
        nMark = val_iMesh%nMark

        val_iSolu%nNode = nPoin
        val_iSolu%nBelv = nBelv
        val_iSolu%nSate = nSate
        val_iSolu%nMark = nMark

        call getSize_SoluPrim(nSoluX, nPrimX)

        val_iSolu%nSolu = nSoluX
        val_iSolu%nPrim = nPrimX

        !---------------------------------------------
        call allocateArray(val_iSolu%soluInfty, nSoluX)
        call allocateArray(val_iSolu%primInfty, nPrimX)
        call allocateArray(val_iSolu%soluMark, nMark, nSoluX)
        call allocateArray(val_iSolu%primMark, nMark, nPrimX)
        call allocateArray(val_iSolu%soluUCRef, nSoluX)

        call allocateArray(val_iSolu%soluvar  , nPoin, nSoluX)
        call allocateArray(val_iSolu%primvar  , nPoin, nPrimX)
        call allocateArray(val_iSolu%soluBelv , nBelv, nSoluX)
        call allocateArray(val_iSolu%primBelv , nBelv, nPrimX)
        call allocateArray(val_iSolu%iPresCoef, nPoin)
        if(val_iParm%isViscFlow) then
        call allocateArray(val_iSolu%iSurfCoef, nPoin)
        call allocateArray(val_iSolu%iSurfTau , nPoin, nDim)
        call allocateArray(val_iSolu%Tau      , nPoin, nDim, nDim)
        endif

        if(KIND_HARDWARE == _CPU) then
            !---------------------------------------------
            call allocateArray(val_iSolu%deltSolu, nPoin, nSoluX)
            call allocateArray(val_iSolu%soluOld , nPoin, nSoluX)
            call allocateArray(val_iSolu%DT      , nPoin)
            call allocateArray(val_iSolu%DTCoef  , nPoin)
            call allocateArray(val_iSolu%Res_All , nPoin, nSoluX)

            call allocateArray(val_iSolu%gradSolu, nPoin, nSoluX, nDim)
            call allocateArray(val_iSolu%limiter , nPoin, nSoluX)

            call allocateArray(val_iSolu%SRconv , nSate)
            call allocateArray(val_iSolu%SRcNode, nPoin)
            call allocateArray(val_iSolu%SRcBelv, nBelv)
            if(val_iParm%isViscFlow) then
                call allocateArray(val_iSolu%SRvisc , nSate)
                call allocateArray(val_iSolu%SRvNode, nPoin)
                call allocateArray(val_iSolu%SRvBelv, nBelv)
            endif

            if(CONV_NUM_METHOD_FLOW == _JST) then
                call allocateArray(val_iSolu%artDiss, nPoin, nSoluX)
                call allocateArray(val_iSolu%artDitt, nPoin, nSoluX)
                call allocateArray(val_iSolu%artBelv, nBelv, nSoluX)
            endif

            !if(TIME_DISCRE_FLOW == _MCGS_IMPLICIT) then
            !    call allocateArray(val_iSolu%Jacob, nSate, nSoluX, nSoluX)
            !endif

            if(UNSTEADY_SIMULATION == _TIME_STEPPING) then
                call allocateArray(val_iSolu%UnstTerm, nPoin, nSoluX)
            elseif(UNSTEADY_SIMULATION == _DUAL_TIME_STEPPING) then
                call allocateArray(val_iSolu%UnstTerm, nPoin, nSoluX)
                call allocateArray(val_iSolu%soluTimN, nPoin, nSoluX)
                call allocateArray(val_iSolu%soluTimX, nPoin, nSoluX)
            endif

            if(IS_INCOMPRESSIBLE == _YES) then
                call allocateArray(val_iSolu%preCoef, nPoin)
                call allocateArray(val_iSolu%pMatrix, nPoin, nDim+2, nDim+2)
            end if

        elseif(KIND_HARDWARE == _GPU) then
            !---------------------------------------------
            call allocateGPUArray(val_iSolu%soluInfty_d, nSoluX)
            call allocateGPUArray(val_iSolu%primInfty_d, nPrimX)
            call allocateGPUArray(val_iSolu%soluMark_d , nMark, nSoluX)
            call allocateGPUArray(val_iSolu%primMark_d , nMark, nPrimX)
            call allocateGPUArray(val_iSolu%soluUCRef_d, nSoluX)

            call allocateGPUArray(val_iSolu%primvar_d , nPoin, nPrimX)
            call allocateGPUArray(val_iSolu%soluvar_d , nPoin, nSoluX)
            call allocateGPUArray(val_iSolu%soluBelv_d, nBelv, nSoluX)
            call allocateGPUArray(val_iSolu%primBelv_d, nBelv, nPrimX)

            call allocateGPUArray(val_iSolu%deltSolu_d, nPoin, nSoluX)
            call allocateGPUArray(val_iSolu%soluOld_d , nPoin, nSoluX)
            call allocateGPUArray(val_iSolu%Res_All_d , nPoin, nSoluX)
            call allocateGPUArray(val_iSolu%DT_d      , nPoin)
            call allocateGPUArray(val_iSolu%DTCoef_d  , nPoin)

            call allocateGPUArray(val_iSolu%gradSolu_d, nPoin, nSoluX, nDim)
            call allocateGPUArray(val_iSolu%limiter_d , nPoin, nSoluX)

            call allocateGPUArray(val_iSolu%SRconv_d , nSate)
            call allocateGPUArray(val_iSolu%SRcNode_d, nPoin)
            call allocateGPUArray(val_iSolu%SRcBelv_d, nBelv)
            if(val_iParm%isViscFlow) then
                call allocateGPUArray(val_iSolu%SRvisc_d , nSate)
                call allocateGPUArray(val_iSolu%SRvNode_d, nPoin)
                call allocateGPUArray(val_iSolu%SRvBelv_d, nBelv)
            endif

            if(val_iParm%isViscFlow) then
                call allocateGPUArray(val_iSolu%Tau_d, nPoin, nDim, nDim)
            endif

            if(CONV_NUM_METHOD_FLOW == _JST) then
                call allocateGPUArray(val_iSolu%artDiss_d, nPoin, nSoluX)
                call allocateGPUArray(val_iSolu%artDitt_d, nPoin, nSoluX)
                call allocateGPUArray(val_iSolu%artBelv_d, nBelv, nSoluX)
            endif

            !if(TIME_DISCRE_FLOW == _MCGS_IMPLICIT) then
            !    call allocateGPUArray(val_iSolu%Jacob_d, nSate, nSoluX, nSoluX)
            !endif

            if(UNSTEADY_SIMULATION == _TIME_STEPPING) then
                call allocateGPUArray(val_iSolu%UnstTerm_d, nPoin, nSoluX)
            elseif(UNSTEADY_SIMULATION == _DUAL_TIME_STEPPING) then
                call allocateGPUArray(val_iSolu%UnstTerm_d, nPoin, nSoluX)
                call allocateGPUArray(val_iSolu%soluTimN_d, nPoin, nSoluX)
                call allocateGPUArray(val_iSolu%soluTimX_d, nPoin, nSoluX)
            endif

            if(IS_INCOMPRESSIBLE == _YES) then
                call allocateGPUArray(val_iSolu%preCoef_d, nPoin)
                call allocateGPUArray(val_iSolu%pMatrix_d, nPoin, nDim+2, nDim+2)
            end if

        else
            write(*,*) "Wrong KIND_HARDWARE specified."
            stop
        end if

        call soluMalloc_TurbModel(val_iSolu)

    endsubroutine
    !
endmodule