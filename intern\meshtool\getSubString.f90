!
subroutine getSubString(val_string,val_div,val_num,val_sub)
    implicit none
    character(len=*),intent(in):: val_string
    character,intent(in):: val_div
    integer,intent(in):: val_num
    character(len=*),intent(out):: val_sub
    
    integer:: i,ibegin,iend
    integer:: subCount
    
    if(val_num == 0) then
        val_sub = val_string
        return
    endif
    
    subCount = 1
    do i=2,len_trim(val_string)-1
        if((val_string(i:i) == val_div).and.(val_string(i-1:i-1) /= val_div)) then
            subCount = subCount+1
        endif
    enddo
    
    if(abs(val_num) > subCount) then
        val_sub = ' '
        return
    endif
    
    if(val_num > 0 ) then
        ibegin = 1
        
        subCount = 0
        do i=2,len_trim(val_string)-1
            if((val_string(i:i) == val_div).and.(val_string(i-1:i-1) /= val_div)) then
                subCount = subCount+1
                
                if(subCount == val_num-1) then
                    ibegin = i+1
                endif
                
                if(subCount == val_num) then
                    iend = i-1
                    val_sub = adjustl(val_string(ibegin:iend))
                    return
                endif
                
            endif
        enddo
        
        iend = len_trim(val_string)
        
    elseif(val_num<0) then
        iend = len_trim(val_string)
        
        subCount = 0
        do i=len_trim(val_string)-1,2,-1
            if((val_string(i:i) == val_div).and.(val_string(i-1:i-1) /= val_div)) then
                subCount = subCount+1
                
                if(subCount == -val_num-1) then
                    iend = i+1
                endif
                
                if(subCount == -val_num) then
                    ibegin = i+1
                    val_sub = adjustl(val_string(ibegin:iend))
                    return
                endif
                
            endif
        enddo
        
        ibegin = 1
        
    endif
    
    val_sub = adjustl(val_string(ibegin:iend))
    
endsubroutine
!
