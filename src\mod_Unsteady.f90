module mod_Unsteady
    use cudafor
    use mod_PreDefine_Precision
    use mod_PreDefine_Flag
    use mod_TypeDef_Parm, only: NTotalParm,IP_PHYSTIMESTEP
    implicit none
    !
contains
    !
    attributes(global) subroutine calDualTerm_Global(val_iCalu_Poin,&
            val_soluvar,val_soluTiN,val_soluTiX,val_UnstTerm,       &
            val_iParm,val_nPoin,val_nSolu,val_nTMax                 )
        implicit none
        integer,device:: val_iCalu_Poin(val_nPoin)
        real(kind=REALLEN),device:: val_soluvar(val_nPoin,val_nSolu)
        real(kind=REALLEN),device:: val_soluTiN(val_nPoin,val_nSolu)
        real(kind=REALLEN),device:: val_soluTiX(val_nPoin,val_nSolu)
        real(kind=REALLEN),device:: val_UnstTerm(val_nPoin,val_nSolu)
        real(kind=REALLEN),device:: val_iParm(NTotalParm)
        integer,value:: val_nPoin
        integer,value:: val_nSolu
        integer,value:: val_nTMax
        !
        integer:: i,k
        real(kind=REALLEN):: dtInv,t1,t2

        i = (blockIdx%x-1)*blockDim%x+threadIdx%x
        if(i > val_nTMax) return
        if(val_iCalu_Poin(i) /= FLAG_VALID) return

        dtInv = 1.0/(4.0*val_iParm(IP_PHYSTIMESTEP))

        do k=1,val_nSolu
            t1 = val_soluvar(i,k) - val_soluTiN(i,k)
            t2 = val_soluTiX(i,k) - val_soluTiN(i,k)
            val_UnstTerm(i,k) = dtInv*(3.0*t1 + t2)
        enddo

    end subroutine calDualTerm_Global
    !
end module mod_Unsteady