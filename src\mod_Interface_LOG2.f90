
!---------------------------------------------
!
!---------------------------------------------
module mod_Interface_LOG2
    implicit none
    !
    interface LOG2
        MODULE PROCEDURE LOG2_Int
        MODULE PROCEDURE LOG2_Real
        MODULE PROCEDURE LOG2_Real8
    endinterface
    !
contains
    !---------------------------------------------
    !
    !---------------------------------------------
    integer function LOG2_Int(val_Num)
        integer,intent(in):: val_Num
        !
        integer:: i,value
    
        i = 0
        value = 1
        do
            if(value >= val_Num) exit
        
            i = i + 1
            value = value + value
        enddo
    
        LOG2_Int = i
    
    endfunction
    !

    !---------------------------------------------
    !
    !---------------------------------------------
    integer function LOG2_Real(val_Num)
        real,intent(in):: val_Num
        !
        integer:: i,value
    
        i = 0
        value = 1
        do
            if(value >= val_Num) exit
        
            i = i + 1
            value = value + value
        enddo
    
        LOG2_Real = i
    
    endfunction
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    integer function LOG2_Real8(val_Num)
        real*8,intent(in):: val_Num
        !
        integer:: i,value
    
        i = 0
        value = 1
        do
            if(value >= val_Num) exit
        
            i = i + 1
            value = value + value
        enddo
    
        LOG2_Real8 = i
    
    endfunction
    !
endmodule
