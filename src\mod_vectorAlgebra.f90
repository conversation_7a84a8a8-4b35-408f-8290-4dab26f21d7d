
!---------------------------------------------
!
!---------------------------------------------
module mod_vector<PERSON><PERSON><PERSON>
    implicit none
    !
    !_V & _P: _V == vector; _P == point  (parameters)
    !_S & _D: _S == single; _D == double (precision)
    !
    interface vector_length
        MODULE PROCEDURE vector_length_V_S
        MODULE PROCEDURE vector_length_V_D
        MODULE PROCEDURE vector_length_P_S
        MODULE PROCEDURE vector_length_P_D
    endinterface
    !
    interface vector_lengthSQ
        MODULE PROCEDURE vector_lengthSQ_V_S
        MODULE PROCEDURE vector_lengthSQ_V_D
        MODULE PROCEDURE vector_lengthSQ_P_S
        MODULE PROCEDURE vector_lengthSQ_P_D
    endinterface
    !
    interface vector_scalarProduct
        MODULE PROCEDURE vector_scalarProduct_V_S
        MODULE PROCEDURE vector_scalarProduct_V_D
        MODULE PROCEDURE vector_scalarProduct_P_S
        MODULE PROCEDURE vector_scalarProduct_P_D
    endinterface
    !
    interface vector_crossProduct
        MODULE PROCEDURE vector_crossProduct_V_S
        MODULE PROCEDURE vector_crossProduct_V_D
        MODULE PROCEDURE vector_crossProduct_P_S
        MODULE PROCEDURE vector_crossProduct_P_D
    endinterface
    !
    interface vector_mixedProduct
        MODULE PROCEDURE vector_mixedProduct_V_S
        MODULE PROCEDURE vector_mixedProduct_V_D
        MODULE PROCEDURE vector_mixedProduct_P_S
        MODULE PROCEDURE vector_mixedProduct_P_D
    endinterface
    !
contains
    !---------------------------------------------
    !---------------------------------------------
    subroutine vector_length_V_S(val_nDim,val_vector,val_len)
        implicit none
        integer,intent( in):: val_nDim
        real*4 ,intent( in):: val_vector(val_nDim)
        real*4 ,intent(out):: val_len
        !
        integer:: i
        real*8:: iLen
        
        iLen = 0.0
        do i=1,val_nDim
            iLen = iLen + val_vector(i)*val_vector(i)
        enddo
        
        val_len = sqrt(max(iLen,1.0e-30))
        
    endsubroutine
    !
    subroutine vector_length_V_D(val_nDim,val_vector,val_len)
        implicit none
        integer,intent( in):: val_nDim
        real*8 ,intent( in):: val_vector(val_nDim)
        real*8 ,intent(out):: val_len
        !
        integer:: i
        real*8:: iLen
        
        iLen = 0.0
        do i=1,val_nDim
            iLen = iLen + val_vector(i)*val_vector(i)
        enddo
        
        val_len = sqrt(max(iLen,1.0e-30))
        
    endsubroutine
    !
    subroutine vector_length_P_S(val_nDim,val_poinA,val_poinB,val_len)
        implicit none
        integer,intent( in):: val_nDim
        real*4 ,intent( in):: val_poinA(val_nDim)
        real*4 ,intent( in):: val_poinB(val_nDim)
        real*4 ,intent(out):: val_len
        !
        integer:: i
        real*8:: iLen,iVector(val_nDim)
        
        iVector(:) = val_poinB(:) - val_poinA(:)
        
        iLen = 0.0
        do i=1,val_nDim
            iLen = iLen + iVector(i)*iVector(i)
        enddo
        
        val_len = sqrt(max(iLen,1.0e-30))
        
    endsubroutine
    !
    subroutine vector_length_P_D(val_nDim,val_poinA,val_poinB,val_len)
        implicit none
        integer,intent( in):: val_nDim
        real*8 ,intent( in):: val_poinA(val_nDim)
        real*8 ,intent( in):: val_poinB(val_nDim)
        real*8 ,intent(out):: val_len
        !
        integer:: i
        real*8:: iLen,iVector(val_nDim)
        
        iVector(:) = val_poinB(:) - val_poinA(:)
        
        iLen = 0.0
        do i=1,val_nDim
            iLen = iLen + iVector(i)*iVector(i)
        enddo
        
        val_len = sqrt(max(iLen,1.0e-30))
        
    endsubroutine
    !---------------------------------------------
    !---------------------------------------------
    subroutine vector_lengthSQ_V_S(val_nDim,val_vector,val_len)
        implicit none
        integer,intent( in):: val_nDim
        real*4 ,intent( in):: val_vector(val_nDim)
        real*4 ,intent(out):: val_len
        !
        integer:: i
        real*8:: iLen
        
        iLen = 0.0
        do i=1,val_nDim
            iLen = iLen + val_vector(i)*val_vector(i)
        enddo
        
        val_len = max(iLen,1.0e-30)
        
    endsubroutine
    !
    subroutine vector_lengthSQ_V_D(val_nDim,val_vector,val_len)
        implicit none
        integer,intent( in):: val_nDim
        real*8 ,intent( in):: val_vector(val_nDim)
        real*8 ,intent(out):: val_len
        !
        integer:: i
        real*8:: iLen
        
        iLen = 0.0
        do i=1,val_nDim
            iLen = iLen + val_vector(i)*val_vector(i)
        enddo
        
        val_len = max(iLen,1.0e-30)
        
    endsubroutine
    !
    subroutine vector_lengthSQ_P_S(val_nDim,val_poinA,val_poinB,val_len)
        implicit none
        integer,intent( in):: val_nDim
        real*4 ,intent( in):: val_poinA(val_nDim)
        real*4 ,intent( in):: val_poinB(val_nDim)
        real*4 ,intent(out):: val_len
        !
        integer:: i
        real*8:: iLen,iVector(val_nDim)
        
        iVector(:) = val_poinB(:) - val_poinA(:)
        
        iLen = 0.0
        do i=1,val_nDim
            iLen = iLen + iVector(i)*iVector(i)
        enddo
        
        val_len = max(iLen,1.0e-30)
        
    endsubroutine
    !
    subroutine vector_lengthSQ_P_D(val_nDim,val_poinA,val_poinB,val_len)
        implicit none
        integer,intent( in):: val_nDim
        real*8 ,intent( in):: val_poinA(val_nDim)
        real*8 ,intent( in):: val_poinB(val_nDim)
        real*8 ,intent(out):: val_len
        !
        integer:: i
        real*8:: iLen,iVector(val_nDim)
        
        iVector(:) = val_poinB(:) - val_poinA(:)
        
        iLen = 0.0
        do i=1,val_nDim
            iLen = iLen + iVector(i)*iVector(i)
        enddo
        
        val_len = max(iLen,1.0e-30)
        
    endsubroutine
    !---------------------------------------------
    !---------------------------------------------
    subroutine vector_scalarProduct_V_S(val_nDim,val_vector_i,val_vector_j,val_value)
        implicit none
        integer,intent(in ):: val_nDim
        real*4 ,intent(in ):: val_vector_i(val_nDim)
        real*4 ,intent(in ):: val_vector_j(val_nDim)
        real*4 ,intent(out):: val_value
        !
        integer:: i
        real*8:: iValu
        
        iValu = 0.0
        do i=1,val_nDim
            iValu = iValu + val_vector_i(i)*val_vector_j(i)
        enddo
        
        val_value = iValu
        
    endsubroutine
    !
    subroutine vector_scalarProduct_V_D(val_nDim,val_vector_i,val_vector_j,val_value)
        implicit none
        integer,intent(in ):: val_nDim
        real*8 ,intent(in ):: val_vector_i(val_nDim)
        real*8 ,intent(in ):: val_vector_j(val_nDim)
        real*8 ,intent(out):: val_value
        !
        integer:: i
        real*8:: iValu
        
        iValu = 0.0
        do i=1,val_nDim
            iValu = iValu + val_vector_i(i)*val_vector_j(i)
        enddo
        
        val_value = iValu
        
    endsubroutine
    !
    subroutine vector_scalarProduct_P_S(val_nDim,val_poinA,val_poinB,val_poinC,val_value)
        implicit none
        integer,intent(in ):: val_nDim
        real*4 ,intent(in ):: val_poinA(val_nDim)
        real*4 ,intent(in ):: val_poinB(val_nDim)
        real*4 ,intent(in ):: val_poinC(val_nDim)
        real*4 ,intent(out):: val_value
        !
        integer:: i
        real*8:: iValu,iVector(val_nDim),jVector(val_nDim)
        
        iVector(:) = val_poinB(:) - val_poinA(:)
        jVector(:) = val_poinC(:) - val_poinA(:)
        
        iValu = 0.0
        do i=1,val_nDim
            iValu = iValu + iVector(i)*jVector(i)
        enddo
        
        val_value = iValu
        
    endsubroutine
    !
    subroutine vector_scalarProduct_P_D(val_nDim,val_poinA,val_poinB,val_poinC,val_value)
        implicit none
        integer,intent(in ):: val_nDim
        real*8 ,intent(in ):: val_poinA(val_nDim)
        real*8 ,intent(in ):: val_poinB(val_nDim)
        real*8 ,intent(in ):: val_poinC(val_nDim)
        real*8 ,intent(out):: val_value
        !
        integer:: i
        real*8:: iValu,iVector(val_nDim),jVector(val_nDim)
        
        iVector(:) = val_poinB(:) - val_poinA(:)
        jVector(:) = val_poinC(:) - val_poinA(:)
        
        iValu = 0.0
        do i=1,val_nDim
            iValu = iValu + iVector(i)*jVector(i)
        enddo
        
        val_value = iValu
        
    endsubroutine
    !---------------------------------------------
    !---------------------------------------------
    subroutine vector_crossProduct_V_S(val_vector_i,val_vector_j,val_valueVector)
        implicit none
        real*4 ,intent(in ):: val_vector_i(3)
        real*4 ,intent(in ):: val_vector_j(3)
        real*4 ,intent(out):: val_valueVector(3)
        !
        
        val_valueVector(1) = val_vector_i(2)*val_vector_j(3) - val_vector_i(3)*val_vector_j(2)
        val_valueVector(2) = val_vector_i(3)*val_vector_j(1) - val_vector_i(1)*val_vector_j(3)
        val_valueVector(3) = val_vector_i(1)*val_vector_j(2) - val_vector_i(2)*val_vector_j(1)
        
    endsubroutine
    !
    subroutine vector_crossProduct_V_D(val_vector_i,val_vector_j,val_valueVector)
        implicit none
        real*8 ,intent(in ):: val_vector_i(3)
        real*8 ,intent(in ):: val_vector_j(3)
        real*8 ,intent(out):: val_valueVector(3)
        !
        
        val_valueVector(1) = val_vector_i(2)*val_vector_j(3) - val_vector_i(3)*val_vector_j(2)
        val_valueVector(2) = val_vector_i(3)*val_vector_j(1) - val_vector_i(1)*val_vector_j(3)
        val_valueVector(3) = val_vector_i(1)*val_vector_j(2) - val_vector_i(2)*val_vector_j(1)
        
    endsubroutine
    !
    subroutine vector_crossProduct_P_S(val_poinA,val_poinB,val_poinC,val_valueVector)
        implicit none
        real*4 ,intent(in ):: val_poinA(3)
        real*4 ,intent(in ):: val_poinB(3)
        real*4 ,intent(in ):: val_poinC(3)
        real*4 ,intent(out):: val_valueVector(3)
        !
        real*8:: iVector(3),jVector(3)
        
        iVector(:) = val_poinB(:) - val_poinA(:)
        jVector(:) = val_poinC(:) - val_poinA(:)
        
        val_valueVector(1) = iVector(2)*jVector(3) - iVector(3)*jVector(2)
        val_valueVector(2) = iVector(3)*jVector(1) - iVector(1)*jVector(3)
        val_valueVector(3) = iVector(1)*jVector(2) - iVector(2)*jVector(1)
        
    endsubroutine
    !
    subroutine vector_crossProduct_P_D(val_poinA,val_poinB,val_poinC,val_valueVector)
        implicit none
        real*8 ,intent(in ):: val_poinA(3)
        real*8 ,intent(in ):: val_poinB(3)
        real*8 ,intent(in ):: val_poinC(3)
        real*8 ,intent(out):: val_valueVector(3)
        !
        real*8:: iVector(3),jVector(3)
        
        iVector(:) = val_poinB(:) - val_poinA(:)
        jVector(:) = val_poinC(:) - val_poinA(:)
        
        val_valueVector(1) = iVector(2)*jVector(3) - iVector(3)*jVector(2)
        val_valueVector(2) = iVector(3)*jVector(1) - iVector(1)*jVector(3)
        val_valueVector(3) = iVector(1)*jVector(2) - iVector(2)*jVector(1)
        
    endsubroutine
    !---------------------------------------------
    !---------------------------------------------
    subroutine vector_mixedProduct_V_S(val_vector_i,val_vector_j,val_vector_k,val_value)
        implicit none
        real*4 ,intent(in ):: val_vector_i(3)
        real*4 ,intent(in ):: val_vector_j(3)
        real*4 ,intent(in ):: val_vector_k(3)
        real*4 ,intent(out):: val_value
        !
        real*4:: val_vector(3)
        
        call vector_crossProduct_V_S(val_vector_i,val_vector_j,val_vector)
        
        call vector_scalarProduct_V_S(3,val_vector,val_vector_k,val_value)
        
    endsubroutine
    !
    subroutine vector_mixedProduct_V_D(val_vector_i,val_vector_j,val_vector_k,val_value)
        implicit none
        real*8 ,intent(in ):: val_vector_i(3)
        real*8 ,intent(in ):: val_vector_j(3)
        real*8 ,intent(in ):: val_vector_k(3)
        real*8 ,intent(out):: val_value
        !
        real*8:: val_vector(3)
        
        call vector_crossProduct_V_D(val_vector_i,val_vector_j,val_vector)
        
        call vector_scalarProduct_V_D(3,val_vector,val_vector_k,val_value)
        
    endsubroutine
    !
    subroutine vector_mixedProduct_P_S(val_poinA,val_poinB,val_poinC,val_poinD,val_value)
        implicit none
        real*4 ,intent(in ):: val_poinA(3)
        real*4 ,intent(in ):: val_poinB(3)
        real*4 ,intent(in ):: val_poinC(3)
        real*4 ,intent(in ):: val_poinD(3)
        real*4 ,intent(out):: val_value
        !
        real*4:: iVector(3),jVector(3)
        
        call vector_crossProduct_P_S(val_poinA,val_poinB,val_poinC,iVector)
        
        jVector(:) = val_poinD(:) - val_poinA(:)
        
        call vector_scalarProduct_V_S(3,iVector,jVector,val_value)
        
    endsubroutine
    !
    subroutine vector_mixedProduct_P_D(val_poinA,val_poinB,val_poinC,val_poinD,val_value)
        implicit none
        real*8 ,intent(in ):: val_poinA(3)
        real*8 ,intent(in ):: val_poinB(3)
        real*8 ,intent(in ):: val_poinC(3)
        real*8 ,intent(in ):: val_poinD(3)
        real*8 ,intent(out):: val_value
        !
        real*8:: iVector(3),jVector(3)
        
        call vector_crossProduct_P_D(val_poinA,val_poinB,val_poinC,iVector)
        
        jVector(:) = val_poinD(:) - val_poinA(:)
        
        call vector_scalarProduct_V_D(3,iVector,jVector,val_value)
        
    endsubroutine
    !---------------------------------------------
    !---------------------------------------------
    
endmodule
!