
!---------------------------------------------
!
!---------------------------------------------
module mod_Re<PERSON>rder<PERSON>atePart
    implicit none
    !
    type:: typ_ReList
        integer:: nPoin
        integer:: nBlock
        !
        integer,allocatable:: blockID(:)
        integer,allocatable:: iFlag(:,:)
        integer,allocatable:: iLinW(:)
        integer,allocatable:: iRowW(:)
        integer,allocatable:: iGrow(:)
        !
        integer:: nLine
        integer,allocatable:: reLine(:,:)
    endtype
    !
contains
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine reOrderSatePart(val_kSate,val_iSate,     &
                val_Order,val_nPoin,val_nSate,val_nPoPB )
        implicit none
        integer,intent(in):: val_kSate(val_nPoin+1)
        integer,intent(in):: val_iSate(val_nSate)
        integer,intent(out):: val_Order(val_nSate)
        integer,intent(in):: val_nPoin
        integer,intent(in):: val_nSate
        integer,intent(in):: val_nPoPB
        !
        integer:: i,j,k
        integer:: nCont,blockID
        logical:: isExist
        integer:: sateBlock(val_nSate),blockList(val_nSate)
        type(typ_ReList):: iRe
        
        
        
        !---------------------------------------------
        !
        iRe%nPoin = val_nPoin
        
        nCont = 0
        do i=1,val_nPoin
            nCont = max(nCont,val_kSate(i+1)-val_kSate(i))
        enddo
        iRe%nLine = nCont
        allocate(iRe%reLine(iRe%nLine, iRe%nPoin))
        
        
        !---------------------------------------------
        !
        nCont = 0
        do i=1,val_nSate
            blockID = (val_iSate(i) + val_nPoPB - 1)/val_nPoPB
            
            sateBlock(i) = blockID
            
            isExist = .false.
            do j=1,nCont
                if(blockID == blockList(j)) then
                    isExist = .true.
                    exit
                endif
            enddo
            
            if(.not.isExist) then
                nCont = nCont + 1
                blockList(nCont) = blockID
            endif
        enddo
        
        iRe%nBlock = nCont
        
        
        !---------------------------------------------
        !
        allocate(iRe%blockID(iRe%nBlock))
        allocate(iRe%iFlag(iRe%nBlock, iRe%nPoin))
        allocate(iRe%iLinW(iRe%nPoin))
        allocate(iRe%iRowW(iRe%nBlock))
        allocate(iRe%iGrow(iRe%nBlock))
        iRe%blockID(:) = blockList(1:iRe%nBlock)
        iRe%iFlag(:,:) = 0
        iRe%iLinW(:)   = 0
        iRe%iRowW(:)   = 0
        iRe%iGrow(:)   = 0
        
        
        !---------------------------------------------
        !
        do i=1,val_nPoin
            do j=val_kSate(i)+1,val_kSate(i+1)
                blockID = sateBlock(j)
                
                do k=1,iRe%nBlock
                    if(blockID == iRe%blockID(k)) then
                        iRe%iFlag(k,i) = iRe%iFlag(k,i) + 1
                        exit
                    endif
                enddo
            enddo
        enddo
        
        do i=1,iRe%nPoin
            nCont = 0
            do j=1,iRe%nBlock
                nCont = nCont + iRe%iFlag(j,i)
            enddo
            iRe%iLinW(i) = nCont
        enddo
        
        do i=1,iRe%nBlock
            nCont = 0
            do j=1,iRe%nPoin
                if(iRe%iFlag(i,j) > 0) then
                    nCont = nCont + 1
                endif
            enddo
            iRe%iRowW(i) = nCont
        enddo
        
        
        !---------------------------------------------
        !
        call reOrder_ReList(iRe)
        
        
        !---------------------------------------------
        !
        do i=1,iRe%nPoin
            do j=1,iRe%nLine
                if(iRe%reLine(j,i) > 0) then
                    do k=val_kSate(i)+1,val_kSate(i+1)
                        if(sateBlock(k) == iRe%reLine(j,i)) then
                            iRe%reLine(j,i) = k
                            sateBlock(k) = 0
                            exit
                        endif
                    enddo
                endif
            enddo
        enddo
        
        
        do i=1,val_nPoin
            do j=val_kSate(i)+1,val_kSate(i+1)
                val_Order(j) = iRe%reLine(j-val_kSate(i),i)
            enddo
        enddo
              
        return
        
    endsubroutine
    
                
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine reOrder_ReList(val_iRe)
        use mod_PreDefine_IOPort
        implicit none
        type(typ_ReList),intent(inout):: val_iRe
        !
        integer:: i,j,k
        integer:: iWeight,maxIP,nCont,nZero
        integer:: tempFlag(val_iRe%nPoin)
        logical:: isOk
        
        
        !---------------------------------------------
        !
        !open(IOPort_Full, file='reOrder01.dat',status='unknown')
        
        do i=1,val_iRe%nLine
            
            !---------------------------------------------
            !s1:获得置空数
            nZero = 0
            do j=1,val_iRe%nPoin
                if(val_iRe%iLinW(j) == 0) then
                    nZero = nZero + 1
                endif
            enddo
            
            !---------------------------------------------
            !获得最长有效blockID
            iWeight = 0
            do j=1,val_iRe%nBlock
                if(val_iRe%irowW(j) > iWeight) then
                    iWeight = val_iRe%irowW(j)
                    maxIP   = j
                endif
            enddo
            
            write(IOPort_Full,*) nZero,iWeight,maxIP
            
            !---------------------------------------------
            !若完整，这取出该行
            if(iWeight + nZero == val_iRe%nPoin) then
                
                do j=1,val_iRe%nPoin
                    if(val_iRe%iLinW(j) > 0) then
                        val_iRe%reLine(i,j) = val_iRe%blockID(maxIP)
                
                        val_iRe%iFlag(maxIP,j) = val_iRe%iFlag(maxIP,j) - 1
                        
                        val_iRe%iLinW(j) = val_iRe%iLinW(j) - 1
                    endif
                    
                enddo
                
                nCont = 0
                do j=1,val_iRe%nPoin
                    if(val_iRe%iFlag(maxIP,j) > 0) then
                        nCont = nCont + 1
                    endif
                enddo
                val_iRe%iRowW(maxIP) = nCont
                
                cycle
            endif
            
            
            !---------------------------------------------
            !若不全，这补全（补全，从对后续影响最小的备选中选取）
            tempFlag(:) = 0
            do j=1,val_iRe%nPoin
                tempFlag(j) = min(tempFlag(j),val_iRe%iFlag(maxIP,j))
            enddo
            
            do j=1,val_iRe%nBlock
                if(j == maxIP) cycle
                
                if(iWeight + nZero + val_iRe%iRowW(j) == val_iRe%nPoin) then
                    isOk = .true.
                    do k=1,val_iRe%nPoin
                        if(val_iRe%iFlag(maxIP,k)*val_iRe%iFlag(j,k) > 0) then
                            isOk = .false.
                            exit
                        endif
                    
                        if(val_iRe%iFlag(maxIP,k)+val_iRe%iFlag(j,k) == 0) then
                            isOk = .false.
                            exit
                        endif
                    enddo
                    
                elseif(iWeight + nZero + val_iRe%iRowW(j) > val_iRe%nPoin) then
                    isOk = .true.
                    do k=1,val_iRe%nPoin
                        if((val_iRe%iFlag(maxIP,k) == 0).and.    &
                           (val_iRe%iFlag(j    ,k) <= 1) ) then
                            isOk = .false.
                            exit
                        endif
                    enddo
                else
                    isOk = .false.    
                endif
                
                if(isOk) then
                    
                    do k=1,val_iRe%nPoin
                        if(val_iRe%iLinW(k) > 0) then
                            if(val_iRe%iFlag(maxIP,k) > 0) then
                                val_iRe%reLine(i,k) = val_iRe%blockID(maxIP)
                
                                val_iRe%iFlag(maxIP,k) = val_iRe%iFlag(maxIP,k) - 1
                            else
                                val_iRe%reLine(i,k) = val_iRe%blockID(j)
                
                                val_iRe%iFlag(j,k) = val_iRe%iFlag(j,k) - 1
                            endif
                        
                            val_iRe%iLinW(k) = val_iRe%iLinW(k) - 1
                        endif
                    
                    enddo
                    
                    
                    nCont = 0
                    do k=1,val_iRe%nPoin
                        if(val_iRe%iFlag(maxIP,k) > 0) then
                            nCont = nCont + 1
                        endif
                    enddo
                    val_iRe%iRowW(maxIP) = nCont
                    
                    nCont = 0
                    do k=1,val_iRe%nPoin
                        if(val_iRe%iFlag(j,k) > 0) then
                            nCont = nCont + 1
                        endif
                    enddo
                    val_iRe%iRowW(j) = nCont
                    
                    exit
                endif
            enddo
            
            if(isOk) cycle
            
            !---------------------------------------------
            !若单次补不齐，就随意取？ or 先不管了，后期随意取？
            !直接随意取，因为后期补全可能会出错
            
            do k=1,val_iRe%nPoin
                if(val_iRe%iLinW(k) > 0) then
                    if(val_iRe%iFlag(maxIP,k) > 0) then
                        val_iRe%reLine(i,k) = val_iRe%blockID(maxIP)
                
                        val_iRe%iFlag(maxIP,k) = val_iRe%iFlag(maxIP,k) - 1
                        
                        if(val_iRe%iFlag(maxIP,k) == 0) then
                            val_iRe%iRowW(maxIP) = val_iRe%iRowW(maxIP) - 1
                        endif
                    else
                        do j=1,val_iRe%nBlock
                            if(val_iRe%iFlag(j,k) == 0) cycle
                            
                            val_iRe%reLine(i,k) = val_iRe%blockID(j)
                                
                            val_iRe%iFlag(j,k) = val_iRe%iFlag(j,k) - 1
                            
                            if(val_iRe%iFlag(j,k) == 0) then
                                val_iRe%iRowW(j) = val_iRe%iRowW(j) - 1
                            endif
                            
                            exit
                        enddo
                    endif
                        
                    val_iRe%iLinW(k) = val_iRe%iLinW(k) - 1
                endif
            enddo
                    
        enddo
        
    endsubroutine
    !
endmodule
!
