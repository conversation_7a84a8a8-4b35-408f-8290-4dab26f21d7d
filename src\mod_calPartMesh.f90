!
!---------------------------------------------
!
!---------------------------------------------
module mod_calPart<PERSON>esh
    use mod_PreDefine_Dimension
    use mod_PreDefine_Flag
    use mod_PreDefine_ExteKind
    use mod_PreDefine_IOPort
    implicit none
    !
contains
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine iniPartMesh(val_iPart)
        use mod_Project
        use mod_Config
        use mod_Interface_allocateArray
        implicit none
        integer,intent(in):: val_iPart
        !
        integer:: nPoin,nElem,nSate,nB<PERSON>,nBelv,nMark,nDoma
    
    
        !---------------------------------------------
        iPartMesh(val_iPart)%nClor = iGlobalMesh%nClor
        iPartMesh(val_iPart)%nMark = iGlobalMesh%nMark
        iPartMesh(val_iPart)%nDoma = iGlobalMesh%nDoma
    
        iPartMesh(val_iPart)%nPoin = iMeshMapp(val_iPart)%nLPoin
        iPartMesh(val_iPart)%nSate = iMeshMapp(val_iPart)%nLSate
        iPartMesh(val_iPart)%nElem = iMeshMapp(val_iPart)%nLElem
        iPartMesh(val_iPart)%nBele = iMeshMapp(val_iPart)%nLBele
        iPartMesh(val_iPart)%nBelv = iMeshMapp(val_iPart)%nLBelv
    
    
        !---------------------------------------------
        nPoin = iPartMesh(val_iPart)%nPoin
        nSate = iPartMesh(val_iPart)%nSate
        nElem = iPartMesh(val_iPart)%nElem
        nBele = iPartMesh(val_iPart)%nBele
        nMark = iPartMesh(val_iPart)%nMark
        nDoma = iPartMesh(val_iPart)%nDoma
        nBelv = iPartMesh(val_iPart)%nBelv
    
    
        !---------------------------------------------
        call allocateArray(iPartMesh(val_iPart)%iFlag_Poin, nPoin)
        call allocateArray(iPartMesh(val_iPart)%iPart_Poin, nPoin)
        call allocateArray(iPartMesh(val_iPart)%iHost_Poin, nPoin)
        call allocateArray(iPartMesh(val_iPart)%iClor_Poin, nPoin)
        call allocateArray(iPartMesh(val_iPart)%iCoor_Poin, nPoin, nDim)
        call allocateArray(iPartMesh(val_iPart)%iVolu_Poin, nPoin)
        if(PHYSICAL_PROBLEM >= _TURB) then
        call allocateArray(iPartMesh(val_iPart)%iWDis_Poin, nPoin)
        endif
        if(GRID_MOVEMENT == _YES) then
        call allocateArray(iPartMesh(val_iPart)%dCoor_Poin, nPoin, nDim)
        endif
        call allocateArray(iPartMesh(val_iPart)%iDoma_Poin, nPoin)

            
        call allocateArray(iPartMesh(val_iPart)%kSate_Poin, nPoin + 1)
        call allocateArray(iPartMesh(val_iPart)%iSate_Poin, nSate, 2)
        call allocateArray(iPartMesh(val_iPart)%iCoef_Sate, nSate, nDim)
        call allocateArray(iPartMesh(val_iPart)%iLens_Sate, nSate)
    
        call allocateArray(iPartMesh(val_iPart)%iElem, 4*nDim-4, nElem)
        call allocateArray(iPartMesh(val_iPart)%iElemProp, nElem, 1)
        call allocateArray(iPartMesh(val_iPart)%iDomaProp, nDoma, 3)
    
        call allocateArray(iPartMesh(val_iPart)%iBele, 2*nDim-2, nBele)
        call allocateArray(iPartMesh(val_iPart)%iMarkProp, nMark, 2)
    
        call allocateArray(iPartMesh(val_iPart)%iBelvProp, nBelv, 2)
        if(iGlobalMesh%iExteRegnSANO%KIND /= EXTE_NONE) then
        call allocateArray(iPartMesh(val_iPart)%iCoor_Belv, nBelv, nDim)
        endif
        call allocateArray(iPartMesh(val_iPart)%iArea_Belv, nBelv)
        call allocateArray(iPartMesh(val_iPart)%iNvor_Belv, nBelv, nDim)
        call allocateArray(iPartMesh(val_iPart)%iCoef_Belv, nBelv, nDim)
        
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine calPartMesh(val_iPart)
        use mod_Project
        use mod_Config
        use mod_PreDefine_Flag
        implicit none
        integer,intent(in):: val_iPart
        !
        integer:: i,j,k
        integer:: poinOD,poinND,elemOD,elemND,beleOD,belvOD
        integer:: sateOP,domaOD,markOD
        integer:: kMark_Bele(iGlobalMesh%nBele),kDoma_Elem(iGLobalMesh%nElem)
        integer:: nBele_Mark(iGlobalMesh%nMark),nElem_Doma(iGlobalMesh%nDoma)
        integer:: iDoma_GPoin(iGlobalMesh%nPoin)
    

        iDoma_GPoin(:) = 0

        !注，Elem和Bele都保留在原GlobalMesh中的先后顺序
        !------------------------
        do i=1,iGlobalMesh%nDoma
            !$omp parallel do private(elemOD)
            do j=1,iGlobalMesh%iDoma(i)%nElem
                elemOD = iGlobalMesh%iDoma(i)%iElem(j) 
                kDoma_Elem(elemOD) = i

                do k=1,4*nDim-4
                    iDoma_GPoin(iGlobalMesh%iElem(k,elemOD)) = i
                end do
            enddo
            !$omp end parallel do
        enddo
        do i=1,iGlobalMesh%nMark
            !$omp parallel do private(beleOD)
            do j=1,iGlobalMesh%iMark(i)%nElem
                beleOD = iGlobalMesh%iMark(i)%iElem(j)
                kMark_Bele(beleOD) = i
            enddo
            !$omp end parallel do
        enddo
    
        
        !------------------------nPoin相关
        iPartMesh(val_iPart)%kSate_Poin(1) = 0
        !$omp parallel do private(poinOD)
        do i=1,iMeshMapp(val_iPart)%nLPoin
            poinOD = iMeshMapp(val_iPart)%iMapLToG_Poin(i)
        
            if(poinOD > 0) then
                iPartMesh(val_iPart)%iFlag_Poin(i  ) = iGlobalMesh%iFlag_Poin(poinOD)
                iPartMesh(val_iPart)%iPart_Poin(i  ) = iGlobalMesh%iPart_Poin(poinOD)
                iPartMesh(val_iPart)%iHost_Poin(i  ) = iGlobalMesh%iHost_Poin(poinOD)
                iPartMesh(val_iPart)%iClor_Poin(i  ) = iGlobalMesh%iClor_Poin(poinOD)
                iPartMesh(val_iPart)%kSate_Poin(i+1) = iGlobalMesh%kSate_Poin(poinOD+1)- &
                                                       iGlobalMesh%kSate_Poin(poinOD)
                iPartMesh(val_iPart)%iCoor_Poin(i,:) = iGlobalMesh%iCoor_Poin(:,poinOD)
                iPartMesh(val_iPart)%iVolu_Poin(i  ) = iGlobalMesh%iVolu_Poin(poinOD)
                if(PHYSICAL_PROBLEM >= _TURB) then
                iPartMesh(val_iPart)%iWDis_Poin(i  ) = iGlobalMesh%iWDis_Poin(poinOD)
                endif
                if(GRID_MOVEMENT == _YES) then
                iPartMesh(val_iPart)%dCoor_Poin(i,:) = iGlobalMesh%dCoor_Poin(:,poinOD)
                endif
                iPartMesh(val_iPart)%iDoma_Poin(i  ) = iDoma_GPoin(poinOD)
            elseif(poinOD == 0) then
                iPartMesh(val_iPart)%iFlag_Poin(i  ) = FLAG_INVAL
                iPartMesh(val_iPart)%iPart_Poin(i  ) = 0
                iPartMesh(val_iPart)%iHost_Poin(i  ) = FLAG_INVAL
                iPartMesh(val_iPart)%iClor_Poin(i  ) = -1
                iPartMesh(val_iPart)%kSate_Poin(i+1) = 0
                iPartMesh(val_iPart)%iCoor_Poin(i,:) = 0.0
                iPartMesh(val_iPart)%iVolu_Poin(i  ) = 0.0
                if(PHYSICAL_PROBLEM >= _TURB) then
                iPartMesh(val_iPart)%iWDis_Poin(i  ) = 0.0
                endif
                if(GRID_MOVEMENT == _YES) then
                iPartMesh(val_iPart)%dCoor_Poin(i,:) = 0.0
                endif
                iPartMesh(val_iPart)%iDoma_Poin(i  ) = 0
            elseif(poinOD < 0) then
                write(ioPort_Out,*) 'Error1 in calPartMesh'
                stop
            endif
        enddo
        !$omp end parallel do

        do i=1,iMeshMapp(val_iPart)%nLPoin
            iPartMesh(val_iPart)%kSate_Poin(i+1) = iPartMesh(val_iPart)%kSate_Poin(i) + &
                                                   iPartMesh(val_iPart)%kSate_Poin(i+1)
        enddo
    
    
        !------------------------nSate相关
        !$omp parallel do private(sateOP,poinOD,poinND)
        do i=1,iMeshMapp(val_iPart)%nLSate
            sateOP = iMeshMapp(val_iPart)%iMapLToG_Sate(i)
        
            if(sateOP > 0) then
                poinOD = iGlobalMesh%iSate_Poin(sateOP,1)
                poinND = iMeshMapp(val_iPart)%iMapGToL_Poin(poinOD)
        
                iPartMesh(val_iPart)%iSate_Poin(i,1) = poinND
                iPartMesh(val_iPart)%iSate_Poin(i,2) = 0
        
                iPartMesh(val_iPart)%iCoef_Sate(i,:) = iGlobalMesh%iCoef_Sate(:,sateOP)
                
            else
                write(ioPort_Out,*) 'Error2 in calPartMesh'
                stop
            endif
        enddo
        !$omp end parallel do
    
    
        !------------------------nElem相关
        nElem_Doma(:) = 0
        !$omp parallel do private(j,elemOD,domaOD,poinOD,poinND)
        do i=1,iMeshMapp(val_iPart)%nLElem
            elemOD = iMeshMapp(val_iPart)%iMapLToG_Elem(i)
            domaOD = kDoma_Elem(elemOD)

            !$omp atomic
            nElem_Doma(domaOD) = nElem_Doma(domaOD) + 1

            do j=1,4*nDim-4
                poinOD = iGlobalMesh%iElem(j,elemOD)
            
                poinND = iMeshMapp(val_iPart)%iMapGToL_Poin(poinOD)
            
                iPartMesh(val_iPart)%iElem(j,i) = poinND
                iPartMesh(val_iPart)%iElemProp(j,1) = domaOD
            enddo
        enddo
        !$omp end parallel do
    
    
        !------------------------nBele相关
        nBele_Mark(:) = 0
        !$omp parallel do private(beleOD,markOD,poinOD,poinND)
        do i=1,iMeshMapp(val_iPart)%nLBele
            beleOD = iMeshMapp(val_iPart)%iMapLToG_Bele(i)
            markOD = kMark_Bele(beleOD)

            !$omp atomic
            nBele_Mark(markOD) = nBele_Mark(markOD) + 1

            do j=1,2*nDim-2
                poinOD = iGlobalMesh%iBele(j,beleOD)
            
                poinND = iMeshMapp(val_iPart)%iMapGToL_Poin(poinOD)
            
                iPartMesh(val_iPart)%iBele(j,i) = poinND
            enddo
        enddo
        !$omp end parallel do
    
        
        !------------------------nBelv相关
        !$omp parallel do private(belvOD,poinOD,markOD,poinND)
        do i=1,iMeshMapp(val_iPart)%nLBelv
            belvOD = iMeshMapp(val_iPart)%iMapLToG_Belv(i)
            
            poinOD = iGlobalMesh%iBelvProp(belvOD,1)
            markOD = iGlobalMesh%iBelvProp(belvOD,2)
            
            poinND = iMeshMapp(val_iPart)%iMapGToL_Poin(poinOD)
            
            iPartMesh(val_iPart)%iBelvProp(i,1) = poinND
            iPartMesh(val_iPart)%iBelvProp(i,2) = markOD
            
            if(iGlobalMesh%iExteRegnSANO%KIND /= EXTE_NONE) then
                iPartMesh(val_iPart)%iCoor_Belv(i,:) = iGlobalMesh%iCoor_Belv(belvOD,:)
            endif
        
            iPartMesh(val_iPart)%iArea_Belv(i  ) = iGlobalMesh%iArea_Belv(  belvOD)
            iPartMesh(val_iPart)%iNvor_Belv(i,:) = iGlobalMesh%iNvor_Belv(belvOD,:)
            iPartMesh(val_iPart)%iCoef_Belv(i,:) = iGlobalMesh%iCoef_Belv(belvOD,:)
        enddo
        !$omp end parallel do
        
    
        !------------------------nMark + nDoma相关
        do i=1,iPartMesh(val_iPart)%nMark
            iPartMesh(val_iPart)%iMarkProp(i,1) = nBele_Mark(i) !iGlobalMesh%iMark(i)%nElem
            iPartMesh(val_iPart)%iMarkProp(i,2) = iGlobalMesh%iMark(i)%KIND
        enddo
    
        do i=1,iPartMesh(val_iPart)%nDoma
            iPartMesh(val_iPart)%iDomaProp(i,1) = nElem_Doma(i) !iGlobalMesh%iDoma(i)%nElem
            iPartMesh(val_iPart)%iDomaProp(i,2) = 0
            iPartMesh(val_iPart)%iDomaProp(i,3) = DomaInfos(i)%markID !doma connected mark
        enddo

        !write(*,*) iPartMesh(val_iPart)%iDomaProp(:,3)
        !write(*,*) 'aaaac'
    
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine calPartMesh_Updating(val_iPart)
        use mod_Project
        use mod_Config
        use mod_PreDefine_Flag
        implicit none
        integer,intent(in):: val_iPart
        !
        integer:: i,j,k
        integer:: poinOD,poinND,elemOD,elemND,beleOD,belvOD
        integer:: sateOP,domaOD,markOD
        integer:: kMark_Bele(iGlobalMesh%nBele),kDoma_Elem(iGLobalMesh%nElem)
        integer:: nBele_Mark(iGlobalMesh%nMark),nElem_Doma(iGlobalMesh%nDoma)
    
    
        !注，Elem和Bele都保留在原GlobalMesh中的先后顺序
        !------------------------
        do i=1,iGlobalMesh%nDoma
            do j=1,iGlobalMesh%iDoma(i)%nElem
                elemOD = iGlobalMesh%iDoma(i)%iElem(j) 
                kDoma_Elem(elemOD) = i
            enddo
        enddo
        do i=1,iGlobalMesh%nMark
            do j=1,iGlobalMesh%iMark(i)%nElem
                beleOD = iGlobalMesh%iMark(i)%iElem(j)
                kMark_Bele(beleOD) = i
            enddo
        enddo
    
        
        !------------------------nPoin相关
        iPartMesh(val_iPart)%kSate_Poin(1) = 0
        do i=1,iMeshMapp(val_iPart)%nLPoin
            poinOD = iMeshMapp(val_iPart)%iMapLToG_Poin(i)
        
            if(poinOD > 0) then
                iPartMesh(val_iPart)%iFlag_Poin(i  ) = iGlobalMesh%iFlag_Poin(poinOD)
                iPartMesh(val_iPart)%iPart_Poin(i  ) = iGlobalMesh%iPart_Poin(poinOD)
                iPartMesh(val_iPart)%iHost_Poin(i  ) = iGlobalMesh%iHost_Poin(poinOD)
                iPartMesh(val_iPart)%iClor_Poin(i  ) = iGlobalMesh%iClor_Poin(poinOD)
                iPartMesh(val_iPart)%iCoor_Poin(i,:) = iGlobalMesh%iCoor_Poin(:,poinOD)
                iPartMesh(val_iPart)%iVolu_Poin(i  ) = iGlobalMesh%iVolu_Poin(poinOD)
                if(PHYSICAL_PROBLEM >= _TURB) then
                iPartMesh(val_iPart)%iWDis_Poin(i  ) = iGlobalMesh%iWDis_Poin(poinOD)
                endif
                if(GRID_MOVEMENT == _YES) then
                iPartMesh(val_iPart)%dCoor_Poin(i,:) = iGlobalMesh%dCoor_Poin(:,poinOD)
                endif
            elseif(poinOD == 0) then
                iPartMesh(val_iPart)%iFlag_Poin(i  ) = FLAG_INVAL
                iPartMesh(val_iPart)%iPart_Poin(i  ) = 0
                iPartMesh(val_iPart)%iHost_Poin(i  ) = FLAG_INVAL
                iPartMesh(val_iPart)%iClor_Poin(i  ) = -1
                iPartMesh(val_iPart)%iCoor_Poin(i,:) = 0.0
                iPartMesh(val_iPart)%iVolu_Poin(i  ) = 0.0
                if(PHYSICAL_PROBLEM >= _TURB) then
                iPartMesh(val_iPart)%iWDis_Poin(i  ) = 0.0
                endif
                if(GRID_MOVEMENT == _YES) then
                iPartMesh(val_iPart)%dCoor_Poin(i,:) = 0.0
                endif
            elseif(poinOD < 0) then
                write(ioPort_Out,*) 'Error1 in calPartMesh'
                stop
            endif
        
        enddo
    
    
        !------------------------nSate相关
        do i=1,iMeshMapp(val_iPart)%nLSate
            sateOP = iMeshMapp(val_iPart)%iMapLToG_Sate(i)
        
            if(sateOP > 0) then
                poinOD = iGlobalMesh%iSate_Poin(sateOP,1)
                poinND = iMeshMapp(val_iPart)%iMapGToL_Poin(poinOD)
        
                iPartMesh(val_iPart)%iCoef_Sate(i,:) = iGlobalMesh%iCoef_Sate(:,sateOP)
            else
                write(ioPort_Out,*) 'Error2 in calPartMesh'
                stop
            endif
        enddo
    
    
        !------------------------nElem相关
        
        !------------------------nBele相关
        
        !------------------------nBelv相关
        do i=1,iMeshMapp(val_iPart)%nLBelv
            belvOD = iMeshMapp(val_iPart)%iMapLToG_Belv(i)
            
            poinOD = iGlobalMesh%iBelvProp(belvOD,1)
            markOD = iGlobalMesh%iBelvProp(belvOD,2)
            
            poinND = iMeshMapp(val_iPart)%iMapGToL_Poin(poinOD)
            
            iPartMesh(val_iPart)%iBelvProp(i,1) = poinND
            iPartMesh(val_iPart)%iBelvProp(i,2) = markOD
            
            if(iGlobalMesh%iExteRegnSANO%KIND /= EXTE_NONE) then
                iPartMesh(val_iPart)%iCoor_Belv(i,:) = iGlobalMesh%iCoor_Belv(belvOD,:)
            endif
        
            iPartMesh(val_iPart)%iArea_Belv(i  ) = iGlobalMesh%iArea_Belv(belvOD  )
            iPartMesh(val_iPart)%iNvor_Belv(i,:) = iGlobalMesh%iNvor_Belv(belvOD,:)
            iPartMesh(val_iPart)%iCoef_Belv(i,:) = iGlobalMesh%iCoef_Belv(belvOD,:)
        enddo
        
    
        !------------------------nMark + nDoma相关
    
    
    endsubroutine
    !
endmodule
!
    
    