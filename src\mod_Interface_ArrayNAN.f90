module mod_Interface_ArrayNAN
    use ieee_arithmetic
    implicit none

    interface isArrayNAN
        MODULE PROCEDURE isArrayNANReal1D
        MODULE PROCEDURE isArrayNANReal2D
        MODULE PROCEDURE isArrayNANReal3D

        MODULE PROCEDURE isArrayNANDouble1D
        MODULE PROCEDURE isArrayNANDouble2D
        MODULE PROCEDURE isArrayNANDouble3D
    endinterface
    !
contains
    !
    logical function isArrayNANReal1D(val_array, val_dimX)
        implicit none
        real(kind=4),intent(in):: val_array(val_dimX)
        integer,intent(in):: val_dimX
        !
        integer:: i
        logical:: rev

        rev = .false.

        do i=1,val_dimX
            if(IEEE_IS_NAN(val_array(i))) then
                rev = .true.
                exit
            endif
        end do

        isArrayNANReal1D = rev

    endfunction
    !
    logical function isArrayNANReal2D(val_array, val_dimX,val_dimY)
        implicit none
        real(kind=4),intent(in):: val_array(val_dimX,val_dimY)
        integer,intent(in):: val_dimX
        integer,intent(in):: val_dimY
        !
        integer:: i,j
        logical:: rev

        rev = .false.

        do i=1,val_dimX
            do j=1,val_dimY
                if(IEEE_IS_NAN(val_array(i,j))) then
                    rev = .true.
                    exit
                endif
            enddo
        end do

        isArrayNANReal2D = rev

    endfunction
    !
    logical function isArrayNANReal3D(val_array, val_dimX, val_dimY, val_dimZ)
        implicit none
        real(kind=4),intent(in):: val_array(val_dimX,val_dimY,val_dimZ)
        integer,intent(in):: val_dimX
        integer,intent(in):: val_dimY
        integer,intent(in):: val_dimZ
        !
        integer:: i,j,k
        logical:: rev

        rev = .false.

        do i=1,val_dimX
            do j=1,val_dimY
                do k=1,val_dimZ
                    if(IEEE_IS_NAN(val_array(i,j,k))) then
                        rev = .true.
                        exit
                    endif
                end do
            end do
        end do

        isArrayNANReal3D = rev

    endfunction
    !
    logical function isArrayNANDouble1D(val_array, val_dimX)
        implicit none
        real(kind=8),intent(in):: val_array(val_dimX)
        integer,intent(in):: val_dimX
        !
        integer:: i
        logical:: rev

        rev = .false.

        do i=1,val_dimX
            if(IEEE_IS_NAN(val_array(i))) then
                rev = .true.
                exit
            endif
        end do

        isArrayNANDouble1D = rev

    endfunction
    !
    logical function isArrayNANDouble2D(val_array, val_dimX,val_dimY)
        implicit none
        real(kind=8),intent(in):: val_array(val_dimX,val_dimY)
        integer,intent(in):: val_dimX
        integer,intent(in):: val_dimY
        !
        integer:: i,j
        logical:: rev

        rev = .false.

        do i=1,val_dimX
            do j=1,val_dimY
                if(IEEE_IS_NAN(val_array(i,j))) then
                    rev = .true.
                    exit
                endif
            enddo
        end do

        isArrayNANDouble2D = rev

    endfunction
    !
    logical function isArrayNANDouble3D(val_array, val_dimX, val_dimY, val_dimZ)
        implicit none
        real(kind=8),intent(in):: val_array(val_dimX,val_dimY,val_dimZ)
        integer,intent(in):: val_dimX
        integer,intent(in):: val_dimY
        integer,intent(in):: val_dimZ
        !
        integer:: i,j,k
        logical:: rev

        rev = .false.

        do i=1,val_dimX
            do j=1,val_dimY
                do k=1,val_dimZ
                    if(IEEE_IS_NAN(val_array(i,j,k))) then
                        rev = .true.
                        exit
                    endif
                end do
            end do
        end do

        isArrayNANDouble3D = rev

    endfunction
    !
endmodule