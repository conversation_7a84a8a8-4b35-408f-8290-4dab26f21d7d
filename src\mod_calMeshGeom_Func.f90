!
module mod_calMeshGeom_Func
    implicit none
    !
    contains
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getVolumeOfElem(val_nDim,val_ick,val_size,val_iccc,val_vol)
        use mod_PreDefine_Precision
        use mod_PreDefine_Elem
        use mod_PreDefine_IOPort
        use mod_ElemProp
        !
        use mod_vectorAlgebra
        implicit none
        integer,intent(in):: val_nDim
        integer,intent(in):: val_ick
        integer,intent(in):: val_size
        real(kind=8),intent(in):: val_iccc(val_nDim,val_size)
        real(kind=8),intent(inout):: val_vol
    
        integer:: i
        real(kind=8):: temp_vol
    
    
        if(val_ick == ELEM_LINE) then !线单元
            temp_vol = 0.0
            do i=1,val_nDim
                temp_vol = temp_vol + (val_iccc(i,1) - val_iccc(i,2))**2
            enddo
            temp_vol = sqrt(temp_vol)
        
            val_vol = temp_vol
        
        elseif((val_ick == ELEM_TRIANGLE).or. &     !三角形单元
               (val_ick == ELEM_RECTANGLE)) then    !四边形单元
        
            val_vol = 0.0
        
            if(val_ick == ELEM_TRIANGLE) then
                call getVolumeOfTriangle(   &
                            val_nDim,       &
                            val_iccc(:,1),  &
                            val_iccc(:,2),  &
                            val_iccc(:,3),  &
                            val_vol         )
            
            elseif(val_ick == ELEM_RECTANGLE) then
                call getVolumeOfRectangle(  &
                            val_nDim,       &
                            val_iccc(:,1),  &
                            val_iccc(:,2),  &
                            val_iccc(:,3),  &
                            val_iccc(:,4),  &
                            val_vol         )
            
            endif
        
        elseif((val_ick == ELEM_TETRAHEDRAL).or. &  !四面体单元
               (val_ick == ELEM_HEXAHEDRAL ).or. &  !六面体单元
               (val_ick == ELEM_WEDGE      ).or. &  !三棱柱单元
               (val_ick == ELEM_PYRAMID    )) then  !金字塔单元
               
            val_vol = 0.0
        
            do i=1,ElemProp_diviNum(val_ick)
                call getVolumeOfTetrahedral(val_nDim,                   &
                            val_iccc(:,ElemProp_diviList(1,i,val_ick)), &
                            val_iccc(:,ElemProp_diviList(2,i,val_ick)), &
                            val_iccc(:,ElemProp_diviList(3,i,val_ick)), &
                            val_iccc(:,ElemProp_diviList(4,i,val_ick)), &
                            temp_vol                                    )
            
                val_vol = val_vol+temp_vol
            enddo
        
        else
            val_vol = 1.0e20
        
        endif
    
    endsubroutine

    
    !---------------------------------------------
    !四边形面积
    !---------------------------------------------
    subroutine getVolumeOfRectangle(val_nDim,val_coorA,val_coorB,val_coorC,val_coorD,val_vol)
        use mod_PreDefine_Precision
        integer,intent(in):: val_nDim
        real(kind=8),intent(in),dimension(val_nDim):: val_coorA
        real(kind=8),intent(in),dimension(val_nDim):: val_coorB
        real(kind=8),intent(in),dimension(val_nDim):: val_coorC
        real(kind=8),intent(in),dimension(val_nDim):: val_coorD
        real(kind=8),intent(inout):: val_vol
        !
        integer:: i
        real(kind=8):: vec_R1(val_nDim),vec_R2(val_nDim)
        real(kind=8):: slen_R1,slen_R2,dotValu
    
    
        !
        vec_R1(:) = val_coorC(:) - val_coorA(:)
        vec_R2(:) = val_coorD(:) - val_coorB(:)
    
        !==采用单精度计算时，由于截断误差，狭长型网格面积为零
        slen_R1 = vec_R1(1)*vec_R1(1) + vec_R1(2)*vec_R1(2)
        slen_R2 = vec_R2(1)*vec_R2(1) + vec_R2(2)*vec_R2(2)
        dotValu = vec_R1(1)*vec_R2(1) + vec_R1(2)*vec_R2(2)
        if(val_nDim == 3) then
            slen_R1 = slen_R1 + vec_R1(val_nDim)*vec_R1(val_nDim)
            slen_R2 = slen_R2 + vec_R2(val_nDim)*vec_R2(val_nDim)
            dotValu = dotValu + vec_R1(val_nDim)*vec_R2(val_nDim)
        endif
    
        val_vol = 0.5*sqrt(slen_R1*slen_R2 - dotValu*dotValu)
    
    
    endsubroutine

    
    !---------------------------------------------
    !四面体体积
    !---------------------------------------------
    subroutine getVolumeOfTetrahedral(val_nDim,val_coorA,val_coorB,val_coorC,val_coorD,val_vol)
        use mod_PreDefine_Precision
        integer,intent(in):: val_nDim
        real(kind=8),intent(in):: val_coorA(val_nDim)
        real(kind=8),intent(in):: val_coorB(val_nDim)
        real(kind=8),intent(in):: val_coorC(val_nDim)
        real(kind=8),intent(in):: val_coorD(val_nDim)
        real(kind=8),intent(out):: val_vol
    
        integer:: i,j
        real(kind=8):: temp
        real(kind=8):: vectorA(3),vectorB(3),vectorC(3)
    
        if(val_nDim /= 3) then
            val_vol = 1.0e20
            return
        endif
    
        vectorA(:) = val_coorB(:) - val_coorA(:)
        vectorB(:) = val_coorC(:) - val_coorA(:)
        vectorC(:) = val_coorD(:) - val_coorA(:)
    
        temp = vectorA(1)*vectorB(2)*vectorC(3) + &
               vectorA(2)*vectorB(3)*vectorC(1) + &
               vectorA(3)*vectorB(1)*vectorC(2) - &
               vectorA(3)*vectorB(2)*vectorC(1) - &
               vectorA(2)*vectorB(1)*vectorC(3) - &
               vectorA(1)*vectorB(3)*vectorC(2)
    
        val_vol = abs(temp)/6.0
    
    endsubroutine

    
    !---------------------------------------------
    !三角形面积
    !---------------------------------------------
    subroutine getVolumeOfTriangle(val_nDim,val_coorA,val_coorB,val_coorC,val_vol)
        use mod_PreDefine_Precision
        integer,intent(in):: val_nDim
        real(kind=8),intent(in),dimension(val_nDim):: val_coorA
        real(kind=8),intent(in),dimension(val_nDim):: val_coorB
        real(kind=8),intent(in),dimension(val_nDim):: val_coorC
        real(kind=8),intent(out):: val_vol
    
        integer:: i
        real(kind=8):: vec_R1(val_nDim),vec_R2(val_nDim)
        real(kind=8):: slen_R1,slen_R2,dotValu
    
        vec_R1(:) = val_coorC(:) - val_coorA(:)
        vec_R2(:) = val_coorB(:) - val_coorA(:)
        !
        slen_R1 = vec_R1(1)*vec_R1(1) + vec_R1(2)*vec_R1(2)
        slen_R2 = vec_R2(1)*vec_R2(1) + vec_R2(2)*vec_R2(2)
        dotValu = vec_R1(1)*vec_R2(1) + vec_R1(2)*vec_R2(2)
        if(val_nDim == 3) then
            slen_R1 = slen_R1 + vec_R1(val_nDim)*vec_R1(val_nDim)
            slen_R2 = slen_R2 + vec_R2(val_nDim)*vec_R2(val_nDim)
            dotValu = dotValu + vec_R1(val_nDim)*vec_R2(val_nDim)
        endif
    
        val_vol = 0.5*sqrt(slen_R1*slen_R2 - dotValu*dotValu)
    
    endsubroutine

    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getNormalVector(val_nDim,val_iek,val_size,val_ieec,val_normalVector)
        use mod_PreDefine_Precision
        use mod_PreDefine_Elem
        use mod_Interface_Unitization
        implicit none
        integer,intent(in):: val_nDim
        integer,intent(in):: val_iek
        integer,intent(in):: val_size
        real(kind=8),intent(in):: val_ieec(val_nDim,val_size)
        real(kind=8),intent(out):: val_normalVector(val_nDim)
    
        integer:: i
        real(kind=8),dimension(val_nDim):: nvi
        real(kind=8),dimension(val_nDim):: r1,r2
    
        if(val_nDim == 2) then
            nvi(1) = val_ieec(2,1) - val_ieec(2,2)
            nvi(2) = val_ieec(1,2) - val_ieec(1,1)
        
            call unitization(val_nDim,nvi)
            val_normalVector(:) = nvi(:)
        
        elseif(val_nDim == 3) then
            r1(:) = val_ieec(:,3) - val_ieec(:,1)
            r2(:) = val_ieec(:,4) - val_ieec(:,2)
        
            nvi(1) = r1(2)*r2(3) - r1(3)*r2(2)
            nvi(2) = r1(3)*r2(1) - r1(1)*r2(3)
            nvi(3) = r1(1)*r2(2) - r1(2)*r2(1)
        
            call unitization(val_nDim,nvi)
            val_normalVector(:) = nvi(:)
        
        else
            val_normalVector(:) = 0.0
        
        endif
    
    endsubroutine

    
    !---------------------------------------------
    !
    !---------------------------------------------   
    subroutine getAreaNvor_Edge(val_nDim,val_iek,val_size,val_ieec,val_areaFi,val_NvorFi)
        use mod_PreDefine_Precision
        use mod_PreDefine_Elem
        use mod_Interface_Unitization
        use mod_ElemProp
        use mod_vectorAlgebra
        implicit none
        integer,intent(in):: val_nDim
        integer,intent(in):: val_iek
        integer,intent(in):: val_size
        real(kind=8),intent(in):: val_ieec(val_nDim,val_size)
        real(kind=8),intent(out):: val_areaFi
        real(kind=8),intent(out):: val_NvorFi(val_nDim)
        !
        integer:: i
        real(kind=8):: Areai
        real(kind=8),dimension(val_nDim):: nvi
        real(kind=8),dimension(val_nDim):: r1,r2
        real(kind=8):: slen_R1,slen_R2,dotValu
    
    
        val_areaFi    = 0.0
        val_NvorFi(:) = 0.0
    
        if(val_nDim == 2) then
            if(val_iek /= ELEM_LINE) return
         
            nvi(1) = val_ieec(2,1) - val_ieec(2,2)
            nvi(2) = val_ieec(1,2) - val_ieec(1,1)
        
            call unitization(val_nDim,nvi)
            val_NvorFi(:) = nvi(:)
        
            call vector_length(val_nDim,val_ieec(:,1),val_ieec(:,2),Areai)
        
            val_areaFi = Areai
        
        elseif(val_nDim == 3) then
            if((val_iek /= ELEM_TRIANGLE).and.(val_iek /= ELEM_RECTANGLE)) return
        
            r1(:) = val_ieec(:,3) - val_ieec(:,1)
            r2(:) = val_ieec(:,4) - val_ieec(:,2)
        
            nvi(1) = r1(2)*r2(3) - r1(3)*r2(2)
            nvi(2) = r1(3)*r2(1) - r1(1)*r2(3)
            nvi(3) = r1(1)*r2(2) - r1(2)*r2(1)
        
            call unitization(val_nDim,nvi)
            val_NvorFi(:) = nvi(:)
            
            slen_R1 = r1(1)*r1(1) + r1(2)*r1(2) + r1(3)*r1(3)
            slen_R2 = r2(1)*r2(1) + r2(2)*r2(2) + r2(3)*r2(3)
            dotValu = r1(1)*r2(1) + r1(2)*r2(2) + r1(3)*r2(3)
    
            Areai = 0.5*sqrt(slen_R1*slen_R2 - dotValu*dotValu)
    
            val_areaFi = Areai
        
        endif
    
    
    endsubroutine
    !
endmodule    
!

    