!
subroutine iniMeshDeform_DWM(val_iDeformZone,val_iMesh)
    use mod_TypeDef_Mesh
    use mod_Mesh_Deform
    use mod_vectorAlgebra
    implicit none
    type(typ_DeformZone),intent(inout):: val_iDeformZone
    type(typ_GlobalMesh),intent(in):: val_iMesh
    !
    integer:: i,j,poinID,regnID
    real(kind=REALLEN):: iCoor(nDim),iVec(nDim),iDist,iCoef
    
    
    if(val_iDeformZone%DeformMethod /= DEFORMMETHOD_DWM) return
    
    allocate(val_iDeformZone%iDeformCoef(val_iDeformZone%nPoinBC,val_iDeformZone%nPoin))
    val_iDeformZone%iDeformCoef(:,:) = 0
    
    do i=1,val_iDeformZone%nPoin
        poinID = val_iDeformZone%iPoinProp(i,1)
        iCoor(:) = val_iMesh%iCoor_Poin(:,poinID)
        
        do j=1,val_iDeformZone%nPoinBC
            poinID = val_iDeformZone%iPoinPropBC(j,1)
            iVec(:) = val_iMesh%iCoor_Poin(:,poinID) -iCoor(:)
            
            call vector_length(nDim, iVec, iDist)
            
            SELECT CASE (val_iDeformZone%iPoinPropBC(j,2))
            CASE (DEFORMPOINT_DEFOM)
                iCoef = iDist**(-val_iDeformZone%iDefoCoef_DWM_Moved)
                
            CASE (DEFORMPOINT_STILL)
                iCoef = iDist**(-val_iDeformZone%iDefoCoef_DWM_Still)
                
            END SELECT
              
            val_iDeformZone%iDeformCoef(j,i) = iCoef  
        enddo
    enddo
    
endsubroutine
!
