!
module mod_calIReMapp_LBL
    use mod_MeshReordering
    use mod_PreDefine_Flag
    use mod_PreDefine_Mark
    use mod_PreDefine_IOPort
    use mod_Interface_AllocateArray
    implicit none
    !
contains
    !
    !---------------------------------------------
    !---------------------------------------------
    subroutine calIReMapp_Poin_LBL(val_iPart,val_isOk)
        use mod_Project
        implicit none
        integer,intent(in):: val_iPart
        logical,intent(out):: val_isOk
        !
        integer:: i,j,k,iCount
        integer:: poinOD,poinGD,sateOD,sateGD
        integer:: nWPoin,nLPoin,miniGD,miniOD
        integer,allocatable:: iWPoin(:),iLPoin(:)
        integer,allocatable:: isWPoin(:),isLPoin(:)
        
        !---------------------------------------------
        !---------------------------------------------
        !
        write(*,*) 'T1'
        iReMapp%nGPoin = iReMesh%nPoin
        iReMapp%nLPoin = iReMesh%nPoin
        call allocateArray(iReMapp%iMapGToL_Poin, iReMapp%nGPoin)
        call allocateArray(iReMapp%iMapLToG_Poin, iReMapp%nLPoin)
        iReMapp%iMapGToL_Poin(:) = 0
        iReMapp%iMapLToG_Poin(:) = 0
        
        !---------------------------------------------
        !---------------------------------------------
        !
        write(*,*) 'T2'
        call allocateArray(isWPoin, iGlobalMesh%nPoin)
        call allocateArray(isLPoin, iGlobalMesh%nPoin)
        isWPoin(:) = 0
        isLPoin(:) = 0
        
        do i=1,iGlobalMesh%nMark
            if(iGlobalMesh%iMark(i)%KIND == MARK_WALL) then
                do j=1,iGlobalMesh%iMark(i)%nPoin
                    poinGD = iGlobalMesh%iMark(i)%iPoin(j)
                    
                    isWPoin(poinGD) = 1
                enddo
            if(nDim==2) exit
            else
                do j=1,iGlobalMesh%iMark(i)%nPoin
                    poinGD = iGlobalMesh%iMark(i)%iPoin(j)
                    
                    isLPoin(poinGD) = 1
                enddo
            endif
        enddo
        
        do i=1,iGlobalMesh%nPoin
            if(isWPoin(i) == 0) then
                isLPoin(i) = 0
            endif
        enddo
        
        
        write(*,*) 'T3'
        !---------------------------------------------
        !---------------------------------------------
        !Select the wall points or BC points to start the Reordering procedure.
        nWPoin = 0
        miniGD = 100000000
        do i=1,iMeshMapp(val_iPart)%nLPoin
            poinGD = iMeshMapp(val_iPart)%iMapLToG_Poin(i)
            
            if(isWPoin(poinGD) == 1) then
                nWPoin = nWPoin + 1
                
                miniGD = min(miniGD, poinGD)
            endif
        enddo
        
        if(nWPoin <= 0) then
            write(IOPort_OUT,*) 'Error in calIReMapp_Poin_LBL: nWPoin <= 0.'
            stop
        endif
        
        if(nDim == 3) then
            nLPoin = 0
            miniGD = 100000000
            do i=1,iMeshMapp(val_iPart)%nLPoin
                poinGD = iMeshMapp(val_iPart)%iMapLToG_Poin(i)
            
                if(isLPoin(poinGD) == 1) then
                    nLPoin = nLPoin + 1
                
                    miniGD = min(miniGD, poinGD)
                endif
            enddo
        endif
        
        write(*,*) 'T4'
        !---------------------------------------------
        !---------------------------------------------
        !ReOrdering the wall points.
        if(nDim == 2) then
            miniOD = iMeshMapp(val_iPart)%iMapGToL_Poin(miniGD)
            
            iReMapp%iMapLToG_Poin(1     ) = miniOD
            iReMapp%iMapGToL_Poin(miniOD) = 1
            
            iCount = 1
            do i=1,nWPoin
                poinOD = iReMapp%iMapLToG_Poin(i)
                poinGD = iMeshMapp(val_iPart)%iMapLToG_Poin(poinOD)
                
                do j=iGlobalMesh%kSate_Poin(poinGD)+1,iGlobalMesh%kSate_Poin(poinGD+1)
                    sateGD = iGlobalMesh%iSate_Poin(j,1)
                    sateOD = iMeshMapp(val_iPart)%iMapGToL_Poin(sateGD)
                    
                    if(isWPoin(sateGD) == 0) cycle
                    
                    if(iReMapp%iMapGToL_Poin(sateOD) == 0) then
                        iCount = iCount + 1
                    
                        iReMapp%iMapGToL_Poin(sateOD) = iCount
                        iReMapp%iMapLToG_Poin(iCount) = sateOD
                        
                        if(i==1) exit
                    endif
                enddo
            enddo
            
        elseif(nDim == 3) then
            !pause 'calIReMapp_Poin_LBL has not been finished for 3D.'
            !stop
            
            if(nLPoin <= 0) then
                pause 'Error nLPoin <= 0'
                stop
            endif
            
            
            miniOD = iMeshMapp(val_iPart)%iMapGToL_Poin(miniGD)
            
            iReMapp%iMapLToG_Poin(1     ) = miniOD
            iReMapp%iMapGToL_Poin(miniOD) = 1
            
            iCount = 1
            do i=1,nLPoin
                poinOD = iReMapp%iMapLToG_Poin(i)
                poinGD = iMeshMapp(val_iPart)%iMapLToG_Poin(poinOD)
                
                do j=iGlobalMesh%kSate_Poin(poinGD)+1,iGlobalMesh%kSate_Poin(poinGD+1)
                    sateGD = iGlobalMesh%iSate_Poin(j,1)
                    sateOD = iMeshMapp(val_iPart)%iMapGToL_Poin(sateGD)
                    
                    if(isLPoin(sateGD) == 0) cycle
                    
                    if(iReMapp%iMapGToL_Poin(sateOD) == 0) then
                        iCount = iCount + 1
                    
                        iReMapp%iMapGToL_Poin(sateOD) = iCount
                        iReMapp%iMapLToG_Poin(iCount) = sateOD
                        
                        if(i==1) exit
                    endif
                enddo
            enddo
            
            do i=1,nWPoin
                poinOD = iReMapp%iMapLToG_Poin(i)
                poinGD = iMeshMapp(val_iPart)%iMapLToG_Poin(poinOD)
                
                do j=iGlobalMesh%kSate_Poin(poinGD)+1,iGlobalMesh%kSate_Poin(poinGD+1)
                    sateGD = iGlobalMesh%iSate_Poin(j,1)
                    sateOD = iMeshMapp(val_iPart)%iMapGToL_Poin(sateGD)
                    
                    if(isWPoin(sateGD) == 0) cycle
                    
                    if(iReMapp%iMapGToL_Poin(sateOD) == 0) then
                        iCount = iCount + 1
                    
                        iReMapp%iMapGToL_Poin(sateOD) = iCount
                        iReMapp%iMapLToG_Poin(iCount) = sateOD
                    endif
                enddo
            enddo
        endif
        
        write(*,*) 'T5'
        
        !Layer-wise spanning
        iCount = nWPoin
        do i=1,iReMapp%nLPoin
            poinOD = iReMapp%iMapLToG_Poin(i)
            poinGD = iMeshMapp(val_iPart)%iMapLToG_Poin(poinOD)
            
            do j=iGlobalMesh%kSate_Poin(poinGD)+1,iGlobalMesh%kSate_Poin(poinGD+1)
                sateGD = iGlobalMesh%iSate_Poin(j,1)
                sateOD = iMeshMapp(val_iPart)%iMapGToL_Poin(sateGD)
                
                if(iReMapp%iMapGToL_Poin(sateOD) == 0) then
                    iCount = iCount + 1
                    
                    iReMapp%iMapGToL_Poin(sateOD) = iCount
                    iReMapp%iMapLToG_Poin(iCount) = sateOD
                endif
            enddo
        enddo
        
        write(*,*) 'T6'
        val_isOk = .true.
        !return
        
    endsubroutine
    !
    !---------------------------------------------
    subroutine calIReMapp_Sate_LBL(val_iPart,val_isOk)
        use mod_Project
        implicit none
        integer,intent(in):: val_iPart
        logical,intent(out):: val_isOk
        !
        integer:: i,j,k,iCount
        integer:: nSate,iLSate(1000)
        integer:: miniID,miniIP,begnIP
        
        
        !---------------------------------------------
        !---------------------------------------------
        iReMapp%nGSate = iReMesh%nSate
        iReMapp%nLSate = iReMesh%nSate
        call allocateArray(iReMapp%iMapGToL_Sate, iReMapp%nGSate)
        call allocateArray(iReMapp%iMapLToG_Sate, iReMapp%nLSate)
        iReMapp%iMapGToL_Sate(:) = 0
        iReMapp%iMapLToG_Sate(:) = 0
        
        !---------------------------------------------
        !---------------------------------------------
        iLSate(:) = 0
        do i=1,iMeshMapp(val_iPart)%nLPoin
            begnIP = iReMesh%kSate_Poin(i)
            nSate  = iReMesh%kSate_Poin(i+1) - begnIP
            if(nSate <= 0) cycle
            
            do j=1,nSate
                iLSate(j) = iReMesh%iSate_Poin(iReMesh%kSate_Poin(i)+j, 1)
            enddo
            
            iCount = 0
            do j=1,nSate
                miniID = 100000000
                miniIP = 0
                do k=1,nSate
                    if(iLSate(k) == 0) cycle
                    if(iLSate(k) < miniID) then
                        miniID = iLSate(k)
                        miniIP = k
                    endif
                enddo
                
                if(miniIP > 0) then
                    iCount = iCount + 1
                    
                    iReMapp%iMapLToG_Sate(begnIP + j     ) = begnIP + miniIP
                    iReMapp%iMapGToL_Sate(begnIP + miniIP) = begnIP + j
                    iLSate(miniIP) = 0
                endif
            enddo
            
            if(iCount /= nSate) then
                write(*,*) 'SSS'
                pause
            endif
        enddo    
        
        val_isOk = .true.
        return
             
    endsubroutine
    !
    !---------------------------------------------
    !---------------------------------------------
    subroutine calIReMapp_Poin_LBL_VO1(val_iPart,val_isOk)
        use mod_Project
        implicit none
        integer,intent(in):: val_iPart
        logical,intent(out):: val_isOk
        !
        integer:: i,j,k,iCont
        integer:: poinOD,poinGD,sateGD,sateOD
        integer:: nFrontPoin,nWallPoin,nBCPoin
        integer,allocatable:: iFrontPoin(:)
        !
        !---------------------------------------------
        !---------------------------------------------
        
        iReMapp%nGPoin = iReMesh%nPoin
        iReMapp%nLPoin = iReMesh%nPoin
        allocate(iReMapp%iMapGToL_Poin(iReMapp%nGPoin))
        allocate(iReMapp%iMapLToG_Poin(iReMapp%nLPoin))
        iReMapp%iMapGToL_Poin(:) = 0
        iReMapp%iMapLToG_Poin(:) = 0
        
        !Build FrontMark for iReMesh
        nWallPoin = 0
        nBCPoin   = 0
        do i=1,iMeshMapp(val_iPart)%nLPoin
            poinOD = iMeshMapp(val_iPart)%iMapLToG_Poin(i)
            
            if(poinOD > 0) then
                if(iGlobalMesh%iFlag_Poin(poinOD) /= FLAG_INNER) then
                    nBCPoin = nBCPoin + 1
                    
                    if(iGlobalMesh%iFlag_Poin(poinOD) == MARK_WALL) then
                        nWallPoin = nWallPoin + 1
                    endif
                endif
            endif
        enddo
        
        if(nWallPoin > 0) then
            nFrontPoin = nWallPoin
            
            allocate(iFrontPoin(nFrontPoin))
            iFrontPoin(:) = 0
            
            nWallPoin   = 0
            do i=1,iMeshMapp(val_iPart)%nLPoin
                poinOD = iMeshMapp(val_iPart)%iMapLToG_Poin(i)
            
                if(poinOD > 0) then
                    if(iGlobalMesh%iFlag_Poin(poinOD) == MARK_WALL) then
                        nWallPoin = nWallPoin + 1
                        iFrontPoin(nWallPoin) = i
                    endif
                endif
            enddo
        elseif(nBCPoin > 0) then
            nFrontPoin = nBCPoin
            
            allocate(iFrontPoin(nFrontPoin))
            iFrontPoin(:) = 0
            
            nBCPoin   = 0
            do i=1,iMeshMapp(val_iPart)%nLPoin
                poinOD = iMeshMapp(val_iPart)%iMapLToG_Poin(i)
            
                if(poinOD > 0) then
                    if(iGlobalMesh%iFlag_Poin(poinOD) /= FLAG_INNER) then
                        nBCPoin = nBCPoin + 1
                        iFrontPoin(nBCPoin) = i
                    endif
                endif
            enddo
            
        else
            nFrontPoin = 1
            
            allocate(iFrontPoin(nFrontPoin))
            iFrontPoin(:) = 0
            
            iFrontPoin(1) = 1
        endif
        
        !Build front face
        do i=1,nFrontPoin
            poinOD = iFrontPoin(i)
            
            iReMapp%iMapGToL_Poin(poinOD) = i
            iReMapp%iMapLToG_Poin(i) = poinOD
        enddo
        
        !advancing-front ordering
        iCont = nFrontPoin
        do i=1,iReMapp%nLPoin
            poinOD = iReMapp%iMapLToG_Poin(i)
            if(poinOD <= 0) cycle
            
            poinGD = iMeshMapp(val_iPart)%iMapLToG_Poin(poinOD)
            
            do j=iGlobalMesh%kSate_Poin(poinGD)+1,iGlobalMesh%kSate_Poin(poinGD+1)
                sateGD = iReMesh%iSate_Poin(j,1)
                sateOD = iMeshMapp(val_iPart)%iMapGToL_Poin(sateGD)
                
                if(iReMapp%iMapGToL_Poin(sateOD) == 0) then
                    iCont = iCont + 1
                    
                    iReMapp%iMapGToL_Poin(sateOD) = iCont
                    iReMapp%iMapLToG_Poin(iCont) = sateOD
                endif
            enddo
        enddo
        
        val_isOk = .true.
        return
        
    endsubroutine
    !
    !---------------------------------------------
    subroutine calIReMapp_Sate_LBL_VO2(val_iPart,val_isOk)
        use mod_Project
        implicit none
        integer,intent(in):: val_iPart
        logical,intent(out):: val_isOk
        !
        integer:: i,j,k,iCont
        integer:: poinOD,nSate,sateMD,sateMP,beginIP
        integer:: iOSate(1000)
        
        iReMapp%nGSate = iReMesh%nSate
        iReMapp%nLSate = iReMesh%nSate
        allocate(iReMapp%iMapGToL_Sate(iReMapp%nGSate))
        allocate(iReMapp%iMapLToG_Sate(iReMapp%nLSate))
        iReMapp%iMapGToL_Sate(:) = 0
        iReMapp%iMapLToG_Sate(:) = 0
        !---------------------------------------------
        !---------------------------------------------
        iOSate(:) = 0
        
        do i=1,iMeshMapp(val_iPart)%nLPoin
            poinOD = iMeshMapp(val_iPart)%iMapLToG_Poin(i)
            
            if(poinOD <= 0) cycle
            
            nSate = iReMesh%kSate_Poin(i+1) - iReMesh%kSate_Poin(i)
            
            if(nSate <= 1) cycle
            
            beginIP = iReMesh%kSate_Poin(i)
            
            
            do j=1,nSate
                iOSate(j) = iReMesh%iSate_Poin(beginIP+j,1)
            enddo
            
            iCont = 0
            do j=1,nSate
                sateMD = 100000000
                do k=1,nSate
                    if(iOSate(k) > 0) then
                        if(IOSate(k) < sateMD) then
                            sateMD = iOSate(k)
                            sateMP = k
                        endif
                    endif
                enddo
                
                if(sateMD /= 100000000) then
                    iCont = iCont +1
                    
                    iReMapp%iMapLToG_Sate(beginIP + iCont ) = beginIP + sateMP
                    iReMapp%iMapGToL_Sate(beginIP + sateMP) = beginIP + iCont
                    
                    iOSate(sateMP) = 0
                endif
            enddo
            
            if(iCont /= nSate) then
                write(*,*) 'SSS'
                pause
            endif
            
        enddo    
        
        
        val_isOk = .true.
        return
            
    endsubroutine
    !
endmodule
! 
  