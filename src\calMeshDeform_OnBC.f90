!
subroutine calMeshDeform_OnBC
    use mod_Mesh_Deform
    implicit none
    integer:: i,j
    integer:: markID,deformKind
    
    
    do i=1,nDeformZone
        deformKind = iDeformZone(i)%DeformKind
        
        if(deformKind == DEFORMKIND_REGNT) then
            do j=1,iDeformZone(i)%nPoinBC
                iDeformZone(i)%iDeltPoinBC(1:nDim,j) = iDeformZone(i)%iTransDist(1:nDim,1)
            enddo
            cycle
        elseif(deformKind == DEFORMKIND_REGNR) then
            do j=1,iDeformZone(i)%nPoinBC
                iDeformZone(i)%iDeltPoinBC(1,j) = iDeformZone(i)%iRotatAngle(1)
            enddo
            cycle
        endif
        
        do j=1,iDeformZone(i)%nPoinBC
            markID = iDeformZone(i)%iPoinPropBC(j,3)
            
            if(deformKind == DEFORMKIND_TRANS) then
                iDeformZone(i)%iDeltPoinBC(1:nDim,j) = iDeformZone(i)%iTransDist(1:nDim,markID)
            
            elseif(deformKind == DEFORMKIND_ROTAT) then
                iDeformZone(i)%iDeltPoinBC(1,j) = iDeformZone(i)%iRotatAngle(markID)
            
            endif
        enddo
    enddo
    
endsubroutine
!
