
set(Sources "")
file(GLOB_RECURSE Sources  "${CMAKE_CURRENT_SOURCE_DIR}/*.f90")

#message(STATUS "Sources are: ${Sources}")

set(MeshTool_Target "MeshTrans_v1.0")

add_executable(${MeshTool_Target} ${Sources})

#target_link_libraries(${TARGET_NAME} PUBLIC metis)
#
#if(NOT MPI_Fortran_FOUND)
#    message(STATUS "MPI Fortran not found")
#endif()
#message(STATUS "MPI Fortran found")

#message(STATUS "MPI_FORTRAN_LIBRARIES are: ${MPI_Fortran_LIBRARIES}")
#message(STATUS "MPI_Fortran_INCLUDE_DIRS are: ${MPI_Fortran_INCLUDE_DIRS}")
#
#message(STATUS "MPI_Fortran_LIB_DIRS are: ${MPI_Fortran_LIB_DIRS}")
#message(STATUS "MPI_Fortran_LIBRARIES are: ${MPI_Fortran_LIBRARIES}")

#target_include_directories(${TARGET_NAME} PRIVATE ${MPI_Fortran_INCLUDE_DIRS})
#target_link_directories(${TARGET_NAME} PRIVATE ${MPI_LIBRARY_DIRS})
#target_link_libraries(${TARGET_NAME} PRIVATE ${MPI_Fortran_LIBRARIES})
