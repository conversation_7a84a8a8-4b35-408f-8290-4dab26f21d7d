!
module mod_Interface_Unitization
    implicit none
    !
    interface unitization
        MODULE PROCEDURE unitization_REAL
        MODULE PROCEDURE unitization_REAL8
    endinterface
    !
contains
    ! 
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine unitization_REAL(val_nDim,val_iVector)
        integer,intent(in):: val_nDim
        real,intent(inout):: val_iVector(val_nDim)
        !
        integer:: i
        real*8:: iLens
        
        iLens = 0.0
        do i=1,val_nDim
            iLens = iLens + val_iVector(i)*val_iVector(i)
        enddo
        
        if(iLens < 1.0E-100) return
        
        iLens = 1/sqrt(iLens)
        
        val_iVector(:) = val_iVector(:)*iLens
        
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine unitization_REAL8(val_nDim,val_iVector)
        integer,intent(in):: val_nDim
        real*8,intent(inout):: val_iVector(val_nDim)
        !
        integer:: i
        real*8:: iLens
        
        iLens = 0.0
        do i=1,val_nDim
            iLens = iLens + val_iVector(i)*val_iVector(i)
        enddo
        
        if(iLens < 1.0E-100) return
        
        iLens = 1/sqrt(iLens)
        
        val_iVector(:) = val_iVector(:)*iLens
        
    endsubroutine
    !
endmodule
!
    
   
