
!---------------------------------------------
!
!---------------------------------------------
subroutine calMesh_ROSate(val_iMesh)
    use mod_PreDefine_Precision
    use mod_PreDefine_IOPort
    use mod_TypeDef_Mesh
    use mod_ReOrderSatePart
    implicit none
    type(typ_Mesh),intent(inout):: val_iMesh
    !
    integer:: i,j
    integer:: iBegin,iEnd,iSize
    integer:: jBegin,jEnd,nSate
    integer:: nPoinPerBlock,sateIP,sateID
    integer,allocatable:: kSatePart(:),iSatePart(:),reSateOrd(:)
    integer,allocatable:: tempSate(:)
    real(kind=REALLEN),allocatable:: tempCoef(:,:)
    integer:: iSate_Temp(val_iMesh%nSate)
    
    
    !---------------------------------------------
    !
    nPoinPerBlock = oneAccessBits/REALLEN
    iSate_Temp(:) = val_iMesh%iSate_Poin(:,1)
    
    !---------------------------------------------
    !
    do i=1,val_iMesh%nPoin,nPoinPerBlock
        
        iBegin = i
        iEnd = min(i+nPoinPerBlock-1,val_iMesh%nPoin)
        iSize = iEnd - iBegin + 1
        
        if(iSize == 1) cycle
        
        jBegin = val_iMesh%kSate_Poin(iBegin)+1
        jEnd   = val_iMesh%kSate_Poin(iEnd+1)
        nSate  = jEnd - jBegin + 1
        
        allocate(kSatePart(iSize + 1))
        allocate(iSatePart(nSate))
        allocate(reSateOrd(nSate))
        allocate(tempSate(nSate))
        allocate(tempCoef(nDim,nSate))
        
        kSatePart(:) = val_iMesh%kSate_Poin(iBegin:iEnd+1) - val_iMesh%kSate_Poin(iBegin)
        iSatePart(:) = val_iMesh%iSate_Poin(jBegin:jEnd,1)
        reSateOrd(:) = 0
        
        
        call reOrderSatePart(kSatePart,iSatePart,       &
                    reSateOrd,iSize,nSate,nPoinPerBlock )
        
        do j=1,nSate
            sateIP = reSateOrd(j)
            sateID = iSatePart(sateIP)
            
            tempSate(j) = sateID
            tempCoef(:,j) = val_iMesh%iCoef_Sate(:,sateIP + val_iMesh%kSate_Poin(iBegin))
        enddo
        
        val_iMesh%iSate_Poin(jBegin:jEnd,1) = tempSate(:)
        do j=1,nDim
            val_iMesh%iCoef_Sate(j,jBegin:jEnd) = tempCoef(j,:)
        enddo
        
        
        deallocate(kSatePart,iSatePart,reSateOrd)
        deallocate(tempSate,tempCoef)
    enddo
    
    open(IOPort_Full,file='iSateCompare.dat',status='unknown')
        do i=1,val_iMesh%nPoin
            write(IOPort_Full,*) i
            write(IOPort_Full,*) iSate_Temp(val_iMesh%kSate_Poin(i)+1:val_iMesh%kSate_Poin(i+1))
            write(IOPort_Full,*) val_iMesh%iSate_Poin(val_iMesh%kSate_Poin(i)+1:val_iMesh%kSate_Poin(i+1),1)
            write(IOPort_Full,*)
        enddo
    
    close(IOPort_Full)
    
    
    !pause
endsubroutine
!
