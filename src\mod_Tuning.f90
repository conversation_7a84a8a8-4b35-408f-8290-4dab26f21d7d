module mod_Tuning
    use cudafor
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    use mod_PreDefine_Flag
    implicit none
    !
contains
    !
    attributes(host,device) subroutine restrainGrad_SYMM(gradEi,nvorEi)
        implicit none
        real(kind=REALLEN):: gradEi(nDim)
        real(kind=REALLEN):: nvorEi(nDim)
        !
        real(kind=REALLEN):: gradNi

        gradNi = gradEi(1)*nvorEi(1) + gradEi(2)*nvorEi(2) + gradEi(nDim)*nvorEi(nDim)*(nDim-2)

        gradEi(1   ) = gradEi(1   ) - gradNi*nvorEi(1   )
        gradEi(2   ) = gradEi(2   ) - gradNi*nvorEi(2   )
        if(nDim==3) then
        gradEi(nDim) = gradEi(nDim) - gradNi*nvorEi(nDim)
        endif

    end subroutine restrainGrad_SYMM
    !
    attributes(host,device) subroutine flowUpdLimt_Fun3D(soluEi, soluDi, limtFi, Gammo, limtCi,isLimt)
        implicit none
        real(kind=REALLEN):: soluEi(nDim+2)
        real(kind=REALLEN):: soluDi(nDim+2)
        real(kind=REALLEN):: limtFi
        real(kind=REALLEN):: Gammo
        real(kind=REALLEN):: limtCi
        logical           :: isLimt
        !
        integer:: k
        real(kind=REALLEN):: dInv,qEi,pEi,dp
        real(kind=REALLEN):: f1,f2,f3,g1


        dInv = 1.0/soluEi(1)

        f1 = abs(soluDi(1)) /soluEi(1)!density
        f2 = abs(soluDi(nDim+2))/soluEi(nDim+2) !energy
        g1 = max(1.0, max(f1,f2)/limtFi)

        ! if pressure changes too rapidly, fix it
        f1 = 1.0/g1
        dp  = Gammo*(soluDi(nDim+2)*f1 + 0.5*(soluEi(2)**2+soluEi(3)**2+soluEi(4)**2*(nDim-2))*dInv - &
                                    0.5*((soluEi(2)+soluDi(2)*f1)**2 + (soluEi(3)+soluDi(3)*f1)**2 + &
                                    (soluEi(4)+soluDi(4)*f1)**2*(nDim-2))/(soluEi(1)+soluDi(1)*f1))

        f3 = abs(dp)/pEi
        g1 = max(g1, f3/limtFi)

        limtCi = 1.0/g1

    endsubroutine
    !
    attributes(host,device) subroutine flowLimt_NNeg(soluEi, limMinD, limMaxD, limMinP, limMaxP, limMinT, limMaxT, Gammo, GasCo,isLimt)
        implicit none
        real(kind=REALLEN):: soluEi(nDim+2)
        real(kind=REALLEN):: limMinD
        real(kind=REALLEN):: limMaxD
        real(kind=REALLEN):: limMinP
        real(kind=REALLEN):: limMaxP
        real(kind=REALLEN):: limMinT
        real(kind=REALLEN):: limMaxT
        real(kind=REALLEN):: Gammo
        real(kind=REALLEN):: GasCo
        logical           :: isLimt
        !
        real(kind=REALLEN):: qEi,pEi,TEi

        isLimt = .false.

        if((soluEi(1) < limMinD).or.(soluEi(1)>limMaxD)) then
            soluEi(1) = min(limMaxD, max(soluEi(1), limMinD))
            isLimt = .true.
        endif

        qEi = 0.5*(soluEi(2)**2+soluEi(3)**2+soluEi(4)**2*(nDim-2))/soluEi(1)

        pEi = Gammo*(soluEi(nDim+2) - qEi)
        if((pEi < limMinP).or.(pEi > limMaxP)) then
            pEi = min(limMaxP, max(pEi, limMinP))
            isLimt = .true.
        endif

        TEi = pEi/(GasCo*soluEi(1))
        if(TEi > limMaxT) then
            TEi = limMaxT
            pEi = GasCo*soluEi(1)*TEi
            isLimt = .true.
        elseif(TEi < limMinT) then
            TEi = limMinT
            soluEi(1)= pEi/(GasCo*TEi)
            isLimt = .true.
        endif

        soluEi(nDim+2) = pEi/Gammo+qEi

    endsubroutine
    !
    attributes(global) subroutine tuningDTCoef_Global(val_iCalc_Poin,   &
            val_kSate_Poin, val_iSate_Poin, val_DTCOld,     &
            val_DTCoef, val_nPoin, val_nSate, val_nTMax     )
        implicit none
        integer,device:: val_iCalc_Poin(val_nPoin)
        integer,device:: val_kSate_Poin(val_nPoin+1)
        integer,device:: val_iSate_Poin(val_nSate,2)
        real(kind=REALLEN),device:: val_DTCOld(val_nPoin)
        real(kind=REALLEN),device:: val_DTCoef(val_nPoin)
        integer,value:: val_nPoin
        integer,value:: val_nSate
        integer,value:: val_nTMax
        !
        integer:: i,j,sID
        real(kind=REALLEN):: jCoef


        i = (blockIdx%x-1)*blockDim%x+threadIdx%x
        if(i > val_nTMax) return
        if(val_iCalc_Poin(i) /= FLAG_VALID) return

        jCoef = 1.0
        do j=val_kSate_Poin(i)+1,val_kSate_Poin(i+1)
            sID = val_iSate_Poin(j,1)
            if(val_iCalc_Poin(sID) == FLAG_INVAL) cycle

            jCoef = min(jCoef, val_DTCOld(sID))
        enddo

        val_DTCoef(i) = min(jCoef, min(1.0,1.3*jCoef))

    endsubroutine
    !
    !
end module mod_Tuning