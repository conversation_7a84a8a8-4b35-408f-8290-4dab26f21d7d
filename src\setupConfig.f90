
!---------------------------------------------
!
!---------------------------------------------
subroutine setupConfig
    use mod_PreDefine_Dimension
    use mod_PreDefine_Environment
    use mod_PreDefine_IOPort
    use mod_MPIEnvironment
    use mod_WorkPath
    use mod_Config
    use mod_Configer
    use mod_ErrMsg
    use omp_lib
    implicit none
    logical:: isOk


    call omp_set_dynamic(.false.)
    call omp_set_num_threads(nOmpThreads)
    write(*,"(1X,A,I3)") ">> Number of omp threads: ", omp_get_max_threads()
    
    !---------------------------------------------
    !---------------------------------------------
    call readCfgFile_DomaLines

    call readCfgFile_MarkLines

    call readCfgFile_MRFLines

    call readCfgFile_NameList
    
    call checkAndCompleteConfig(isOk)

    call trtConfig_DimensionLess

    if(.not.isOk) then
        if(myID == MASTER_NODE) then
            write(ioPort_Out,*) 'chkConfig ERROR !'
            stop
        endif
    endif

endsubroutine
!