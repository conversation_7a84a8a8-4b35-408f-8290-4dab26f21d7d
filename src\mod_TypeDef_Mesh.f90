
!---------------------------------------------
!
!---------------------------------------------
module mod_TypeDef_Mesh
    use cudafor
    !
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    use mod_PreDefine_Mark
    use mod_TypeDef_MeshComponent
    use mod_TypeDef_Division
    implicit none
    
    
    !---------------------------------------------
    !网格公共数据 
    !---------------------------------------------
    type:: typ_MeshBase
        !---------------------------------------------
        ! 基本信息
        !---------------------------------------------
        integer:: nPoin = 0                     !节点数目
        integer:: nElem = 0                     !单元数目
        integer:: nBEle = 0                     !边界数目
        integer:: nSate = 0                     !卫星点数
        integer:: nEdge = 0                     !连接数目
        !
        integer:: nDime = 0                     !维度
        integer:: nDoma = 0                     !区域数目
        integer:: nMark = 0                     !Mark数目
        integer:: nRegn = 0                     !Regn数目
        integer:: nSlip = 0                     !Slip边界对数目
        integer:: nMRF  = 0                     ! Number of MRF zones
        !
        !输入信息
        type(typ_Block   ),allocatable:: iDoma(:)           !网格块信息
        type(typ_Block   ),allocatable:: iMark(:)           !边界Mark信息
        type(typ_MeshRegn),allocatable:: iRegn(:)           !网格区域信息（单连通域）
        type(typ_BCPair  ),allocatable:: iSlip(:)           !Slip边界对信息
        !
        real(kind=REALLEN),allocatable:: iCoor_Poin(:,:)    !节点的坐标
        integer,allocatable:: iElem(:,:)                    !单元的顶点编号
        integer,allocatable:: iBele(:,:)                    !边界的顶点编号
        integer,allocatable:: iFlag_Elem(:)                 !单元标识
        integer,allocatable:: iEdge(:,:)                    !边界信息

        integer,allocatable:: iElemProp(:,:)                !Dim(nElem,1) domaID
        
        
        !---------------------------------------------
        !网格重构数据
        !---------------------------------------------
        !染色信息
        integer:: nClor                                     !图层数
        integer,allocatable:: iClor_Poin(:)                 !节点颜色
        integer,allocatable:: iFlag_Poin(:)                 !节点标记1
        integer,allocatable:: iIner_Poin(:)                 !节点标记2
        integer,allocatable:: kPoin_Clor(:)                 !每色层包含的点数
        integer,allocatable:: iPoin_Clor(:)                 !每色层包含的点编号列表
        integer,allocatable:: kBelv_Poin(:)                 !边界新增点云Ex
        integer,allocatable:: iBelv_Poin(:,:)               !边界新增点云Ex
        !重叠网格
        integer,allocatable:: iHost_Poin(:)                 !重叠宿主标识
        integer,allocatable:: iHostAlte_Poin(:,:)           !备选点，上限9个（nPoin，10）个数+编号
        !分区信息
        integer,allocatable:: iPart_Poin(:)                 !节点归属分区
        !延拓区域,用于重叠网格或滑移网格
        type(typ_ExteRegn):: iExteRegnSANO                  !Slip或Overset网格总区
        
        
        !---------------------------------------------
        !点信息Poin
        !---------------------------------------------
        integer,allocatable:: iDoma_Poin(:)                 !
        integer,allocatable:: iRegn_Poin(:)                 !节点所在区域编号
        integer,allocatable:: kSate_Poin(:)                 !节点的相邻点数
        integer,allocatable:: iSate_Poin(:,:)               !节点对相邻点编号列表
        !
        real(kind=REALLEN),allocatable:: iVolu_Poin(:)      !节点的体积
        real(kind=REALLEN),allocatable:: iRadu_Poin(:)      !节点的参考半径
        real(kind=REALLEN),allocatable:: iWDis_Poin(:)      !节点的物面距
        real(kind=REALLEN),allocatable:: dCoor_Poin(:,:)    !节点的位移
        !
        real(kind=REALLEN),allocatable:: iArea_Sate(:)      !点云的面积
        real(kind=REALLEN),allocatable:: iCoef_Sate(:,:)    !点云系数
        real(kind=REALLEN),allocatable:: iLens_Sate(:)      !点云长度
        real(kind=REALLEN),allocatable:: iPhii_Sate(:)      !点云各向异性系数
        
        
        !---------------------------------------------
        !边界信息Bele，意指网格定义边界
        !---------------------------------------------
        integer,allocatable:: iDomaProp(:,:)                !子区信息(单元数）
        integer,allocatable:: iMarkProp(:,:)                !边界区（点数+类型）
        integer,allocatable:: iBeleProp(:,:)                !边界信息（点数+MarkID）
        integer,allocatable:: iBeleLink(:,:)                !边界连接信息（左单元的第几个面）
        !
        real(kind=REALLEN),allocatable:: iNvor_Bele(:,:)    !边界法矢
        real(kind=REALLEN),allocatable:: iArea_Bele(:)      !边界面积
        
        
        !---------------------------------------------
        !改进边界信息Belv
        !---------------------------------------------
        integer:: nBelv             = 0                     !边界点数
        integer:: nBelx             = 0                     !边界点连单元总数
        integer           ,allocatable:: iBelvProp(:,:)     !poinID+markID
        integer           ,allocatable:: kBelx_Belv(:)      !
        integer           ,allocatable:: iBelx_Belv(:,:)    !
        real(kind=REALLEN),allocatable:: iCoor_Belv(:,:)    !新增边界点坐标
        real(kind=REALLEN),allocatable:: iArea_Belv(:)      !边界元面积
        real(kind=REALLEN),allocatable:: iNvor_Belv(:,:)    !边界元法矢
        real(kind=REALLEN),allocatable:: iCoef_Belv(:,:)    !边界元系数
        !
    endtype
    
    
    !---------------------------------------------
    !全局网格数据区
    !---------------------------------------------
    type,extends(typ_MeshBase):: typ_GlobalMesh
        !
        !---------------------------------------------
        !重构所需变量
        !---------------------------------------------
        logical:: isNeedMC              = .false. !是否染色
        logical:: isNeedOS              = .false. !是否重叠
        logical:: isNeedSP              = .false. !是否滑移
        logical:: isNeedPT              = .false. !是否分区
        
        
        !---------------------------------------------
        !辅助类变量
        !---------------------------------------------
        !线信息Line
        integer:: nLine             = 0                     !总线元数
        integer:: nElem_Line        = 0                     !线连接的总单元数
        integer,allocatable:: iLine(:,:)                    !线元信息
        integer,allocatable:: iFlag_Line(:)                 !线元标记
        integer,allocatable:: kElem_Line(:)                 !线元连接的单元数
        integer,allocatable:: iElem_Line(:,:)               !线元连接的单元编号列表
        
        
        !面信息Edge，意指单元的边界
        !integer:: nEdge             = 0                     !总边界数
        !integer,allocatable:: iEdge(:,:)                    !边界信息
        integer,allocatable:: iEdgeKind(:)                  !边界类型
        integer,allocatable:: iEdgeLink(:,:)                !边界连接信息 (el,er,kl,kr)
        !
        real(kind=REALLEN),allocatable:: iNvor_Edge(:,:)    !边界单位法矢
        real(kind=REALLEN),allocatable:: iArea_Edge(:)      !边界面积
        real(kind=REALLEN),allocatable:: iCoor_Edge(:,:)    !边界中心坐标
        
        
        !单元信息
        integer,allocatable:: iElemKind(:)                  !单元类型
        integer,allocatable:: iElemLink(:,:,:)              !单元连接信 (el,kl,fi)
        !
        real(kind=REALLEN),allocatable:: iVolu_Elem(:)      !单元体积
        real(kind=REALLEN),allocatable:: iCoor_Elem(:,:)    !单元中心坐标
    endtype
    
    
    !---------------------------------------------
    !分区网格数据区
    !---------------------------------------------
    
    type,extends(typ_MeshBase):: typ_Mesh
        !Interface information
        integer:: nInfc
        integer           ,allocatable:: iProp_Infc(:,:) !(nInfc,4) pL,pR,isReciprocal,Type !if(pR < 0) belv
        real(kind=REALLEN),allocatable:: iCoef_Infc(:,:) !(nInfc,2*nDim) nx,ny,nz,CL,CR for FVM, cxl,cyl,czl,cxr,cyr,czr for Meshless
        !动网格数据
        real(kind=REALLEN),allocatable:: iVelo_Poin(:,:)    !节点速度
        real(kind=REALLEN),allocatable:: iVelo_Belv(:,:)    !边界元速度
        real(kind=REALLEN),allocatable:: iVolu_Poin_n(:)    !第n步体积
        real(kind=REALLEN),allocatable:: iVolu_Poin_n1(:)   !第n-1步体积
        !网格数据交互区
        integer:: nExch             = 0                     !交互区数量
        type(typ_ExchRegn),allocatable:: iExch(:)           !交互区数据
        
        integer           ,allocatable:: iCalc_Poin(:)      !计算实体标识

        !备用数据 无网格发展
        real(kind=REALLEN),allocatable:: iCofR_Sate(:,:)
        real(kind=REALLEN),allocatable:: iCofR_Belv(:,:)
        !
        !---------------------------------------------
        !LU计算新增数据
        !---------------------------------------------
        integer:: nSatL,nSatR
        integer,allocatable:: kSatL_Poin(:)
        integer,allocatable:: kSatR_Poin(:)
        integer,allocatable:: iSatL_Poin(:,:)
        integer,allocatable:: iSatR_Poin(:,:)
        real(kind=REALLEN),allocatable:: iCoef_SatL(:,:)
        real(kind=REALLEN),allocatable:: iCoef_SatR(:,:)
        !
        integer:: nEdgL,nEdgR
        integer,allocatable:: kEdgL_Clor(:)
        integer,allocatable:: kEdgR_Clor(:)
        integer,allocatable:: iEdgL_Clor(:,:)
        integer,allocatable:: iEdgR_Clor(:,:)
        !
        integer:: nSaCL,nSaCR
        integer,allocatable:: kSatL_Clor(:)
        integer,allocatable:: kSatR_Clor(:)
        integer,allocatable:: iSatL_Clor(:,:)
        integer,allocatable:: iSatR_Clor(:,:)

#ifdef GFLOW_ENABLE_CUDA
        !---------------------------------------------
        !GPU DATA
        !---------------------------------------------
        integer,device,allocatable:: iFlag_Poin_d(:)        !节点标记
        integer,device,allocatable:: iIner_Poin_d(:)        !节点标记
        integer,device,allocatable:: iCalc_Poin_d(:)        !节点标记
        integer,device,allocatable:: iClor_Poin_d(:)        !节点颜色信息
        integer,device,allocatable:: kSate_Poin_d(:)        !点云
        integer,device,allocatable:: iSate_Poin_d(:,:)      !点云
        integer,device,allocatable:: kBelv_Poin_d(:)        !点云Ex
        integer,device,allocatable:: iBelv_Poin_d(:,:)      !点云Ex
        integer,device,allocatable:: kPoin_Clor_d(:)        !着色
        integer,device,allocatable:: iPoin_Clor_d(:)        !着色
        integer,device,allocatable:: iProp_Infc_d(:,:)
        !边界
        integer,device,allocatable:: iMarkProp_d(:,:)       !Mark信息
        integer,device,allocatable:: iBelvProp_d(:,:)       !Bele顶点信息
        !几何量
        real(kind=REALLEN),device,allocatable:: iCoor_Poin_d(:,:)   !节点的坐标
        real(kind=REALLEN),device,allocatable:: iVolu_Poin_d(:)     !顶点控制体体积
        real(kind=REALLEN),device,allocatable:: iRadu_Poin_d(:)     !节点的参考半径
        real(kind=REALLEN),device,allocatable:: iWDis_Poin_d(:)     !节点的全局物面距
        real(kind=REALLEN),device,allocatable:: iCoef_Sate_d(:,:)   !点云   
        real(kind=REALLEN),device,allocatable:: iLens_Sate_d(:)     !i-j长度
        real(kind=REALLEN),device,allocatable:: iPhii_Sate_d(:)     !各向异性系数
        real(kind=REALLEN),device,allocatable:: iCoor_Belv_d(:,:)   !边界元坐标
        real(kind=REALLEN),device,allocatable:: iArea_Belv_d(:)     !边界面积（按点）
        real(kind=REALLEN),device,allocatable:: iNvor_Belv_d(:,:)   !边界法矢（按点）
        real(kind=REALLEN),device,allocatable:: iCoef_Belv_d(:,:)   !边界系数（按点）
        real(kind=REALLEN),device,allocatable:: iCoef_Infc_d(:,:)
        ! 动网格
        real(kind=REALLEN),device,allocatable:: dCoor_Poin_d(:,:)   !节点运动矢量
        real(kind=REALLEN),device,allocatable:: iVelo_Poin_d(:,:)   !节点速度
        real(kind=REALLEN),device,allocatable:: iVelo_Belv_d(:,:)   !边界元速度
        real(kind=REALLEN),device,allocatable:: iVolu_Poin_N_d(:)   !上一步体积
        real(kind=REALLEN),device,allocatable:: iVolu_Poin_N1_d(:)  !上上步体积
        
        !备用数据 无网格发展
        real(kind=REALLEN),device,allocatable:: iCofR_Sate_d(:,:)
        real(kind=REALLEN),device,allocatable:: iCofR_Belv_d(:,:)
        !
        !---------------------------------------------
        !LU计算新增数据
        !---------------------------------------------
        integer,device,allocatable:: kSatL_Poin_d(:)
        integer,device,allocatable:: kSatR_Poin_d(:)
        integer,device,allocatable:: iSatL_Poin_d(:,:)
        integer,device,allocatable:: iSatR_Poin_d(:,:)
        real(kind=REALLEN),device,allocatable:: iCoef_SatL_d(:,:)
        real(kind=REALLEN),device,allocatable:: iCoef_SatR_d(:,:)
        !
        integer,device,allocatable:: kEdgL_Clor_d(:)
        integer,device,allocatable:: kEdgR_Clor_d(:)
        integer,device,allocatable:: iEdgL_Clor_d(:,:)
        integer,device,allocatable:: iEdgR_Clor_d(:,:)
        !
        integer,device,allocatable:: kSatL_Clor_d(:)
        integer,device,allocatable:: kSatR_Clor_d(:)
        integer,device,allocatable:: iSatL_Clor_d(:,:)
        integer,device,allocatable:: iSatR_Clor_d(:,:)
        !
        integer,device,allocatable:: iEdge_d(:,:)
#endif
        !---------------------------------------------
        !额外数据
        !---------------------------------------------
        !输出用：边界点提取重排，减小输出文件大小
        integer:: nBCPoin                       !边界点数
        integer,allocatable:: iMapGToB_Poin(:)  !映射关系
        integer,allocatable:: iMapBToG_Poin(:)  !映射关系
        !物面距计算用：
        integer:: nWallPoin         = 0         !物面点数
        integer,allocatable:: iPoin_Wall(:)     !映射关系
    endtype
    
    
endmodule
!
