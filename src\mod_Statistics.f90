module mod_Statistics
    implicit none
    !
    type:: typ_TimeStatistics
        character(len=100):: name
        real:: bgnTime
        real:: endTime
    end type typ_TimeStatistics
    !
    integer,parameter:: nMaxStatistics = 1000
    type(typ_TimeStatistics):: myTimeStatistics(nMaxStatistics)
    integer:: currStatisticsID = 0
    !
contains
    !
    subroutine beginTimer(val_StepStr)
        character(len=*),intent(in):: val_StepStr
        !
        currStatisticsID = currStatisticsID + 1
        myTimeStatistics(currStatisticsID)%name = trim(val_StepStr)

        call CPU_Time(myTimeStatistics(currStatisticsID)%bgnTime)

    end subroutine beginTimer
    !
    subroutine beginNextTimer(val_StepStr)
        character(len=*),intent(in):: val_StepStr
        !
        call CPU_Time(myTimeStatistics(currStatisticsID)%endTime)

        currStatisticsID = currStatisticsID + 1
        myTimeStatistics(currStatisticsID)%name = trim(val_StepStr)

        call CPU_Time(myTimeStatistics(currStatisticsID)%bgnTime)

    end subroutine beginNextTimer
    !
    subroutine endTimer(val_StepStr)
        character(len=*),intent(in):: val_StepStr
        !
        integer:: i
        logical:: isFound = .false.

        do i=1,currStatisticsID
            if(trim(myTimeStatistics(i)%name) == trim(val_StepStr)) then
                call CPU_Time(myTimeStatistics(i)%endTime)
                isFound = .true.
                exit
            end if
        end do

        if(.not.isFound) then
            write(*,*) 'not found '//trim(val_StepStr)//'for time statistics'
        end if
        
    end subroutine endTimer
    !
    subroutine printTimeStatistics
        use mod_PreDefine_IOPort
        implicit none
        integer:: i

        write(ioPort_Out, "(A)") "==== Time Statistics ===="
        do i=1,currStatisticsID
            write(ioPort_Out, "(A24,F15.8)") trim(myTimeStatistics(i)%name)//" :" , &
                    myTimeStatistics(i)%endTime - myTimeStatistics(i)%bgnTime
        enddo
        write(ioPort_Out, "(A)") "========================"

    endsubroutine
    !
    subroutine clearTimeStatistics
        implicit none
        integer:: i

        do i=1,currStatisticsID
            myTimeStatistics(i)%name    = ""
            myTimeStatistics(i)%bgnTime = 0.0
            myTimeStatistics(i)%endTime = 0.0
        end do

        currStatisticsID = 0

    end subroutine clearTimeStatistics
    !
end module mod_Statistics