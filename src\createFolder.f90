
!---------------------------------------------
!
!---------------------------------------------
subroutine createFolder(val_folderFullPath,val_iErr)
    implicit none
    character(len=*),intent(in):: val_folderFullPath
    integer,intent(out):: val_iErr
    !
    integer:: i
    integer:: FEXIST, system
    !logical function
    logical:: isPathOrFileExist
    character(len=255):: iPath


    do i=1,folderLevelOf(val_folderFullPath)
        iPath = trim(rootPathOf(val_folderFullPath,i))

        if(.not.isPathOrFileExist(trim(iPath))) then
            FEXIST = system('mkdir "'//trim(iPath)//'"')
        endif
    enddo
    !
    val_iErr = 0
    return

contains
    !---------------------------------------------
    !
    !---------------------------------------------
    integer function folderLevelOf(val_path)
        implicit none
        character(len=*),intent(in):: val_path
        integer:: nLevel
        integer:: i,j


        nLevel = 0

        do i=2,len_trim(val_path)-1
            if((val_path(i:i) == '/').and.(val_path(i-1:i-1) /= '/')) then
                nLevel = nLevel+1
            endif
        enddo

        folderLevelOf = nLevel

    endfunction


    !---------------------------------------------
    !
    !---------------------------------------------
    character(len=255) function rootPathOf(val_path,val_iLevel)
        implicit none
        character(len=*),intent(in):: val_path
        integer,intent(in):: val_iLevel

        integer:: nLevel
        integer:: i
        integer:: iPlace(255)
        character(len=255) :: iName

        iPlace(:) = 0

        nLevel = 0
        do i=2,len_trim(val_path)-1
            if((val_path(i:i) == '/').and.(val_path(i-1:i-1) /= '/')) then
                nLevel = nLevel+1

                iPlace(nLevel) = i
            endif
        enddo

        if(val_iLevel == nLevel) then
            iName = val_path
        else
            iName = val_path(1:iPlace(val_iLevel+1))
        endif

        rootPathOf = iName

    endfunction
    !
endsubroutine
!

