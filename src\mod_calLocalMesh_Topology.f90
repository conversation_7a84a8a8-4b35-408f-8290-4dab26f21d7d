!
module mod_calLocalMesh_Topology
    use mod_PreDefine_Flag
    use mod_PreDefine_IOPort
    use mod_Interface_AllocateArray
    implicit none
    !
contains
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine calLocalMesh_Topology(val_iMesh)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_Mesh),intent(inout):: val_iMesh
    
        call getIDomaAndIMark(val_iMesh)!
        
        call calIFlag_Elem(val_iMesh)   !用于输出
        
        call calIIner_Poin(val_iMesh)   !内点标识
        
        call calICalu_Poin(val_iMesh)   !
        
        call calIPoin_Clor(val_iMesh)   !<PERSON>-<PERSON><PERSON>
    
        call calIBelv_Poin(val_iMesh)   !边界点云
        
        call bldISatLR_Poin(val_iMesh)  !LR-Sate计算
        
        call bldILocal<PERSON><PERSON>(val_iMesh)
        
        call bldISatLR_Clor(val_iMesh)
        
        call calIMapGB_Poin(val_iMesh)  !用于输出
        
        call calIInfc(val_iMesh)
    
    endsubroutine
    !
    !---------------------------------------------
    subroutine getIDomaAndIMark(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_PreDefine_Flag
        use mod_PreDefine_Mark
        use mod_Interface_AllocateArray
        implicit none
        type(typ_Mesh),intent(inout):: val_iMesh
        !
        integer:: i,j,iCount
        integer:: nElem,nBele
        
        
        !---------------------------------------------
        if(allocated(val_iMesh%iDoma)) then
            deallocate(val_iMesh%iDoma)
        endif
        if(allocated(val_iMesh%iMark)) then
            deallocate(val_iMesh%iMark)
        endif
        allocate(val_iMesh%iDoma(val_iMesh%nDoma))
        allocate(val_iMesh%iMark(val_iMesh%nMark))
        
        
        !---------------------------------------------
        iCount = 0
        do i=1,val_iMesh%nDoma
            nElem = val_iMesh%iDomaProp(i,1)
            val_iMesh%iDoma(i)%nElem = nElem
            if(nElem == 0) cycle
            
            call allocateArray(val_iMesh%iDoma(i)%iElem, nElem)
            do j=1,nElem
                val_iMesh%iDoma(i)%iElem(j) = j+ iCount
            enddo
            
            iCount = iCount + nElem
        enddo
        
        !---------------------------------------------
        iCount = 0
        do i=1,val_iMesh%nMark
            nBele = val_iMesh%iMarkProp(i,1)
            val_iMesh%iMark(i)%nElem = nBele
            if(nBele == 0) cycle
            
            call allocateArray(val_iMesh%iMark(i)%iElem, nBele)
            do j=1,nBele
                val_iMesh%iMark(i)%iElem(j) = j+ iCount
            enddo
            
            iCount = iCount + nBele
        enddo
        
    endsubroutine
    !
    !---------------------------------------------
    !
    subroutine calIInfc(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_PreDefine_Flag
        use mod_PreDefine_Infc
        use mod_Interface_AllocateArray
        use mod_Interface_Unitization
        !use mod_Config
        implicit none
        type(typ_Mesh),intent(inout):: val_iMesh
        !
        integer:: i,j,k,s
        integer:: sateID,sateJD,belvID,poinID
        integer:: iCount,poinL,poinR
        logical:: isCounted
        real(kind=8):: iCoef(nDim),jCoef(nDim),iValu,iErrv
        !
        real(kind=8):: iSum(val_iMesh%nPoin,nDim),sumEi(nDim)
        real(kind=8):: iDSum(val_iMesh%nPoin,nDim)
        
        iCount = 0
        do i=1,val_iMesh%nPoin
            if(val_iMesh%iCalc_Poin(i) /= FLAG_VALID) cycle
            do j=val_iMesh%kSate_Poin(i)+1,val_iMesh%kSate_Poin(i+1)
                sateID = val_iMesh%iSate_Poin(j,1)
                if(val_iMesh%iCalc_Poin(sateID) /= FLAG_VALID) cycle
                
                if(sateID > i) then
                    iCount = iCount + 1
                else
                    isCounted = .false.
                    do k=val_iMesh%kSate_Poin(sateID)+1,val_iMesh%kSate_Poin(sateID+1)
                        sateJD = val_iMesh%iSate_Poin(k,1)
                        if(val_iMesh%iCalc_Poin(satejD) /= FLAG_VALID) cycle
                        if(i == sateJD) then
                            isCounted = .true.
                            exit
                        endif
                    enddo
                
                    if(.not.isCounted) iCount = iCount + 1
                endif
            enddo
        enddo
        iCount = iCount + val_iMesh%nBelv
        
        !
        val_iMesh%nInfc = iCount
        call allocateArray(val_iMesh%iProp_Infc, val_iMesh%nInfc, 5     )
        call allocateArray(val_iMesh%iCoef_Infc, val_iMesh%nInfc, 2*nDim)
        val_iMesh%iProp_Infc(:,:) = 0
        val_iMesh%iCoef_Infc(:,:) = 0.0
        val_iMesh%iProp_Infc(:,3) = INFC_NONE
        !write(*,*) 'nSate,nBelv,nInfc',val_iMesh%nSate,val_iMesh%nBelv,val_iMesh%nInfc
        
        
        iCount = 0
        do i=1,val_iMesh%nPoin
            if(val_iMesh%iCalc_Poin(i) /= FLAG_VALID) cycle
            do j=val_iMesh%kSate_Poin(i)+1,val_iMesh%kSate_Poin(i+1)
                sateID = val_iMesh%iSate_Poin(j,1)
                if(val_iMesh%iCalc_Poin(sateID) /= FLAG_VALID) cycle
                
                if(sateID > i) then
                    iCount = iCount + 1
                    val_iMesh%iProp_Infc(iCount, 1) = i
                    val_iMesh%iProp_Infc(iCount, 2) = sateID
                    val_iMesh%iProp_Infc(iCount, 4) = j
                    
                    do s=1,nDim
                        val_iMesh%iCoef_Infc(iCount,s) = val_iMesh%iCoef_Sate(j,s)
                    enddo
                    
                    isCounted = .false.
                    do k=val_iMesh%kSate_Poin(sateID)+1,val_iMesh%kSate_Poin(sateID+1)
                        sateJD = val_iMesh%iSate_Poin(k,1)
                        if(val_iMesh%iCalc_Poin(satejD) /= FLAG_VALID) cycle
                        
                        if(i == sateJD) then
                            val_iMesh%iProp_Infc(iCount, 3) = INFC_TWOWAY
                            val_iMesh%iProp_Infc(iCount, 5) = k
                            
                            do s=1,nDim
                            val_iMesh%iCoef_Infc(iCount, nDim+s) = val_iMesh%iCoef_Sate(k,s)
                            enddo
                            
                            isCounted = .true.
                            exit
                        endif
                    enddo
                    
                    if(.not.isCounted) then
                        val_iMesh%iProp_Infc(iCount, 3) = INFC_ONEWAY
                    endif
                    
                else
                    
                    isCounted = .false.
                    do k=val_iMesh%kSate_Poin(sateID)+1,val_iMesh%kSate_Poin(sateID+1)
                        sateJD = val_iMesh%iSate_Poin(k,1)
                        if(val_iMesh%iCalc_Poin(satejD) /= FLAG_VALID) cycle
                        if(i == sateJD) then
                            isCounted = .true.
                            exit
                        endif
                    enddo
                    
                    if(.not.isCounted) then
                        iCount = iCount + 1
                        write(*,*) 'ERROR'
                        
                        val_iMesh%iProp_Infc(iCount, 1) = i
                        val_iMesh%iProp_Infc(iCount, 2) = sateID
                        val_iMesh%iProp_Infc(iCount, 3) = INFC_ONEWAY
                        val_iMesh%iProp_Infc(iCount, 4) = j
                    
                        val_iMesh%iCoef_Infc(iCount, 1:nDim) = val_iMesh%iCoef_Sate(j,:)
                    endif
                endif
            enddo
        enddo
        
        do i=1,val_iMesh%nBelv
            iCount = iCount + 1
            
            poinID = val_iMesh%iBelvProp(i,1)
            
            val_iMesh%iProp_Infc(iCount, 1) = poinID
            val_iMesh%iProp_Infc(iCount, 2) = -i
            val_iMesh%iProp_Infc(iCount, 3) = INFC_BND
                    
            val_iMesh%iCoef_Infc(iCount, 1:nDim) = val_iMesh%iCoef_Belv(i,:)
        enddo
        
        iCount = 0
        do i=1,val_iMesh%nInfc
            if(val_iMesh%iProp_Infc(i,3) <= 0) cycle
            
            do s=1,nDim
            iCoef(s) = val_iMesh%iCoef_Infc(i, s     )
            jCoef(s) = val_iMesh%iCoef_Infc(i, nDim+s)
            enddo
            
            call unitization(nDim, iCoef)
            call unitization(nDim, jCoef)
            
            iValu = 0.0
            do k=1,nDim
                iValu = iValu + iCoef(k)*jCoef(k)
            enddo
            
            iErrv = abs(1.0 - abs(iValu))
            if(iErrv < 1.0E-3) then
                val_iMesh%iProp_Infc(i,3) = INFC_RECIPROCAL
                iCount = iCount + 1
            endif
        enddo
        !write(*,*) iCount,val_iMesh%nSate/2
        !pause
        !-----
        iSum(:,:) = 0.0
        do i=1,val_iMesh%nInfc
            poinL = val_iMesh%iProp_Infc(i,1)
            
            do j=1,nDim
            iSum(poinL,j) = iSum(poinL,j) + val_iMesh%iCoef_Infc(i,j)
            enddo
            
            poinR = val_iMesh%iProp_Infc(i,2)
            if(poinR <= 0) cycle
            
            do j=1,nDim
            iSum(poinR,j) = iSum(poinR,j) + val_iMesh%iCoef_Infc(i,j+nDim)
            enddo
        enddo
        
        do i=1,val_iMesh%nPoin
            if(val_iMesh%iCalc_Poin(i) /= FLAG_VALID) cycle
            
            SumEi(:) = 0.0
            do j=val_iMesh%kSate_Poin(i)+1,val_iMesh%kSate_Poin(i+1)
                sateID = val_iMesh%iSate_Poin(j,1)
                if(val_iMesh%iCalc_Poin(sateID) /= FLAG_VALID) cycle
                
                SumEi(:) = SumEi(:) + val_iMesh%iCoef_Sate(j,:)
                
            enddo
            
            do j=val_iMesh%kBelv_Poin(i)+1,val_iMesh%kBelv_Poin(i+1)
                belvID = val_iMesh%iBelv_Poin(j,1)
                
                SumEi(:) = SumEi(:) + val_iMesh%iCoef_Belv(belvID,:)
            enddo
            
            
            iDSum(i,:) = iSum(i,:) - SumEi(:)
        enddo
        
        
        !open(99,file='iCoef_Infc.dat')
        !do i=1,val_iMesh%nPoin
        !write(99,"(9E15.5)") val_iMesh%iCoor_Poin(i,:), iSum(i,:),iDSum(i,:)
        !enddo
        
        close(99)
        !pause;stop
        
    endsubroutine
    !
    !---------------------------------------------
    subroutine calIFlag_Elem(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_PreDefine_Flag
        use mod_Interface_AllocateArray
        implicit none
        type(typ_Mesh),intent(inout):: val_iMesh
        !
        integer:: i,j,iCount
        integer:: elemID,poinID
        
        
        call allocateArray(val_iMesh%iFlag_Elem, val_iMesh%nElem)
        val_iMesh%iFlag_Elem(:) = FLAG_INVAL

        !$omp parallel do private(j,poinID)
        do i=1,val_iMesh%nElem
            do j=1,4*nDim-4
                poinID = val_iMesh%iElem(j,i)
                if(val_iMesh%iHost_Poin(poinID) == FLAG_VALID) then
                    val_iMesh%iFlag_Elem(i) = FLAG_VALID
                    exit
                endif
            enddo
        enddo
        !$omp end parallel do
        
        do i=1,val_iMesh%nDoma
            iCount = 0
            do j=1,val_iMesh%iDoma(i)%nElem
                elemID = val_iMesh%iDoma(i)%iElem(j)
                
                if(val_iMesh%iFlag_Elem(elemID) == FLAG_VALID) then
                    iCount = iCount + 1
                endif
            enddo
            val_iMesh%iDoma(i)%nValdElem = iCount
        enddo
        
        
    endsubroutine
    !
    !---------------------------------------------
    subroutine calIIner_Poin(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_PreDefine_Flag
        use mod_PreDefine_Mark
        implicit none
        type(typ_Mesh),intent(inout):: val_iMesh
        !
        integer:: i,pID,mID,mKD
        
        call allocateArray(val_iMesh%iIner_Poin, val_iMesh%nPoin)
        val_iMesh%iIner_Poin(:) = FLAG_INVAL
        
        do i=1,val_iMesh%nPoin
            if(val_iMesh%iHost_Poin(i) == FLAG_VALID) then
                val_iMesh%iIner_Poin(i) = FLAG_INNER
                !write(*,*) i
            endif
        enddo
        
        do i=1,val_iMesh%nBelv
            pID = val_iMesh%iBelvProp(i,1)
            mID = val_iMesh%iBelvProp(i,2)
            
            if(mID > 0) then
                val_iMesh%iIner_Poin(pID) = FLAG_BOUND
            endif
        enddo
        
        do i=1,val_iMesh%nBelv
            pID = val_iMesh%iBelvProp(i,1)
            mID = val_iMesh%iBelvProp(i,2)
            
            if(mID > 0) then
                mKD = val_iMesh%iMarkProp(mID,2)
                
                if(mKD==MARK_WALL .or. mKD==MARK_EQTW .or. mKD==MARK_EULW .or. mKD==MARK_MOVW) then
                    val_iMesh%iIner_Poin(pID) = FLAG_WALL
                endif
            endif
        enddo
        
    endsubroutine
    !
    !---------------------------------------------
    subroutine calICalu_Poin(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_PreDefine_Flag
        use mod_PreDefine_Mark
        use mod_MPIEnvironment
        implicit none
        type(typ_Mesh),intent(inout):: val_iMesh
        !
        integer:: i,j,poinID,sateID
        
        call allocateArray(val_iMesh%iCalc_Poin, val_iMesh%nPoin)
        val_iMesh%iCalc_Poin(:) = FLAG_INVAL
        
        do i=1,val_iMesh%nPoin
            if((val_iMesh%iPart_Poin(i) == myID+1).and. &
               (val_iMesh%iHost_Poin(i) == FLAG_VALID)) then
                val_iMesh%iCalc_Poin(i) = FLAG_VALID
            endif
        enddo
        
        do i=1,val_iMesh%nPoin
            if(val_iMesh%iCalc_Poin(i) == FLAG_VALID) then
                do j=val_iMesh%kSate_Poin(i)+1,val_iMesh%kSate_Poin(i+1)
                    sateID = val_iMesh%iSate_Poin(j,1)
                    
                    if(val_iMesh%iCalc_Poin(sateID) == FLAG_INVAL) then
                        val_iMesh%iCalc_Poin(sateID) = FLAG_EXCH !必要点云
                    endif
                enddo
            endif
        enddo
        
    endsubroutine
    !
    !---------------------------------------------
    subroutine calIPoin_Clor(val_iMesh)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_Mesh),intent(inout):: val_iMesh
        !
        integer:: i 
        integer:: iClor,nInvl
    
        if(val_iMesh%nClor <= 0) then
            write(ioPort_Out,*) 'val_iMesh%nClor <= 0'
            stop
        endif
    
        call allocateArray(val_iMesh%kPoin_Clor, val_iMesh%nClor+1)
    
    
        val_iMesh%kPoin_Clor(:) = 0
        nInvl = 0
        do i=1,val_iMesh%nPoin
            if(val_iMesh%iCalc_Poin(i) /= FLAG_VALID) then
                nInvl = nInvl + 1
                cycle
            endif
            
            iClor = val_iMesh%iClor_Poin(i)
        
            if(iClor > 0) then
                val_iMesh%kPoin_Clor(iClor+1) = val_iMesh%kPoin_Clor(iClor+1) + 1
            else
                nInvl = nInvl + 1
            endif
        enddo
    
        do i=1,val_iMesh%nClor
            val_iMesh%kPoin_Clor(i+1) = val_iMesh%kPoin_Clor(i+1) + &
                                        val_iMesh%kPoin_Clor(i)
        enddo
    
        !由于存在补充点，有效点数+无效点数=总点数
        if(val_iMesh%kPoin_Clor(val_iMesh%nClor+1) + nInvl /= val_iMesh%nPoin) then
           write(ioPort_Out,*) 'val_iMesh%kPoin_Clor(val_iMesh%nClor+1) + nInvl /= val_iMesh%nPoin'
            stop
        endif
    
        call allocateArray(val_iMesh%iPoin_Clor, val_iMesh%nPoin)
        val_iMesh%iPoin_Clor(:) = 0
    
        do i=1,val_iMesh%nPoin
            if(val_iMesh%iCalc_Poin(i) /= FLAG_VALID) cycle
            
            iClor = val_iMesh%iClor_Poin(i)
        
            if(iClor > 0) then
                val_iMesh%kPoin_Clor(iClor) = val_iMesh%kPoin_Clor(iClor) + 1
        
                val_iMesh%iPoin_Clor(val_iMesh%kPoin_Clor(iClor)) = i
            endif
        enddo
    
        do i=val_iMesh%nClor,1,-1
            val_iMesh%kPoin_Clor(i+1) = val_iMesh%kPoin_Clor(i)
        enddo
        val_iMesh%kPoin_Clor(1) = 0
    
    
    endsubroutine
    !
    !---------------------------------------------
    subroutine calIBelv_Poin(val_iMesh)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_Mesh),intent(inout):: val_iMesh
        !
        integer:: i,poinID
        
        call allocateArray(val_iMesh%kBelv_Poin, val_iMesh%nPoin+1)
        call allocateArray(val_iMesh%iBelv_Poin, val_iMesh%nBelv, 2)
        val_iMesh%kBelv_Poin(:  ) = 0
        val_iMesh%iBelv_Poin(:,:) = 0
        
        do i=1,val_iMesh%nBelv
            poinID = val_imesh%iBelvProp(i,1)
            
            val_iMesh%kBelv_Poin(poinID+1) = val_iMesh%kBelv_Poin(poinID+1)+1
        enddo
        
        do i=1,val_iMesh%nPoin
            val_iMesh%kBelv_Poin(i+1) = val_iMesh%kBelv_Poin(i) + &
                                        val_iMesh%kBelv_Poin(i+1)
        enddo
        
        do i=1,val_iMesh%nBelv
            poinID = val_iMesh%iBelvProp(i,1)
            
            val_iMesh%kBelv_Poin(poinID) = val_iMesh%kBelv_Poin(poinID)+1
            
            val_iMesh%iBelv_Poin(val_iMesh%kBelv_Poin(poinID),1) = i
            val_iMesh%iBelv_Poin(val_iMesh%kBelv_Poin(poinID),2) = val_iMesh%iBelvProp(i,2)
        enddo
        
        do i=val_iMesh%nPoin,1,-1
            val_iMesh%kBelv_Poin(i+1) = val_iMesh%kBelv_Poin(i)
        enddo
        val_iMesh%kBelv_Poin(1) = 0
        
    endsubroutine
    !
    !---------------------------------------------
    subroutine bldISatLR_Poin(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_Interface_AllocateArray
        implicit none
        type(typ_Mesh),intent(inout):: val_iMesh
        !
        integer:: i,j,sateID
        integer:: kb,ke
        
        
        !---------------------------------------------
        call allocateArray(val_iMesh%kSatL_Poin, val_iMesh%nPoin+1)
        call allocateArray(val_iMesh%kSatR_Poin, val_iMesh%nPoin+1)
        val_iMesh%kSatL_Poin(:) = 0
        val_iMesh%kSatR_Poin(:) = 0
        
        
        !---------------------------------------------
        do i=1,val_iMesh%nPoin
            if(val_iMesh%iCalc_Poin(i) /= FLAG_VALID) then
                val_iMesh%ksatL_Poin(i+1) = val_iMesh%ksatL_Poin(i)
                val_iMesh%ksatR_Poin(i+1) = val_iMesh%ksatR_Poin(i)
                cycle
            endif
            
            do j=val_iMesh%kSate_Poin(i)+1,val_iMesh%kSate_Poin(i+1)
                sateID = val_iMesh%iSate_Poin(j,1)
                
                if(val_iMesh%iCalc_Poin(sateID) == FLAG_INVAL) cycle
                
                if(val_iMesh%iClor_Poin(sateID) < val_iMesh%iClor_Poin(i)) then
                    val_iMesh%ksatL_Poin(i+1) = val_iMesh%ksatL_Poin(i+1) + 1
                elseif(val_iMesh%iClor_Poin(sateID) > val_iMesh%iClor_Poin(i)) then
                    val_iMesh%ksatR_Poin(i+1) = val_iMesh%ksatR_Poin(i+1) + 1
                endif
            enddo
            
            val_iMesh%kSatL_Poin(i+1) = val_iMesh%kSatL_Poin(i+1) + val_iMesh%kSatL_Poin(i)
            val_iMesh%kSatR_Poin(i+1) = val_iMesh%kSatR_Poin(i+1) + val_iMesh%kSatR_Poin(i)
        enddo
        
        val_iMesh%nSatL = val_iMesh%ksatL_Poin(val_iMesh%nPoin+1)
        val_iMesh%nSatR = val_iMesh%ksatR_Poin(val_iMesh%nPoin+1)
        
        !---------------------------------------------
        call allocateArray(val_iMesh%iSatL_Poin, val_iMesh%nSatL, 2)
        call allocateArray(val_iMesh%iSatR_Poin, val_iMesh%nSatR, 2)
        val_iMesh%iSatL_Poin(:,:) = 0
        val_iMesh%iSatR_Poin(:,:) = 0
        call allocateArray(val_iMesh%iCoef_SatL, val_iMesh%nSatL, nDim)
        call allocateArray(val_iMesh%iCoef_SatR, val_iMesh%nSatR, nDim)
        val_iMesh%iCoef_SatL(:,:) = 0
        val_iMesh%iCoef_SatR(:,:) = 0
        
        do i=1,val_iMesh%nPoin
            if(val_iMesh%iCalc_Poin(i) /= FLAG_VALID) then
                cycle
            endif
            
            do j=val_iMesh%kSate_Poin(i)+1,val_iMesh%kSate_Poin(i+1)
                sateID = val_iMesh%iSate_Poin(j,1)
                
                if(val_iMesh%iCalc_Poin(sateID) == FLAG_INVAL) cycle
                
                if(val_iMesh%iClor_Poin(sateID) < val_iMesh%iClor_Poin(i)) then
                    val_iMesh%ksatL_Poin(i) = val_iMesh%ksatL_Poin(i) + 1
                    
                    val_iMesh%iSatL_Poin(val_iMesh%ksatL_Poin(i),1) = sateID 
                    val_iMesh%iSatL_Poin(val_iMesh%ksatL_Poin(i),2) = val_iMesh%iSate_Poin(j,2)
                    
                    val_iMesh%iCoef_SatL(val_iMesh%ksatL_Poin(i),:) = val_iMesh%iCoef_Sate(j,:)
                elseif(val_iMesh%iClor_Poin(sateID) > val_iMesh%iClor_Poin(i)) then
                    val_iMesh%ksatR_Poin(i) = val_iMesh%ksatR_Poin(i) + 1
                    
                    val_iMesh%iSatR_Poin(val_iMesh%ksatR_Poin(i),1) = sateID 
                    val_iMesh%iSatR_Poin(val_iMesh%ksatR_Poin(i),2) = val_iMesh%iSate_Poin(j,2)
                    
                    val_iMesh%iCoef_SatR(val_iMesh%ksatR_Poin(i),:) = val_iMesh%iCoef_Sate(j,:)
                endif
            enddo
        enddo
        
        do i=val_iMesh%nPoin,1,-1
            val_iMesh%ksatL_Poin(i+1) = val_iMesh%ksatL_Poin(i)
            val_iMesh%ksatR_Poin(i+1) = val_iMesh%ksatR_Poin(i)
        enddo
        val_iMesh%ksatL_Poin(1) = 0
        val_iMesh%ksatR_Poin(1) = 0
        !
        !do i=1,val_iMesh%nPoin
        !    if((val_iMesh%kSate_Poin(i+1) - val_iMesh%kSate_Poin(i) ) /=    &
        !       (val_iMesh%kSatL_Poin(i+1) - val_iMesh%kSatL_Poin(i) +       &
        !        val_iMesh%kSatR_Poin(i+1) - val_iMesh%kSatR_Poin(i) )  ) then
        !        pause 'XX'
        !    endif
        !enddo
        !
        !do i=1,val_iMesh%nPoin,100
        !    write(*,*) i,":",val_iMesh%iClor_Poin(i)
        !    kb = val_iMesh%kSatL_Poin(i)+1
        !    ke = val_iMesh%kSatL_Poin(i+1)
        !    write(*,*) val_iMesh%iClor_Poin(val_iMesh%iSatL_Poin(kb:ke,1))
        !    kb = val_iMesh%kSatR_Poin(i)+1
        !    ke = val_iMesh%kSatR_Poin(i+1)
        !    write(*,*) val_iMesh%iClor_Poin(val_iMesh%iSatR_Poin(kb:ke,1))
        !enddo
        !pause
        
    endsubroutine
    !
    !---------------------------------------------
    subroutine bldILocalEdge(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_Interface_AllocateArray
        implicit none
        type(typ_Mesh),intent(inout):: val_iMesh
        !
        integer:: i,j,k,sateID
        integer:: iCont
        logical:: isSingle
        
        
        iCont = 0
        do i=1,val_iMesh%nPoin
            if(val_iMesh%iCalc_Poin(i) /= FLAG_VALID) cycle
            
            do j=val_iMesh%kSate_Poin(i)+1,val_iMesh%kSate_Poin(i+1)
                sateID = val_iMesh%iSate_Poin(j,1)
                
                if(val_iMesh%iCalc_Poin(sateID) == FLAG_INVAL) cycle
                
                if(sateID >= i) then
                    iCont = iCont + 1
                else
                    isSingle = .true.
                    do k=val_iMesh%kSate_Poin(sateID)+1,val_iMesh%kSate_Poin(sateID+1)
                        if(val_iMesh%iSate_Poin(k,1) == i) then
                            isSingle = .false.
                            exit
                        endif
                    enddo
                    
                    if(isSingle) then
                        iCont = iCont + 1
                    endif
                endif
            enddo
            
            do j=val_iMesh%kBelv_Poin(i)+1,val_iMesh%kBelv_Poin(i+1)
                iCont = iCont + 1
            enddo
        enddo
        
        val_iMesh%nEdge = iCont
        call allocateArray(val_iMesh%iEdge, val_iMesh%nEdge, 4)
        val_iMesh%iEdge(:,:) = 0
        
        
        iCont = 0
        do i=1,val_iMesh%nPoin
            if(val_iMesh%iCalc_Poin(i) /= FLAG_VALID) cycle
            
            do j=val_iMesh%kSate_Poin(i)+1,val_iMesh%kSate_Poin(i+1)
                sateID = val_iMesh%iSate_Poin(j,1)
                
                if(val_iMesh%iCalc_Poin(sateID) == FLAG_INVAL) cycle
                
                if(sateID >= i) then
                    iCont = iCont + 1
                    
                    val_iMesh%iEdge(iCont,1) = i
                    val_iMesh%iEdge(iCont,2) = sateID
                    val_iMesh%iEdge(iCont,3) = j
                    val_iMesh%iEdge(iCont,4) = 0
                    
                    do k=val_iMesh%kSate_Poin(sateID)+1,val_iMesh%kSate_Poin(sateID+1)
                        if(val_iMesh%iSate_Poin(k,1) == i) then
                            val_iMesh%iEdge(iCont,4) = k
                        endif
                    enddo
                    
                else
                    isSingle = .true.
                    do k=val_iMesh%kSate_Poin(sateID)+1,val_iMesh%kSate_Poin(sateID+1)
                        if(val_iMesh%iSate_Poin(k,1) == i) then
                            isSingle = .false.
                            exit
                        endif
                    enddo
                    
                    val_iMesh%iEdge(iCont,1) = i
                    val_iMesh%iEdge(iCont,2) = sateID
                    val_iMesh%iEdge(iCont,3) = j
                    val_iMesh%iEdge(iCont,4) = 0
                    
                    if(isSingle) then
                        iCont = iCont + 1
                    endif
                endif
            enddo
            
            do j=val_iMesh%kBelv_Poin(i)+1,val_iMesh%kBelv_Poin(i+1)
                iCont = iCont + 1
                
                val_iMesh%iEdge(iCont,1) = i
                val_iMesh%iEdge(iCont,2) = 0
                val_iMesh%iEdge(iCont,3) = val_iMesh%iBelv_Poin(j,1)
                val_iMesh%iEdge(iCont,4) = val_iMesh%iBelv_Poin(j,2)
            enddo
        enddo
        
    endsubroutine
    !
    subroutine bldISatLR_Clor(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_Interface_AllocateArray
        implicit none
        type(typ_Mesh),intent(inout):: val_iMesh
        !
        integer:: i,j,k
        integer:: sateID,iCont,jCont
        integer:: iClor,jClor
        
        
        
        !call allocateArray(val_iMesh%kEdgL_Clor, val_iMesh%nClor+1)
        !call allocateArray(val_iMesh%kEdgR_Clor, val_iMesh%nClor+1)
        call allocateArray(val_iMesh%kSatL_Clor, val_iMesh%nClor+1)
        call allocateArray(val_iMesh%kSatR_Clor, val_iMesh%nClor+1)
        !val_iMesh%kEdgL_Clor(:) = 0
        !val_iMesh%kEdgR_Clor(:) = 0
        val_iMesh%kSatL_Clor(:) = 0
        val_iMesh%kSatR_Clor(:) = 0
        
        do i=1,val_iMesh%nPoin
            if(val_iMesh%iCalc_Poin(i) /= FLAG_VALID) cycle
            
            iClor = val_iMesh%iClor_Poin(i)
            
            do j=val_iMesh%kSate_Poin(i)+1,val_iMesh%kSate_Poin(i+1)
                sateID = val_iMesh%iSate_Poin(j,1)
                
                if(val_iMesh%iCalc_Poin(sateID) == FLAG_INVAL) cycle
                
                if(val_iMesh%iClor_Poin(sateID) < iClor) then
                    val_iMesh%kSatL_Clor(iClor+1) =  val_iMesh%kSatL_Clor(iClor+1) + 1
                elseif(val_iMesh%iClor_Poin(sateID) > iClor) then
                    val_iMesh%kSatR_Clor(iClor+1) =  val_iMesh%kSatR_Clor(iClor+1) + 1
                endif
            enddo
        enddo
        
        do i=1,val_iMesh%nClor
            val_iMesh%kSatL_Clor(i+1) = val_iMesh%kSatL_Clor(i+1) + val_iMesh%kSatL_Clor(i)
            val_iMesh%kSatR_Clor(i+1) = val_iMesh%kSatR_Clor(i+1) + val_iMesh%kSatR_Clor(i)
        enddo
        val_iMesh%nSaCL = val_iMesh%kSatL_Clor(val_iMesh%nClor+1)
        val_iMesh%nSaCR = val_iMesh%kSatR_Clor(val_iMesh%nClor+1)
        !write(*,*) val_iMesh%nClor,val_iMesh%nSaCL,val_iMesh%nSaCR
        !write(*,*) val_iMesh%kSatL_Clor
        !write(*,*) val_iMesh%kSatR_Clor
        
        
        call allocateArray(val_iMesh%iSatL_Clor, val_iMesh%nSaCL, 3)
        call allocateArray(val_iMesh%iSatR_Clor, val_iMesh%nSaCR, 3)
        val_iMesh%iSatL_Clor(:,:) = 0
        val_iMesh%iSatR_Clor(:,:) = 0
        
        
        do i=1,val_iMesh%nPoin
            if(val_iMesh%iCalc_Poin(i) /= FLAG_VALID) cycle
            
            iClor = val_iMesh%iClor_Poin(i)
            
            do j=val_iMesh%kSate_Poin(i)+1,val_iMesh%kSate_Poin(i+1)
                sateID = val_iMesh%iSate_Poin(j,1)
                
                if(val_iMesh%iCalc_Poin(sateID) == FLAG_INVAL) cycle
                
                if(val_iMesh%iClor_Poin(sateID) < iClor) then
                    val_iMesh%kSatL_Clor(iClor) =  val_iMesh%kSatL_Clor(iClor) + 1
                    
                    val_iMesh%iSatL_Clor(val_iMesh%kSatL_Clor(iClor),1) = j
                    val_iMesh%iSatL_Clor(val_iMesh%kSatL_Clor(iClor),2) = i
                    val_iMesh%iSatL_Clor(val_iMesh%kSatL_Clor(iClor),3) = sateID
                elseif(val_iMesh%iClor_Poin(sateID) > iClor) then
                    val_iMesh%kSatR_Clor(iClor) =  val_iMesh%kSatR_Clor(iClor) + 1
                    
                    val_iMesh%iSatR_Clor(val_iMesh%kSatR_Clor(iClor),1) = j
                    val_iMesh%iSatR_Clor(val_iMesh%kSatR_Clor(iClor),2) = i
                    val_iMesh%iSatR_Clor(val_iMesh%kSatR_Clor(iClor),3) = sateID
                endif
            enddo
        enddo
        
        do i=val_iMesh%nClor,1,-1
            val_iMesh%kSatL_Clor(i+1) = val_iMesh%kSatL_Clor(i)
            val_iMesh%kSatR_Clor(i+1) = val_iMesh%kSatL_Clor(i)
        enddo
        val_iMesh%kSatL_Clor(1) = 0
        val_iMesh%kSatR_Clor(1) = 0
        
    endsubroutine
    !
    !---------------------------------------------
    subroutine calIMapGB_Poin(val_iMesh)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_Mesh),intent(inout):: val_iMesh
        !
        integer:: i,j,iCount
        integer:: poinID,markID
        integer:: iTempFlag(val_iMesh%nPoin)
        
        
        !---------------------------------------------
        iTempFlag(:) = 0
        do i=1,val_iMesh%nBelv
            poinID = val_iMesh%iBelvProp(i,1)
            markID = val_iMesh%iBelvProp(i,2)
            if(markID > 0) then
                iTempFlag(poinID) = 1
            endif
        enddo
        
        !---------------------------------------------
        iCount = 0
        do i=1,val_iMesh%nPoin
            if(iTempFlag(i) == 1) then
                iCount = iCount + 1
            endif
        enddo
        val_iMesh%nBCPoin = iCount
                
        if(val_iMesh%nBCPoin <= 0) return
        
        !---------------------------------------------
        call allocateArray(val_iMesh%iMapGToB_Poin, val_iMesh%nPoin)
        call allocateArray(val_iMesh%iMapBToG_Poin, val_iMesh%nBCPoin)
        val_iMesh%iMapGToB_Poin(:) = 0
        val_iMesh%iMapBToG_Poin(:) = 0
        
        !---------------------------------------------
        iCount = 0
        do i=1,val_iMesh%nPoin
            if(iTempFlag(i) == 1) then
                iCount = iCount + 1
                
                val_iMesh%iMapGToB_Poin(i) = iCount
                val_iMesh%iMapBToG_Poin(iCount) = i
            endif
        enddo
        
    endsubroutine
    !
endmodule
!
    
    