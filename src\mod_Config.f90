!---------------------------------------------
!输入参数模块
!---------------------------------------------
module mod_Config
    use mod_PreDefine_Precision
    use mod_Options
    implicit none

    type typ_DomaInfo
        integer:: domaID    = 0
        integer:: domaKD    = 0
        integer:: markID    = 0
    endtype typ_DomaInfo

    type typ_MarkInfo
        integer:: markID    = 0
        integer:: markKD    = 0
        !=====
        !Parameters for inlet
        real(kind=REALLEN):: surfPres     = 0.0
        real(kind=REALLEN):: surfTemp     = 0.0
        real(kind=REALLEN):: surfPtot     = 0.0
        real(kind=REALLEN):: surfTtot     = 0.0
        real(kind=REALLEN):: surfVelo     = 0.0
        real(kind=REALLEN):: surfNorm(3)  = (/0.0, 0.0, 0.0/)
        !Parameters for mass flow inlet
        real(kind=REALLEN):: surfArea     = 0.0
        real(kind=REALLEN):: surfMass     = 0.0
        !=====
        real(kind=REALLEN):: BackPresFact = 1.0
        real(kind=REALLEN):: BackPrecIncr = 0.0
        !=====
        !Parameter for trans wall
        real(kind=REALLEN):: transVelo(3) = (/0.0, 0.0, 0.0/)
        !Parameter for rotation wall
        real(kind=REALLEN):: rotatOrgn(3) = (/0.0, 0.0, 0.0/)
        real(kind=REALLEN):: rotatAxis(3) = (/0.0, 0.0, 1.0/)
        real(kind=REALLEN):: rotatOmga    = 0.0

    endtype typ_MarkInfo

    type typ_MRFInfo
        integer:: mrfID     = -1
        integer:: zoneID    = -1
        !
        real(kind=REALLEN):: transVelo(3) = (/0.0, 0.0, 0.0/)
        real(kind=REALLEN):: rotatOrgn(3) = (/0.0, 0.0, 0.0/)
        real(kind=REALLEN):: rotatAxis(3) = (/0.0, 0.0, 1.0/)
        real(kind=REALLEN):: rotatOmga    = 0.0
        !
        real(kind=REALLEN):: mrfRadu    = -1.0
        real(kind=REALLEN):: mrfXLen    = 0.0
    endtype typ_MRFInfo

    !---------------------------------------------
    !求解器定义
    !---------------------------------------------
    integer:: NUMERICAL_METHOD                          = _NUMERICAL_FVM        !求解器类型
    integer:: KIND_HARDWARE                             = _CPU                  !硬件类型
    integer:: NUMBER_PART                               = 1                     !是否MPI并行
    integer:: NTHREADPERBLOCK                           = 128                   !GPU线程结构
    integer:: KIND_POINTREORDERING                      = _NONE                 !是否节点重排
    integer:: IS_MESHLESSCLOUD_RECONS                   = 0                     !无网格点云是否重重新构造
    
    !---------------------------------------------
    !基本问题定义
    !---------------------------------------------
    character(len=STRLEN):: GRID_FILENAME               = 'default'             !选择网格文件名
    real(kind=REALLEN):: GRID_SCALE_FACTOR(3)           = (/1.0000, 1.0000, 1.0000/)
    real(kind=REALLEN):: MESH_DYFIRST                   = 1.0E-5
    real(kind=REALLEN):: MESH_DOMALEN                   = 1.0


    integer:: PHYSICAL_DIMENSION                        = 2                     !网格维度
    integer:: PHYSICAL_PROBLEM                          = _NONE                 !流动问题类型
    integer:: KIND_TURB_MODEL                           = _NONE                 !湍流模型类型
    integer:: KIND_TURBSA                               = _SA_STANDARD
    integer:: KIND_TURBSST                              = _SST_STANDARD
    integer:: IS_INCOMPRESSIBLE                         = _NO                   !是否不可压缩
    integer:: IS_DIMENSIONLESS                          = _NO                   !是否无量纲化
    integer:: RESTART_SOL                               = _NO                   !是否继续未完成的计算
    integer:: CONTAIN_MOVINGBC                          = _NO
    integer:: CONVERT_OUTP2WALL                         = _NO

    type(typ_DomaInfo),allocatable:: DomaInfos(:)
    type(typ_MarkInfo),allocatable:: MarkInfos(:)
    type(typ_MRFInfo),allocatable:: MRFInfos(:)

    !---------------------------------------------
    !动网格定义
    !---------------------------------------------
    integer:: GRID_MOVEMENT                             = _NO                    !网格是否运动
    integer:: GRID_MOVEMENT_KIND                        = _NO                    !网格运动方法
    !integer:: OVERLAP_GRID_KIND                         = _NO                   !重叠网格方法
    !integer:: EXCHINFORMATION_KIND                      = _NONE
    !integer:: NUMBERFORREPLACE                          = 1                     !嵌套滑移新增点云控制参数
    
    
    !---------------------------------------------
    !气体性质参数定义
    !---------------------------------------------
    real(kind=REALLEN):: AIR_GAMMA                      = 1.4000                !气体常数gamma
    real(kind=REALLEN):: AIR_GASCONSTANT                = 287.87                !气体常数R
    real(kind=REALLEN):: AIR_CP                         = 1007.5                !等压比热容比
    real(kind=REALLEN):: AIR_VISCOSITY                  = 1.8530E-05            !粘性系数
    real(kind=REALLEN):: AIR_PRANDTL_LAM                = 0.7200                !Prandtl常数 层流
    real(kind=REALLEN):: AIR_PRANDTL_TURB               = 0.9000                !Prandtl常数 湍流
    !---------------------------------------------
    !来流定义
    !---------------------------------------------
    integer:: FLOW_PARMTYPE                             = _NON_DIMENSIONAL      !来流参数类型
    integer:: FLOW_LVPLANE                              = _XZ                   !longitudinal vertical plane
    real(kind=REALLEN):: FLOW_DENSITY                   = 1.2886                !密度
    real(kind=REALLEN):: FLOW_PRESSURE                  = 101325                !压强
    real(kind=REALLEN):: FLOW_TEMPERATURE               = 273.15                !温度
    real(kind=REALLEN):: FLOW_VELOCITY(3)               = (/0.0000, 0.0000, 0.0000/)!速度分量
    real(kind=REALLEN):: FLOW_MACH                      = 0.0000                !马赫数
    real(kind=REALLEN):: FLOW_DIR(3)                    = (/1.0000, 0.0000, 0.0000/)!方向
    real(kind=REALLEN):: FLOW_AOA                       = 0.0000                !迎角
    real(kind=REALLEN):: FLOW_AOS                       = 0.0000                !侧滑角
    real(kind=REALLEN):: FLOW_AOA_RANGE(3)              = (/0.0000, 0.0000, 0.0000/)  !多重迎角
    real(kind=REALLEN):: FLOW_AOS_RANGE(3)              = (/0.0000, 0.0000, 0.0000/)  !多重迎角
    real(kind=REALLEN):: FLOW_REYNOLDS_NUMBER           = 0.0000                !雷诺数
    real(kind=REALLEN):: REYNOLDS_REFLENGTH             = 1.0000                !雷诺长度

    !---------------------------------------------
    !分量形式
    !---------------------------------------------
    !---------------------------------------------
    !Flow Controls
    real(kind=REALLEN):: FLOW_ALLOW_CHANGES             = 0.1
    real(kind=REALLEN):: FLOW_ALLOW_MINIMUM             = 0.001
    real(kind=REALLEN):: FLOW_ALLOW_MAXIMUM             = 10
    real(kind=REALLEN):: FLOW_ALLOW_MIND                = 0.001
    real(kind=REALLEN):: FLOW_ALLOW_MINP                = 1.0
    real(kind=REALLEN):: FLOW_ALLOW_MINT                = 1.0
    real(kind=REALLEN):: FLOW_ALLOW_MAXD                = 1000.0
    real(kind=REALLEN):: FLOW_ALLOW_MAXP                = 10000000.0
    real(kind=REALLEN):: FLOW_ALLOW_MAXT                = 5000.0
    real(kind=REALLEN):: FLOW_ALLOW_MAXX                = 10000.0
    real(kind=REALLEN):: FLOW_ALLOW_MINX                = 0.0


    !气动力计算参考量
    !---------------------------------------------
    real(kind=REALLEN):: AEROFM_REFM                    = -1.0                  ! reference mach number, used for zero speed flows
    real(kind=REALLEN):: AEROFM_REFL                    = 1.0                   !气动长度
    real(kind=REALLEN):: AEROFM_REFS                    = 1.0                   !气动面积
    real(kind=REALLEN):: AEROFM_ORGN(3)                 = (/0.0000, 0.0000, 0.0000/)!气动中心
    integer:: AEROFM_NAVGITER                           = 1                     ! number-iteration for FM-AVG
    
    !---------------------------------------------
    !离散格式定义
    !---------------------------------------------
    integer:: CONV_NUM_METHOD_FLOW                      = _UPW                  !对流通量个事
    integer:: UPWIND_ORDER                              = _ORDER_2ND            !迎风格式阶数
    integer:: NITER_O1TUNING                            = 1000                  !
    integer:: UPWIND_SCHEME                             = _SLAU                 !迎风格式类型
    integer:: UPWIND_LIMITER                            = _VENKATAKRISHNAN      !
    real(kind=REALLEN)::UPWIND_LIMITER_K                = 1.0                   !迎风格式限制器参数
    real(kind=REALLEN):: ARTDISS_K2                     = 0.72                  !中心格式系数
    real(kind=REALLEN):: ARTDISS_K4                     = 0.02                  !中心格式系数
    !
    integer:: TIME_DISCRE_FLOW                          = _MCLUSGS_IMPLICIT    !时间推进方法
    integer:: TIME_DISCRE_TURB                          = _MCLUSGS_IMPLICIT    !时间推进方法
    real(kind=REALLEN):: CFL_NUMBER                     = 1.25                  !CFL数
    real(kind=REALLEN):: CFL_RAMP(3)                    = (/1.0000, 100.00, 100.0000/) !CFL更新方法
    integer:: CFL_RAMP_KIND                             = _NONE

    integer:: NITER_OF_MCGS                             = 10                    !隐式内迭代次数
    integer:: IMPLICIT_TREATMENT                        = _SIMPLE_SR
    real(kind=REALLEN):: IMPLICIT_WEIGHT                = 1.5000                !隐式格式残值松弛因子
    
    
    !---------------------------------------------
    !非定常定义
    !---------------------------------------------
    integer:: UNSTEADY_SIMULATION                       = _NONE                 !非定常计算类型
    integer:: MESH_DEFORM_METHOD                        = _NONE
    integer:: UNST_INNER_ITER                           = 500                   !内部迭代步数
    integer:: UNST_OUTER_ITER                           = 0                     !外部迭代步数
    real(kind=REALLEN):: UNST_PHYSTIMEBGN               = 0.0                   !起始时间
    real(kind=REALLEN):: UNST_PHYSTIMEEND               = 0.0                   !终止时间
    real(kind=REALLEN):: UNST_PHYSTIMESTEP              = 1.0E-6                !时间步长
    !
    !---------------------------------------------
    !计算及收敛判断定义
    !---------------------------------------------
    integer:: NITER_STEADY                              = 1000

    integer:: RESAVE_BGNITER                            = 1
    integer:: RESAVE_ITER                               = 100                   !结果保存步数
    integer:: RESERR_ITER                               = 1                     !残差计算步数
    integer:: RESERR_SHOW                               = 100                   !残差显示步数
    real(kind=REALLEN):: RESERR_RESUCTION               = -3.0000               !残差下降等级
    real(kind=REALLEN):: RESERR_MINVAL                  = -8.0000               !残差最低等级

    integer:: RESU_FILETYPE                             = 0

endmodule

    
!---------------------------------------------
!无量纲化参数模块
!---------------------------------------------
module mod_parmDimLess
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    implicit none
    !Basic independent variables
    real(kind=REALLEN):: DLRef_length         !=input
    real(kind=REALLEN):: DLRef_density        !=input
    real(kind=REALLEN):: DLRef_pressure       !=input
    real(kind=REALLEN):: DLRef_temperature    !=input
    real(kind=REALLEN):: DLRef_velocity       !=sqrt(Ref_Pressure/Ref_density)
    real(kind=REALLEN):: DLRef_time           !=Ref_length/ref_velocity
    real(kind=REALLEN):: DLRef_viscosity      !=Ref_density*Ref_velocity*Ref_length
    real(kind=REALLEN):: DLRef_bodyForce      !=Ref_velocity**2/Ref_length
    !Derived variables
    real(kind=REALLEN):: DLRef_KinVoscosity   !=Ref_viscosity/Ref_density
    real(kind=REALLEN):: DLRef_energy         !=Ref_velocity**2
    real(kind=REALLEN):: DLRef_enthalpy       !=Ref_velocity**2
    real(kind=REALLEN):: DLRef_entropy        !=Ref_energy/Ref_temperature
    real(kind=REALLEN):: DLRef_heatFlux       !=Ref_density*Ref_energy*Ref_velocity
    real(kind=REALLEN):: DLRef_gasConstant    !=Ref_energy/Ref_temperature
    real(kind=REALLEN):: DLRef_Cp             !=Ref_gasConstant
    real(kind=REALLEN):: DLRef_Cv             !=Ref_gasConstant
    real(kind=REALLEN):: DLRef_heatConduct    !=Ref_Cp*Ref_viscosity
    real(kind=REALLEN):: DLRef_turbK          !=Ref_velocity**2
    real(kind=REALLEN):: DLRef_turbW          !=Ref_velocity/Ref_temperature
endmodule
!
