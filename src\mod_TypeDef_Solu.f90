! 
!---------------------------------------------
!
!---------------------------------------------
module mod_TypeDef_Solu
    use mod_PreDefine_Precision
    implicit none
    !
#define HYPERX_HRVect real(kind=REALLEN),allocatable
#define HYPERX_DRVect real(kind=REALLEN),device,allocatable
    !
    type:: typ_Solu
        ! ==== dimension values ====
        integer:: nNode
        integer:: nBelv
        integer:: nSate
        integer:: nMark
        !
        integer:: nSolu
        integer:: nPrim


        !=====================================================
        ! ==== common solution variables on host ====
        ! ---- boundary flow variables on host ----
        HYPERX_HRVect:: primm(:)
        HYPERX_HRVect:: primInfty(:)       !
        HYPERX_HRVect:: soluInfty(:)       !
        HYPERX_HRVect:: primMark(:,:)      !
        HYPERX_HRVect:: soluMark(:,:)      !
        HYPERX_HRVect:: soluUCRef(:)       !
        ! ---- field flow variables on host ----
        HYPERX_HRVect:: soluvar(:,:)       !solution variables
        HYPERX_HRVect:: primvar(:,:)       !primivate variables
        HYPERX_HRVect:: primBelv(:,:)      !
        HYPERX_HRVect:: soluBelv(:,:)      !
        HYPERX_HRVect:: iPresCoef(:)       !array of pressure coefficient
        HYPERX_HRVect:: iSurfCoef(:)       !array of surface triction coefficient
        HYPERX_HRVect:: iSurfTau(:,:)       !array of surface triction coefficient
        HYPERX_HRVect:: Tau(:,:,:)         !Dim(nDim,nDim,nNode)


        !=====================================================
        ! ==== solution variables for CPU-based simulation ===
        ! ---- mid variables
        HYPERX_HRVect:: deltSolu(:,:)      !old solution variables
        HYPERX_HRVect:: soluOld(:,:)       !old solution variables
        HYPERX_HRVect:: DT(:)              !时间步长
        HYPERX_HRVect:: DTCoef(:)          !时间步长
        HYPERX_HRVect:: Res_All(:,:)       !残值

        HYPERX_HRVect:: gradSolu(:,:,:)    !原始变量梯度
        HYPERX_HRVect:: limiter(:,:)       !UPW限制器
        HYPERX_HRVect:: SRconv(:)          !对流谱半径
        HYPERX_HRVect:: SRcNode(:)         !
        HYPERX_HRVect:: SRcBelv(:)         !
        HYPERX_HRVect:: SRvisc(:)          !粘性谱半径
        HYPERX_HRVect:: SRvNode(:)         !
        HYPERX_HRVect:: SRvBelv(:)         !
        ! ---- aux variables for JST central scheme
        HYPERX_HRVect:: artDiss(:,:)       !JST耗散项
        HYPERX_HRVect:: artDitt(:,:)       !JST耗散项
        HYPERX_HRVect:: artBelv(:,:)       !JST耗散项
        ! ---- aux variables for unsteady flows
        HYPERX_HRVect:: UnstTerm(:,:)      !Dual残值
        HYPERX_HRVect:: soluTimN(:,:)       !
        HYPERX_HRVect:: soluTimX(:,:)       !
        ! ---- aux variables for incompressible flows
        HYPERX_HRVect:: preCoef(:)         !预处理系数
        HYPERX_HRVect:: pMatrix(:,:,:)     !预处理矩阵


        !=====================================================
        ! ==== solution variables for GPU-based simulation ===
        ! ---- bound flow variables on gpu
        HYPERX_DRVect:: primInfty_d(:)      !
        HYPERX_DRVect:: soluInfty_d(:)      !
        HYPERX_DRVect:: primMark_d(:,:)     !
        HYPERX_DRVect:: soluMark_d(:,:)     !
        HYPERX_DRVect:: soluUCRef_d(:)      !
        ! ---- field flow variables on gpu
        HYPERX_DRVect:: primvar_d(:,:)      !
        HYPERX_DRVect:: soluvar_d(:,:)      !
        HYPERX_DRVect:: primBelv_d(:,:)     !
        HYPERX_DRVect:: soluBelv_d(:,:)     !
        ! ---- middle variables for updating
        HYPERX_DRVect:: deltSolu_d(:,:)     !
        HYPERX_DRVect:: soluOld_d(:,:)      !
        HYPERX_DRVect:: DT_d(:)             !
        HYPERX_DRVect:: DTCoef_d(:)         ! time tuning coefficient
        HYPERX_DRVect:: Res_All_d(:,:)      !
        ! ---- middle variables for grad
        HYPERX_DRVect:: gradSolu_d(:,:,:)   !原始变量梯度
        HYPERX_DRVect:: limiter_d(:,:)      !
        ! ---- moddle variable for SR
        HYPERX_DRVect:: SRconv_d(:)         !
        HYPERX_DRVect:: SRcBelv_d(:)        !
        HYPERX_DRVect:: SRcNode_d(:)        !
        HYPERX_DRVect:: SRvisc_d(:)         !
        HYPERX_DRVect:: SRvBelv_d(:)        !
        HYPERX_DRVect:: SRvNode_d(:)        !
        ! --- aux variable for tau
        HYPERX_DRVect:: Tau_d(:,:,:)
        ! --- aux variable for JST
        HYPERX_DRVect:: artDiss_d(:,:)      !
        HYPERX_DRVect:: artDitt_d(:,:)      !
        HYPERX_DRVect:: artBelv_d(:,:)      !
        ! ---- aux variables for unsteady flows
        HYPERX_DRVect:: UnstTerm_d(:,:)     !
        HYPERX_DRVect:: soluTimN_d(:,:)     !
        HYPERX_DRVect:: soluTimX_d(:,:)     !
        ! ---- aux variables for incompressible flows
        HYPERX_DRVect:: preCoef_d(:)         !
        HYPERX_DRVect:: pMatrix_d(:,:,:)    !


        !=====================================================
        ! ==== turbModel variables for GPU-based simulation ===
        ! ---- variables for turbSST ----
        integer:: nNodeAuxv = 0
        integer:: nSateAuxv = 0
        integer:: nBelvAuxv = 0
        HYPERX_DRVect:: nodeAuxv_d(:,:)
        HYPERX_DRVect:: sateAuvx_d(:,:)
        HYPERX_DRVect:: belvAuvx_d(:,:)

    endtype
    !
endmodule
!
