!
!---------------------------------------------
!
!---------------------------------------------
subroutine screenPrint(val_iProcess)
    use mod_PreDefine_Environment
    use mod_PreDefine_Dimension
    use mod_PreDefine_Precision
    use mod_PreDefine_IOPort
    use mod_MPIEnvironment
    use mod_WorkPath
    implicit none
    integer,intent(in):: val_iProcess
    
    if(myID == MASTER_NODE) then
        SELECT CASE (val_iProcess)
        CASE (000)
            if((nDim == 2).and.(REALLEN == 4)) then
            write(ioPort_Out,"(1X,A)")  "=====================  HyperFlow 2D SP v1.0 ======================="
            elseif((nDim == 2).and.(REALLEN == 8)) then
            write(ioPort_Out,"(1X,A)")  "=====================  HyperFlow 2D DP v1.0 ======================="
            elseif((nDim == 3).and.(REALLEN == 4)) then
            write(ioPort_Out,"(1X,A)")  "=====================  HyperFlow 3D SP v1.0 ======================="
            elseif((nDim == 3).and.(REALLEN == 8)) then
            write(ioPort_Out,"(1X,A)")  "=====================  HyperFlow 3D DP v1.0 ======================="
            endif
        
            write(ioPort_Out,"(1X,A)")  "$                                                                 $"
        
#ifdef GFLOW_WITH_MPI
            write(ioPort_Out,"(1X,A)")  "$          (NO_MPI version for single CPU and single GPU)         $"
#else
            write(ioPort_Out,"(1X,A)")  "$          (MPI version for multi-Core CPU and multi-GPU)         $"
#endif


            write(ioPort_Out,"(1X,A)")  "$                                                                 $"
            write(ioPort_Out,"(1X,A)")  "$                 Copyright by Jiale Zhang (2016-2024)            $"
            write(ioPort_Out,"(1X,A)")  "$                 Email: <EMAIL>                   $"
            write(ioPort_Out,"(1X,A)")  "==================================================================="
            !write(ioPort_Out,"(1X,A)")  " "

        CASE(1)
            write(ioPort_Out,"(1X,A)")  " "
            write(ioPort_Out,"(2X,A,A)" ) '>> projName : ',trim(projName)
            write(ioPort_Out,"(2X,A,A)" ) '>> soluName : ',trim(soluName)
            write(ioPort_Out,"(1X,A)")  " "

        CASE (100)
            write(ioPort_Out,*) '==Config======================================='

        CASE (200)
            write(ioPort_Out,*) "==MESH========================================="
        CASE (220)
            write(ioPort_Out,*) "==Global MESH=="
        CASE (221)
            write(ioPort_Out,"(2X,A)") ">> Global Mesh File Reading... "
        CASE (222)
            write(ioPort_Out,"(2X,A)") ">> Global Mesh Initializing... "
        CASE (223)
            write(ioPort_Out,"(2X,A)") ">> Global Mesh Reforming... "
        CASE (224)
            write(ioPort_Out,"(2X,A)") ">> Global Mesh DeformInit... "
            
        CASE (230)
            write(ioPort_Out,*) "==Part MESH=="
            
        CASE (240)
            write(ioPort_Out,*) "==Local MESH=="
        CASE (241)
        write(ioPort_Out,"(2X,A)") ">> Local Mesh Initializing... "
        CASE (242)
        write(ioPort_Out,"(2X,A)") ">> GPU Mesh Initializing... "
            
        
        CASE (300)
            write(ioPort_Out,*) '==Solution====================================='
    
            
        CASE (400)
            write(ioPort_Out,*) '===================FINISHED===================='
    
            
        CASE DEFAULT
            write(ioPort_Out,*) ' '
            write(ioPort_Out,*) ' '
            
        END SELECT
    endif
    
endsubroutine
!
    
    