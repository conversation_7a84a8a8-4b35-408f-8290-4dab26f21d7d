!
!---------------------------------------------
!
!---------------------------------------------
module mod_bldExchLink
    use mod_Project
    implicit none
    !
contains
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine bldExchLink
        implicit none
        !
        integer:: i,j
    
        do i=1,nMeshPart
            iPartMesh(i)%nExch = 2*nMeshPart
        
            if(allocated(iPartMesh(i)%iExch)) deallocate(iPartMesh(i)%iExch)
        
            allocate(iPartMesh(i)%iExch(iPartMesh(i)%nExch))
        enddo
        
        do i=1,nMeshPart
            do j=1,nMesh<PERSON>art
                call bldExchLinkFromPartToPart(i, j, iPartMesh(i)%iExch(j), &
                                            iPartMesh(j)%iExch(i+nMeshPart) )
            enddo
        enddo
    
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine bldExchLinkFromPartToPart(val_recvID,val_sendID,val_recvRegn,val_sendRegn)
        use mod_Interface_AllocateArray
        use mod_PreDefine_ExchKind
        implicit none
        integer,intent(in):: val_recvID
        integer,intent(in):: val_sendID
        type(typ_ExchRegn),intent(inout):: val_recvRegn
        type(typ_ExchRegn),intent(inout):: val_sendRegn
        !
        integer:: i,j,k,iCount
        integer:: poinID,poinOD,sateRD
        integer:: nExch,nPoinExch,nBelvExch
        integer:: iTempFlag(iGlobalMesh%nPoin)
        
        
        !---------------------------------------------
        nExch     = 0
        nPoinExch = 0
        nBelvExch = 0
        
        if(val_sendID /= val_recvID) then
            iCount = 0
            do i=1,iPartMesh(val_recvID)%nPoin
                if(iPartMesh(val_recvID)%iPart_Poin(i) == val_sendID) then
                    iCount = iCount + 1
                endif
            enddo
            nPoinExch = iCount
        endif
        
        iCount = 0
        do i=1,iPartMesh(val_recvID)%nBelv
            if(iPartMesh(val_recvID)%iBelvProp(i,2) <= 0) then
                sateRD = iMeshMapp(val_recvID)%iMapBToR_SaBv(i)
                
                poinOD = iGlobalMesh%iExteRegnSANO%iSate_Poin(sateRD,1)
                
                if(iGlobalMesh%iPart_Poin(poinOD) == val_sendID) then
                    iCount = iCount + 1
                endif
            endif
        enddo
        nBelvExch = iCount
        
        nExch = nPoinExch + nBelvExch
        
        
        !---------------------------------------------
        val_recvRegn%KIND  = EXCH_RECV
        val_recvRegn%EXID  = val_sendID
        val_recvRegn%nPoin = nExch
        
        val_sendRegn%KIND  = EXCH_SEND
        val_sendRegn%EXID  = val_recvID
        val_sendRegn%nPoin = nExch
        
        if(nExch == 0) return
        
        !---------------------------------------------
        call allocateArray(val_recvRegn%iPoin, nExch)
        call allocateArray(val_sendRegn%iPoin, nExch)
        
        
        iCount = 0
        if(val_sendID /= val_recvID) then
            do i=1,iPartMesh(val_recvID)%nPoin
                if(iPartMesh(val_recvID)%iPart_Poin(i) == val_sendID) then
                    iCount = iCount + 1
                    
                    poinOD = iMeshMapp(val_recvID)%iMapLToG_Poin(i)
                    
                    val_recvRegn%iPoin(iCount) = i
                    val_sendRegn%iPoin(iCount) = iMeshMapp(val_sendID)%iMapGToL_Poin(poinOD)
                endif
            enddo
        endif
        
        do i=1,iPartMesh(val_recvID)%nBelv
            if(iPartMesh(val_recvID)%iBelvProp(i,2) <= 0) then
                sateRD = iMeshMapp(val_recvID)%iMapBToR_SaBv(i)
                
                poinOD = iGlobalMesh%iExteRegnSANO%iSate_Poin(sateRD,1)
                
                if(iGlobalMesh%iPart_Poin(poinOD) == val_sendID) then
                    iCount = iCount + 1
                    
                    val_recvRegn%iPoin(iCount) = -i
                    val_sendRegn%iPoin(iCount) = iMeshMapp(val_sendID)%iMapGToL_Poin(poinOD)
                endif
            endif
        enddo
        
        
    endsubroutine
    !
endmodule
!
    
    