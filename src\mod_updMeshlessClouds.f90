！
!---------------------------------------------
!
!---------------------------------------------
module mod_updMeshlessClouds
    use mod_TypeDef_Mesh
    use mod_Interface_AllocateArray
    implicit none
    !
    integer,allocatable:: iReed_Poin(:)
    integer,allocatable:: iIner_Poin(:)
    integer,allocatable:: iRegn_Poin(:)
    integer:: nBCPoin
    integer,allocatable:: iPoin_BC(:)
    !
    real(kind=REALLEN),allocatable:: iAxis_Poin(:,:,:)
    !
contains
    !
    subroutine updMeshlessClouds(val_iMesh)
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
        !
        integer:: i,j
        integer:: nSateI,iSateI(99)
        logical:: isReBld,isReTotal
        integer:: kSate_Temp(val_iMesh%nPoin+1)
        integer:: iSate_Temp(val_iMesh%nSate*2,2)
        
        
        
        call initializationII(val_iMesh) !初始化必要数组
        
        
        call calIAxis_Poin(val_iMesh) !设定重构局部坐标系
        
        
        iReed_Poin(:)   = 0
        kSate_Temp(:)   = 0
        iSate_Temp(:,:) = 0
    
        isReTotal = .false.
        do i=1,val_iMesh%nPoin
            if(iIner_Poin(i) == 0) then
                isReBld = .false.
            else
                call reBuildCloudI(val_iMesh, i, nSateI, iSateI, isReBld)
            endif
        
            if(isReBld) then
                kSate_Temp(i+1) = kSate_Temp(i) + nSateI
                
                do j=1,nSateI
                    iSate_Temp(kSate_Temp(i)+j,1) = iSateI(j)
                    iSate_Temp(kSate_Temp(i)+j,2) = 0
                enddo
            else
                isReTotal = .true.
            
                nSateI = val_iMesh%kSate_Poin(i+1) - val_iMesh%kSate_Poin(i)
            
                kSate_Temp(i+1) = kSate_Temp(i) + nSateI
            
                do j=1,nSateI
                    iSate_Temp(kSate_Temp(i)+j,1) = val_iMesh%iSate_Poin(val_iMesh%kSate_Poin(i)+j,1)
                    iSate_Temp(kSate_Temp(i)+j,2) = val_iMesh%iSate_Poin(val_iMesh%kSate_Poin(i)+j,2)
                enddo
            endif
            
            iReed_Poin(i) = 1
        enddo
    
        if(isReTotal) then
            val_iMesh%nSate = kSate_Temp(val_iMesh%nPoin+1)
    
            call allocateArray(val_iMesh%iSate_Poin,val_iMesh%nSate,2)
        
            do i=1,val_iMesh%nSate
                val_iMesh%iSate_Poin(i,1) = iSate_temp(i,1)
                val_iMesh%iSate_Poin(i,2) = iSate_temp(i,2)
            enddo
        endif
            
    endsubroutine
    !
    subroutine initializationII(val_iMesh)
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
        !
        integer:: i,j,iCount
        
        
        call allocateArray(iReed_Poin, val_iMesh%nPoin)
        call allocateArray(iRegn_Poin, val_iMesh%nPoin)
        call allocateArray(iIner_Poin, val_iMesh%nPoin)
        
        !iReed_Poin
        iReed_Poin(:) = 0
        
        !iRegn_Poin
        iRegn_Poin(:) = val_iMesh%iRegn_Poin(:)
        

        !iIner_Poin
        iIner_Poin(:) = 1
        
        do i=1,val_iMesh%nBele
            do j=1,2*nDim-2
                iIner_Poin(val_iMesh%iBele(j,i)) = 0
            enddo
        enddo
        
        
        !iPoin_BC
        iCount = 0
        do i=1,val_iMesh%nPoin
            if(iIner_Poin(i) == 0) then
                iCount = iCount + 1
            endif
        enddo
        
        nBCPoin = iCount
        call allocateArray(iPoin_BC, nBCPoin)
        
        iCount = 0
        do i=1,val_iMesh%nPoin
            if(iIner_Poin(i) == 0) then
                iCount = iCount + 1
                
                iPoin_BC(iCount) = i
            endif
        enddo
        !
    endsubroutine
    !
    subroutine calIAxis_Poin(val_iMesh)
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
        
        
        
    endsubroutine
    !
    subroutine reBuildCloudI(val_iMesh,val_poinID,  &
                val_nSateI,val_iSateI,val_isReBld   )
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
        integer,intent(in):: val_poinID
        integer,intent(inout):: val_nSateI
        integer,intent(inout):: val_iSateI(99)
        logical,intent(inout):: val_isReBld
        !
        integer:: k,s
        integer:: nSate,sateID
        !
        val_isReBld = .false.
        return
        
        nSate = val_iMesh%kSate_Poin(val_poinID+1) - &
                val_iMesh%kSate_Poin(val_poinID)
        
        if(nSate > 3*nDim) then
            !点太多 剔除
            !1:看当地网格正交性
            !
            !选点方向依据：其与最近边界点连线为主方向
        endif
        
        !点云奇异，重选点
        !if( ) then 
        !endif
        
        !
    endsubroutine
    !               
endmodule
!
    
