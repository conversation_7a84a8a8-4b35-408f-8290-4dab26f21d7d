!
subroutine iniInMesh
    use mod_Mesh
    implicit none
    integer:: i,j,k
    
    allocate(iInMesh(nMesh))
    iInMesh(:)%nDime = nDime
    
    do i=1,nMesh
        call readMesh_starCD(workDir,iMeshNameList(i),iInMesh(i))
        
        call getElemInDoma(iInMesh(i))
        
        write(*,*) 'nelem_all:',iInMesh(i)%nElem
        do j=1,iInMesh(i)%nDoma
            write(*,*) 'Doma',j,' :',iInMesh(i)%iDoma(j)%DomaID,' nElem :',iInMesh(i)%iDoma(j)%nElem
            
        enddo
        
        deallocate(iInMesh(i)%iElem)
        deallocate(iInMesh(i)%elemDoma)
        
    enddo
    
endsubroutine
!
