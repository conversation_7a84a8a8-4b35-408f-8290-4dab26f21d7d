
!---------------------------------------------
!
!---------------------------------------------
subroutine calIElemLink(val_iMesh)
    use mod_ElemProp
    use mod_TypeDef_Mesh
    use mod_PreDefine_Dimension
    use mod_PreDefine_IOPort
    use mod_calMeshTopo_Func
    implicit none
    type(typ_GlobalMesh),intent(inout):: val_iMesh
    !
    integer:: i,el,kl,er,kr,ick,jck,ieei(2*nDim-2),jeej(2*nDim-2)
    
    
    
    val_iMesh%iElemLink(:,:,:) = 0
    !
    call calIEdgeTemp(val_iMesh)
    
    !
    val_iMesh%iElemLink(:,:,:) = 0
    !$omp parallel do private(i,el,kl,er,kr,ick,jck,ieei,jeej)
    do i=1,val_iMesh%nEdge
        el = val_iMesh%iEdge(2*nDim-1 , i)
        kl = val_iMesh%iEdge(2*nDim   , i)
        er = val_iMesh%iEdge(2*nDim+1 , i)
        kr = val_iMesh%iEdge(2*nDim+2 , i)
        
        val_iMesh%iElemLink(el,kl,1) = er
        val_iMesh%iElemLink(el,kl,2) = kr
        val_iMesh%iElemLink(el,kl,3) = i
        
        if(er > 0) then
            val_iMesh%iElemLink(er,kr,1) = el
            val_iMesh%iElemLink(er,kr,2) = kl
            val_iMesh%iElemLink(er,kr,3) = i

            ick = val_iMesh%iElemKind(el)
            jck = val_iMesh%iElemKind(er)

            ieei(:) = val_iMesh%iElem(ElemProp_EdgeList(1:2*nDim-2,kl,ick),el)
            jeej(:) = val_iMesh%iElem(ElemProp_EdgeList(1:2*nDim-2,kr,jck),er)

            SELECT CASE (getEqual_Edge(2*nDim-2,ieei,jeej))
            CASE (0)
                write(ioPort_Out,*) 'ERROR, not Equal',el,kl,er,kr
                write(ioPort_Out,*) ieei,jeej
            CASE(1)
                write(ioPort_Out,*) 'ERROR, same dir',el,kl,er,kr
                write(ioPort_Out,*) val_iMesh%iElem(:,el)," : ",ieei
                write(ioPort_Out,*) val_iMesh%iElem(:,er)," : ",jeej
            END SELECT
        endif
    enddo
    !$omp end parallel do
    !pause 'XXXX'
    
    val_iMesh%nEdge = 0
    deallocate(val_iMesh%iEdge)
    
contains
    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine calIEdgeTemp(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_Interface_AllocateArray
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
    
        integer:: i,count
        integer:: kEdgeOnPoin(val_iMesh%nPoin+1)
        integer:: allEdgeCount
        integer,allocatable:: allEdge(:,:)
        logical:: iok = .true.
    
        !获取每个顶点上相连的Edge数目
        call getKEdgeOnPoin(nDim                , &
                            val_iMesh%nElem     , &
                            val_iMesh%iElem     , &
                            val_iMesh%iElemKind , &
                            val_iMesh%nPoin     , &
                            kEdgeOnPoin         )
    
        allEdgeCount = kEdgeOnPoin(val_iMesh%nPoin+1)
    
        call allocateArray(allEdge, 2*nDim+2 , allEdgeCount)
        allEdge(:,:) = 0
    
        !获取AllEdge
        call getAllEdge(nDim                , &
                        val_iMesh%nElem     , &
                        val_iMesh%iElem     , &
                        val_iMesh%iElemKind , &
                        val_iMesh%nPoin     , &
                        kEdgeOnPoin         , &
                        allEdgeCount        , &
                        allEdge             , &
                        val_iMesh%nMark     , &
                        val_iMesh%iMark     )
    
        !获取IEdge
        call getIEdge(nDim          , &
                      allEdgeCount  , &
                      allEdge       , &
                      val_iMesh     )
    
        !Mark处理
        call trtIEdgeByIMark(val_iMesh)
    
        !检查IEdge
        call chkIEdge(val_iMesh%nElem       , &
                      nDim*4-4              , &
                      val_iMesh%iElem       , &
                      val_iMesh%iElemKind   , &
                      val_iMesh%nEdge       , &
                      nDim*2+2              , &
                      val_iMesh%iEdge       , &
                      iok                   )
    
        if(.not.iok) then
            write(ioPort_Out,*) 'check iedge error'
            stop
        endif
    
        deallocate(allEdge)
    
    endsubroutine
    
    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getKEdgeOnPoin(val_nDim,val_nElem,val_iElem,val_elemKind,val_nPoin,val_kEdgeOnPoin)
        use mod_ElemProp
        implicit none
        integer,intent(in):: val_nDim
        integer,intent(in):: val_nElem
        integer,intent(in):: val_iElem(val_nDim*4-4,val_nElem)
        integer,intent(in):: val_elemKind(val_nElem)
        integer,intent(in):: val_nPoin
        integer,intent(inout):: val_kEdgeOnPoin(val_nPoin+1)

        integer:: i,j,k,nk
        integer:: icci(val_nDim*4-4),ick
        integer:: ieei(val_nDim*2-2)

        val_kEdgeOnPoin(:) = 0

        !$omp parallel do private(j,k,ick,icci,ieei,nk)
        do i=1,val_nElem
            icci(:) = val_iElem(:,i)
            ick = val_elemKind(i)
        
            if((ick < 1).or.(ick > nElemProp)) cycle
        
            do j=1,ElemProp_edgeNum(ick)
                ieei(:) = icci(ElemProp_edgeList(1:val_nDim*2-2,j,ick))
            
                nk = ieei(1)
                do k=2,val_nDim*2-2
                    nk = min(nk,ieei(k))
                enddo

                !$omp atomic
                val_kEdgeOnPoin(nk+1) = val_kEdgeOnPoin(nk+1)+1
            enddo
        enddo
        !$omp end parallel do

        val_kEdgeOnPoin(1) = 0
        do i=2,val_nPoin+1
            val_kEdgeOnPoin(i) = val_kEdgeOnPoin(i)+val_kEdgeOnPoin(i-1)
        enddo
    
    endsubroutine
    
    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getAllEdge(val_nDim,val_nElem,val_iElem,val_elemKind,val_nPoin,val_kEdgeOnPoin,val_allEdgeCount,val_allEdge,val_nMark,val_iMark)
        use mod_ElemProp
        use mod_TypeDef_Mesh
        use mod_calMeshTopo_Func
        implicit none
        integer,intent(in)   :: val_nDim
        integer,intent(in)   :: val_nElem
        integer,intent(in)   :: val_iElem(val_nDim*4-4,val_nElem)
        integer,intent(in)   :: val_elemKind(val_nElem)
        integer,intent(in)   :: val_nPoin
        integer,intent(inout):: val_kEdgeOnPoin(val_nPoin+1)
        integer,intent(in)   :: val_allEdgeCount
        integer,intent(inout):: val_allEdge(val_nDim*2+2,val_allEdgeCount)
        integer,intent(in)   :: val_nMark
        type(typ_Block),intent(in):: val_iMark(val_nMark)
        !logical function
        !logical:: isEqual_Edge
    
        integer:: i,j,k,nk,iid
        integer:: icci(val_nDim*4-4),ick
        integer:: ieei(val_nDim*2-2),jeej(val_nDim*2-2)
        integer:: Er,ck,iPoinFlag(val_nPoin)

        !!!$omp parallel do private(j,k,nk,iid,icci,ick,ieei,jeej,Er,ck,iPoinFlag)
        do i=1,val_nElem
            icci(:) = val_iElem(:,i)
            ick = val_elemKind(i)
        
            if((ick < 1).or.(ick > nElemProp)) cycle
        
            do j=1,ElemProp_edgeNum(ick)
                ieei(:) = icci(ElemProp_edgeList(1:val_nDim*2-2,j,ick))
            
                nk = ieei(1)
                do k=2,val_nDim*2-2
                    nk = min(nk,ieei(k))
                enddo

                !!!$omp atomic capture
                iid = val_kEdgeOnPoin(nk)+1
                val_kEdgeOnPoin(nk) = val_kEdgeOnPoin(nk)+1
                !!!$omp end atomic

                val_allEdge(1:val_nDim*2-2 , iid) = ieei(:)
                val_allEdge(  val_nDim*2-1 , iid) = i
                val_allEdge(  val_nDim*2   , iid) = j
            enddo
        enddo
        !!!$omp end parallel do

        do i=val_nPoin+1,2,-1
            val_kEdgeOnPoin(i) = val_kEdgeOnPoin(i-1)
        enddo
        val_kEdgeOnPoin(1) = 0
        
        !处理边界（主要用于内部零厚度边界处理）
        do i=1,val_nMark
            iPoinFlag(:) = 0
            
            do j=1,val_iMark(i)%nPoin
                iPoinFlag(val_iMark(i)%iPoin(j)) = 1
            enddo

            !$omp parallel do private(j,k,ieei,nk)
            do j=1,val_allEdgeCount
                ieei(:) = val_allEdge(1:val_nDim*2-2,j)
                
                nk = 0
                do k=1,val_nDim*2-2
                    nk = nk + iPoinFlag(ieei(k))
                enddo
                
                if(nk == val_nDim*2-2) then
                    val_allEdge(val_nDim*2+2,j) = -100
                endif
            enddo
            !$omp end parallel do
        enddo
    
        !重复Edge处理
        !$omp parallel do private(i,j,k,ieei,jeej)
        do i=1,val_nPoin
            do j=val_kEdgeOnPoin(i)+2,val_kEdgeOnPoin(i+1)
                
                ieei(:) = val_allEdge(1:val_nDim*2-2,j)
                do k=val_kEdgeOnPoin(i)+1,j-1
                    jeej(:) = val_allEdge(1:val_nDim*2-2,k)
                
                    if(jeej(1) == 0) cycle
                
                    if(isEqual_Edge(val_nDim*2-2,ieei,jeej)) then
                        val_allEdge(val_nDim*2+1,k) =val_allEdge(val_nDim*2-1,j)
                        val_allEdge(val_nDim*2+2,k) =val_allEdge(val_nDim*2  ,j)
                        val_allEdge(:,j) = 0
                        exit
                    endif
                enddo
            enddo
        enddo
        !$omp end parallel do

        !$omp parallel do
        do i=1,val_allEdgeCount
            if(val_allEdge(val_nDim*2+2,i) == -100) then
                val_allEdge(val_nDim*2+2,i) = 0
            endif
        enddo
        !$omp end parallel do
    
    endsubroutine
    
    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getIEdge(val_nDim,val_allEdgeCount,val_allEdge,val_iMesh)
        use mod_TypeDef_Mesh
        implicit none
        integer,intent(in):: val_nDim
        integer,intent(in):: val_allEdgeCount
        integer,intent(in):: val_allEdge(val_nDim*2+2,val_allEdgeCount)
        type(typ_GlobalMesh),intent(inout):: val_iMesh
    
        integer:: i,count,iid
    
        count = 0
        do i=1,val_allEdgeCount
            if(val_allEdge(1,i) /= 0) then
                count = count+1
            endif
        enddo
    
        val_iMesh%nEdge = count
        
        allocate(val_iMesh%iEdge(val_nDim*2+2 , val_iMesh%nEdge))
        val_iMesh%iEdge(:,:) = 0
    
        count = 0
        !!!$omp parallel do
        do i=1,val_allEdgeCount
            if(val_allEdge(1,i) /= 0) then
                !!!$omp atomic capture
                iid = count + 1
                count = count+1
                !!!$omp end atomic

                val_iMesh%iEdge(:,iid) = val_allEdge(:,i)
            endif
        enddo
        !!!$omp end parallel do
    
    endsubroutine
    
    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine trtIEdgeByIMark(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_PreDefine_IOPort
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
    
        integer:: i,j,k
        integer:: er
        logical:: isOk
        integer,dimension(val_iMesh%nDime*2-2):: ieei
        integer,dimension(val_iMesh%nPoin):: poinFlag
    

        do i=1,val_iMesh%nMark
            poinFlag(:) = 0

            !$omp parallel do
            do j=1,val_iMesh%iMark(i)%nPoin
                poinFlag(val_iMesh%iMark(i)%iPoin(j)) = 1
            enddo
            !$omp end parallel do

            !$omp parallel do private(k,er,ieei,isOk)
            do j=1,val_iMesh%nEdge
                er = val_iMesh%iEdge(nDim*2+1,j)
                if(er == 0) then
                    ieei(:) = val_iMesh%iEdge(1:nDim*2-2,j)
                
                    isOk = .true.
                    do k=1,nDim*2-2
                        if(poinFlag(ieei(k)) == 0) then
                            isOk = .false.
                            exit
                        endif
                    enddo
                
                    if(isOk) then
                        val_iMesh%iEdge(nDim*2+1,j) = -i
                    endif
                endif
            enddo
            !$omp end parallel do

        enddo
    
        do i=1,val_iMesh%nEdge
            er = val_iMesh%iEdge(nDim*2+1,i)
            if(er == 0) then
                write(ioPort_Out,*) i,'Edge:',val_iMesh%iEdge(:,i)
                write(ioPort_Out,*) 'error: er == 0'
                stop
            endif
        enddo
    
    endsubroutine
    
    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine chkIEdge(val_nElem,val_chSize,val_iCell,val_elemKind,val_nEdge,val_ehSize,val_iEdge,val_iok)
        use mod_ElemProp
        use mod_calMeshTopo_Func
        implicit none
        integer,intent(in):: val_nElem
        integer,intent(in):: val_chSize
        integer,intent(in),dimension(val_chSize,val_nElem):: val_iCell
        integer,intent(in),dimension(val_nElem):: val_elemKind
        integer,intent(in):: val_nEdge
        integer,intent(in):: val_ehSize
        integer,intent(in),dimension(val_ehSize,val_nEdge):: val_iEdge
        logical,intent(out):: val_iok
    
        integer:: i,j,iok,ick
        integer:: el,kl,er,kr
        integer:: icci(val_chSize),ieei(val_ehSize-4),jeej(val_ehSize-4)
        !logical:: isEqual_Edge

        iOk = .true.

        !$omp parallel do private(j,iok,ick,el,kl,er,kr,icci,ieei,jeej)
        do i=1,val_nEdge
            ieei(:) = val_iEdge(1:val_ehSize-4,i)
            el = val_iEdge(val_ehSize-3,i)
            kl = val_iEdge(val_ehSize-2,i)
            er = val_iEdge(val_ehSize-1,i)
            kr = val_iEdge(val_ehSize,i)
        
            if(el > 0) then
                icci(:) = val_iCell(:,el)
            
                ick = val_elemKind(el)
            
                jeej(:) = icci(ElemProp_edgeList(1:val_ehSize-4,kl,ick))
            
                !logical function isEqual_Edge(val_size,val_edgei,val_edgej)
                if(.not.isEqual_Edge(val_ehSize-4,ieei,jeej)) then
                    write(*,*) 'EEE'
                    iOk = .false.
                endif
            else
                write(*,*) 'EEE'
                iOk = .false.
            endif
        
            if(er > 0 .and. iOk) then
                icci(:) = val_iCell(:,er)
            
                ick = val_elemKind(er)
            
                jeej(:) = icci(ElemProp_edgeList(1:val_ehSize-4,kr,ick))
            
                !logical function isEqual_Edge(val_size,val_edgei,val_edgej)
                if(.not.isEqual_Edge(val_ehSize-4,ieei,jeej)) then
                    write(*,*) 'EEE'
                    iOk = .false.
                endif
            endif
        enddo
        !$omp end parallel do
    
        val_iok = iOk
        return
    
    endsubroutine
    !
endsubroutine


