!
subroutine testIO_LocalMesh_Belv(val_iMesh,val_iPart,val_iIter)
    use mod_PreDefine_Dimension
    use mod_PreDefine_IOPort
    use mod_PreDefine_Flag
    use mod_TypeDef_Mesh
    use mod_WorkPath
    use mod_strOfNumber
    implicit none
    type(typ_Mesh),intent(in):: val_iMesh
    integer,intent(in):: val_iPart
    integer,intent(in):: val_iIter
    !
    integer:: i,j,elemID,poinID
    character(len=STRLEN):: string_varList
    character(len=STRLEN):: string_ZoneType
    character(len=STRLEN):: string_VarShare
    character(len=STRLEN):: fileName
    real(kind=REALLEN):: iCoef_Poin(nDim,val_iMesh%nPoin)
    
    
    iCoef_Poin(:,:) = 0.0
    do i=1,val_iMesh%nBelv
        poinID = val_iMesh%iBelvProp(i,1)
        
        iCoef_Poin(:,poinID) = iCoef_Poin(:,poinID) + val_iMesh%iCoef_Belv(i,:)
    enddo
    
    
    if(nDim == 2) then
        string_varList  = 'VARIABLES="X","Y","iCoefX","iCoefY" '
        string_ZoneType = 'FEQUADRILATERAL'
        string_VarShare = '([1-4]=1)'
    elseif(nDim == 3) then
        string_varList  = 'VARIABLES="X","Y","Z","iCoefX","iCoefY","iCoefZ" '
        string_ZoneType = 'FEBRICK'
        string_VarShare = '([1-6]=1)'
    else
        return
    endif
    
    fileName = 'LocalBelv'//strTwo_Number(val_iPart)//'_'//strSix_Number(val_iIter)//'.plt'
    
    open(ioPort_FULL,file=trim(testFullPath)//'/'//trim(fileName),asynchronous='yes',status='unknown')
    
    do i = 1,val_iMesh%nDoma
        if(val_iMesh%iDoma(i)%nValdElem <= 0) cycle
        
        write(ioPort_FULL,*) trim(adjustl(string_varList))
        if(i == 1) then
            write(ioPort_FULL,*)'ZONE DATAPACKING = POINT, NODES =', val_iMesh%nPoin,   &
                                    ', ELEMENTS =', val_iMesh%iDoma(i)%nValdElem,       &
                                    ', ZONETYPE = ',trim(adjustl(string_ZoneType))
            
            do j=1,val_iMesh%nPoin
                write(ioPort_FULL,"(6E14.5)") val_iMesh%iCoor_Poin(j,:),iCoef_Poin(:,j)
            enddo
            
        else
            write(ioPort_FULL,*)'ZONE DATAPACKING = POINT, NODES =', val_iMesh%nPoin,   &
                                    ', ELEMENTS =', val_iMesh%iDoma(i)%nValdElem,       &
                                    ', ZONETYPE = ',trim(adjustl(string_ZoneType)),     &
                                    ', VARSHARELIST=',trim(adjustl(string_VarShare))
        endif
        
        do j=1,val_iMesh%iDoma(i)%nElem
            elemID = val_iMesh%iDoma(i)%iElem(j)
            
            if(val_iMesh%iFlag_Elem(elemID) /= FLAG_VALID) cycle
            
            write(ioPort_FULL,*) val_iMesh%iElem(:,elemID)
        enddo
    enddo
    
    close(ioPort_FULL)
    
endsubroutine
!