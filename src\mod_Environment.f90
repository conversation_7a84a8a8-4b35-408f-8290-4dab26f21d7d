

!---------------------------------------------
!
!---------------------------------------------
module mod_MPIEnvironment
    implicit none
#ifdef GFLOW_WITH_MPI
    include 'mpif.h'
#endif
    !
    integer,parameter:: MASTER_NODE = 0 !主节点预定义
    !
    integer:: myID      = MASTER_NODE   !当前节点编号
    integer:: nProcess  = 1             !总进程数
    integer:: iError    = 0             !Error标识
    !
    integer:: nOMPThreads =  1          !Number of OpenMP threads per mpi rank
    integer:: GPUIDX      = -1          !Index of GPU card for use
endmodule

    
!---------------------------------------------
!
!---------------------------------------------
module mod_GPUEnvironment
    use cudafor
    implicit none
    !
    integer             :: deviceCount  = 0    !GPU数目
    integer             :: currentGPUID = -1   !选用GPU的编号
    type(cudaDeviceProp):: currentDeviceProp   !选用GPU的属性
    !
    type(cudaDeviceProp),allocatable :: deviceProps(:)  !所有GPU的属性
    !
endmodule
