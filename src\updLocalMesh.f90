
!---------------------------------------------
!
!---------------------------------------------
subroutine updLocalMesh(val_iMesh,val_iIter)
    use mod_TypeDef_Mesh
    use mod_Config
    use mod_WorkPath
    use mod_MPIEnvironment
    use mod_PartMeshIO
    use mod_calLocalMesh_Topology
    use mod_calLocalMesh_Geometric
    implicit none
    type(typ_Mesh),intent(inout):: val_iMesh
    integer,intent(in):: val_iIter
    
    
    !---------------------------------------------
    !从网格子区读取网格
    call readPartMesh_Updating(val_iMesh,geomFullPath,myID)
    
    
    !---------------------------------------------
    !计算局部网格拓扑信息
    call calLocalMesh_Topology(val_iMesh)
    
    
    !---------------------------------------------
    !计算局部网格几何信息
    call calLocalMesh_Geometric(val_iMesh, val_iIter)
    
    
    !---------------------------------------------
    !测试输出
    call testIO_LocalMesh(val_iMesh      , myID+1, val_iIter)
    call testIO_LocalMesh_Belv(val_iMesh , myID+1, val_iIter)
    call testIO_LocalMesh_iCoef(val_iMesh, myID+1, val_iIter)
    
endsubroutine
!
