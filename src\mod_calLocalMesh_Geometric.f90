!
module mod_calLocalMesh_Geometric
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    use mod_PreDefine_IOPort
    use mod_Interface_AllocateArray
    implicit none
    !
contains
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine calLocalMesh_Geometric(val_iMesh,val_iIter)
        use mod_TypeDef_Mesh
        use mod_Config
        use mod_WorkPath
        implicit none
        type(typ_Mesh),intent(inout):: val_iMesh
        integer,intent(in):: val_iIter
        !
        integer:: i
        
    
        !---------------------------------------------
        call allocateArray(val_iMesh%iVelo_Poin, val_iMesh%nPoin, nDim)
        call allocateArray(val_iMesh%iVelo_Belv, val_iMesh%nBelv, nDim)
        val_iMesh%iVelo_Poin(:,:) = 0.0
        val_iMesh%iVelo_Belv(:,:) = 0.0

        if(GRID_MOVEMENT == _YES) then
            if(val_iIter > 1) then
                val_iMesh%iVelo_Poin(:,:) = val_iMesh%dCoor_Poin(:,:)/UNST_PHYSTIMESTEP
                val_iMesh%iVelo_Belv(:,:) = 0.0
            endif
        endif
        if(size(MRFInfos) > 0) then
            call calMeshVeloForMRFZone(val_iMesh, val_iMesh%iVelo_Poin)
            call calBelvVeloFromNodeVelo(val_iMesh, val_iMesh%iVelo_Belv)
        endif

        !---------------------------------------------
        CONTAIN_MOVINGBC = _NO
        do i=1,val_iMesh%nMark
            if(val_iMesh%iMarkProp(i,2) == MARK_MOVW) then
                CONTAIN_MOVINGBC = _YES
                exit
            end if
        end do

        if(CONTAIN_MOVINGBC == _YES) then
            call calIVelo_MovingWall(val_iMesh, val_iMesh%iVelo_Belv)
        endif
    
    
        !---------------------------------------------
        if(NUMERICAL_METHOD == _NUMERICAL_MESHLESS) then
            call allocateArray(val_iMesh%iCofR_Sate, val_iMesh%nSate, nDim)
            call allocateArray(val_iMesh%iCofR_Belv, val_iMesh%nBelv, nDim)
            
            call calICoef_Meshless(val_iMesh)
        endif
        
        call revICoef_Exte(val_iMesh)
        
        
        call allocateArray(val_iMesh%iRadu_Poin, val_iMesh%nPoin)
        val_iMesh%iRadu_Poin(:) = 0.0
        call calIRadu_Poin(val_iMesh)
    
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine calICoef_Meshless(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_PreDefine_Flag
        use mod_calMesh_Meshless
        implicit none
        type(typ_Mesh),intent(inout):: val_iMesh
        !
        integer,parameter:: maxSize = 1000
        integer:: i,j
        integer:: nSate,sateID,nBegin
        real(kind=REALLEN):: dCoor(nDim,maxSize)
        real(kind=REALLEN):: iCoef(nDim,maxSize)
        logical:: isOK
        
        !$omp parallel do private(j,nSate,sateID,nBegin,dCoor,iCoef,isOk)
        do i=1,val_iMesh%nPoin
            if(val_iMesh%iHost_Poin(i) /= FLAG_VALID) cycle
            if(val_iMesh%iIner_Poin(i) /= FLAG_INNER) cycle
            
            nSate = val_iMesh%kSate_Poin(i+1) - val_iMesh%kSate_Poin(i)
            
            if(nSate > maxSize) then
                write(ioPort_Out,*) 'nSate > maxSize 1 in mod_calLocalMesh_Geometric.f90'
                stop
            endif
            
            nBegin = val_iMesh%kSate_Poin(i)
            
            do j=1,nSate
                sateID = val_iMesh%iSate_Poin(nBegin+j,1)
                
                dCoor(:,j) = val_iMesh%iCoor_Poin(sateID,:) - &
                             val_iMesh%iCoor_Poin(i     ,:)
                
                iCoef(:,j) = val_iMesh%iCoef_Sate(nBegin+j,:)
            enddo
            
            call getICoef_WLSF(dCoor(1:nDim,1:nSate),   &
                               iCoef(1:nDim,1:nSate),   &
                               nSate , WTYPR_DSQ_INV, isOK)
            
            if(isOK) then
                do j=1,nSate
                    val_iMesh%iCoef_Sate(nBegin+j,:) = iCoef(:,j)
                enddo
            endif
            
            call getICoef_WLSF(dCoor(1:nDim,1:nSate),   &
                               iCoef(1:nDim,1:nSate),   &
                               nSate, WTYPR_ONE, isOK   )
            
            if(isOK) then
                do j=1,nSate
                    val_iMesh%iCofR_Sate(nBegin+j,:) = iCoef(:,j)
                enddo
            else
                do j=1,nSate
                    val_iMesh%iCofR_Sate(nBegin+j,:) = val_iMesh%iCoef_Sate(nBegin+j,:)
                enddo
            endif
        enddo
        !$omp end parallel do
        
    endsubroutine
    !    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine revICoef_Exte(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_PreDefine_Flag
        use mod_calMesh_Meshless
        implicit none
        type(typ_Mesh),intent(inout):: val_iMesh
        !
        integer,parameter:: maxSize = 100
        integer:: i,j
        integer:: poinID,markID,sateID,belvID,nSate
        real(kind=REALLEN):: dCoor(nDim,maxSize),iCoef(nDim,maxSize)
        integer:: iTempFlag(val_iMesh%nPoin)
        logical:: isOK
        
        
        iTempFlag(:) = 0
        !$omp parallel do private(poinID,markID)
        do i=1,val_iMesh%nBelv
            poinID = val_iMesh%iBelvProp(i,1)
            markID = val_iMesh%iBelvProp(i,2)
            
            if(markID <= 0) then
                iTempFlag(poinID) = 1
            endif
        enddo
        !$omp end parallel do


        !$omp parallel do private(j,nSate,sateID,dCoor,iCoef,belvID,isOk)
        do i=1,val_iMesh%nPoin
            if(iTempFlag(i) /= 1) cycle
            
            nSate = 0
            do j=val_iMesh%kSate_Poin(i)+1,val_iMesh%kSate_Poin(i+1)
                sateID = val_iMesh%iSate_Poin(j,1)
                if(val_iMesh%iHost_Poin(sateID) /= FLAG_VALID) cycle
                
                nSate = nSate + 1
                dCoor(:,nSate) = val_iMesh%iCoor_Poin(sateID,:) - &
                                 val_iMesh%iCoor_Poin(i     ,:)
            enddo
            
            do j=val_iMesh%kBelv_Poin(i)+1,val_iMesh%kBelv_Poin(i+1) 
                belvID = val_iMesh%iBelv_Poin(j,1)
                
                nSate = nSate + 1
                dCoor(:,nSate) = val_iMesh%iCoor_Belv(belvID,:) - &
                                 val_iMesh%iCoor_Poin(i     ,:)
            enddo
            
            if(nSate > maxSize) then
                write(ioPort_Out,*) 'nSate > maxSize 2 in mod_calLocalMesh_Geometric.f90'
                stop
            endif
            
            call getICoef_WLSF(dCoor(1:nDim,1:nSate),   &
                               iCoef(1:nDim,1:nSate),   &
                               nSate, WTYPR_DSQ_INV, isOK)
            
            if(isOK) then
            nSate = 0
            do j=val_iMesh%kSate_Poin(i)+1,val_iMesh%kSate_Poin(i+1)
                sateID = val_iMesh%iSate_Poin(j,1)
                if(val_iMesh%iHost_Poin(sateID) /= FLAG_VALID) cycle
                
                nSate = nSate + 1
                val_iMesh%iCoef_Sate(j,:) = iCoef(:,nSate)
            enddo
            
            do j=val_iMesh%kBelv_Poin(i)+1,val_iMesh%kBelv_Poin(i+1)
                belvID = val_iMesh%iBelv_Poin(j,1)
                
                nSate = nSate + 1
                val_iMesh%iCoef_Belv(belvID,:) = iCoef(:,nSate)
            enddo
            endif
            
            dCoor(:,:) = 0.0
            iCoef(:,:) = 0.0
        enddo
        !$omp end parallel do
        
    endsubroutine
    !    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine calIRadu_Poin(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_PreDefine_Flag
        use mod_calMesh_Meshless
        implicit none
        type(typ_Mesh),intent(inout):: val_iMesh
        !
        integer:: i,j,k
        integer:: sateID
        real*8:: iLen,jLen,dCoor
        

        !$omp parallel do private(j,k,sateID,iLen,jLen,dCoor)
        do i=1,val_iMesh%nPoin
            iLen = 0.0
            do j=val_iMesh%kSate_Poin(i)+1,val_iMesh%kSate_Poin(i+1)
                sateID = val_iMesh%iSate_Poin(j,1)
                
                jLen = 0.0
                do k=1,nDim
                    dCoor = val_iMesh%iCoor_Poin(i,k) - val_iMesh%iCoor_Poin(sateID,k)
                    jLen = jLen + dCoor*dCoor
                enddo
                
                iLen = max(iLen, jLen)
            enddo
            
            val_iMesh%iRadu_Poin(i) = sqrt(iLen)
        enddo
        !$omp end parallel do
        
        !if(nDim == 2) then
        !    do i=1,val_iMesh%nPoin
        !        val_iMesh%iRadu_Poin(i)= sqrt(val_iMesh%iVolu_Poin(i))
        !    enddo
        !elseif(nDim == 3) then
        !    do i=1,val_iMesh%nPoin
        !        val_iMesh%iRadu_Poin(i)= (val_iMesh%iVolu_Poin(i))**(1.0/3.0)
        !    enddo
        !endif
        
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine calIVelo_MovingWall(val_iMesh, val_iVelo)
        use mod_WorkPath
        use mod_Config, only: MarkInfos
        use mod_TypeDef_Mesh
        implicit none
        type(typ_Mesh),intent(in):: val_iMesh
        real(kind=REALLEN),intent(out):: val_iVelo(val_iMesh%nBelv,nDim)
        !
        integer:: i,j,pID,mID,mKD
        real*8:: Ri(nDim),Xi(nDim),veloFi(nDim),omgaFi

        val_iVelo(:,:) = 0.0

        write(*,"(A,100I6)") 'markKD :',val_iMesh%iMarkProp(:, 2)

        open(9999,file=trim(workPath)//'/111.dat')
        write(9999,*) 'VARIABLES =  X Y Vx Vy'
        do i=1,val_iMesh%nBelv
            pID = val_iMesh%iBelvProp(i,1)
            mID = val_iMesh%iBelvProp(i,2)

            mKD = val_iMesh%iMarkProp(mID,2)

            veloFi(1:nDim) = 0.0

            if(mKD == MARK_MOVW) then
                Ri(1:nDim) = val_iMesh%iCoor_Poin(pID,1:nDim) - MarkInfos(mID)%rotatOrgn(1:nDim)

                if(nDim == 2) then
                    veloFi(1) = -MarkInfos(mID)%rotatOmga*Ri(2)
                    veloFi(2) =  MarkInfos(mID)%rotatOmga*Ri(1)
                elseif(nDim == 3) then
                    Xi(1:nDim) = MarkInfos(mID)%rotatAxis(1:nDim)
                    omgaFi = MarkInfos(mID)%rotatOmga
                    call getRotatVelo3D(Ri, Xi, omgaFi, veloFi)
                end if

                veloFi(1:nDim) = veloFi(1:nDim) + MarkInfos(mID)%transVelo(1:nDim)
                write(9999,*) val_iMesh%iCoor_Poin(pID,1:nDim),veloFi(:)
            end if

            val_iVelo(i,1:nDim) = veloFi(1:nDim)
        end do

    end subroutine calIVelo_MovingWall
    !
    subroutine calBelvVeloFromNodeVelo(val_iMesh, val_iVelo)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_Mesh),intent(in):: val_iMesh
        real(kind=REALLEN),intent(out):: val_iVelo(val_iMesh%nBelv,nDim)
        !
        integer:: i,pID

        if(size(val_iMesh%iVelo_Poin,1) == 0) then
            return
        end if

        do i=1,val_iMesh%nBelv
            pID = val_iMesh%iBelvProp(i,1)

            val_iVelo(i,:) = val_iMesh%iVelo_Poin(pID,:)
        end do

    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine calMeshVeloForMRFZone(val_iMesh, val_iVelo)
        use mod_WorkPath
        use mod_Config, only: MRFInfos
        use mod_TypeDef_Mesh
        implicit none
        type(typ_Mesh),intent(in):: val_iMesh
        real(kind=REALLEN),intent(out):: val_iVelo(val_iMesh%nPoin,nDim)
        !
        integer:: i,j,k,zID,pID,mID
        integer:: pFlag(val_iMesh%nPoin)
        real*8 :: dCoor(nDim),iRadu,iXLen,iAxis(nDim),ilen,jlen
        real*8 :: iOmga, veloFi(nDim)


        pFlag(:) = 0
        write(*,*) "size(MRFInfos) = ",size(MRFInfos)
        do i=1,size(MRFInfos)
            iAxis(1:nDIm) = MRFInfos(i)%rotatAxis(1:nDim)
            iRadu = MRFInfos(i)%mrfRadu
            iXLen = MRFInfos(i)%mrfXLen

            if(MRFInfos(i)%zoneID >0) then
                zID = MRFInfos(i)%zoneID
                do j=1,val_iMesh%nElem
                    if(val_iMesh%iElemProp(j,1) /= zID) cycle
                    do k=1,4*nDim-4
                        pID = val_iMesh%iElem(j,k)
                        pFlag(pID) = i
                    enddo
                enddo
            else
                if(iRadu <= 1.0E-5) then
                    write(*,*) 'Wrong Radu for MRF zone. too small'
                    stop
                end if
                if(abs(iXLen) <= 1.0E-5) then
                    write(*,*) 'Wrong Lens for MRF zone. too small'
                    stop
                end if

                do j=1,val_iMesh%nPoin
                    dCoor(:) = val_iMesh%iCoor_Poin(j,:) - MRFInfos(i)%rotatOrgn(1:nDim)

                    if(nDim==2) then
                        if(dCoor(1)**2+dCoor(2)**2 <= iRadu**2) then
                            if(pFlag(j) /= 0) then
                                write(*,*) 'warning: overlapping of mrf zone'
                            endif
                            pFlag(j) = i
                        endif
                    elseif(nDim == 3) then
                        ilen = (dCoor(1)*iAxis(1) + dCoor(2)*iAxis(2) +dCoor(nDim)*iAxis(nDim))/iXLen
                        jlen = sqrt(dCoor(1)**2+dCoor(2)**2+dCoor(nDim)**2-ilen**2)/iRadu

                        if(ilen >= 0 .and. ilen <= 1.0 .and. jlen <= 1.0) then
                            if(pFlag(j) /= 0) then
                                write(*,*) 'warning: overlapping of mrf zone'
                            endif
                            pFlag(j) = i
                        endif
                    endif
                enddo
            endif
        enddo


        val_iVelo(:,:) = 0.0
        do i=1,val_iMesh%nPoin
            mID = pFlag(i)
            if(mID == 0) cycle

            dCoor(:) = val_iMesh%iCoor_Poin(i,:) - MRFInfos(mID)%rotatOrgn(1:nDim)
            iOmga = MRFInfos(mID)%rotatOmga


            if(nDim == 2) then
                veloFi(1) = -iOmga*dCoor(2)
                veloFi(2) =  iOmga*dCoor(1)
            elseif(nDim == 3) then
                iAxis(1:nDim) = MRFInfos(mID)%rotatAxis(1:nDim)
                call getRotatVelo3D(dCoor, iAxis, iOmga, veloFi)
            end if

            val_iVelo(i,:) = veloFi(1:nDim) + MRFInfos(mID)%transVelo(1:nDim)
        end do

    end subroutine calMeshVeloForMRFZone
    !
    !
    subroutine getRotatVelo3D(CoorPi,Axis,Omega,VeloPi)
        implicit none
        real(kind=8):: CoorPi(3)
        real(kind=8):: Axis(3)
        real(kind=8):: Omega
        real(kind=8):: VeloPi(3)
        !
        real*8:: R1(3),R2(3),R3(3),Ri

        Ri = sqrt(CoorPi(1)**2+CoorPi(2)**2+CoorPi(3)**2)
        if(Ri*omega < 1.0E-5) then
            veloPi(1:3) = 0.0
        else
            R1(1:3) = Axis(1:3)

            Ri = CoorPi(1)*Axis(1) + CoorPi(2)*Axis(2) + CoorPi(3)*Axis(3)
            R2(1:3) = CoorPi(1:3) - Ri*Axis(1:3)

            R3(1) = R1(2)*R2(3) - R2(2)*R1(3)
            R3(2) = R1(3)*R2(1) - R2(3)*R1(1)
            R3(3) = R1(1)*R2(2) - R2(1)*R1(2)

            Ri = sqrt(R2(1)**2+R2(2)**2+R2(3)**2)/sqrt(R3(1)**2+R3(2)**2+R3(3)**2)
            R3(1:3) = R3(1:3)*Ri

            veloPi(1:3) = R3(1:3)*Omega
        endif

    end subroutine getRotatVelo3D
    !
endmodule
!
    
    