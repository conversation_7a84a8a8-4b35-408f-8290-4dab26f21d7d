
!-------------------------------------
!
!-------------------------------------
subroutine calIEdge(val_iMesh)
    use mod_TypeDef_Mesh
    use mod_PreDefine_Dimension
    use mod_ElemProp
    use mod_calMeshTopo_Func
    implicit none
    type(typ_GlobalMesh),intent(inout):: val_iMesh
    !
    integer:: i,j,k
    integer:: eL,kL,eR,kR,ick,iek,iCounter,edgeID
    integer:: icci(4*nDim-4),ieei(2*nDim-2)
    
    
    !---------------------------------------------
    iCounter = 0
    do i=1,val_iMesh%nElem
        ick = val_imesh%iElemKind(i)
        do j=1,elemProp_edgeNum(ick)
            eR = val_iMesh%iElemLink(i,j,1)
            
            if((eR < 0).or.(eR > i)) then
                iCounter = iCounter + 1
            endif
        enddo
    enddo
    
    
    !---------------------------------------------
    val_iMesh%nEdge = iCounter
    
    allocate(val_iMesh%iEdge(2*nDim+2 , val_iMesh%nEdge))
    allocate(val_iMesh%iEdgeKind(val_iMesh%nEdge))
    val_iMesh%iEdge(:,:) = 0
    val_iMesh%iEdgeKind(:) = 0
    
    
    !---------------------------------------------
    iCounter = 0
    do i=1,val_iMesh%nElem
        ick = val_imesh%iElemKind(i)
        icci(:) = val_iMesh%iElem(:,i)
        
        do j=1,elemProp_edgeNum(ick)
            eR = val_iMesh%iElemLink(i,j,1)
            kR = val_iMesh%iElemLink(i,j,2)
            
            if((er < 0).or.(er > i)) then
                iCounter = iCounter + 1
                
                call getEdgeOfElem(ick , 4*nDim-4 , icci , j , &
                                   iek , 2*nDim-2 , ieei )
                
                val_iMesh%iEdge(1:2*nDim-2 , icounter) = ieei(:)
                val_iMesh%iEdge(  2*nDim-1 , icounter) = i
                val_iMesh%iEdge(  2*nDim   , icounter) = j
                val_iMesh%iEdge(  2*nDim+1 , icounter) = er
                val_iMesh%iEdge(  2*nDim+2 , icounter) = kr
                
                val_iMesh%iEdgeKind(icounter) = iek
                
                val_iMesh%iElemLink(i,j,3) = iCounter
            else
                val_iMesh%iElemLink(i,j,3) = val_iMesh%iElemLink(eR,kR,3)
            endif
        enddo
    enddo
    
    
endsubroutine
!
