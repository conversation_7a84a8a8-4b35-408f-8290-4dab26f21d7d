
!---------------------------------------------
!
!---------------------------------------------
module mod_SoluEuler
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    use mod_PreDefine_Flag
    use mod_PreDefine_Mark
    use mod_Interface_AllocateArray
    use mod_GPUThreadDim
    use mod_Options
    use cudafor
    implicit none
contains
    !
    subroutine updFieldWithBC_Euler(val_iParm,val_iMesh,val_iSolu)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_Euler_Def
        use mod_Euler_GPUFunc
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        !
        integer:: i,istat

        call bldBC_Global<<<nGridDim(IP_BB), nBlockDim>>>                               &
                (val_iMesh%iCalc_Poin_d, val_iMesh%iBelvProp_d, val_iMesh%iMarkProp_d,  &
                val_iMesh%iWDis_Poin_d, val_iMesh%iCoor_Poin_d, val_iMesh%iNvor_Belv_d, &
                val_iMesh%iVelo_Poin_d, val_iMesh%iVelo_Belv_d,                         &
                val_iSolu%soluvar_d, val_iSolu%primvar_d, val_iSolu%soluBelv_d,         &
                val_iSolu%primBelv_d, val_iSolu%soluMark_d, val_iSolu%primMark_d,       &
                val_iParm%iGPUSParm, val_iParm%isWithPrec, val_iMesh%nBelv,             &
                val_iMesh%nMark, val_iMesh%nPoin, val_iMesh%nBelv, val_iParm%iIter      )

    endsubroutine
    !
    subroutine getMarkSoluAndPrim_Euler(val_iParm,val_iSolu,val_markInfo,val_iStep,val_soluMi,val_primMi)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_Euler_Def
        use mod_Config, only: typ_MarkInfo
        use mod_parmDimLess
        use mod_GetSoluParm, only: getSoluParm_PhysicalTime
        use mod_Euler_GPUFunc, only: getPrimBySolu_Euler
        implicit none
        type(typ_Parm    ),intent(in ):: val_iParm
        type(typ_Solu    ),intent(in ):: val_iSolu
        type(typ_MarkInfo),intent(in ):: val_markInfo
        integer           ,intent(in ):: val_iStep
        real(kind=REALLEN),intent(out):: val_soluMi(nDim+2)
        real(kind=REALLEN),intent(out):: val_primMi(nDim+2)
        !
        integer:: i
        real(kind=REALLEN):: dEi,vEi(nDim),qEi,iTime

        call getSoluParm_PhysicalTime(val_iParm, val_iStep, iTime)



        val_soluMi(:) = 0.0
        val_primMi(:) = 0.0

        if(val_markInfo%markKD == MARK_WALL) then
            !do nothing
        elseif(val_markInfo%markKD == MARK_EQTW) then
            val_soluMi(1     ) = val_markInfo%surfTemp

        elseif(val_markInfo%markKD == MARK_EULW) then
            !do nothing
        elseif(val_markInfo%markKD == MARK_MOVW) then
            val_soluMi(1     :nDim  ) = val_markInfo%transVelo(1:nDim)
            val_soluMi(nDim+1       ) = val_markInfo%rotatOmga
            val_primMi(1     :nDim  ) = val_markInfo%rotatOrgn(1:nDim)
            val_primMi(nDim+1:nDim+2) = val_markInfo%rotatAxis(1:2   )
            val_soluMi(nDim+2       ) = val_markInfo%rotatAxis(nDim  ) !used for 3D

        elseif(val_markInfo%markKD == MARK_INLE) then
            dEi = val_markInfo%surfPres/(val_markInfo%surfTemp*val_iParm%iHostParm(IP_GASCO))
            vEi(1:nDim) = val_markInfo%surfVelo*val_markInfo%surfNorm(1:nDim)
            qEi = 0.5*dEi*(vEi(1)**2+vEi(2)**2+vEi(nDim)**2*(nDim-2))

            val_soluMi(1       ) = dEi
            val_soluMi(2:nDim+1) = dEi*vEi(1:nDim)
            val_soluMi(nDim+2  ) = val_markInfo%surfPres/(val_iParm%iHostParm(IP_GAMMA) - 1) + qEi

            call getPrimBySolu_Euler(               &
                    val_soluMi, val_primMi,         &
                    val_iParm%iHostParm(IP_GAMMA),   &
                    val_iParm%iHostParm(IP_GASCO),   &
                    val_iParm%iHostParm(IP_LIMMINP), &
                    val_iParm%iHostParm(IP_LIMMAXP)  )

        elseif(val_markInfo%markKD == MARK_INLM) then
            dEi = val_markInfo%surfPres/(val_markInfo%surfTemp*val_iParm%iHostParm(IP_GASCO))
            vEi(1:nDim) = val_markInfo%surfMass/(dEi*val_markInfo%surfArea)*val_markInfo%surfNorm(1:nDim)
            qEi = 0.5*dEi*(vEi(1)**2+vEi(2)**2+vEi(nDim)**2*(nDim-2))

            val_soluMi(1       ) = dEi
            val_soluMi(2:nDim+1) = dEi*vEi(1:nDim)
            val_soluMi(nDim+2  ) = val_markInfo%surfPres/(val_iParm%iHostParm(IP_GAMMA) - 1) + qEi

            call getPrimBySolu_Euler(               &
                    val_soluMi, val_primMi,         &
                    val_iParm%iHostParm(IP_GAMMA),   &
                    val_iParm%iHostParm(IP_GASCO),   &
                    val_iParm%iHostParm(IP_LIMMINP), &
                    val_iParm%iHostParm(IP_LIMMAXP)  )

        elseif(val_markInfo%markKD == MARK_OUTL) then
            !do nothing
        elseif(val_markInfo%markKD == MARK_OUTP) then
            val_soluMi(1) = val_markInfo%surfPres * (val_markInfo%BackPresFact &
                    + val_markInfo%BackPrecIncr*max(0,val_iStep - 1) )

            val_soluMi(2) = val_markInfo%surfTtot / DLRef_temperature
            val_soluMi(3) = val_markInfo%surfPtot / DLRef_pressure

        elseif(val_markInfo%markKD == MARK_OUTM) then
            !do nothing

        elseif(val_markInfo%markKD == MARK_PFAR) then
            val_soluMi(:) = val_iSolu%soluInfty(:)
            val_primMi(:) = val_iSolu%primInfty(:)
        else
            val_soluMi(:) = val_iSolu%soluInfty(:)
            val_primMi(:) = val_iSolu%primInfty(:)
        end if

    endsubroutine
    !
    subroutine updFlowInftyInSolu_Euler(val_iParm, val_iSolu)
        use mod_FreeStream
        use mod_TypeDef_Parm
        use mod_TypeDef_Solu
        implicit none
        type(typ_Parm),intent(in   ):: val_iParm
        type(typ_Solu),intent(inout):: val_iSolu
        !
        integer:: i
        real(kind=REALLEN):: sq_vel,gammo

        gammo = val_iParm%iHostParm(IP_GAMMO)

        sq_vel = 0.0
        do i=1,nDim
            sq_vel = sq_vel + flowInfty_Velocity(i)**2
        enddo

        val_iSolu%soluInfty(       1) = flowInfty_Density
        val_iSolu%soluInfty(2:nDim+1) = flowInfty_Density*flowInfty_Velocity(:)
        val_iSolu%soluInfty(  nDim+2) = flowInfty_Pressure/gammo + 0.50*flowInfty_Density*sq_vel

        val_iSolu%primInfty(       1) = flowInfty_Pressure
        val_iSolu%primInfty(2:nDim+1) = flowInfty_Velocity(:)
        val_iSolu%primInfty(  nDim+2) = flowInfty_Temperature

    endsubroutine
    !
    subroutine RunOneIteration_UPW_GPU_Euler(val_iParm,val_iMesh,val_iSolu)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_Config, only: TIME_DISCRE_FLOW, UPWIND_SCHEME, NITER_O1TUNING, UPWIND_LIMITER, UPWIND_ORDER
        use mod_Operation_GPUArrayCpy
        use mod_Euler_GPUFunc
        use mod_Unsteady, only: calDualTerm_Global
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        !
        integer:: i, j, iIter, iClor, iStat, iRK
        logical:: is2ndOrder,relFlag

        is2ndOrder = (val_iParm%iIter > NITER_O1TUNING).and.(UPWIND_ORDER == 2)


        !---------------------------------------------
        !隐式LU-SGS迭代
        if(TIME_DISCRE_FLOW == _MCLUSGS_IMPLICIT) then
            call GPURealArrayCpy2D1_Global<<<nGridDim(IP_BP), nBlockDim>>>              &
                    (val_iSolu%soluvar_d, val_iSolu%soluOld_d,val_iMesh%nPoin, nDim+2  )

            !do i=1,3
            !    call GPURealArrayCpy1D1_Global<<<nGridDim(IP_BP), nBlockDim>>>          &
            !            (val_iSolu%DTCoef_d, val_iSolu%DTCoef2_d,val_iMesh%nPoin        )
            !
            !    call tuningDTCoef_Global<<<nGridDim(IP_BP), nBlockDim>>>                        &
            !            (val_iMesh%iCalc_Poin_d, val_iMesh%kSate_Poin_d, val_iMesh%iSate_Poin_d,&
            !            val_iSolu%DTCoef2_d, val_iSolu%DTCoef_d, val_iMesh%nPoin,               &
            !            val_iMesh%nSate,val_iMesh%nPoin                                         )
            !end do

            if(val_iParm%isWithPrec ) then
                call calPrecCoefMatx_Global<<<nGridDim(IP_BP), nBlockDim>>>             &
                        (val_iMesh%iCalc_Poin_d,val_iSolu%soluvar_d,val_iSolu%primvar_d,&
                        val_iSolu%preCoef_d, val_iSolu%pMatrix_d, val_iParm%iGPUSParm,  &
                        val_iMesh%nPoin, val_iMesh%nPoin, val_iParm%isWithPrec          )
            end if

            if(val_iParm%isUnstDual) then
                call calDualTerm_Global<<<nGridDim(IP_BP), nBlockDim>>>                 &
                        (val_iMesh%iCalc_Poin_d,val_iSolu%soluvar_d,val_iSolu%soluTimN_d,&
                        val_iSolu%soluTimX_d, val_iSolu%UnstTerm_d, val_iParm%iGPUSParm,&
                        val_iMesh%nPoin, nDim+2, val_iMesh%nPoin                       )
            endif

            call calSpecRaduAndDeltTime_Global<<<nGridDim(IP_BP), nBlockDim>>>              &
                    (val_iMesh%iCalc_Poin_d, val_iMesh%kSate_Poin_d, val_iMesh%iSate_Poin_d,&
                    val_iMesh%iCoef_Sate_d, val_iMesh%kBelv_Poin_d, val_iMesh%iBelv_Poin_d, &
                    val_iMesh%iCoef_Belv_d, val_iMesh%iVelo_Poin_d, val_iMesh%iVelo_Belv_d, &
                    val_iSolu%primvar_d, val_iSolu%primBelv_d, val_iSolu%SRconv_d,          &
                    val_iSolu%SRcNode_d, val_iSolu%SRcBelv_d, val_iSolu%DT_d,               &
                    val_iSolu%DTCoef_d, val_iParm%iGPUSParm, val_iMesh%nPoin,               &
                    val_iMesh%nSate, val_iMesh%nBelv, val_iMesh%nPoin,                      &
                    val_iParm%isWithPrec, val_iSolu%preCoef_d, val_iParm%isUnsteady         )

            call calSoluGradWithLimt_Global<<<nGridDim(IP_BP),nBlockDim>>>                  &
                    (val_iMesh%iCalc_Poin_d, val_iMesh%iBelvProp_d,val_iMesh%iMarkProp_d,   &
                    val_iMesh%kSate_Poin_d,val_iMesh%iSate_Poin_d,val_iMesh%iCoef_Sate_d,   &
                    val_iMesh%iCoor_Poin_d,val_iMesh%iRadu_Poin_d,val_iMesh%kBelv_Poin_d,   &
                    val_iMesh%iBelv_Poin_d,val_iMesh%iCoef_Belv_d,val_iSolu%soluvar_d,      &
                    val_iSolu%primvar_d,val_iSolu%soluBelv_d,val_iSolu%primBelv_d,          &
                    val_iSolu%gradSolu_d,val_iSolu%limiter_d, val_iSolu%soluUCRef_d,        &
                    val_iParm%iGPUSParm,val_iMesh%nPoin,val_iMesh%nSate, val_iMesh%nBelv,   &
                    val_iMesh%nMark,val_iMesh%nPoin, UPWIND_LIMITER                         )

            call calFluxConvViscSour_UPW_Global<<<nGridDim(IP_BP),nBlockDim>>>              &
                    (val_iMesh%iCalc_Poin_d, val_iMesh%iIner_Poin_d, val_iMesh%kSate_Poin_d,&
                    val_iMesh%iSate_Poin_d, val_iMesh%iCoef_Sate_d, val_iMesh%iCoor_Poin_d, &
                    val_iMesh%kBelv_Poin_d, val_iMesh%iBelv_Poin_d, val_iMesh%iCoef_Belv_d, &
                    val_iMesh%iVelo_Poin_d, val_iMesh%iVelo_Belv_d, val_iSolu%soluvar_d,    &
                    val_iSolu%primvar_d, val_iSolu%soluBelv_d, val_iSolu%primBelv_d,        &
                    val_iSolu%gradSolu_d, val_iSolu%limiter_d, val_iSolu%pMatrix_d,         &
                    val_iSolu%UnstTerm_d, val_iSolu%Res_All_d, val_iparm%iGPUSParm,         &
                    val_iMesh%iMarkProp_d, val_iMesh%nPoin, val_iMesh%nSate,                &
                    val_iMesh%nBelv, val_iMesh%nMark, val_iMesh%nPoin,                      &
                    is2ndOrder, UPWIND_SCHEME, val_iParm%isWithPrec, val_iParm%isUnstDual   )

            do iClor=1,val_iMesh%nClor
            call loopLUSGS_Global<<<nGridDim(IP_BP),nBlockDim>>>                            &
                    (val_iMesh%iCalc_Poin_d, val_iMesh%iClor_Poin_d, val_iMesh%kSate_Poin_d,&
                    val_iMesh%iSate_Poin_d, val_iMesh%iCoef_Sate_d, val_iMesh%iVelo_Poin_d, &
                    val_iSolu%soluvar_d, val_iSolu%deltSolu_d, val_iSolu%SRconv_d,          &
                    val_iSolu%SRcNode_d, val_iSolu%Res_All_d, val_iSolu%DT_d,               &
                    val_iParm%iGPUSParm, iClor, Dir_Forward, val_iMesh%nPoin,               &
                    val_iMesh%nSate, val_iMesh%nPoin                                        )

            istat = cudaDeviceSynchronize()
            end do

            do iClor=val_iMesh%nClor,1,-1
            call loopLUSGS_Global<<<nGridDim(IP_BP),nBlockDim>>>                            &
                    (val_iMesh%iCalc_Poin_d, val_iMesh%iClor_Poin_d, val_iMesh%kSate_Poin_d,&
                    val_iMesh%iSate_Poin_d, val_iMesh%iCoef_Sate_d, val_iMesh%iVelo_Poin_d, &
                    val_iSolu%soluvar_d, val_iSolu%deltSolu_d, val_iSolu%SRconv_d,          &
                    val_iSolu%SRcNode_d, val_iSolu%Res_All_d, val_iSolu%DT_d,               &
                    val_iParm%iGPUSParm, iClor, Dir_Backward, val_iMesh%nPoin,              &
                    val_iMesh%nSate, val_iMesh%nPoin                                        )

            istat = cudaDeviceSynchronize()
            end do

            istat = cudaDeviceSynchronize()

            call updSoluAndDTCoef_Global<<<nGridDim(IP_BP),nBlockDim>>>                 &
                    (val_iMesh%iCalc_Poin_d, val_iSolu%soluvar_d, val_iSolu%primvar_d,  &
                    val_iSolu%soluOld_d, val_iSolu%deltSolu_d, val_iSolu%DTCoef_d,      &
                    val_iParm%iGPUSParm, val_iMesh%nPoin, val_iMesh%nPoin               )

            call bldBC_Global<<<nGridDim(IP_BB), nBlockDim>>>                               &
                    (val_iMesh%iCalc_Poin_d, val_iMesh%iBelvProp_d, val_iMesh%iMarkProp_d,  &
                    val_iMesh%iWDis_Poin_d, val_iMesh%iCoor_Poin_d, val_iMesh%iNvor_Belv_d, &
                    val_iMesh%iVelo_Poin_d, val_iMesh%iVelo_Belv_d, val_iSolu%soluvar_d,    &
                    val_iSolu%primvar_d, val_iSolu%soluBelv_d, val_iSolu%primBelv_d,        &
                    val_iSolu%soluMark_d, val_iSolu%primMark_d, val_iParm%iGPUSParm,        &
                    val_iParm%isWithPrec, val_iMesh%nBelv, val_iMesh%nMark,                 &
                    val_iMesh%nPoin, val_iMesh%nBelv, val_iParm%iIter                       )

        else if(TIME_DISCRE_FLOW == _RUNGE_KUTTA_EXPLICIT) then
            call GPURealArrayCpy2D1_Global<<<nGridDim(IP_BP),nBlockDim>>>               &
                    (val_iSolu%soluvar_d, val_iSolu%soluOld_d,val_iMesh%nPoin, nDim+2  )

            if(val_iParm%isWithPrec ) then
                call calPrecCoefMatx_Global<<<nGridDim(IP_BP), nBlockDim>>>             &
                        (val_iMesh%iCalc_Poin_d,val_iSolu%soluvar_d,val_iSolu%primvar_d,&
                        val_iSolu%preCoef_d, val_iSolu%pMatrix_d, val_iParm%iGPUSParm,  &
                        val_iMesh%nPoin, val_iMesh%nPoin, val_iParm%isWithPrec          )
            end if

            if(val_iParm%isUnstDual) then
                call calDualTerm_Global<<<nGridDim(IP_BP), nBlockDim>>>                 &
                        (val_iMesh%iCalc_Poin_d,val_iSolu%soluvar_d,val_iSolu%soluTimN_d,&
                        val_iSolu%soluTimX_d, val_iSolu%UnstTerm_d, val_iParm%iGPUSParm,&
                        val_iMesh%nPoin, nDim+2, val_iMesh%nPoin                       )
            endif

            do iRK=1,4
                if(iRK == 1) then
                    call calSpecRaduAndDeltTime_Global<<<nGridDim(IP_BP), nBlockDim>>>              &
                            (val_iMesh%iCalc_Poin_d, val_iMesh%kSate_Poin_d, val_iMesh%iSate_Poin_d,&
                            val_iMesh%iCoef_Sate_d, val_iMesh%kBelv_Poin_d, val_iMesh%iBelv_Poin_d, &
                            val_iMesh%iCoef_Belv_d, val_iMesh%iVelo_Poin_d, val_iMesh%iVelo_Belv_d, &
                            val_iSolu%primvar_d, val_iSolu%primBelv_d, val_iSolu%SRconv_d,          &
                            val_iSolu%SRcNode_d, val_iSolu%SRcBelv_d, val_iSolu%DT_d,               &
                            val_iSolu%DTCoef_d, val_iParm%iGPUSParm, val_iMesh%nPoin,               &
                            val_iMesh%nSate, val_iMesh%nBelv,val_iMesh%nPoin,                       &
                            val_iParm%isWithPrec, val_iSolu%preCoef_d, val_iParm%isUnstDual         )

                    call calSoluGradWithLimt_Global<<<nGridDim(IP_BP),nBlockDim>>>                  &
                            (val_iMesh%iCalc_Poin_d, val_iMesh%iBelvProp_d,val_iMesh%iMarkProp_d,   &
                            val_iMesh%kSate_Poin_d,val_iMesh%iSate_Poin_d,val_iMesh%iCoef_Sate_d,   &
                            val_iMesh%iCoor_Poin_d,val_iMesh%iRadu_Poin_d,val_iMesh%kBelv_Poin_d,   &
                            val_iMesh%iBelv_Poin_d,val_iMesh%iCoef_Belv_d,val_iSolu%soluvar_d,      &
                            val_iSolu%primvar_d,val_iSolu%soluBelv_d,val_iSolu%primBelv_d,          &
                            val_iSolu%gradSolu_d,val_iSolu%limiter_d, val_iSolu%soluUCRef_d,        &
                            val_iParm%iGPUSParm,val_iMesh%nPoin,val_iMesh%nSate, val_iMesh%nBelv,   &
                            val_iMesh%nMark,val_iMesh%nPoin, UPWIND_LIMITER                         )
                endif

                call calFluxConvViscSour_UPW_Global<<<nGridDim(IP_BP),nBlockDim>>>              &
                        (val_iMesh%iCalc_Poin_d, val_iMesh%iIner_Poin_d, val_iMesh%kSate_Poin_d,&
                        val_iMesh%iSate_Poin_d, val_iMesh%iCoef_Sate_d, val_iMesh%iCoor_Poin_d, &
                        val_iMesh%kBelv_Poin_d, val_iMesh%iBelv_Poin_d, val_iMesh%iCoef_Belv_d, &
                        val_iMesh%iVelo_Poin_d, val_iMesh%iVelo_Belv_d, val_iSolu%soluvar_d,    &
                        val_iSolu%primvar_d, val_iSolu%soluBelv_d, val_iSolu%primBelv_d,        &
                        val_iSolu%gradSolu_d, val_iSolu%limiter_d, val_iSolu%pMatrix_d,         &
                        val_iSolu%UnstTerm_d, val_iSolu%Res_All_d, val_iparm%iGPUSParm,         &
                        val_iMesh%iMarkProp_d, val_iMesh%nPoin, val_iMesh%nSate,                &
                        val_iMesh%nBelv, val_iMesh%nMark, val_iMesh%nPoin,                      &
                        is2ndOrder, UPWIND_SCHEME, val_iParm%isWithPrec, val_iParm%isUnsteady   )

                call updSolu_RK_Global<<<nGridDim(IP_BP),nBlockDim>>>                       &
                        (val_iMesh%iCalc_Poin_d, val_iSolu%soluOld_d, val_iSolu%soluvar_d,  &
                        val_iSolu%primvar_d,  val_iSolu%Res_All_d, val_iSolu%DT_d,          &
                        val_iSolu%DTCoef_d, val_iParm%iGPUSParm, val_iMesh%nPoin,           &
                        val_iMesh%nPoin, iRK                                                )

                call bldBC_Global<<<nGridDim(IP_BB), nBlockDim>>>                               &
                        (val_iMesh%iCalc_Poin_d, val_iMesh%iBelvProp_d, val_iMesh%iMarkProp_d,  &
                        val_iMesh%iWDis_Poin_d, val_iMesh%iCoor_Poin_d, val_iMesh%iNvor_Belv_d, &
                        val_iMesh%iVelo_Poin_d, val_iMesh%iVelo_Belv_d, val_iSolu%soluvar_d,    &
                        val_iSolu%primvar_d, val_iSolu%soluBelv_d, val_iSolu%primBelv_d,        &
                        val_iSolu%soluMark_d, val_iSolu%primMark_d, val_iParm%iGPUSParm,        &
                        val_iParm%isWithPrec, val_iMesh%nBelv, val_iMesh%nMark,                 &
                        val_iMesh%nPoin, val_iMesh%nBelv, val_iParm%iIter                       )

                istat = cudaDeviceSynchronize()
            end do
        end if

    endsubroutine
    !
endmodule
!
