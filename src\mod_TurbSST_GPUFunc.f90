module mod_TurbSST_GPUFunc
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    use mod_PreDefine_Flag
    use mod_PreDefine_Mark
    use mod_Options
    use mod_TypeDef_Parm
    use mod_ParmIndex
    use mod_TurbSST_ParmIndx
    use cudafor
    implicit none
    !
contains
    !
    attributes(host,device) subroutine getPrimBySolu_TurbSST(soluEi,primEi,Gammo,GasCO,T0,Ts,Mu0,A1,pMin,pMax,mutLim,Omega,Sigma,F2)
        implicit none
        real(kind=REALLEN):: soluEi(nDim+4)
        real(kind=REALLEN):: primEi(nDim+4)
        real(kind=REALLEN):: Gammo,GasCO,T0,Ts,Mu0,A1
        real(kind=REALLEN):: pMin,pMax,mutLim,Omega,Sigma,F2
        !
        real(kind=REALLEN):: dInv,qEi,pEi,TEi,xhi,mulEi,mutEi

        dInv = 1.0/soluEi(1)

        primEi(     2) = soluEi(     2)*dInv
        primEi(     3) = soluEi(     3)*dInv
        primEi(nDim+1) = soluEi(nDim+1)*dInv

        qEi   = primEi(2)*soluEi(2) + primEi(3)*soluEi(3) + primEi(4)*soluEi(4)*(nDim-2)

        pEi   = Gammo * (soluEi(nDim+2) - 0.5*qEi)
        pEi   = min(max(pEi, pMin), pMax)
        TEi   = pEi * dInv / GasCO
        mulEi = Mu0 * (TEi/T0)**1.5*(T0+Ts)/(TEi+Ts)

        mutEi = soluEi(1) * soluEi(nDim+3) / max(soluEi(nDim+4), max(1.0e-20, Omega*F2/A1)) !Standard SST
        mutEi = soluEi(1) * soluEi(nDim+3) / max(soluEi(nDim+4), max(1.0e-20, Sigma*F2/A1)) !Menter SST-2003
        mutEi = min(mutLim, max(0.0, mutEi))

        primEi(1     ) = pEi
        primEi(nDim+2) = TEi
        primEi(nDim+3) = mulEi
        primEi(nDim+4) = mutEi

    endsubroutine
    !
    attributes(global) subroutine bldBC_Global(val_iCalu_Poin,val_iBelvProp,&
            val_iMarkProp,val_iWDis_Poin,val_iCoor_Poin,val_iNvor_Belv,     &
            val_iVelo_Poin,val_iVelo_Belv,val_soluvar,val_primvar,          &
            val_soluBelv,val_primBelv,val_soluMark,val_primMark,val_iParm,  &
            val_isWithPrec,val_nBelv,val_nMark,val_nPoin,val_nTMax,val_iIter)
        use mod_BC
        implicit none
        integer,device:: val_iCalu_Poin(val_nPoin)
        integer,device:: val_iBelvProp(val_nBelv, 2)
        integer,device:: val_iMarkProp(val_nMark, 2)
        real(kind=REALLEN),device:: val_iWDis_Poin(val_nPoin)
        real(kind=REALLEN),device:: val_iCoor_Poin(val_nPoin, nDim)
        real(kind=REALLEN),device:: val_iNvor_Belv(val_nBelv, nDim)
        real(kind=REALLEN),device:: val_iVelo_Poin(val_nPoin, nDim)
        real(kind=REALLEN),device:: val_iVelo_Belv(val_nBelv, nDim)
        real(kind=REALLEN),device:: val_soluvar(val_nPoin, nDim+4)
        real(kind=REALLEN),device:: val_primvar(val_nPoin, nDim+4)
        real(kind=REALLEN),device:: val_soluBelv(val_nBelv, nDim+4)
        real(kind=REALLEN),device:: val_primBelv(val_nBelv, nDim+4)
        real(kind=REALLEN),device:: val_soluMark(val_nMark, nDim+4)
        real(kind=REALLEN),device:: val_primMark(val_nMark, nDim+4)
        real(kind=REALLEN),device:: val_iParm(NTotalParm)
        logical,value:: val_isWithPrec
        integer,value:: val_nBelv
        integer,value:: val_nMark
        integer,value:: val_nPoin
        integer,value:: val_nTMax
        integer,value:: val_iIter
        !
        integer:: i,j,k,pID,mID,mKD,iDir
        real(kind=REALLEN):: Gamma,GasCo,sstA1,T0,Ts,Mu0,iWDis,pMin,pMax,xLim
        real(kind=REALLEN):: soluEl(nDim+2),nvorFi(nDim),soluEr(nDim+2),VgFi(nDim)
        real(kind=REALLEN):: soluFi(nDim+4),primFi(nDim+4),veloFi(nDim),zero
        logical:: isPrec


        i = (blockIdx%x-1)*blockDim%x+threadIdx%x
        if(i > val_nTMax) return

        pID = val_iBelvProp(i,1)
        mID = val_iBelvProp(i,2)

        if((val_iCalu_Poin(pID) /= FLAG_VALID).or.(mID == 0)) then
            return
        elseif(mID < 0) then
            do k=1,nDim+4
                val_soluBelv(i,k) = val_soluvar(-mID,k)
            enddo
            do k=1,nDim+4
                val_primBelv(i,k) = val_primvar(-mID,k)
            enddo
            return
        endif

        mKD = val_iMarkProp(mID,2)

        do k=1,nDim+2
            soluEl(k) = val_soluvar(pID ,k)
            soluEr(k) = val_soluMark(mID,k)
        enddo
        do k=1,nDim
            nvorFi(k) = val_iNvor_Belv(i,k)
        enddo

        Gamma = val_iParm(IP_Gamma)
        GasCo = val_iParm(IP_GasCO)
        T0    = val_iParm(IP_SutherlandT0)
        Ts    = val_iParm(IP_SutherlandTs)
        Mu0   = val_iParm(IP_SutherlandM0)
        sstA1 = val_iParm(IP_TurbSST_A1)
        pMin  = val_iParm(IP_LIMMINP)
        pMax  = val_iParm(IP_LIMMAXP)
        xLim  = val_iParm(IP_LIMMAXMuT)
        zero = 0.0


        VgFi(1:nDim) = val_iVelo_Poin(pID,1:nDim)

        if(mKD == MARK_WALL) then
            call BC_Wall_NS(soluEl,nvorFi,vgFi,soluFi,Gamma,GasCo)
            iDir = 0
        elseif(mKD == MARK_EQTW) then
            !call BC_EQTW(soluEl,soluEr(1),vgFi,nvorFi,soluFi)
            iDir = 0
        elseif(mKD == MARK_EULW) then
            call BC_Wall_Euler(soluEl,nvorFi,vgFi,soluFi)
            iDir = 0
        elseif(mKD == MARK_MOVW) then
            veloFi(1:nDim) = val_iVelo_Belv(i,1:nDim)
            call BC_MovingWall_NS(soluEl,nvorFi,veloFi,Gamma,soluFi)
            iDir = 0
        elseif(mKD == MARK_SYMM) then
            call BC_Symm(soluEl,nvorFi,soluFi)
            iDir = 1
        elseif(mKD == MARK_PFAR) then
            isPrec = val_isWithPrec
            call BC_PFar(soluEl,soluEr,nvorFi,soluFi,Gamma,GasCo,isPrec,iDir)
        elseif(mKD == MARK_INLM) then
            iWDis = val_iWDis_Poin(pID)
            call BC_Inlet(soluEl,soluEr,nvorFi,soluFi,Gamma,GasCo,iWDis)
            iDir = -1
        elseif(mKD == MARK_OUTL) then
            call BC_OutL(soluEl,soluEr,nvorFi,soluFi,Gamma,GasCo)
            iDir = 1
        elseif(mKD == MARK_OUTP) then
            if(val_iParm(IP_OUTP2WALL) > 0.0) then
                call BC_Wall_NS(soluEl,nvorFi,vgFi,soluFi,Gamma,GasCo)
                iDir = 0
            else
                call BC_OutP(soluEl,soluEr,nvorFi,soluFi,Gamma,GasCo,iDir)
                iDir = 1
            endif
        endif

        if(iDir == 1) then
            soluFi(nDim+3) = val_soluvar(pID ,nDim+3)
            soluFi(nDim+4) = val_soluvar(pID ,nDim+4)
        elseif(iDir == -1) then
            soluFi(nDim+3) = val_soluMark(mID,nDim+3)
            soluFi(nDim+4) = val_soluMark(mID,nDim+4)
        else
            soluFi(nDim+3) = val_iParm(IP_TurbSST_kWall)
            !soluFi(nDim+4) = val_iParm(IP_TurbSST_wWall)
            soluFi(nDim+4) = 60.0*val_primvar(i,nDim+3)/ &
                            (soluEl(1)*val_iParm(IP_TurbSST_Belta1)*val_iParm(IP_TurbSST_DY1))
        endif

        call getPrimBySolu_TurbSST(soluFi, primFi, Gamma, GasCo,    &
                    T0, Ts, Mu0, sstA1, pMin, pMax, xLim, zero, zero, zero)

        do k=1,nDim+4
            val_soluBelv(i,k) = soluFi(k)
        enddo
        do k=1,nDim+4
            val_primBelv(i,k) = primFi(k)
        enddo

        if((mKD == MARK_WALL)      &
            .or.(mKD == MARK_EQTW) &
            .or.(mKD == MARK_EULW) &
            .or.(mKD == MARK_MOVW) &
            .or.(mKD == MARK_INLM) &
            .or.(mKD == MARK_SYMM) &
            .or.(mKD == MARK_OUTP) &
            ) then
            do k=1,nDim+4
                val_soluvar(pID,k) = soluFi(k)
            enddo
            do k=1,nDim+4
                val_primvar(pID,k) = primFi(k)
            enddo
        endif

    endsubroutine
    !
    attributes(global) subroutine calPrecCoefMatx_Global(        &
            val_iCalu_Poin,val_soluvar,val_primvar,val_precCoef, &
            val_precMatx,val_iParm,val_nPoin,val_nTMax,val_isPrec)
        implicit none
        integer,device:: val_iCalu_Poin(val_nPoin)
        real(kind=REALLEN),device:: val_soluvar(val_nPoin, nDim+4)
        real(kind=REALLEN),device:: val_primvar(val_nPoin, nDim+4)
        real(kind=REALLEN),device:: val_precCoef(val_nPoin)
        real(kind=REALLEN),device:: val_precMatx(val_nPoin, nDim+2, nDim+2)
        real(kind=REALLEN),device:: val_iParm(NTotalParm)
        integer,value:: val_nPoin
        integer,value:: val_nTMax
        logical,value:: val_isPrec
        !
        integer:: i,ix,iy
        real(kind=REALLEN):: primEi(nDim+2),qEi,c2Ei,m2Ei,hEi,coefEi,coexEi
        real(kind=REALLEN):: matxEi(nDim+2,nDim+2),vectEi(nDim+2),Gamma,GasCo,Gammo


        i = (blockIdx%x-1)*blockDim%x+threadIdx%x
        if(i > val_nTMax) return
        if(val_iCalu_Poin(i) /= FLAG_VALID) return
        if(.not.val_isPrec) return

        do ix=1,nDim+2
            primEi(ix) = val_primvar(i,ix)
        enddo

        Gamma = val_iParm(IP_GAMMA)
        GasCo = val_iParm(IP_GASCO)
        Gammo = Gamma - 1.0

        qEi  = 0.5*(val_primvar(i,2)**2 + val_primvar(i,3)**2 + val_primvar(i,nDim+1)**2*(nDim-2))
        c2Ei = Gamma*GasCo*val_primvar(i,nDim+2)
        m2Ei = 2.0*qEi/c2Ei
        hEi  = (val_soluvar(i,nDim+2) + val_primvar(i,1))/val_soluvar(i,1)

        coefEi = min(max(m2Ei, val_iParm(IP_PrecBelt)), 1.0)

        val_precCoef(i) = coefEi

        coexEi = Gammo*(coefEi-1.0)/c2Ei

        vectEi(     1) =  qEi*coexEi
        vectEi(     2) = -primEi(     2)*coexEi
        vectEi(     3) = -primEi(     3)*coexEi
        vectEi(nDim+1) = -primEi(nDim+1)*coexEi
        vectEi(nDim+2) = 1.0*coexEi

        do ix=1,nDim+2
            matxEi(     1,ix) = vectEi(ix)*1.0
            matxEi(     2,ix) = vectEi(ix)*primEi(     2)
            matxEi(     3,ix) = vectEi(ix)*primEi(     3)
            matxEi(nDim+1,ix) = vectEi(ix)*primEi(nDim+1)
            matxEi(nDim+2,ix) = vectEi(ix)*hEi
        enddo

        do ix=1,nDim+1
            matxEi(ix,ix) = 1.0 + matxEi(ix,ix)
        enddo
        matxEi(nDim+2,nDim+2) = coefEi + qEi*coexEi

        do ix=1,nDim+2
            do iy=1,nDim+2
                val_precMatx(i,ix,iy) = matxEi(ix,iy)
            enddo
        enddo

    endsubroutine
    !
    attributes(global) subroutine calSpecRaduAndDeltTime_Global(&
            val_iCalu_Poin,val_kSate_Poin,val_iSate_Poin,       &
            val_iCoef_Sate,val_kBelv_Poin,val_iBelv_Poin,       &
            val_iCoef_Belv,val_iVelo_Poin,val_iVelo_Belv,       &
            val_primvar,val_primBelv,val_pCoef,val_SRcInfc,     &
            val_SRvInfc,val_SRcNode,val_SRvNode,val_SRcBelv,    &
            val_SRvBelv,val_DT,val_DTCoef,val_iParm,val_nPoin,  &
            val_nSate,val_nBelv,val_nTMax,val_isPrec,val_isUnst )
        implicit none
        integer,device:: val_iCalu_Poin(val_nPoin)
        integer,device:: val_kSate_Poin(val_nPoin+1)
        integer,device:: val_iSate_Poin(val_nSate,2)
        real(kind=REALLEN),device:: val_iCoef_Sate(val_nSate, nDim)
        integer,device:: val_kBelv_Poin(val_nPoin+1)
        integer,device:: val_iBelv_Poin(val_nBelv,2)
        real(kind=REALLEN),device:: val_iCoef_Belv(val_nBelv, nDim)
        real(kind=REALLEN),device:: val_iVelo_Poin(val_nPoin, nDim)
        real(kind=REALLEN),device:: val_iVelo_Belv(val_nBelv, nDim)
        real(kind=REALLEN),device:: val_primvar(val_nPoin, nDim+4)
        real(kind=REALLEN),device:: val_primBelv(val_nBelv, nDim+4)
        real(kind=REALLEN),device:: val_pCoef(val_nPoin)
        real(kind=REALLEN),device:: val_SRcInfc(val_nSate)
        real(kind=REALLEN),device:: val_SRvInfc(val_nSate)
        real(kind=REALLEN),device:: val_SRcNode(val_nPoin)
        real(kind=REALLEN),device:: val_SRvNode(val_nPoin)
        real(kind=REALLEN),device:: val_SRcBelv(val_nBelv)
        real(kind=REALLEN),device:: val_SRvBelv(val_nBelv)
        real(kind=REALLEN),device:: val_DT(val_nPoin)
        real(kind=REALLEN),device:: val_DTCoef(val_nPoin)
        real(kind=REALLEN),device:: val_iParm(NTotalParm)
        integer,value:: val_nPoin
        integer,value:: val_nSate
        integer,value:: val_nBelv
        integer,value:: val_nTMax
        logical,value:: val_isPrec
        logical,value:: val_isUnst
        !
        integer:: i,j,k,s,sID
        real(kind=REALLEN):: lenFi,DFi,VgFi(nDim),VnFi,VgnFi,primFi(nDim+4),coefFj
        real(kind=REALLEN):: SRcFi,SRvFi,SRcEi,SRvEi,SRvFj,SRvEj,deltEi
        real(kind=REALLEN):: Gamma,GasCo,PrandtlL,PrandtlT,coFi,vnFj,cFj


        i = (blockIdx%x-1)*blockDim%x+threadIdx%x
        if(i > val_nTMax) return
        if(val_iCalu_Poin(i) /= FLAG_VALID) return

        Gamma    = val_iParm(IP_GAMMA)
        GasCo    = val_iParm(IP_GASCO)
        PrandtlL = val_iParm(IP_PrandtlL)
        PrandtlT = val_iParm(IP_PrandtlT)

        coFi = 1.0
        VgFi(1:nDim) = val_iVelo_Poin(i,1:nDim)

        SRcEi = 0.0
        SRvEi = 0.0
        SRvEj = 0.0
        do j=val_kSate_Poin(i)+1,val_kSate_Poin(i+1)
            sID = val_iSate_Poin(j,1)
            if(val_iCalu_Poin(sID) == FLAG_INVAL) cycle

            do k=1,nDim+4
                primFi(k) = 0.5*(val_primvar(i,k) + val_primvar(sID,k))
            end do
            DFi = primFi(1)/(GasCo*primFi(nDim+2))

            VnFi  = 0.0
            VgnFi = 0.0
            lenFi = 0.0
            do k=1,nDim
                coefFj = val_iCoef_Sate(j,k)
                VnFi   = VnFi   + coefFj*primFi(k+1)
                VgnFi  = VgnFi  + coefFj*VgFi(k)
                lenFi  = lenFi  + coefFj*coefFj
            end do
            lenFi = sqrt(lenFi)
            VnFi = (VnFi - VgnFi)/lenFi

            if(val_isPrec) then
                coFi  = 0.5*(val_pCoef(i) + val_pCoef(sID))
            endif

            VnFj  = 0.5*abs(VnFi)*(1+coFi)
            cFj   = 0.5*sqrt((1-coFi)*(1-coFi)*VnFi*VnFi + 4.0*coFi*coFi*Gamma*GasCo*primFi(nDim+2))
            SRcFi = (abs(VnFj) + cFj)*lenFi

            SRvFi = (Gamma/DFi) * (primFi(nDim+3) + primFi(nDim+4)) *lenFi*lenFi
            SRvFj = (Gamma/DFi) * (primFi(nDim+3)/PrandtlL + primFi(nDim+4)/PrandtlT) *lenFi*lenFi

            SRcFi = 0.5*SRcFi
            !SRvFi = 0.5*SRvFi

            val_SRcInfc(j) = SRcFi
            val_SRvInfc(j) = SRvFi
            SRcEi = SRcEi + SRcFi
            SRvEi = SRvEi + SRvFi
            SRvEj = SRvEj + SRvFj
        end do

        do j=val_kBelv_Poin(i)+1,val_kBelv_Poin(i+1)
            sID = val_iBelv_Poin(j,1)
            do k=1,nDim+4
                primFi(k) = val_primBelv(sID,k)
            end do
            DFi = primFi(1)/(GasCo*primFi(nDim+2))


            VnFi  = 0.0
            VgnFi = 0.0
            lenFi = 0.0
            do k=1,nDim
                coefFj = val_iCoef_Belv(sID,k)
                VnFi   = VnFi   + coefFj*primFi(k+1)
                VgnFi  = VgnFi  + coefFj*VgFi(k)
                lenFi  = lenFi  + coefFj*coefFj
            end do
            lenFi = sqrt(lenFi)
            VnFi = (VnFi - VgnFi)/lenFi

            if(val_isPrec) then
                coFi  = val_pCoef(i)
            endif

            VnFj  = 0.5*abs(VnFi)*(1+coFi)
            cFj   = 0.5*sqrt((1-coFi)*(1-coFi)*VnFi*VnFi + 4.0*coFi*coFi*Gamma*GasCo*primFi(nDim+2))

            SRcFi = (abs(VnFj) + cFj)*lenFi
            SRvFi = (Gamma/DFi) * (primFi(nDim+3) + primFi(nDim+4)) *lenFi*lenFi
            SRvFj = (Gamma/DFi) * (primFi(nDim+3)/PrandtlL + primFi(nDim+4)/PrandtlT) *lenFi*lenFi

            SRcFi = 0.5*SRcFi

            val_SRcBelv(sID) = SRcFi
            val_SRvBelv(sID) = SRvFi
            SRcEi = SRcEi + SRcFi
            SRvEi = SRvEi + SRvFi
            SRvEj = SRvEj + SRvFj
        end do

        val_SRcNode(i) = SRcEi
        val_SRvNode(i) = SRvEi

        deltEi = val_iParm(IP_CFLCurt)*val_DTCoef(i)/(2.0*SRcEi + SRvEj*val_iParm(IP_DTViscCoef))
        if(val_isUnst) then
            deltEi = min(deltEi, val_iParm(IP_DTLIMIT))
        endif
        val_DT(i) = deltEi

    endsubroutine
    !
    attributes(global) subroutine calSoluGradWithLimt_Global(   &
            val_iCalu_Poin,val_kSate_Poin,val_iSate_Poin,       &
            val_iCoef_Sate,val_iCoor_Poin,val_iRadu_Poin,       &
            val_kBelv_Poin,val_iBelv_Poin,val_iCoef_Belv,       &
            val_soluvar,val_primvar,val_soluBelv,val_primBelv,  &
            val_gradSolu,val_limiter,val_soluLimRef,val_iParm,  &
            val_nPoin,val_nSate,val_nBelv,val_nTMax,val_limitType)
        use mod_Limiter
        implicit none
        integer,device:: val_iCalu_Poin(val_nPoin)
        integer,device:: val_kSate_Poin(val_nPoin+1)
        integer,device:: val_iSate_Poin(val_nSate,2)
        real(kind=REALLEN),device:: val_iCoef_Sate(val_nSate, nDim)
        real(kind=REALLEN),device:: val_iCoor_Poin(val_nPoin, nDim)
        real(kind=REALLEN),device:: val_iRadu_Poin(val_nPoin)
        integer,device:: val_kBelv_Poin(val_nPoin+1)
        integer,device:: val_iBelv_Poin(val_nBelv,2)
        real(kind=REALLEN),device:: val_iCoef_Belv(val_nBelv, nDim)
        real(kind=REALLEN),device:: val_soluvar(val_nPoin, nDim+4)
        real(kind=REALLEN),device:: val_primvar(val_nPoin, nDim+4)
        real(kind=REALLEN),device:: val_soluBelv(val_nBelv, nDim+4)
        real(kind=REALLEN),device:: val_primBelv(val_nBelv, nDim+4)
        real(kind=REALLEN),device:: val_gradSolu(val_nPoin, nDim+4, nDim)
        real(kind=REALLEN),device:: val_limiter(val_nPoin, nDim+4)
        real(kind=REALLEN),device:: val_soluLimRef(nDim+3)
        real(kind=REALLEN),device:: val_iParm(NTotalParm)
        integer,value:: val_nPoin
        integer,value:: val_nSate
        integer,value:: val_nBelv
        integer,value:: val_nTMax
        integer,value:: val_limitType
        !
        integer:: i,j,k,sID
        real(kind=REALLEN):: primEl(nDim+4),primFi(nDim+4)
        real(kind=REALLEN):: gradEi(nDim+4,nDim),dMax(nDim+4),dMin(nDim+4)
        real(kind=REALLEN):: phi(nDim+4),phii,delt1,delt2,coefFi(nDim)
        real(kind=REALLEN):: epcilon2,fact,lenFi

        i = (blockIdx%x-1)*blockDim%x+threadIdx%x
        if(i > val_nTMax) return
        if(val_iCalu_Poin(i) /= FLAG_VALID) return

        do k=1,nDim+4
            primEl(k) = val_primvar(i,k)

            gradEi(k,   1) = 0.0
            gradEi(k,   2) = 0.0
            gradEi(k,nDim) = 0.0

            dMax(k) = 0.0
            dMin(k) = 0.0
            phi( k) = 1.0
        end do
        primEl(nDim+3) = val_soluvar(i,nDim+3)
        primEl(nDim+4) = val_soluvar(i,nDim+4)

        do j=val_kSate_Poin(i)+1, val_kSate_Poin(i+1)
            sID = val_iSate_Poin(j,1)
            if(val_iCalu_Poin(sID) == FLAG_INVAL) cycle

            do k=1,nDim
                coefFi(k) = val_iCoef_Sate(j,k)
            end do

            primFi(     1) = 0.5*(val_primvar(sID,     1) - primEl(     1))
            primFi(     2) = 0.5*(val_primvar(sID,     2) - primEl(     2))
            primFi(     3) = 0.5*(val_primvar(sID,     3) - primEl(     3))
            primFi(nDim+1) = 0.5*(val_primvar(sID,nDim+1) - primEl(nDim+1))
            primFi(nDim+2) = 0.5*(val_primvar(sID,nDim+2) - primEl(nDim+2))
            primFi(nDim+3) = 0.5*(val_soluvar(sID,nDim+3) - primEl(nDim+3))
            primFi(nDim+4) = 0.5*(val_soluvar(sID,nDim+4) - primEl(nDim+4))

            do k=1,nDim+4
                dMax(k) = max(dMax(k), primFi(k))
                dMin(k) = min(dMin(k), primFi(k))

                gradEi(k,   1) = gradEi(k,   1) + primFi(k)*coefFi(   1)
                gradEi(k,   2) = gradEi(k,   2) + primFi(k)*coefFi(   2)
                if(nDim == 3) then
                gradEi(k,nDim) = gradEi(k,nDim) + primFi(k)*coefFi(nDim)
                endif
            end do
        end do

        do j=val_kBelv_Poin(i)+1, val_kBelv_Poin(i+1)
            sID = val_iBelv_Poin(j,1)

            do k=1,nDim
                coefFi(k) = val_iCoef_Belv(sID,k)
            end do

            primFi(     1) = val_primBelv(sID,     1) - primEl(     1)
            primFi(     2) = val_primBelv(sID,     2) - primEl(     2)
            primFi(     3) = val_primBelv(sID,     3) - primEl(     3)
            primFi(nDim+1) = val_primBelv(sID,nDim+1) - primEl(nDim+1)
            primFi(nDim+2) = val_primBelv(sID,nDim+2) - primEl(nDim+2)
            primFi(nDim+3) = val_soluBelv(sID,nDim+3) - primEl(nDim+3)
            primFi(nDim+4) = val_soluBelv(sID,nDim+4) - primEl(nDim+4)

            do k=1,nDim+4
                dMax(k) = max(dMax(k), primFi(k))
                dMin(k) = min(dMin(k), primFi(k))

                gradEi(k,   1) = gradEi(k,   1) + primFi(k)*coefFi(   1)
                gradEi(k,   2) = gradEi(k,   2) + primFi(k)*coefFi(   2)
                if(nDim == 3) then
                gradEi(k,nDim) = gradEi(k,nDim) + primFi(k)*coefFi(nDim)
                endif
            end do
        end do

        do k=1,nDim+4
            val_gradSolu(i,k,   1) = gradEi(k,   1)
            val_gradSolu(i,k,   2) = gradEi(k,   2)
            val_gradSolu(i,k,nDim) = gradEi(k,nDim)
        end do


        !limiter
        do j=val_kSate_Poin(i)+1,val_kSate_Poin(i+1)
            sID = val_iSate_Poin(j,1)
            if(val_iCalu_Poin(sID) == FLAG_INVAL) cycle

            do k=1,nDim
                coefFi(k) = 0.5*(val_iCoor_Poin(sID,k) - val_iCoor_Poin(i,k))
            end do
            lenFi = sqrt(coefFi(1)**2+coefFi(2)**2+coefFi(nDim)**2*(nDim-2))

            epcilon2 = (5.0*lenFi)**3
            do k=1,nDim+4
                !epcilon = val_soluLimRef(k)*(1.0*lenFi)**3
                delt2 = gradEi(k,1)*coefFi(1) + gradEi(k,2)*coefFi(2) + &
                        gradEi(k,nDim)*coefFi(nDim)*(nDim-2)

                if(val_LimitType == _VENKATAKRISHNAN)then
                    if(delt2 > 0) then
                        delt1 = dMax(k)
                    elseif(delt2 < 0) then
                        delt1 = dMin(k)
                    else
                        delt1 = 0.0
                    end if
                else
                    delt1 = val_primvar(sID,k) - primEl(1)
                    if(k >= nDim+3) then
                    delt1 = val_soluvar(sID,k) - primEl(k)
                    endif
                endif

                call limit_Adapter(delt2, delt1, phii, epcilon2, val_LimitType)

                phi(k) = min(phi(k), phii)
            end do
        enddo

        do k=1,nDim+4
            val_limiter(i,k) = phi(k)
        end do

    endsubroutine
    !
    attributes(global) subroutine calTau_Global(val_iCalu_Poin, &
            val_primvar,val_gradSolu,val_Tau,val_nPoin,val_nTMax)
        implicit none
        integer           ,device:: val_iCalu_Poin(val_nPoin)
        real(kind=REALLEN),device:: val_primvar(val_nPoin, nDim+4)
        real(kind=REALLEN),device:: val_gradSolu(val_nPoin, nDim+4, nDim)
        real(kind=REALLEN),device:: val_Tau(val_nPoin, nDim, nDim)
        integer,value:: val_nPoin
        integer,value:: val_nTMax
        !
        integer:: i,j,k
        real(kind=REALLEN):: muEi, lumdi, divVi
        real(kind=REALLEN):: gradVi(nDim,nDim),tau(nDim)


        i=(blockIdx%x-1)*blockDim%x+threadIdx%x
        if(i > val_nTMax) return
        if(val_iCalu_Poin(i) /= FLAG_VALID) return

        do j=1,nDim
            do k=1,nDim
                gradVi(j,k) = val_gradSolu(i,j+1,k)
            enddo
        enddo

        muEi  = val_primvar(i,nDim+3) !+ val_primvar(i,nDim+4)
        lumdi = -2.0/3.0*muEi
        divVi = gradVi(1,1) + gradVi(2,2) + gradVi(nDim,nDim)*(nDim-2)

        do j=1,nDim
            tau(1   ) = muEi*(gradVi(1   ,j) + gradVi(j,1   ))
            tau(2   ) = muEi*(gradVi(2   ,j) + gradVi(j,2   ))
            tau(nDim) = muEi*(gradVi(nDim,j) + gradVi(j,nDim))

            tau(j) = tau(j) + lumdi*divVi

            do k=1,nDim
                val_tau(i,j,k) = tau(k)
            enddo
        enddo

    endsubroutine
    !
    attributes(global) subroutine calFluxConvViscSour_UPW_Global(&
            val_iCalu_Poin,val_iFlag_Poin,val_kSate_Poin,       &
            val_iSate_Poin,val_iCoef_Sate,val_iCoor_Poin,       &
            val_kBelv_Poin,val_iBelv_Poin,val_iCoef_Belv,       &
            val_iVelo_Poin,val_iVelo_Belv,val_nodeWDis,         &
            val_soluvar,val_primvar,val_soluBelv,val_primBelv,  &
            val_gradSolu,val_limiter,val_pMatx,val_UnstTerm,    &
            val_turbNodeAuxv,val_Res,val_iParm,val_iMarkProp,   &
            val_nPoin,val_nSate,val_nBelv,val_nMark,val_nPAux,  &
            val_nTMax,val_is2ndOrder,val_upwScheme,val_isPrec,  &
            val_isUnst,val_limType                              )
        use mod_FluxSplit
        use mod_Limiter
        implicit none
        integer,device:: val_iCalu_Poin(val_nPoin)
        integer,device:: val_iFlag_Poin(val_nPoin)
        integer,device:: val_kSate_Poin(val_nPoin+1)
        integer,device:: val_iSate_Poin(val_nSate,2)
        real(kind=REALLEN),device:: val_iCoef_Sate(val_nSate, nDim)
        real(kind=REALLEN),device:: val_iCoor_Poin(val_nPoin, nDim)
        integer,device:: val_kBelv_Poin(val_nPoin+1)
        integer,device:: val_iBelv_Poin(val_nBelv,2)
        real(kind=REALLEN),device:: val_iCoef_Belv(val_nBelv, nDim)
        real(kind=REALLEN),device:: val_iVelo_Poin(val_nPoin, nDim)
        real(kind=REALLEN),device:: val_iVelo_Belv(val_nBelv, nDim)
        real(kind=REALLEN),device:: val_nodeWDis(val_nPoin)
        real(kind=REALLEN),device:: val_soluvar(val_nPoin, nDim+4)
        real(kind=REALLEN),device:: val_primvar(val_nPoin, nDim+4)
        real(kind=REALLEN),device:: val_soluBelv(val_nBelv, nDim+4)
        real(kind=REALLEN),device:: val_primBelv(val_nBelv, nDim+4)
        real(kind=REALLEN),device:: val_gradSolu(val_nPoin, nDim+4, nDim)
        real(kind=REALLEN),device:: val_limiter(val_nPoin, nDim+4)
        real(kind=REALLEN),device:: val_pMatx(val_nPoin, nDim+2,nDim+2)
        real(kind=REALLEN),device:: val_UnstTerm(val_nPoin, nDim+4)
        real(kind=REALLEN),device:: val_turbNodeAuxv(val_nPoin,val_nPAux)
        real(kind=REALLEN),device:: val_Res(val_nPoin, nDim+4)
        real(kind=REALLEN),device:: val_iParm(NTotalParm)
        integer,device:: val_iMarkProp(val_nMark, 2)
        integer,value:: val_nPoin
        integer,value:: val_nSate
        integer,value:: val_nBelv
        integer,value:: val_nMark
        integer,value:: val_nPAux
        integer,value:: val_nTMax
        logical,value:: val_is2ndOrder
        integer,value:: val_UPWScheme
        logical,value:: val_isPrec
        logical,value:: val_isUnst
        integer,value:: val_limType
        !
        real(kind=REALLEN),shared:: Gamma,GasCo,Cp,prandtlL,prandtlT
        real(kind=REALLEN),shared:: SST_BStar,SST_kappa,SST_a1
        real(kind=REALLEN),shared:: SST_sigmak1,SST_sigmak2,SST_sigmaw1,SST_sigmaw2
        real(kind=REALLEN),shared:: SST_belta1,SST_belta2,SST_gamma1,SST_gamma2
        !
        integer:: i,j,k,s,sID,mID,mKD,iDir
        real(kind=REALLEN):: primFl(nDim+4),primFr(nDim+4)
        real(kind=REALLEN):: fluxEi(nDim+4),fluxFi(nDim+4),fluvEi(nDim+4)
        real(kind=REALLEN):: coefFi(nDim),coorDi(nDim),dCoor,DFi,VnFi,sqFi,VgFi(nDim),VgnFi
        real(kind=REALLEN):: gradVi(nDim,nDim),gradTi(nDim),gradKi(nDim),gradWi(nDim),vFi(nDim)
        real(kind=REALLEN):: mulFi,mutFi,tauFi(nDim),sFi,muFi,ktFi,divvFi
        real(kind=REALLEN):: delt1,delt2,phii,epcilon2
        real(kind=REALLEN):: sst_sigmak,sst_sigmaw,coefKi,coefWi,kFi,wFi,OmgaFi,SigmFi,crssFi,WDi
        real(kind=REALLEN):: a1,a2,a3,a4,arg1,arg2,F1,F2,SST_gamma,SST_belta,prodK,diffK,prodW,diffW
        !

        if(threadIdx%x == 1) then
        Gamma       = val_iParm(IP_Gamma)
        GasCo       = val_iParm(IP_GasCO)
        Cp          = val_iParm(IP_CP)

        prandtlL    = val_iParm(IP_PrandtlL)
        prandtlT    = val_iParm(IP_PrandtlT)

        SST_BStar   = val_iParm(IP_TurbSST_BStar)
        SST_kappa   = val_iParm(IP_TurbSST_Kappa)
        SST_a1      = val_iParm(IP_TurbSST_A1)
        SST_sigmak1 = val_iParm(IP_TurbSST_SigmaK1)
        SST_sigmak2 = val_iParm(IP_TurbSST_SigmaK2)
        SST_sigmaw1 = val_iParm(IP_TurbSST_SigmaW1)
        SST_sigmaw2 = val_iParm(IP_TurbSST_SigmaW2)
        SST_belta1  = val_iParm(IP_TurbSST_Belta1)
        SST_belta2  = val_iParm(IP_TurbSST_Belta2)
        SST_gamma1  = val_iParm(IP_TurbSST_Gamma1)
        SST_gamma2  = val_iParm(IP_TurbSST_Gamma2)

        endif
        call syncthreads()

        i=(blockIdx%x-1)*blockDim%x+threadIdx%x
        if(i > val_nTMax) return
        if(val_iCalu_Poin(i) /= FLAG_VALID) return

        F1 = val_turbNodeAuxv(i,IP_SST_F1)
        sst_sigmak = val_iParm(IP_TurbSST_SigmaK1)*F1 + val_iParm(IP_TurbSST_SigmaK2)*(1-F1)
        sst_sigmaw = val_iParm(IP_TurbSST_SigmaW1)*F1 + val_iParm(IP_TurbSST_SigmaW2)*(1-F1)

        do k=1,nDim+4
            fluxEi(k) = 0.0
            fluvEi(k) = 0.0
        end do

        VgFi(1:nDim) = val_iVelo_Poin(i,1:nDim)

        do j=val_kSate_Poin(i)+1,val_kSate_Poin(i+1)
            sID = val_iSate_Poin(j,1)
            if(val_iCalu_Poin(sID) == FLAG_INVAL) cycle

            do k=1,nDim+2
                primFl(k) = val_primvar(i  ,k)
                primFr(k) = val_primvar(sID,k)
            end do
            primFl(nDim+3) = val_soluvar(i  ,nDim+3)
            primFr(nDim+3) = val_soluvar(sID,nDim+3)
            primFl(nDim+4) = val_soluvar(i  ,nDim+4)
            primFr(nDim+4) = val_soluvar(sID,nDim+4)

            do k=1,nDim
                coefFi(k) = val_iCoef_Sate(j,k)
                coorDi(k) = 0.5*(val_iCoor_Poin(sID,k) - val_iCoor_Poin(i,k))
            enddo
            if(val_is2ndOrder) then
                epcilon2 = (coorDi(1)**2+coorDi(2)**2+coorDi(nDim)**2*(nDim-2))**1.5
                epcilon2 = 1.5
                do s=1,nDim+2
                    delt1 = primFr(s) - primFl(s)
                    delt2 = coorDi(1)*val_gradSolu(i,s,1) + &
                            coorDi(2)*val_gradSolu(i,s,2) + &
                            coorDi(nDim)*val_gradSolu(i,s,nDim)*(nDim-2)
                    phii = val_limiter(i,s)
                    !call limit_Adapter(delt2, delt1, phii, epcilon2, val_limType)
                    primFl(s) = primFl(s) + delt2*phii

                    delt2 = coorDi(1)*val_gradSolu(sID,s,1) + &
                            coorDi(2)*val_gradSolu(sID,s,2) + &
                            coorDi(nDim)*val_gradSolu(sID,s,nDim)*(nDim-2)
                    phii = val_limiter(sID,s)
                    !call limit_Adapter(delt2, delt1, phii, epcilon2, val_limType)
                    primFr(s) = primFr(s) - delt2*phii
                enddo
            endif

            VgnFi = VgFi(1)*coefFi(1) + VgFi(2)*coefFi(2) + VgFi(nDim)*coefFi(nDim)*(nDim-2)

            !convective flux term
            if(val_UPWScheme == _ROE) then
                call fluxSplit_Roe(primFl, primFr, coefFi, VgnFi, fluxFi, Gamma, GasCo, iDir)
            elseif(val_UPWScheme == _SLAU) then
                call fluxSplit_SLAU(primFl, primFr, coefFi, VgnFi, fluxFi, Gamma, GasCo, iDir)
            elseif(val_UPWScheme == _SLAU2) then
                call fluxSplit_SLAU2(primFl, primFr, coefFi, VgnFi, fluxFi, Gamma, GasCo, iDir)
            endif

            if(iDir == 1) then
                VnFi = primFl(2)*coefFi(1) + primFl(3)*coefFi(2) + primFl(4)*coefFi(nDim)*(nDim-2) - VgnFi
                fluxFi(nDim+3) = VnFi*primFl(nDim+3)*val_soluvar(i,1)
                fluxFi(nDim+4) = VnFi*primFl(nDim+4)*val_soluvar(i,1)
            elseif(iDir == -1) then
                VnFi = primFr(2)*coefFi(1) + primFr(3)*coefFi(2) + primFr(4)*coefFi(nDim)*(nDim-2) - VgnFi
                fluxFi(nDim+3) = VnFi*primFr(nDim+3)*val_soluvar(sID,1)
                fluxFi(nDim+4) = VnFi*primFr(nDim+4)*val_soluvar(sID,1)
            endif

            do k=1,nDim+4
                fluxEi(k) = fluxEi(k) + fluxFi(k)
                fluxFi(k) = 0.0
            end do

            !viscous flux term
            do k=1,nDim
                vFi(k) = 0.5*(val_primvar(i,k+1) + val_primvar(sID,k+1)) !- VgFi(k)

                do s=1,nDim
                    gradVi(k,s) = 0.5*(val_gradSolu(i,s+1,k)*val_limiter(i,s+1) + val_gradSolu(sID,s+1,k)*val_limiter(sID,s+1))
                enddo
                gradTi(k) = 0.5*(val_gradSolu(i,nDim+2,k)*val_limiter(i,nDim+2) + val_gradSolu(sID,nDim+2,k)*val_limiter(sID,nDim+2))
                gradKi(k) = 0.5*(val_gradSolu(i,nDim+3,k)*val_limiter(i,nDim+3) + val_gradSolu(sID,nDim+3,k)*val_limiter(sID,nDim+3))
                gradWi(k) = 0.5*(val_gradSolu(i,nDim+4,k)*val_limiter(i,nDim+4) + val_gradSolu(sID,nDim+4,k)*val_limiter(sID,nDim+4))
                !gradTi(k) = 0.5*(val_gradSolu(i,nDim+2,k) + val_gradSolu(sID,nDim+2,k))
                !gradXi(k) = 0.5*(val_gradSolu(i,nDim+3,k) + val_gradSolu(sID,nDim+3,k))
            enddo
            mulFi = 0.5*(val_primvar(i,nDim+3) + val_primvar(sID,nDim+3))
            mutFi = 0.5*(val_primvar(i,nDim+4) + val_primvar(sID,nDim+4))

            muFi = mulFi + mutFi
            ktFi = Cp * (mulFi/prandtlL + mutFi/prandtlT)

            divvFi = gradVi(1,1) + gradVi(2,2) + gradVi(nDim,nDim)*(nDim-2)

            do k=1,nDim
                do s=1,nDim
                    tauFi(s) = muFi*(gradVi(k,s) + gradVi(s,k))
                enddo
                tauFi(k) = tauFi(k) - 2.0/3.0*muFi*divvFi
                sFi = vFi(1)*tauFi(1) + vFi(2)*tauFi(2) + vFi(nDim)*tauFi(nDim)*(nDim-2) + ktFi*gradTi(k)

                fluxFi(     1) = fluxFi(     1) + 0.0
                fluxFi(     2) = fluxFi(     2) + coefFi(k)*tauFi(1)
                fluxFi(     3) = fluxFi(     3) + coefFi(k)*tauFi(2)
                if(nDim==3) then
                fluxFi(     4) = fluxFi(     4) + coefFi(k)*tauFi(nDim)
                endif
                fluxFi(nDim+2) = fluxFi(nDim+2) + coefFi(k)*sFi
            enddo

            coefKi = mulFi + sst_sigmak*mutFi
            coefWi = mulFi + sst_sigmaw*mutFi
            fluxFi(nDim+3) = coefKi*(gradKi(1)*coefFi(1)+gradKi(2)*coefFi(2)+gradKi(nDim)*coefFi(nDim)*(nDim-2))
            fluxFi(nDim+4) = coefWi*(gradWi(1)*coefFi(1)+gradWi(2)*coefFi(2)+gradWi(nDim)*coefFi(nDim)*(nDim-2))

            do k=1,nDim+4
                fluvEi(k) = fluvEi(k) + fluxFi(k)
            end do
        end do

        do j=val_kBelv_Poin(i)+1,val_kBelv_Poin(i+1)
            sID = val_iBelv_Poin(j,1)

            DFi = val_soluBelv(sID,1)
            do k=1,nDim+2
                primFl(k) = val_primBelv(sID,k)
            end do
            primFl(nDim+3) = val_soluBelv(sID,nDim+3)
            primFl(nDim+4) = val_soluBelv(sID,nDim+4)

            do k=1,nDim
                coefFi(k) = val_iCoef_Belv(sID,k)
            end do

            VgnFi = VgFi(1)*coefFi(1) + VgFi(2)*coefFi(2) + VgFi(nDim)*coefFi(nDim)*(nDim-2)

            VnFi = primFl(2)*coefFi(1) + primFl(3)*coefFi(2) + primFl(4)*coefFi(nDim)*(nDim-2) - vgnFi
            sqFi = primFl(2)*primFl(2) + primFl(3)*primFl(3) + primFl(4)*primFl(4   )*(nDim-2)

            fluxFi(     1) = VnFi*DFi
            fluxFi(     2) = VnFi*DFi*primFl(     2) + primFl(1)*coefFi(   1)
            fluxFi(     3) = VnFi*DFi*primFl(     3) + primFl(1)*coefFi(   2)
            fluxFi(nDim+1) = VnFi*DFi*primFl(nDim+1) + primFl(1)*coefFi(nDim)
            fluxFi(nDim+2) = VnFi*(primFl(1)*Gamma/(Gamma-1.0) + 0.5*DFi*sqFi)
            fluxFi(nDim+3) = VnFi*primFl(nDim+3)*val_soluBelv(sID,1)
            fluxFi(nDim+4) = VnFi*primFl(nDim+4)*val_soluBelv(sID,1)

            do k=1,nDim+4
                fluxEi(k) = fluxEi(k) + fluxFi(k)
                fluxFi(k) = 0.0
            end do


            do k=1,nDim
                vFi(k) = primFl(k+1)

                do s=1,nDim
                    gradVi(k,s) = val_gradSolu(i,s+1,k)*val_limiter(i,s+1)
                enddo
                gradTi(k) = val_gradSolu(i,nDim+2,k)*val_limiter(i,nDim+2)
                gradKi(k) = val_gradSolu(i,nDim+3,k)*val_limiter(i,nDim+3)
                gradWi(k) = val_gradSolu(i,nDim+4,k)*val_limiter(i,nDim+4)
            enddo
            mulFi = val_primBelv(sID,nDim+3)
            mutFi = val_primBelv(sID,nDim+4)

            muFi = mulFi + mutFi
            ktFi = Cp * (mulFi/prandtlL + mutFi/prandtlT)

            divvFi = gradVi(1,1) + gradVi(2,2) + gradVi(nDim,nDim)*(nDim-2)

            if(mKD == MARK_WALL .or.mKD == MARK_EQTW .or.mKD == MARK_MOVW) then
                ktFi = 0.0
            elseif(mKD == MARK_SYMM) then
                muFi = 0.0
                !ktFi = 0.0
                divvFi = 0.0
            endif

            do k=1,nDim
                do s=1,nDim
                    tauFi(s) = muFi*(gradVi(k,s) + gradVi(s,k))
                enddo
                tauFi(k) = tauFi(k) - 2.0/3.0*muFi*divvFi
                sFi = vFi(1)*tauFi(1) + vFi(2)*tauFi(2) + vFi(nDim)*tauFi(nDim)*(nDim-2) + ktFi*gradTi(k)

                fluxFi(     1) = fluxFi(     1) + 0.0
                fluxFi(     2) = fluxFi(     2) + coefFi(k)*tauFi(1)
                fluxFi(     3) = fluxFi(     3) + coefFi(k)*tauFi(2)
                if(nDim==3) then
                fluxFi(     4) = fluxFi(     4) + coefFi(k)*tauFi(nDim)
                endif
                fluxFi(nDim+2) = fluxFi(nDim+2) + coefFi(k)*sFi
            enddo

            coefKi = mulFi + sst_sigmak*mutFi
            coefWi = mulFi + sst_sigmaw*mutFi
            fluxFi(nDim+3) = coefKi*(gradKi(1)*coefFi(1)+gradKi(2)*coefFi(2)+gradKi(nDim)*coefFi(nDim)*(nDim-2))
            fluxFi(nDim+4) = coefWi*(gradWi(1)*coefFi(1)+gradWi(2)*coefFi(2)+gradWi(nDim)*coefFi(nDim)*(nDim-2))

            do k=1,nDim+4
                fluvEi(k) = fluvEi(k) + fluxFi(k)
            end do
        end do

        !===turbSour
        if(val_nodeWDis(i) <= 1.0E-7) then
            val_turbNodeAuxv(i,IP_SST_F1) = 0.0
        else
            do k=1,nDim
                do s=1,nDim
                    gradVi(k,s) = val_gradSolu(i,k+1,s)
                enddo
                gradKi(k) = val_gradSolu(i,nDim+3,k)
                gradWi(k) = val_gradSolu(i,nDim+4,k)
            enddo
            DFi = val_soluvar(i,1     )
            kFi = val_soluvar(i,nDim+3)
            wFi = val_soluvar(i,nDim+4)

            mulFi = val_primvar(i,nDim+3)
            mutFi = val_primvar(i,nDim+4)

            WDi = val_nodeWDis(i)

            OmgaFi = 0.0
            SigmFi = 0.0
            divvFi = 0.0
            crssFi = 0.0
            do k=1,nDim
                do s=1,nDim
                    OmgaFi = OmgaFi + (gradVi(k,s)-gradVi(s,k))**2
                    SigmFi = SigmFi + (gradVi(k,s)+gradVi(s,k))**2
                enddo
                divvFi = divvFi + gradVi(k,k)
                crssFi = crssFi + gradKi(k)*gradWi(k)
            enddo
            OmgaFi = 0.5*OmgaFi
            SigmFi = 0.5*SigmFi

            a1 = dsqrt(abs(kFi))/max(SST_BStar*wFi*WDi, 1.0E-36)
            a2 = 500.0D0*mulFi/(DFi*wFi*WDi*WDi)
            a3 = max(2.0D0*DFi*SST_sigmaw2*crssFi/(wFi+1.0E-20), 1.0E-36)
            a4 = 4.0D0*DFi*SST_sigmaw2*kFi/(max(a3, 1.0E-20)*WDi*WDi+1.0E-36)

            arg1 = min(max(a1, a2), a4)
            arg2 = max(2.0*a1, a2)
            F1   = tanh(arg1*arg1*arg1*arg1)
            F2   = tanh(arg2*arg2)

            val_turbNodeAuxv(i,IP_SST_A1) = a1
            val_turbNodeAuxv(i,IP_SST_F1) = F1
            val_turbNodeAuxv(i,IP_SST_F2) = F2
            val_turbNodeAuxv(i,IP_SST_Omega) = OmgaFi
            val_turbNodeAuxv(i,IP_SST_Sigma) = SigmFi

            SST_gamma = SST_gamma1 * F1 + SST_gamma2 * (1.0 - F1)
            SST_belta = SST_belta1 * F1 + SST_belta2 * (1.0 - F1)

            prodK = mutFi*SigmFi - 2.0/3.0*DFi*kFi*divvFi !SST-V
            !prodK = mutEi*OmgaFi - 2.0/3.0*DFi*kFi*divvFi !SST-V
            !prodK = mutEi*SigmFi - 2.0/3.0*(mutEi*divvFi*divvFi + DFi*kFi*divvFi)
            diffK = SST_BStar*DFi*kFi*wFi

            prodK = min(prodK, 10.0D0*diffK) !limiter in SST-2003
            !prodK = min(P0, 20.0D0*turbSST_betaStar*DFi*wTi*kFi) !limiter in SST-Standard

            !prodW = gama*DFi*Omega
            prodW = prodK*SST_gamma*DFi/(mutFi + 1.0E-36)
            diffW = SST_belta*DFi*wFi*wFi

            fluvEi(nDim+3) = fluvEi(nDim+3) + DFi*(prodK - diffK)
            fluvEi(nDim+4) = fluvEi(nDim+4) + DFi*(prodW - diffW + (1-F1)*a3)
        endif

        if(val_isPrec) then
            do k=1,nDim+2
                fluxFi(k) = fluxEi(k)
            enddo

            do k=1,nDim+2
                fluxEi(k) = 0.0
                do s=1,nDim+2
                    fluxEi(k) = fluxEi(k) + val_pMatx(i,k,s)*fluxFi(s)
                enddo
            enddo
        endif

        if(val_isUnst) then
            do k=1,nDim+4
                fluxEi(k) = fluxEi(k) - val_UnstTerm(i,k)
            end do
        endif

        do k=1,nDim+4
            val_Res(i,k) = fluxEi(k) - fluvEi(k)
        end do

    endsubroutine
    !
    attributes(global) subroutine updSolu_RK_Global(&
            val_iCalc_Poin,val_soluOld,val_soluvar, &
            val_primvar,val_turbNodeAuxv,val_Res,   &
            val_DT,val_DTCoef,val_iParm,val_nPoin,  &
            val_nPAux,val_nTMax,val_iRK             )
        use mod_Tuning
        implicit none
        integer,device:: val_iCalc_Poin(val_nPoin)
        real(kind=REALLEN),device:: val_soluOld(val_nPoin, nDim+4)
        real(kind=REALLEN),device:: val_soluvar(val_nPoin, nDim+4)
        real(kind=REALLEN),device:: val_primvar(val_nPoin, nDim+4)
        real(kind=REALLEN),device:: val_turbNodeAuxv(val_nPoin,val_nPAux)
        real(kind=REALLEN),device:: val_Res(val_nPoin, nDim+4)
        real(kind=REALLEN),device:: val_DT(val_nPoin)
        real(kind=REALLEN),device:: val_DTCoef(val_nPoin)
        real(kind=REALLEN),device:: val_iParm(nTotalParm)
        integer,value:: val_nPoin
        integer,value:: val_nPAux
        integer,value:: val_nTMax
        integer,value:: val_iRK
        !
        integer:: i,k
        real(kind=REALLEN):: Gamma, GasCO, Gammo, T0, Ts, Mu0, RKCoef, pMin, pMax, xLim, dMin, dMax, tMin, tMax
        real(kind=REALLEN):: soluEi(nDim+4),primEi(nDim+4),soluDi(nDim+4),limRefC,limtCi
        real(kind=REALLEN):: sstA1,sstOg,sstSg,sstF2
        logical:: isLimtDi
        !
        i = (blockIdx%x-1)*blockDim%x+threadIdx%x
        if(i > val_nTMax) return
        if(val_iCalc_Poin(i) /= FLAG_VALID) return

        Gamma = val_iParm(IP_GAMMA)
        GasCO = val_iParm(IP_GASCO)
        T0    = val_iParm(IP_SutherlandT0)
        Ts    = val_iParm(IP_SutherlandTs)
        Mu0   = val_iParm(IP_SutherlandM0)
        pMin  = val_iParm(IP_LIMMINP)
        pMax  = val_iParm(IP_LIMMAXP)
        xLim  = val_iParm(IP_LIMMAXMuT)

        Gammo = Gamma - 1.0

        RKCoef = val_iParm(IP_RKAlpha+val_iRK)

        do k=1,nDim+4
            soluDi(k) = -RKCoef*val_DT(i)*val_Res(i,k)
        end do

        limRefC = val_iParm(IP_ALLOWCHANGE)
        soluEi(:) = val_soluOld(i,:)
        call flowUpdLimt_Fun3D(soluEi, soluDi, limRefC, Gammo, limtCi, isLimtDi)

        do k=1,nDim+2
            soluEi(k) = val_soluOld(i,k) + soluDi(k)*limtCi
        end do
        soluEi(nDim+3) = (val_soluOld(i,1)*val_soluOld(i,nDim+3) + soluDi(nDim+3)*limtCi)/soluEi(1)
        soluEi(nDim+4) = (val_soluOld(i,1)*val_soluOld(i,nDim+4) + soluDi(nDim+4)*limtCi)/soluEi(1)

        !soluEi(nDim+3) = max(soluEi(nDim+3), 0.0)
        !soluEi(nDim+3) = min(soluEi(nDim+3), 1000000.0)

        dMin = val_iParm(IP_LIMMIND)
        pMin = val_iParm(IP_LIMMINP)
        tMin = val_iParm(IP_LIMMINT)
        dMax = val_iParm(IP_LIMMAXD)
        pMax = val_iParm(IP_LIMMAXP)
        tMax = val_iParm(IP_LIMMAXT)

        call flowLimt_NNeg(soluEi, dMin, dMax, pMin, pMax, tMin, tMax, Gammo, GasCo, isLimtDi)

        if(isLimtDi) then
            val_DTCoef(i) = max(0.01 ,val_DTCoef(i)*0.3)
        else
            val_DTCoef(i) = min(1.0 ,val_DTCoef(i)*1.1)
        endif

        sstA1 = val_turbNodeAuxv(i,IP_SST_A1   )
        sstF2 = val_turbNodeAuxv(i,IP_SST_F2   )
        sstOg = val_turbNodeAuxv(i,IP_SST_OMEGA)
        sstSg = val_turbNodeAuxv(i,IP_SST_SIGMA)

        call getPrimBySolu_TurbSST(soluEi,primEi,Gamma,GasCo,T0,Ts,Mu0,sstA1,pMin,pMax,xLim,sstOg,sstSg,sstF2)

        do k=1,nDim+4
            val_soluvar(i,k) = soluEi(k)
        end do
        do k=1,nDim+4
            val_primvar(i,k) = primEi(k)
        end do

    endsubroutine
    !
    attributes(global) subroutine loopLUSGS_Global(             &
            val_iCalc_Poin,val_iClor_Poin,val_kSate_Poin,       &
            val_iSate_Poin,val_iCoef_Sate,val_iVelo_Poin,val_soluOld, &
            val_deltSolu,val_SRcInfc,val_SRvInfc,val_SRcNode,   &
            val_SRvNode,val_Res,val_DT,val_iParm,val_iClor,     &
            val_iDir,val_nPoin,val_nSate,val_nTMax              )
        use mod_Tuning
        implicit none
        integer,device:: val_iCalc_Poin(val_nPoin)
        integer,device:: val_iClor_Poin(val_nPoin)
        integer,device:: val_kSate_Poin(val_nPoin+1)
        integer,device:: val_iSate_Poin(val_nSate, 2)
        real(kind=REALLEN),device:: val_iCoef_Sate(val_nSate, nDim)
        real(kind=REALLEN),device:: val_iVelo_Poin(val_nPoin, nDim)
        real(kind=REALLEN),device:: val_soluOld(val_nPoin, nDim+4)
        real(kind=REALLEN),device:: val_deltSolu(val_nPoin, nDim+4)
        real(kind=REALLEN),device:: val_SRcInfc(val_nSate)
        real(kind=REALLEN),device:: val_SRvInfc(val_nSate)
        real(kind=REALLEN),device:: val_SRcNode(val_nPoin)
        real(kind=REALLEN),device:: val_SRvNode(val_nPoin)
        real(kind=REALLEN),device:: val_Res(val_nPoin, nDim+4)
        real(kind=REALLEN),device:: val_DT(val_nPoin)
        real(kind=REALLEN),device:: val_iParm(nTotalParm)
        integer,value:: val_iClor
        integer,value:: val_iDir
        integer,value:: val_nPoin
        integer,value:: val_nSate
        integer,value:: val_nTMax
        !
        integer:: i,j,k,sID
        real(kind=REALLEN):: fluxEi(nDim+4),coefFi(nDim),soluEr(nDim+4),soluEo(nDim+4)
        real(kind=REALLEN):: VnEr,VnEo,qEr,qEo,PEr,PEo,SRFi,fluxDi(nDim+4),DTEi,DTEj,Gammo
        real(kind=REALLEN):: DTQi(nDim+4),VgFi(nDim),VgnFi,limRefC,limtCi
        logical:: isLimtDi

        !
        i = (blockIdx%x-1)*blockDim%x+threadIdx%x
        if(i > val_nTMax) return
        if(val_iCalc_Poin(i) /= FLAG_VALID) return
        if(val_iClor_Poin(i) /= val_iClor ) return

        Gammo  = val_iParm(IP_Gamma) - 1.0

        do k=1,nDim+4
            fluxEi(K) = 0.0
        end do
        VgFi(1:nDim) = val_iVelo_Poin(i,1:nDim)

        do j=val_kSate_Poin(i)+1,val_kSate_Poin(i+1)
            sID = val_iSate_Poin(j,1)
            if(val_iCalc_Poin(sID) == FLAG_INVAL) cycle
            if(val_iDir*(val_iClor_Poin(sID)-val_iClor) >= 0) cycle

            do k=1,nDim
                coefFi(k) = val_iCoef_Sate(j,k)
            end do

            do k=1,nDim+4
                soluEo(k) = val_soluOld(sID,k)
                if(k>nDim+2) then
                soluEo(k) = soluEo(k)*soluEo(1)
                endif
                soluEr(k) = soluEo(k) + val_deltSolu(sID,k)
            end do

            VnEr = (soluEr(2)*coefFi(1) + soluEr(3)*coefFi(2) + soluEr(4)*coefFi(nDim)*(nDim-2))/soluEr(1)
            VnEo = (soluEo(2)*coefFi(1) + soluEo(3)*coefFi(2) + soluEo(4)*coefFi(nDim)*(nDim-2))/soluEo(1)
            qEr = 0.5*(soluEr(2)*soluEr(2) + soluEr(3)*soluEr(3) + soluEr(4)*soluEr(4)*(nDim-2))/soluEr(1)
            qEo = 0.5*(soluEo(2)*soluEo(2) + soluEo(3)*soluEo(3) + soluEo(4)*soluEo(4)*(nDim-2))/soluEo(1)

            PEr = Gammo*(soluEr(nDim+2) - qEr)
            PEo = Gammo*(soluEo(nDim+2) - qEo)

            SRFi = val_iParm(IP_ImplicitW) * val_SRcInfc(j) + val_SRvInfc(j)

            fluxDi(     1) = VnEr*soluEr(     1) - VnEo*soluEo(     1)
            fluxDi(     2) = VnEr*soluEr(     2) - VnEo*soluEo(     2) + (PEr-PEo)*coefFi(   1)
            fluxDi(     3) = VnEr*soluEr(     3) - VnEo*soluEo(     3) + (PEr-PEo)*coefFi(   2)
            fluxDi(nDim+1) = VnEr*soluEr(nDim+1) - VnEo*soluEo(nDim+1) + (PEr-PEo)*coefFi(nDim)
            fluxDi(nDim+2) = VnEr*soluEr(nDim+2) - VnEo*soluEo(nDim+2) + (PEr*VnEr - PEo*VnEo)
            fluxDi(nDim+3) = VnEr*soluEr(nDim+3) - VnEo*soluEo(nDim+3)
            fluxDi(nDim+4) = VnEr*soluEr(nDim+4) - VnEo*soluEo(nDim+4)

            do k=1,nDim+2
                fluxEi(k) = fluxEi(k) + fluxDi(k) - SRFi*(soluEr(k) - soluEo(k))
            end do
        end do

        DTEi = 1.0/(1.0/val_DT(i) + val_iParm(IP_ImplicitW)*val_SRcNode(i) + val_SRvNode(i))
        DTEj = 1.0/(1.0/val_DT(i) + val_iParm(IP_ImplicitW)*val_SRcNode(i) + 2.0*val_SRvNode(i))
        DTQi(1:nDim+2) = DTEi
        DTQi(nDim+3  ) = DTEj !val_DT(i)*0.5
        DTQi(nDim+4  ) = DTEj !val_DT(i)*0.5

        if(val_iDir > 0) then
            do k=1,nDim+4
                fluxEi(k) = -(val_Res(i,k) + 0.5*fluxEi(k))*DTQi(k)
            end do
        else
            do k=1,nDim+4
                fluxEi(k) = val_deltSolu(i,k) - 0.5*fluxEi(k)*DTQi(k)
            end do
        end if

        limRefC = val_iParm(IP_ALLOWCHANGE)
        soluEo(1:nDim+4) = val_soluOld(i,1:nDim+4)
        call flowUpdLimt_Fun3D(soluEo, fluxEi, limRefC, Gammo, limtCi, isLimtDi)

        do k=1,nDim+4
            val_deltSolu(i,k) = fluxEi(k)*limtCi
        end do

    endsubroutine
    !
    !attributes(global)
    attributes(global) subroutine updSoluAndDTCoef_Global(val_iCalc_Poin,       &
                    val_soluvar,val_primvar,val_soluOld,val_deltSolu,val_DTCoef,&
                    val_turbNodeAuxv,val_iParm,val_nPoin,val_nPAux,val_nTMax    )
        use mod_Tuning
        implicit none
        integer,device:: val_iCalc_Poin(val_nPoin)
        real(kind=REALLEN),device:: val_soluvar(val_nPoin,nDim+4)
        real(kind=REALLEN),device:: val_primvar(val_nPoin,nDim+4)
        real(kind=REALLEN),device:: val_soluOld(val_nPoin,nDim+4)
        real(kind=REALLEN),device:: val_deltSolu(val_nPoin,nDim+4)
        real(kind=REALLEN),device:: val_DTCoef(val_nPoin)
        real(kind=REALLEN),device:: val_turbNodeAuxv(val_nPoin,val_nPAux)
        real(kind=REALLEN),device:: val_iParm(nTotalParm)
        integer,value:: val_nPoin
        integer,value:: val_nPAux
        integer,value:: val_nTMax
        !
        integer:: i,k
        logical:: isLimtDi,isLimtEi,isSoluNAN
        real(kind=REALLEN):: Gamma,GasCO,Gammo,T0,Ts,Mu0,pMin,pMax,xLim,dMin,dMax,tMin,tMax
        real(kind=REALLEN):: soluEi(nDim+4),primEi(nDim+4)
        real(kind=REALLEN):: sstA1,sstOg,sstSg,sstF2
        logical:: isLimtDi

        i = (blockIdx%x-1)*blockDim%x+threadIdx%x
        if(i > val_nTMax) return
        if(val_iCalc_Poin(i) /= FLAG_VALID) return

        Gamma = val_iParm(IP_GAMMA)
        GasCO = val_iParm(IP_GASCO)
        T0    = val_iParm(IP_SutherlandT0)
        Ts    = val_iParm(IP_SutherlandTs)
        Mu0   = val_iParm(IP_SutherlandM0)
        !Cv1   = val_iParm(IP_TurbSST_Cv1)
        pMin  = val_iParm(IP_LIMMINP)
        pMax  = val_iParm(IP_LIMMAXP)
        xLim  = val_iParm(IP_LIMMAXMuT)


        do k=1,nDim+2
            soluEi(k) = val_soluvar(i,k) + val_deltSolu(i,k)
        end do
        soluEi(nDim+3) = (val_soluvar(i,1)*val_soluvar(i,nDim+3) + val_deltSolu(nDim+3))/soluEi(1)
        soluEi(nDim+4) = (val_soluvar(i,1)*val_soluvar(i,nDim+4) + val_deltSolu(nDim+4))/soluEi(1)


        Gammo = Gamma - 1.0
        dMin = val_iParm(IP_LIMMIND)
        pMin = val_iParm(IP_LIMMINP)
        tMin = val_iParm(IP_LIMMINT)
        dMax = val_iParm(IP_LIMMAXD)
        pMax = val_iParm(IP_LIMMAXP)
        tMax = val_iParm(IP_LIMMAXT)

        call flowLimt_NNeg(soluEi, dMin, dMax, pMin, pMax, tMin, tMax, Gammo, GasCo, isLimtDi)

        if(isLimtDi) then
            val_DTCoef(i) = max(0.01 ,val_DTCoef(i)*0.3)
        else
            val_DTCoef(i) = min(1.0 ,val_DTCoef(i)*1.1)
        endif

        sstA1 = val_turbNodeAuxv(i,IP_SST_A1   )
        sstF2 = val_turbNodeAuxv(i,IP_SST_F2   )
        sstOg = val_turbNodeAuxv(i,IP_SST_OMEGA)
        sstSg = val_turbNodeAuxv(i,IP_SST_SIGMA)

        call getPrimBySolu_TurbSST(soluEi,primEi,Gamma,GasCo,T0,Ts,Mu0,sstA1,pMin,pMax,xLim,sstOg,sstSg,sstF2)

        do k=1,nDim+4
            val_soluvar(i,k) = soluEi(k)
        end do
        do k=1,nDim+4
            val_primvar(i,k) = primEi(k)
        end do

    endsubroutine
    !
end module mod_TurbSST_GPUFunc