!
!---------------------------------------------
!
!---------------------------------------------
module mod_addExteRegnToBelv
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    implicit none
    !
    type:: typ_Belv
        integer:: nBelv
        !
        integer,allocatable:: iRegnPlace(:)
        integer,allocatable:: iBelvProp(:,:)
        real(kind=REALLEN),allocatable:: iCoor_Belv(:,:)
        real(kind=REALLEN),allocatable:: iArea_Belv(:)
        real(kind=REALLEN),allocatable:: iNvor_Belv(:,:)
        real(kind=REALLEN),allocatable:: iCoef_Belv(:,:)
        !
    endtype
    !
contains
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine addExteRegnToBelv(val_iPart)
        use mod_Project
        use mod_Interface_AllocateArray
        implicit none
        integer,intent(in):: val_iPart
        !
        integer:: i
        integer:: nValid,nBelvAdd
        type(typ_Belv):: iOrgnBelv
        type(typ_Belv):: iAppdBelv
        
        
        !---------------------------------------------
        call getNBelvAdd(val_iPart, nBelvAdd)
        if(nBelvAdd <= 0) return
        
        call iniTypBelv(iAppdBelv, nBelvAdd)
        
        call getIBelvAdd(val_iPart, iAppdBelv)
        
        
        !---------------------------------------------
        call iniTypBelv(iOrgnBelv, iPartMesh(val_iPart)%nBelv)
        iOrgnBelv%iBelvProp = iPartMesh(val_iPart)%iBelvProp
        iOrgnBelv%iCoor_Belv = iPartMesh(val_iPart)%iCoor_Belv
        iOrgnBelv%iArea_Belv = iPartMesh(val_iPart)%iArea_Belv
        iOrgnBelv%iNvor_Belv = iPartMesh(val_iPart)%iNvor_Belv
        iOrgnBelv%iCoef_Belv = iPartMesh(val_iPart)%iCoef_Belv
        
        nValid = 0
        do i=1,iPartMesh(val_iPart)%nBelv
            if(iPartMesh(val_iPart)%iBelvProp(i,2) > 0) then
                nValid = max(i, nValid)
            endif
        enddo
        
        
        !---------------------------------------------
        iPartMesh(val_iPart)%nBelv = nValid + nBelvAdd
        call allocateArray(iPartMesh(val_iPart)%iBelvProp , iPartMesh(val_iPart)%nBelv, 2)
        call allocateArray(iPartMesh(val_iPart)%iCoor_Belv, iPartMesh(val_iPart)%nBelv, nDim)
        call allocateArray(iPartMesh(val_iPart)%iArea_Belv, iPartMesh(val_iPart)%nBelv)
        call allocateArray(iPartMesh(val_iPart)%iNvor_Belv, iPartMesh(val_iPart)%nBelv, nDim)
        call allocateArray(iPartMesh(val_iPart)%iCoef_Belv, iPartMesh(val_iPart)%nBelv, nDim)
        
        iMeshMapp(val_iPart)%nRSate = iGlobalMesh%nSate
        iMeshMapp(val_iPart)%nBBelv = iPartMesh(val_iPart)%nBelv
        call allocateArray(iMeshMapp(val_iPart)%iMapRToB_SaBv, iMeshMapp(val_iPart)%nRSate)
        call allocateArray(iMeshMapp(val_iPart)%iMapBToR_SaBv, iMeshMapp(val_iPart)%nBBelv)
        
        do i=1,nValid
            iPartMesh(val_iPart)%iBelvProp(i ,:) = iOrgnBelv%iBelvProp(i ,:)
            iPartMesh(val_iPart)%iCoor_Belv(i,:) = iOrgnBelv%iCoor_Belv(i,:)
            iPartMesh(val_iPart)%iArea_Belv(i  ) = iOrgnBelv%iArea_Belv(i  )
            iPartMesh(val_iPart)%iNvor_Belv(i,:) = iOrgnBelv%iNvor_Belv(i,:)
            iPartMesh(val_iPart)%iCoef_Belv(i,:) = iOrgnBelv%iCoef_Belv(i,:)
        enddo
        
        iMeshMapp(val_iPart)%iMapRToB_SaBv(:) = 0
        iMeshMapp(val_iPart)%iMapBToR_SaBv(:) = 0
        
        do i=1,nBelvAdd
            iPartMesh(val_iPart)%iBelvProp(nValid+i ,:) = iAppdBelv%iBelvProp(i ,:)
            iPartMesh(val_iPart)%iCoor_Belv(nValid+i,:) = iAppdBelv%iCoor_Belv(i,:)
            iPartMesh(val_iPart)%iArea_Belv(nValid+i  ) = iAppdBelv%iArea_Belv(i  )
            iPartMesh(val_iPart)%iNvor_Belv(nValid+i,:) = iAppdBelv%iNvor_Belv(i,:)
            iPartMesh(val_iPart)%iCoef_Belv(nValid+i,:) = iAppdBelv%iCoef_Belv(i,:)
            
            iMeshMapp(val_iPart)%iMapRToB_SaBv(iAppdBelv%iRegnPlace(i)) = nValid+i
            iMeshMapp(val_iPart)%iMapBToR_SaBv(nValid+i) = iAppdBelv%iRegnPlace(i)
        enddo
        
    endsubroutine
    !
    subroutine iniTypBelv(val_iBelv,val_nBelv)
        use mod_Interface_AllocateArray
        implicit none
        type(typ_Belv),intent(inout):: val_iBelv
        integer,intent(in):: val_nBelv
        
        
        call allocateArray(val_iBelv%iRegnPlace, val_nBelv)
        call allocateArray(val_iBelv%iBelvProp , val_nBelv, 2)
        call allocateArray(val_iBelv%iCoor_Belv, val_nBelv, nDim)
        call allocateArray(val_iBelv%iArea_Belv, val_nBelv)
        call allocateArray(val_iBelv%iNvor_Belv, val_nBelv, nDim)
        call allocateArray(val_iBelv%iCoef_Belv, val_nBelv, nDim)
        
    endsubroutine
    !
    subroutine getNBelvAdd(val_iPart,val_nAdd)
        use mod_Project, only: iGlobalMesh
        implicit none
        integer,intent(in):: val_iPart
        integer,intent(out):: val_nAdd
        !
        integer:: i,iCount,poinL
        
        iCount = 0
        do i=1,iGlobalMesh%iExteRegnSANO%nSate
            poinL = iGlobalMesh%iExteRegnSANO%iSate_Poin(i,2)
            
            if(iGlobalMesh%iPart_Poin(poinL) == val_iPart) then
                iCount = iCount + 1
            endif
        enddo
        val_nAdd = iCount
            
    endsubroutine
    !
    subroutine getIBelvAdd(val_iPart,val_iBelv)
        use mod_Project
        implicit none
        integer,intent(in):: val_iPart
        type(typ_Belv),intent(inout):: val_iBelv
        !
        integer:: i,iCount,poinL,poinR,poinN
        
        iCount = 0
        do i=1,iGlobalMesh%iExteRegnSANO%nSate
            poinR = iGlobalMesh%iExteRegnSANO%iSate_Poin(i,1)
            poinL = iGlobalMesh%iExteRegnSANO%iSate_Poin(i,2)
        
            poinN = iMeshMapp(val_iPart)%iMapGToL_Poin(poinL)
            
            if(iGlobalMesh%iPart_Poin(poinL) == val_iPart) then
                iCount = iCount + 1
            
                val_iBelv%iRegnPlace(iCount  ) = i
            
                val_iBelv%iBelvProp(iCount, 1) = poinN
                if(iGlobalMesh%iPart_Poin(poinR) == val_iPart) then
                val_iBelv%iBelvProp(iCount, 2) = -iMeshMapp(val_iPart)%iMapGToL_Poin(poinR)
                else
                val_iBelv%iBelvProp(iCount, 2) = 0
                endif
                
                val_iBelv%iCoor_Belv(iCount,:) = iGlobalMesh%iCoor_Poin(:,poinR)
            
                val_iBelv%iArea_Belv(iCount  ) = 0.0
                val_iBelv%iNvor_Belv(iCount,:) = 0.0
                val_iBelv%iCoef_Belv(iCount,:) = 0.0
            endif
        enddo
            
    endsubroutine
    !
endmodule
!
    
    