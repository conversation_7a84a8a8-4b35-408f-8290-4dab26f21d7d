module mod_saveAeroFM
    !
contains
    !
subroutine saveAeroFM(val_iParm,val_strFlag,val_iIter,val_isViscFlow)
    use mod_TypeDef_Parm
    use mod_WorkPath
    use mod_PreDefine_IOPort
    use mod_strOfNumber
    use mod_MPIEnvironment
    implicit none
    type(typ_Parm),intent(in):: val_iParm
    character(len=STRLEN),intent(in):: val_strFlag
    integer,intent(in):: val_iIter
    logical,intent(in):: val_isViscFlow
    !
    integer:: i,wallNum
    character(len=STRLEN):: fileName
    

    wallNum = 0.0
    do i=1,val_iParm%nMark
        if(val_iParm%isMarkWall(i)) then
            wallNum = wallNum + 1
        endif
    enddo
    
    !==============================================
    !==============================================
    if(nProcess == 1) then
        fileName = "AeroFM_"//trim(val_strFlag)//'.dat'
    else
        fileName = "AeroFM_"//trim(val_strFlag)//'_P'//trim(strTwo_Number(myID))//'.dat'
    endif

    open(ioPort_Full,file=trim(resuFullPath)//'/'//trim(fileName),asynchronous='yes',status='unknown')
    
        write(ioPort_Full,*) 'TOTAL===================TOTAL'
        if(nDim == 2) then
        write(ioPort_Full,*) '           CFx      |    CFy      |    CFz      |    CMomt'
        elseif(nDim == 3) then
        write(ioPort_Full,*) '           CFx      |    CFy      |    CFz      |     CMx     |     CMy     |     CMz'
        endif
        write(ioPort_Full,'(6X,12E14.6)') val_iParm%avgAeroFM%totalCForce(1:nDim), &
                val_iParm%avgAeroFM%totalCMomnt(1:2*nDim-3)
            
        write(ioPort_Full,*) '============================='
        write(ioPort_Full,*) '============================='
        write(ioPort_Full,*) ' nWall =',wallNum
        if(nDim == 2) then  
        write(ioPort_Full,*) ' iMark|    CFx      |    CFy      |    CFz      |    CMomt'
        elseif(nDim == 3) then
        write(ioPort_Full,*) ' iMark|    CFx      |    CFy      |    CFz      |     CMx     |     CMy     |     CMz'
        endif
        
        write(ioPort_Full,*) '===========PRES=============='
        do i=1,val_iParm%nMark
            if(val_iParm%isMarkWall(i)) then
                write(ioPort_Full,'(I6,12E14.6)') i, val_iParm%avgAeroFM%surfaceCForce_pres(1:nDim,i), &
                        val_iParm%avgAeroFM%surfaceCMomnt_pres(1:2*nDim-3,i)
            endif
        enddo
        
        if(val_isViscFlow) then
            write(ioPort_Full,*) '===========VISC=============='
            do i=1,val_iParm%nMark
                if(val_iParm%isMarkWall(i)) then
                    write(ioPort_Full,'(I6,12E14.6)') i, val_iParm%avgAeroFM%surfaceCForce_visc(1:nDim,i), &
                            val_iParm%avgAeroFM%surfaceCMomnt_visc(1:2*nDim-3,i)
                endif
            enddo
            
            write(ioPort_Full,*) '===========SUM==============='
            do i=1,val_iParm%nMark
                if(val_iParm%isMarkWall(i)) then
                    write(ioPort_Full,'(I6,12E14.6)') i, val_iParm%avgAeroFM%surfaceCForce_total(1:nDim,i), &
                            val_iParm%avgAeroFM%surfaceCMomnt_total(1:2*nDim-3,i)
                endif
            enddo
        endif
        
        write(ioPort_Full,*) '============================='
        
    close(ioPort_Full)
        
endsubroutine
!

endmodule
