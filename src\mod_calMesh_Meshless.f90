!
module mod_calMesh_Meshless
    implicit none
    !
    integer,parameter:: WTYPR_DSQ_INV = 0
    integer,parameter:: WTYPR_DIS_INV = 1
    integer,parameter:: WTYPR_ONE     = 2
    
contains
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getICoef_WLSF(val_dCoor,val_iCoef,val_nSate,val_WType,val_isOK)
        use mod_PreDefine_Dimension
        use mod_PreDefine_Precision
        use MAT_INV
        use mod_MatrixInversion
        use ieee_arithmetic
        implicit none
        real(kind=REALLEN),intent(in):: val_dCoor(nDim,val_nSate)
        real(kind=REALLEN),intent(inout):: val_iCoef(nDim,val_nSate)
        integer,intent(in):: val_nSate
        integer,intent(in):: val_WType
        logical,intent(out):: val_isOK
        !
        integer:: i,j,k
        real(kind=8):: iLenSq(val_nSate),iLenFi(val_nSate),iMSD,iWeight
        real(kind=8):: iMatrixA(nDim+1,nDim+1),iVector(nDim+1)
        real(kind=8):: iCoef(nDim),iMatrix(nDim+1,nDim+1),iSum
    
        
        if(val_WType == WTYPR_DSQ_INV) then
            !---------------------------------------------
            iLenSq(:) = 0.0
            iMSD = 1.0E+10
            do i=1,val_nSate
                do j=1,nDim
                    iLenSq(i) = iLenSq(i) + val_dCoor(j,i)*val_dCoor(j,i)
                enddo
                iLenSq(i) = 0.25*iLenSq(i)
        
                iMSD = min(iMSD,iLenSq(i))
            enddo
    
    
            !---------------------------------------------
            iMatrixA(:,:) = 0.0
            do i=1,val_nSate
                iVector(1       ) = 1.0
                iVector(2:nDim+1) = 0.5*val_dCoor(:,i)
        
                iWeight = iMSD/iLenSq(i)
        
                do j=1,nDim+1
                    do k=1,nDim+1
                        iMatrix(j,k) = iWeight*iVector(j)*iVector(k)
                    enddo
                enddo
        
                iMatrixA(:,:) = iMatrixA(:,:) + iMatrix(:,:)
            enddo
    
            !call INV(iMatrixA, iMatrix, nDim+1)
            call getMatrixInv(iMatrixA, iMatrix, nDim+1)
            
    
            do i=1,nDim+1
                do j=1,nDim+1
                    if(IEEE_IS_NAN(iMatrix(i,j))) then
                        val_isOK = .false.
                        return
                    endif
                enddo
            enddo
    
            !---------------------------------------------
            do i=1,val_nSate
                iVector(1       ) = 1.0
                iVector(2:nDim+1) = 0.5*val_dCoor(:,i)
        
                iWeight = iMSD/iLenSq(i)
        
                iCoef(:) = 0
                do j=1,nDim+1
                    iCoef(:) = iCoef(:) + iWeight*iMatrix(2:nDim+1,j)*iVector(j)
                enddo
            
                val_iCoef(:,i) = iCoef(:)
            enddo
        
        
            do i=1,nDim
                iSum = 0.0
                do j=1,val_nSate
                    iSum = iSum + val_iCoef(i,j)
                enddo
            
                if(abs(iSum) > 1.0E-3) then
                    val_isOk = .false.
                    return
                endif
            enddo
        
        
            val_isOK = .true.
        
        elseif(val_WType == WTYPR_DIS_INV) then
            !---------------------------------------------
            iLenFi(:) = 0.0
            iMSD = 1.0E+10
            do i=1,val_nSate
                do j=1,nDim
                    iLenFi(i) = iLenFi(i) + val_dCoor(j,i)*val_dCoor(j,i)
                enddo
                iLenFi(i) = 0.5*sqrt(iLenFi(i))
        
                iMSD = min(iMSD,iLenFi(i))
            enddo
    
    
            !---------------------------------------------
            iMatrixA(:,:) = 0.0
            do i=1,val_nSate
                iVector(1       ) = 1.0
                iVector(2:nDim+1) = 0.5*val_dCoor(:,i)
        
                iWeight = iMSD/iLenFi(i)
        
                do j=1,nDim+1
                    do k=1,nDim+1
                        iMatrix(j,k) = iWeight*iVector(j)*iVector(k)
                    enddo
                enddo
        
                iMatrixA(:,:) = iMatrixA(:,:) + iMatrix(:,:)
            enddo
    
            call INV(iMatrixA, iMatrix, nDim+1)
    
            do i=1,nDim+1
                do j=1,nDim+1
                    if(IEEE_IS_NAN(iMatrix(i,j))) then
                        val_isOK = .false.
                        return
                    endif
                enddo
            enddo
    
            !---------------------------------------------
            do i=1,val_nSate
                iVector(1       ) = 1.0
                iVector(2:nDim+1) = 0.5*val_dCoor(:,i)
        
                iWeight = iMSD/iLenFi(i)
        
                iCoef(:) = 0
                do j=1,nDim+1
                    iCoef(:) = iCoef(:) + iWeight*iMatrix(2:nDim+1,j)*iVector(j)
                enddo
            
                val_iCoef(:,i) = iCoef(:)
            enddo
        
        
            do i=1,nDim
                iSum = 0.0
                do j=1,val_nSate
                    iSum = iSum + val_iCoef(i,j)
                enddo
            
                if(abs(iSum) > 1.0E-3) then
                    val_isOk = .false.
                    return
                endif
            enddo
        
        
            val_isOK = .true.
        
        
        elseif(val_WType == WTYPR_ONE) then    
            !---------------------------------------------
            iMatrixA(:,:) = 0.0
            do i=1,val_nSate
                iVector(1       ) = 1.0
                iVector(2:nDim+1) = 0.5*val_dCoor(:,i)
        
                iWeight = 1.0
        
                do j=1,nDim+1
                    do k=1,nDim+1
                        iMatrix(j,k) = iWeight*iVector(j)*iVector(k)
                    enddo
                enddo
        
                iMatrixA(:,:) = iMatrixA(:,:) + iMatrix(:,:)
            enddo
    
            call INV(iMatrixA, iMatrix, nDim+1)
    
            do i=1,nDim+1
                do j=1,nDim+1
                    if(IEEE_IS_NAN(iMatrix(i,j))) then
                        val_isOK = .false.
                        return
                    endif
                enddo
            enddo
    
            !---------------------------------------------
            do i=1,val_nSate
                iVector(1       ) = 1.0
                iVector(2:nDim+1) = 0.5*val_dCoor(:,i)
        
                iWeight = 1.0
        
                iCoef(:) = 0
                do j=1,nDim+1
                    iCoef(:) = iCoef(:) + iWeight*iMatrix(2:nDim+1,j)*iVector(j)
                enddo
            
                val_iCoef(:,i) = iCoef(:)
            enddo
        
        
            do i=1,nDim
                iSum = 0.0
                do j=1,val_nSate
                    iSum = iSum + val_iCoef(i,j)
                enddo
            
                if(abs(iSum) > 1.0E-3) then
                    val_isOk = .false.
                    return
                endif
            enddo
        
        
            val_isOK = .true.
        
        
        
        endif
        
    
    endsubroutine
    !
endmodule
!
    
    