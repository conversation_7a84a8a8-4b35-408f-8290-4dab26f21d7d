
!---------------------------------------------
!
!---------------------------------------------
module mod_MeshReordering
    use mod_TypeDef_Mesh
    use mod_TypeDef_MeshMapping
    use mod_PreDefine_IOPort
    use mod_Interface_AllocateArray
    implicit none
    integer,parameter:: ReKind_Poin = 1
    integer,parameter:: ReKind_Sate = 2
    integer          :: iReKind     = 0
    !
    integer,parameter:: nPoinPerAccess = 16
    !
    type(typ_Mesh       ):: iReMesh
    type(typ_meshMapping):: iReMapp
    !
contains
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine bldIReMesh_Poin(val_iMesh,val_iMeshMapp)
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        type(typ_MeshMapping),intent(in):: val_iMeshMapp
        !
        integer:: i,poinOD
        
        
        !
        iReMesh%nClor = val_iMesh%nClor
        iReMesh%nPoin = val_iMeshMapp%nLPoin !- val_iMeshMapp%nLExte
        !
        call allocateArray(iReMesh%iCoor_Poin, nDim, iReMesh%nPoin)
        call allocateArray(iReMesh%iClor_Poin, iReMesh%nPoin)
        iReMesh%iCoor_Poin(:,:) = 0.0
        iReMesh%iClor_Poin(:)   = 1
        !
        do i=1,iReMesh%nPoin
            poinOD = val_iMeshMapp%iMapLToG_Poin(i)
            
            iReMesh%iCoor_Poin(:,i) = val_iMesh%iCoor_Poin(:,poinOD)
            iReMesh%iClor_Poin(i)   = val_iMesh%iClor_Poin(poinOD)
        enddo
        
    endsubroutine   
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine bldIReMesh_Sate(val_iMesh,val_iMeshMapp)
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        type(typ_MeshMapping),intent(in):: val_iMeshMapp
        !
        integer:: i,j,k
        integer:: poinOD,sateOP,sateOD,sateND,nSate
        logical:: isOk
        
        
        !---------------------------------------------
        !---------------------------------------------
        iReMesh%nPoin = val_iMeshMapp%nLPoin !- val_iMeshMapp%nLExte
        iReMesh%nSate = val_iMeshMapp%nLSate
        
        call allocateArray(iReMesh%kSate_Poin, iReMesh%nPoin+1)
        call allocateArray(iReMesh%iSate_Poin, iReMesh%nSate, 1)
        iReMesh%kSate_Poin(:)   = 0
        iReMesh%iSate_Poin(:,:) = 0
        
        
        !---------------------------------------------
        !---------------------------------------------
        do i=1,iReMesh%nPoin
            poinOD = val_iMeshMapp%iMapLToG_Poin(i)
            
            if(poinOD > 0) then
                iReMesh%kSate_Poin(i+1) = val_iMesh%kSate_Poin(poinOD+1) - &
                                          val_iMesh%kSate_Poin(poinOD)
            endif
            
            iReMesh%kSate_Poin(i+1) = iReMesh%kSate_Poin(i+1) + &
                                      iReMesh%kSate_Poin(i)
        enddo
        
        
        !---------------------------------------------
        !---------------------------------------------
        do i=1,iReMesh%nSate
            sateOP = val_iMeshMapp%iMapLToG_Sate(i)
            
            sateOD = val_iMesh%iSate_Poin(sateOP,1)
            
            sateND = val_iMeshMapp%iMapGToL_Poin(sateOD)
            
            iReMesh%iSate_Poin(i,1) = sateND
        enddo
        
        
        !---------------------------------------------
        !---------------------------------------------
        isOk = .true.
        do i=1,iReMesh%nPoin
            poinOD = val_iMeshMapp%iMapLToG_Poin(i)
            
            nSate = iReMesh%kSate_Poin(i+1) -iReMesh%kSate_Poin(i)
            
            if(poinOD /= 0) then
                if(nSate /= val_iMesh%kSate_Poin(poinOD+1)-val_iMesh%kSate_Poin(poinOD)) then
                    write(ioPort_Out,*) 'E1'
                    isOk = .false.
                    exit
                endif
                
                do j=iReMesh%kSate_Poin(i)+1,iReMesh%kSate_Poin(i+1)
                    sateOP = j - iReMesh%kSate_Poin(i) + val_iMesh%kSate_Poin(poinOD)
                    
                    sateOD = val_iMesh%iSate_Poin(sateOP,1)
                    sateND = val_iMeshMapp%iMapGToL_Poin(sateOD)
                    
                    if(sateND /= iReMesh%iSate_Poin(j,1)) then
                        write(ioPort_Out,*) 'E2'
                        isOk = .false.
                        exit
                    endif
                enddo
                
            else
                if(nSate /= 0) then
                    write(ioPort_Out,*) 'E3'
                    isOk = .false.
                    exit
                endif
                
            endif
        enddo
        
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine updIMeshMapp_Poin(val_iPart)
        use mod_Project
        implicit none
        integer,intent(in):: val_iPart
        !
        integer:: i,j,k
        integer:: poinID,poinOD,poinND,sateIP,sateOP,sateNP
        integer,allocatable:: kSate_Poin(:)
        type(typ_MeshMapping):: iTempMapp
        
        
        !---------------------------------------------
        !---------------------------------------------
        if(iReMapp%nGPoin == 0) return
        if(iMeshMapp(val_iPart)%nLPoin /= iReMapp%nGPoin) then
            write(ioPort_Out,*) 'Error.'
            stop
        endif
        
        
        !---------------------------------------------
        !---------------------------------------------
        iTempMapp%nGPoin = iMeshMapp(val_iPart)%nGPoin
        iTempMapp%nGSate = iMeshMapp(val_iPart)%nGSate
        iTempMapp%nLPoin = iReMapp%nLPoin
        iTempMapp%nLSate = iMeshMapp(val_iPart)%nLSate
        call allocateArray(iTempMapp%iMapGToL_Poin, iTempMapp%nGPoin)
        call allocateArray(iTempMapp%iMapLToG_Poin, iTempMapp%nLPoin)
        call allocateArray(iTempMapp%iMapGToL_Sate, iTempMapp%nGSate)
        call allocateArray(iTempMapp%iMapLToG_Sate, iTempMapp%nLSate)
        
        iTempMapp%iMapGToL_Poin(:) = 0
        iTempMapp%iMapLToG_Poin(:) = 0
        iTempMapp%iMapGToL_Sate(:) = 0
        iTempMapp%iMapLToG_Sate(:) = 0
        
        
        
        !---------------------------------------------
        !---------------------------------------------
        do i=1,iMeshMapp(val_iPart)%nLPoin
            poinOD = iMeshMapp(val_iPart)%iMapLToG_Poin(i)
            poinND = iReMapp%iMapGToL_Poin(i)
            
            if(poinND <= 0) then
                write(ioPort_Out,*) 'poinND <= 0'
                stop
            endif
            
            iTempMapp%iMapLToG_Poin(poinND) = poinOD
            
            if(poinOD > 0) then
                iTempMapp%iMapGToL_Poin(poinOD) = poinND
            endif
            
        enddo
            
        
        !---------------------------------------------
        !---------------------------------------------        
        allocate(kSate_Poin(iReMapp%nLPoin + 1))
        kSate_Poin(:) = 0
        
        do i=1,iTempMapp%nLPoin
            poinOD = iTempMapp%iMapLToG_Poin(i)
            
            if(poinOD > 0) then
                kSate_Poin(i+1) = iGlobalMesh%kSate_Poin(poinOD + 1) - &
                                    iGlobalMesh%kSate_Poin(poinOD)
            endif
            
            kSate_Poin(i+1) = kSate_Poin(i+1) + kSate_Poin(i)
        enddo
        
        
        do i=1,iTempMapp%nLPoin
            poinOD = iTempMapp%iMapLToG_Poin(i)
            
            if(poinOD > 0) then
                do j=iGlobalMesh%kSate_Poin(poinOD)+1,iGlobalMesh%kSate_Poin(poinOD+1)
                    kSate_Poin(i) = kSate_Poin(i) + 1
                    
                    sateOP = j
                    sateNP = kSate_Poin(i)
                    
                    iTempMapp%iMapGToL_Sate(sateOP) = sateNP
                    iTempMapp%iMapLToG_Sate(sateNP) = sateOP
                enddo
            endif
        enddo
        
        deallocate(kSate_Poin)
        
        
        !---------------------------------------------
        !---------------------------------------------  
        if(iMeshMapp(val_iPart)%nLPoin /= iReMapp%nLPoin) then
            iMeshMapp(val_iPart)%nLPoin = iReMapp%nLPoin
            
            call allocateArray(iMeshMapp(val_iPart)%iMapLToG_Poin, iMeshMapp(val_iPart)%nLPoin)
        endif
            
        iMeshMapp(val_iPart)%iMapGToL_Poin(:) = iTempMapp%iMapGToL_Poin(:)
        iMeshMapp(val_iPart)%iMapLToG_Poin(:) = iTempMapp%iMapLToG_Poin(:)
        iMeshMapp(val_iPart)%iMapGToL_Sate(:) = iTempMapp%iMapGToL_Sate(:)
        iMeshMapp(val_iPart)%iMapLToG_Sate(:) = iTempMapp%iMapLToG_Sate(:)
        
        if(allocated(iTempMapp%iMapGToL_Poin)) deallocate(iTempMapp%iMapGToL_Poin)
        if(allocated(iTempMapp%iMapLToG_Poin)) deallocate(iTempMapp%iMapLToG_Poin)
        if(allocated(iTempMapp%iMapGToL_Sate)) deallocate(iTempMapp%iMapGToL_Sate)
        if(allocated(iTempMapp%iMapLToG_Sate)) deallocate(iTempMapp%iMapLToG_Sate)
        
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine updIMeshMapp_Sate(val_iPart)
        use mod_Project
        implicit none
        integer,intent(in):: val_iPart
        !
        integer:: i
        integer:: sateOP,sateNP
        type(typ_MeshMapping):: iTempMapp
        
        
        !---------------------------------------------
        !---------------------------------------------
        if(iReMapp%nGPoin == 0) return
        if(iMeshMapp(val_iPart)%nLSate /= iReMapp%nGSate) then
            write(ioPort_Out,*) 'Error.'
            stop
        endif
        
        
        !---------------------------------------------
        !---------------------------------------------
        iTempMapp%nGSate = iMeshMapp(val_iPart)%nGSate
        iTempMapp%nLSate = iReMapp%nLSate
        allocate(iTempMapp%iMapGToL_Sate(iTempMapp%nGSate))
        allocate(iTempMapp%iMapLToG_Sate(iTempMapp%nLSate))
        
        iTempMapp%iMapGToL_Sate(:) = 0
        iTempMapp%iMapLToG_Sate(:) = 0
        
        
        !---------------------------------------------
        !---------------------------------------------
        do i=1,iReMapp%nGSate !(iMeshMapp(val_iPart)%nLSate
            sateOP = iMeshMapp(val_iPart)%iMapLToG_Sate(i)
            sateNP = iReMapp%iMapGToL_Sate(i)
            
            if(sateOP == 0) then
                write(ioPort_Out,*) 'sateOP == 0'
                stop
            endif
            
            if(sateNP == 0) then
                write(ioPort_Out,*) 'sateNP == 0'
                stop
            endif
            
            iTempMapp%iMapGToL_Sate(sateOP) = sateNP
            iTempMapp%iMapLToG_Sate(sateNP) = sateOP    
        enddo
        
        
        !---------------------------------------------
        !---------------------------------------------  
        iMeshMapp(val_iPart)%iMapGToL_Sate(:) = iTempMapp%iMapGToL_Sate(:)
        iMeshMapp(val_iPart)%iMapLToG_Sate(:) = iTempMapp%iMapLToG_Sate(:)
        
        deallocate(iTempMapp%iMapGToL_Sate)
        deallocate(iTempMapp%iMapLToG_Sate)
        
        !
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine cleanIReMeshAndMapp
        implicit none
        
        iReKind = 0
        
        iReMesh%nClor = 0
        iReMesh%nPoin = 0
        iReMesh%nSate = 0
        
        if(allocated(iReMesh%iCoor_Poin)) then
            deallocate(iReMesh%iCoor_Poin)
        endif
        if(allocated(iReMesh%kSate_Poin)) then
            deallocate(iReMesh%kSate_Poin)
        endif
        if(allocated(iReMesh%iSate_Poin)) then
            deallocate(iReMesh%iSate_Poin)
        endif
        if(allocated(iReMesh%iClor_Poin)) then
            deallocate(iReMesh%iClor_Poin)
        endif
        !deallocate(iReMesh%kSate_Poin)
        !deallocate(iReMesh%iSate_Poin)
        !deallocate(iReMesh%iClor_Poin)
        
        iReMapp%nGPoin = 0; iReMapp%nLPoin = 0
        iReMapp%nGSate = 0; iReMapp%nLSate = 0
        
        if(allocated(iReMapp%iFlag_GPoin)) then
            deallocate(iReMapp%iFlag_GPoin)
        endif
        if(allocated(iReMapp%iFlag_GSate)) then
            deallocate(iReMapp%iFlag_GSate)
        endif
        if(allocated(iReMapp%iFlag_LPoin)) then
            deallocate(iReMapp%iFlag_LPoin)
        endif
        if(allocated(iReMapp%iFlag_LSate)) then
            deallocate(iReMapp%iFlag_LSate)
        endif
        if(allocated(iReMapp%iMapLToG_Poin)) then
            deallocate(iReMapp%iMapLToG_Poin)
        endif
        if(allocated(iReMapp%iMapLToG_Sate)) then
            deallocate(iReMapp%iMapLToG_Sate)
        endif
        if(allocated(iReMapp%iMapGToL_Poin)) then
            deallocate(iReMapp%iMapGToL_Poin)
        endif
        if(allocated(iReMapp%iMapGToL_Poin)) then
            deallocate(iReMapp%iMapGToL_Poin)
        endif
        !deallocate(iReMapp%iFlag_GPoin)
        !deallocate(iReMapp%iFlag_GSate)
        !deallocate(iReMapp%iFlag_LPoin)
        !deallocate(iReMapp%iFlag_LSate)
        !deallocate(iReMapp%iMapLToG_Poin)
        !deallocate(iReMapp%iMapLToG_Sate)
        !deallocate(iReMapp%iMapGToL_Poin)
        !deallocate(iReMapp%iMapGToL_Sate)
        
    endsubroutine
    !
    !---------------------------------------------
    !
endmodule
!
