
!---------------------------------------------
!
!---------------------------------------------
module mod_SoluTurbSA
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    use mod_PreDefine_Flag
    use mod_PreDefine_Mark
    use mod_TypeDef_Parm
    use mod_Interface_AllocateArray
    use mod_GPUThreadDim
    use mod_TurbSA_ParmIndx
    use mod_Options
    use cudafor
    implicit none
contains
    !
    subroutine setTurbModelConstants_TurbSA(val_iParm)
        use mod_TypeDef_Parm
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        !
        real(kind=REALLEN):: turbSA_Cb1   = 0.1355
        real(kind=REALLEN):: turbSA_Cb2   = 0.622
        real(kind=REALLEN):: turbSA_Cv1   = 7.1
        real(kind=REALLEN):: turbSA_Cv2   = 5.0
        real(kind=REALLEN):: turbSA_sigma = 2.0/3.
        real(kind=REALLEN):: turbSA_kappa = 0.41
        real(kind=REALLEN):: turbSA_Cw1   = 3.2391
        real(kind=REALLEN):: turbSA_Cw2   = 0.3
        real(kind=REALLEN):: turbSA_Cw3   = 2.0
        real(kind=REALLEN):: turbSA_Ct1   = 1.0
        real(kind=REALLEN):: turbSA_Ct2   = 2.0
        real(kind=REALLEN):: turbSA_Ct3   = 1.2
        real(kind=REALLEN):: turbSA_Ct4   = 0.5
        !
        val_iParm%iHostParm(IP_TurbSA_Cb1  ) = turbSA_Cb1
        val_iParm%iHostParm(IP_TurbSA_Cb2  ) = turbSA_Cb2
        val_iParm%iHostParm(IP_TurbSA_Cv1  ) = turbSA_Cv1
        val_iParm%iHostParm(IP_TurbSA_Cv2  ) = turbSA_Cv2
        val_iParm%iHostParm(IP_TurbSA_Cw1  ) = turbSA_Cw1
        val_iParm%iHostParm(IP_TurbSA_Cw2  ) = turbSA_Cw2
        val_iParm%iHostParm(IP_TurbSA_Cw3  ) = turbSA_Cw3
        val_iParm%iHostParm(IP_TurbSA_Ct1  ) = turbSA_Ct1
        val_iParm%iHostParm(IP_TurbSA_Ct2  ) = turbSA_Ct2
        val_iParm%iHostParm(IP_TurbSA_Ct3  ) = turbSA_Ct3
        val_iParm%iHostParm(IP_TurbSA_Ct4  ) = turbSA_Ct4
        val_iParm%iHostParm(IP_TurbSA_Sigma) = turbSA_Sigma
        val_iParm%iHostParm(IP_TurbSA_Kappa) = turbSA_Kappa
        !
    endsubroutine
    !
    subroutine soluMalloc_TurbSA(val_iSolu)
        use mod_TypeDef_Solu
        implicit none
        type(typ_Solu),intent(inout):: val_iSolu
        !
        val_iSolu%nNodeAuxv = 4
        val_iSolu%nSateAuxv = 0
        val_iSolu%nBelvAuxv = 0
        !
        call allocateGPUArray(val_iSolu%nodeAuxv_d, val_iSolu%nNode, val_iSolu%nNodeAuxv)
        call allocateGPUArray(val_iSolu%sateAuvx_d, val_iSolu%nNode, val_iSolu%nSateAuxv)
        call allocateGPUArray(val_iSolu%belvAuvx_d, val_iSolu%nNode, val_iSolu%nBelvAuxv)
        !
    endsubroutine
    !
    subroutine updFieldWithBC_TurbSA(val_iParm,val_iMesh,val_iSolu)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_TurbSA_GPUFunc
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        !
        integer:: i,istat

        call bldBC_Global<<<nGridDim(IP_BB), nBlockDim>>>                               &
                (val_iMesh%iCalc_Poin_d, val_iMesh%iBelvProp_d, val_iMesh%iMarkProp_d,  &
                val_iMesh%iWDis_Poin_d, val_iMesh%iCoor_Poin_d, val_iMesh%iNvor_Belv_d, &
                val_iMesh%iVelo_Poin_d, val_iMesh%iVelo_Belv_d, val_iSolu%soluvar_d,    &
                val_iSolu%primvar_d, val_iSolu%soluBelv_d, val_iSolu%primBelv_d,        &
                val_iSolu%soluMark_d, val_iSolu%primMark_d, val_iSolu%nodeAuxv_d,       &
                val_iParm%iGPUSParm, val_iParm%isWithPrec, val_iMesh%nBelv,             &
                val_iMesh%nMark, val_iMesh%nPoin, val_iSolu%nNodeAuxv,                  &
                val_iMesh%nBelv, val_iParm%iIter                                        )

    endsubroutine
    !
    subroutine updFlowInftyInSolu_TurbSA(val_iParm,val_iSolu)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_FreeStream
        use mod_Config
        implicit none
        type(typ_Parm),intent(in   ):: val_iParm
        type(typ_Solu),intent(inout):: val_iSolu
        !
        integer:: i
        real(kind=REALLEN):: sq_vel,vBari,cv1,xhi,gammo


        gammo = val_iParm%iHostParm(IP_GAMMO)
        cv1   = val_iParm%iHostParm(IP_TurbSA_CV1)

        sq_vel = flowInfty_Velocity(1)**2 + flowInfty_Velocity(2)**2 + flowInfty_Velocity(nDim)**2*(nDim-2)

        vBari = 3.0*flowInfty_Viscosity/flowInfty_Density
        xhi   = 3.0**3/(3.0**3 + cv1**3)

        val_iSolu%soluInfty(       1) = flowInfty_Density
        val_iSolu%soluInfty(2:nDim+1) = flowInfty_Density*flowInfty_Velocity(:)
        val_iSolu%soluInfty(  nDim+2) = flowInfty_Pressure/gammo + 0.50*flowInfty_Density*sq_vel
        val_iSolu%soluInfty(  nDim+3) = vBari

        val_iSolu%primInfty(       1) = flowInfty_Pressure
        val_iSolu%primInfty(2:nDim+1) = flowInfty_Velocity(:)
        val_iSolu%primInfty(  nDim+2) = flowInfty_Temperature
        val_iSolu%primInfty(  nDim+3) = flowInfty_Viscosity
        val_iSolu%primInfty(  nDim+4) = xhi*vBari*flowInfty_Density

    endsubroutine
    !
    subroutine getMarkSoluAndPrim_TurbSA(val_iParm,val_iSolu,val_markInfo,val_iStep,val_soluMi,val_primMi)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_TurbSA_Def
        use mod_Config, only: typ_MarkInfo
        use mod_parmDimLess
        use mod_getSoluParm, only: getSoluParm_PhysicalTime
        use mod_TurbSA_GPUFunc, only: getPrimBySolu_TurbSA
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Solu),intent(inout):: val_iSolu
        type(typ_MarkInfo),intent(in):: val_markInfo
        integer       ,intent(in   ):: val_iStep
        real(kind=REALLEN),intent(inout):: val_soluMi(nDim+3)
        real(kind=REALLEN),intent(inout):: val_primMi(nDim+4)

        !
        integer:: istat,i
        real*8 :: dEi,vEi(nDim),qEi
        real(kind=REALLEN):: iTime = 0.0


        call getSoluParm_PhysicalTime(val_iParm, val_iStep, iTime)

        val_soluMi(:) = 0.0
        val_primMi(:) = 0.0

        if(val_markInfo%markKD == MARK_WALL) then
            !do nothing
        elseif(val_markInfo%markKD == MARK_EQTW) then
            val_soluMi(1     ) = val_markInfo%surfTemp

        elseif(val_markInfo%markKD == MARK_EULW) then
            !do nothing
        elseif(val_markInfo%markKD == MARK_MOVW) then
            val_soluMi(1     :nDim  ) = val_markInfo%transVelo(1:nDim)
            val_soluMi(nDim+1       ) = val_markInfo%rotatOmga
            val_primMi(1     :nDim  ) = val_markInfo%rotatOrgn(1:nDim)
            val_primMi(nDim+1:2*nDim) = val_markInfo%rotatAxis(1:nDim)

        elseif(val_markInfo%markKD == MARK_INLE) then
            dEi = val_markInfo%surfPres/(val_markInfo%surfTemp*val_iParm%iHostParm(IP_GASCO))
            vEi(1:nDim) = val_markInfo%surfVelo*val_markInfo%surfNorm(1:nDim)
            qEi = 0.5*dEi*(vEi(1)**2+vEi(2)**2+vEi(nDim)**2*(nDim-2))

            val_soluMi(1       ) = dEi
            val_soluMi(2:nDim+1) = dEi*vEi(1:nDim)
            val_soluMi(nDim+2  ) = val_markInfo%surfPres/(val_iParm%iHostParm(IP_GAMMA) - 1) + qEi
            val_soluMi(nDim+3  ) = val_iSolu%soluInfty(nDim+3)

            call getPrimBySolu_TurbSA(                    &
                    val_soluMi, val_primMi,               &
                    val_iParm%iHostParm(IP_GAMMA),        &
                    val_iParm%iHostParm(IP_GASCO),        &
                    val_iParm%iHostParm(IP_SutherlandT0), &
                    val_iParm%iHostParm(IP_SutherlandTs), &
                    val_iParm%iHostParm(IP_SutherlandM0), &
                    val_iParm%iHostParm(IP_TurbSA_Cv1),   &
                    val_iParm%iHostParm(IP_LIMMINP),      &
                    val_iParm%iHostParm(IP_LIMMAXP),      &
                    val_iParm%iHostParm(IP_LIMMAXMuT)     )

        elseif(val_markInfo%markKD == MARK_INLM) then
            dEi = val_markInfo%surfPres/(val_markInfo%surfTemp*val_iParm%iHostParm(IP_GASCO))
            vEi(1:nDim) = val_markInfo%surfMass/(dEi*val_markInfo%surfArea)*val_markInfo%surfNorm(1:nDim)
            qEi = 0.5*dEi*(vEi(1)**2+vEi(2)**2+vEi(nDim)**2*(nDim-2))

            val_soluMi(1       ) = dEi
            val_soluMi(2:nDim+1) = dEi*vEi(1:nDim)
            val_soluMi(nDim+2  ) = val_markInfo%surfPres/(val_iParm%iHostParm(IP_GAMMA) - 1) + qEi
            val_soluMi(nDim+3  ) = val_iSolu%soluInfty(nDim+3)

            call getPrimBySolu_TurbSA(         &
                    val_soluMi, val_primMi,    &
                    val_iParm%iHostParm(IP_GAMMA),        &
                    val_iParm%iHostParm(IP_GASCO),        &
                    val_iParm%iHostParm(IP_SutherlandT0), &
                    val_iParm%iHostParm(IP_SutherlandTs), &
                    val_iParm%iHostParm(IP_SutherlandM0), &
                    val_iParm%iHostParm(IP_TurbSA_Cv1),   &
                    val_iParm%iHostParm(IP_LIMMINP),      &
                    val_iParm%iHostParm(IP_LIMMAXP),      &
                    val_iParm%iHostParm(IP_LIMMAXMuT)     )

        elseif(val_markInfo%markKD == MARK_OUTL) then
            !do nothing
            val_soluMi(nDim+3) = val_iSolu%soluInfty(nDim+3)

        elseif(val_markInfo%markKD == MARK_OUTP) then
            val_soluMi(1) = val_markInfo%surfPres * (val_markInfo%BackPresFact &
                            + val_markInfo%BackPrecIncr*max(0,val_iStep - 1) )

            val_soluMi(2) = val_markInfo%surfTtot / DLRef_temperature
            val_soluMi(3) = val_markInfo%surfPtot / DLRef_pressure

            val_soluMi(nDim+3) = val_iSolu%soluInfty(nDim+3)

        elseif(val_markInfo%markKD == MARK_OUTM) then
            !do nothing

            val_soluMi(nDim+3) = val_iSolu%soluInfty(nDim+3)

        elseif(val_markInfo%markKD == MARK_PFAR) then
            val_soluMi(:) = val_iSolu%soluInfty(:)
            val_primMi(:) = val_iSolu%primInfty(:)
        else
            val_soluMi(:) = val_iSolu%soluInfty(:)
            val_primMi(:) = val_iSolu%primInfty(:)
        end if

    end subroutine
    !
    subroutine RunOneIteration_UPW_GPU_TurbSA(val_iParm,val_iMesh,val_iSolu)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_Config, only: TIME_DISCRE_FLOW, UPWIND_SCHEME, NITER_O1TUNING, UNSTEADY_SIMULATION, UPWIND_LIMITER, UPWIND_ORDER
        use mod_Operation_GPUArrayCpy
        use mod_TurbSA_GPUFunc
        use mod_Unsteady, only: calDualTerm_Global
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        !
        integer:: i, j, iIter, iClor, iStat, iRK
        logical:: is2ndOrder,relFlag

        is2ndOrder = (val_iParm%iIter > NITER_O1TUNING).and.(UPWIND_ORDER == 2)


        !---------------------------------------------
        !隐式LU-SGS迭代
        if(TIME_DISCRE_FLOW == _MCLUSGS_IMPLICIT) then
            call GPURealArrayCpy2D1_Global<<<nGridDim(IP_BP), nBlockDim>>>              &
                    (val_iSolu%soluvar_d, val_iSolu%soluOld_d,val_iMesh%nPoin, nDim+3  )

            !do i=1,3
            !    call GPURealArrayCpy1D1_Global<<<nGridDim(IP_BP), nBlockDim>>>          &
            !            (val_iSolu%DTCoef_d, val_iSolu%DTCoef2_d,val_iMesh%nPoin        )
            !
            !    call tuningDTCoef_Global<<<nGridDim(IP_BP), nBlockDim>>>                        &
            !            (val_iMesh%iCalc_Poin_d, val_iMesh%kSate_Poin_d, val_iMesh%iSate_Poin_d,&
            !            val_iSolu%DTCoef2_d, val_iSolu%DTCoef_d, val_iMesh%nPoin,               &
            !            val_iMesh%nSate,val_iMesh%nPoin                                         )
            !end do

            if(val_iParm%isWithPrec ) then
                call calPrecCoefMatx_Global<<<nGridDim(IP_BP), nBlockDim>>>             &
                        (val_iMesh%iCalc_Poin_d,val_iSolu%soluvar_d,val_iSolu%primvar_d,&
                        val_iSolu%preCoef_d, val_iSolu%pMatrix_d, val_iParm%iGPUSParm,  &
                        val_iMesh%nPoin, val_iMesh%nPoin, val_iParm%isWithPrec          )
            end if

            if(val_iParm%isUnstDual) then
                call calDualTerm_Global<<<nGridDim(IP_BP), nBlockDim>>>                 &
                        (val_iMesh%iCalc_Poin_d,val_iSolu%soluvar_d,val_iSolu%soluTimN_d,&
                        val_iSolu%soluTimX_d, val_iSolu%UnstTerm_d, val_iParm%iGPUSParm,&
                        val_iMesh%nPoin, nDim+3, val_iMesh%nPoin                       )
            endif

            call calSpecRaduAndDeltTime_Global<<<nGridDim(IP_BP), nBlockDim>>>              &
                    (val_iMesh%iCalc_Poin_d, val_iMesh%kSate_Poin_d, val_iMesh%iSate_Poin_d,&
                    val_iMesh%iCoef_Sate_d, val_iMesh%kBelv_Poin_d, val_iMesh%iBelv_Poin_d, &
                    val_iMesh%iCoef_Belv_d, val_iMesh%iVelo_Poin_d, val_iMesh%iVelo_Belv_d, &
                    val_iSolu%primvar_d, val_iSolu%primBelv_d, val_iSolu%preCoef_d,         &
                    val_iSolu%SRconv_d, val_iSolu%SRvisc_d, val_iSolu%SRcNode_d,            &
                    val_iSolu%SRvNode_d, val_iSolu%SRcBelv_d, val_iSolu%SRvBelv_d,          &
                    val_iSolu%DT_d, val_iSolu%DTCoef_d, val_iParm%iGPUSParm,                &
                    val_iMesh%nPoin, val_iMesh%nSate, val_iMesh%nBelv,                      &
                    val_iMesh%nPoin, val_iParm%isWithPrec, val_iParm%isUnstDual             )

            call calSoluGradWithLimt_Global<<<nGridDim(IP_BP),nBlockDim>>>                  &
                    (val_iMesh%iCalc_Poin_d, val_iMesh%iBelvProp_d,val_iMesh%iMarkProp_d,   &
                    val_iMesh%kSate_Poin_d,val_iMesh%iSate_Poin_d,val_iMesh%iCoef_Sate_d,   &
                    val_iMesh%iCoor_Poin_d,val_iMesh%iRadu_Poin_d,val_iMesh%kBelv_Poin_d,   &
                    val_iMesh%iBelv_Poin_d,val_iMesh%iCoef_Belv_d,val_iSolu%soluvar_d,      &
                    val_iSolu%primvar_d,val_iSolu%soluBelv_d,val_iSolu%primBelv_d,          &
                    val_iSolu%gradSolu_d,val_iSolu%limiter_d,val_iSolu%tau_d,               &
                    val_iSolu%soluUCRef_d,val_iParm%iGPUSParm,val_iMesh%nPoin,val_iMesh%nSate,&
                    val_iMesh%nBelv,val_iMesh%nMark,val_iMesh%nPoin, UPWIND_LIMITER         )

            call calFluxConvViscSour_UPW_Global<<<nGridDim(IP_BP),nBlockDim>>>              &
                    (val_iMesh%iCalc_Poin_d, val_iMesh%iIner_Poin_d, val_iMesh%kSate_Poin_d,&
                    val_iMesh%iSate_Poin_d, val_iMesh%iCoef_Sate_d, val_iMesh%iCoor_Poin_d, &
                    val_iMesh%kBelv_Poin_d, val_iMesh%iBelv_Poin_d, val_iMesh%iCoef_Belv_d, &
                    val_iMesh%iVelo_Poin_d, val_iMesh%iVelo_Belv_d, val_iMesh%iWDis_Poin_d, &
                    val_iSolu%soluvar_d, val_iSolu%primvar_d, val_iSolu%soluBelv_d,         &
                    val_iSolu%primBelv_d, val_iSolu%gradSolu_d, val_iSolu%limiter_d,        &
                    val_iSolu%tau_d, val_iSolu%pMatrix_d, val_iSolu%UnstTerm_d, val_iSolu%nodeAuxv_d,        &
                    val_iSolu%Res_All_d, val_iparm%iGPUSParm, val_iMesh%iMarkProp_d,        &
                    val_iMesh%nPoin, val_iMesh%nSate, val_iMesh%nBelv, val_iMesh%nMark,     &
                    val_iSolu%nNodeAuxv, val_iMesh%nPoin, is2ndOrder, UPWIND_SCHEME,        &
                    val_iParm%isWithPrec, val_iParm%isUnsteady,UPWIND_LIMITER               )

            do iClor=1,val_iMesh%nClor
            call loopLUSGS_Global<<<nGridDim(IP_BP),nBlockDim>>>                            &
                    (val_iMesh%iCalc_Poin_d, val_iMesh%iClor_Poin_d, val_iMesh%kSate_Poin_d,&
                    val_iMesh%iSate_Poin_d, val_iMesh%iCoef_Sate_d, val_iMesh%iVelo_Poin_d, &
                    val_iSolu%soluvar_d, val_iSolu%deltSolu_d, val_iSolu%SRconv_d,          &
                    val_iSolu%SRvisc_d, val_iSolu%SRcNode_d, val_iSolu%SRvNode_d,           &
                    val_iSolu%Res_All_d, val_iSolu%DT_d, val_iParm%iGPUSParm,               &
                    iClor, Dir_Forward, val_iMesh%nPoin, val_iMesh%nSate, val_iMesh%nPoin   )

            istat = cudaDeviceSynchronize()
            end do

            do iClor=val_iMesh%nClor,1,-1
            call loopLUSGS_Global<<<nGridDim(IP_BP),nBlockDim>>>                            &
                    (val_iMesh%iCalc_Poin_d, val_iMesh%iClor_Poin_d, val_iMesh%kSate_Poin_d,&
                    val_iMesh%iSate_Poin_d, val_iMesh%iCoef_Sate_d, val_iMesh%iVelo_Poin_d, &
                    val_iSolu%soluvar_d, val_iSolu%deltSolu_d, val_iSolu%SRconv_d,          &
                    val_iSolu%SRvisc_d, val_iSolu%SRcNode_d, val_iSolu%SRvNode_d,           &
                    val_iSolu%Res_All_d, val_iSolu%DT_d, val_iParm%iGPUSParm,               &
                    iClor, Dir_Backward, val_iMesh%nPoin, val_iMesh%nSate, val_iMesh%nPoin  )

            istat = cudaDeviceSynchronize()
            end do

            istat = cudaDeviceSynchronize()

            call updSoluAndDTCoef_Global<<<nGridDim(IP_BP),nBlockDim>>>                 &
                    (val_iMesh%iCalc_Poin_d, val_iSolu%soluvar_d, val_iSolu%primvar_d,  &
                    val_iSolu%soluOld_d, val_iSolu%deltSolu_d, val_iSolu%DTCoef_d,      &
                    val_iSolu%nodeAuxv_d, val_iParm%iGPUSParm, val_iMesh%nPoin,         &
                    val_iSolu%nNodeAuxv, val_iMesh%nPoin                                )

            call bldBC_Global<<<nGridDim(IP_BB), nBlockDim>>>                               &
                    (val_iMesh%iCalc_Poin_d, val_iMesh%iBelvProp_d, val_iMesh%iMarkProp_d,  &
                    val_iMesh%iWDis_Poin_d, val_iMesh%iCoor_Poin_d, val_iMesh%iNvor_Belv_d, &
                    val_iMesh%iVelo_Poin_d, val_iMesh%iVelo_Belv_d, val_iSolu%soluvar_d,    &
                    val_iSolu%primvar_d, val_iSolu%soluBelv_d, val_iSolu%primBelv_d,        &
                    val_iSolu%soluMark_d, val_iSolu%primMark_d, val_iSolu%nodeAuxv_d,       &
                    val_iParm%iGPUSParm, val_iParm%isWithPrec, val_iMesh%nBelv,             &
                    val_iMesh%nMark, val_iMesh%nPoin, val_iSolu%nNodeAuxv,                  &
                    val_iMesh%nBelv, val_iParm%iIter                                        )

        else if(TIME_DISCRE_FLOW == _RUNGE_KUTTA_EXPLICIT) then
            call GPURealArrayCpy2D1_Global<<<nGridDim(IP_BP),nBlockDim>>>               &
                    (val_iSolu%soluvar_d, val_iSolu%soluOld_d,val_iMesh%nPoin, nDim+3   )

            if(val_iParm%isWithPrec ) then
                call calPrecCoefMatx_Global<<<nGridDim(IP_BP), nBlockDim>>>             &
                        (val_iMesh%iCalc_Poin_d,val_iSolu%soluvar_d,val_iSolu%primvar_d,&
                        val_iSolu%preCoef_d, val_iSolu%pMatrix_d, val_iParm%iGPUSParm,  &
                        val_iMesh%nPoin, val_iMesh%nPoin, val_iParm%isWithPrec          )
            end if

            if(val_iParm%isUnstDual) then
                call calDualTerm_Global<<<nGridDim(IP_BP), nBlockDim>>>                  &
                        (val_iMesh%iCalc_Poin_d,val_iSolu%soluvar_d,val_iSolu%soluTimN_d,&
                        val_iSolu%soluTimX_d, val_iSolu%UnstTerm_d, val_iParm%iGPUSParm, &
                        val_iMesh%nPoin, nDim+3, val_iMesh%nPoin                         )
            endif

            do iRK=1,4
                if(iRK == 1) then
                call calSpecRaduAndDeltTime_Global<<<nGridDim(IP_BP), nBlockDim>>>              &
                        (val_iMesh%iCalc_Poin_d, val_iMesh%kSate_Poin_d, val_iMesh%iSate_Poin_d,&
                        val_iMesh%iCoef_Sate_d, val_iMesh%kBelv_Poin_d, val_iMesh%iBelv_Poin_d, &
                        val_iMesh%iCoef_Belv_d, val_iMesh%iVelo_Poin_d, val_iMesh%iVelo_Belv_d, &
                        val_iSolu%primvar_d, val_iSolu%primBelv_d, val_iSolu%preCoef_d,         &
                        val_iSolu%SRconv_d, val_iSolu%SRvisc_d, val_iSolu%SRcNode_d,            &
                        val_iSolu%SRvNode_d, val_iSolu%SRcBelv_d, val_iSolu%SRvBelv_d,          &
                        val_iSolu%DT_d, val_iSolu%DTCoef_d, val_iParm%iGPUSParm,                &
                        val_iMesh%nPoin, val_iMesh%nSate, val_iMesh%nBelv,                      &
                        val_iMesh%nPoin, val_iParm%isWithPrec, val_iParm%isUnstDual             )

                call calSoluGradWithLimt_Global<<<nGridDim(IP_BP),nBlockDim>>>                  &
                        (val_iMesh%iCalc_Poin_d, val_iMesh%iBelvProp_d,val_iMesh%iMarkProp_d,   &
                        val_iMesh%kSate_Poin_d,val_iMesh%iSate_Poin_d,val_iMesh%iCoef_Sate_d,   &
                        val_iMesh%iCoor_Poin_d,val_iMesh%iRadu_Poin_d,val_iMesh%kBelv_Poin_d,   &
                        val_iMesh%iBelv_Poin_d,val_iMesh%iCoef_Belv_d,val_iSolu%soluvar_d,      &
                        val_iSolu%primvar_d,val_iSolu%soluBelv_d,val_iSolu%primBelv_d,          &
                        val_iSolu%gradSolu_d,val_iSolu%limiter_d,val_iSolu%tau_d,               &
                        val_iSolu%soluUCRef_d,val_iParm%iGPUSParm,val_iMesh%nPoin,val_iMesh%nSate,&
                        val_iMesh%nBelv,val_iMesh%nMark,val_iMesh%nPoin, UPWIND_LIMITER         )
                endif

                call calFluxConvViscSour_UPW_Global<<<nGridDim(IP_BP),nBlockDim>>>              &
                        (val_iMesh%iCalc_Poin_d, val_iMesh%iIner_Poin_d, val_iMesh%kSate_Poin_d,&
                        val_iMesh%iSate_Poin_d, val_iMesh%iCoef_Sate_d, val_iMesh%iCoor_Poin_d, &
                        val_iMesh%kBelv_Poin_d, val_iMesh%iBelv_Poin_d, val_iMesh%iCoef_Belv_d, &
                        val_iMesh%iVelo_Poin_d, val_iMesh%iVelo_Belv_d, val_iMesh%iWDis_Poin_d, &
                        val_iSolu%soluvar_d, val_iSolu%primvar_d, val_iSolu%soluBelv_d,         &
                        val_iSolu%primBelv_d, val_iSolu%gradSolu_d, val_iSolu%limiter_d,        &
                        val_iSolu%tau_d, val_iSolu%pMatrix_d, val_iSolu%UnstTerm_d, val_iSolu%nodeAuxv_d,        &
                        val_iSolu%Res_All_d, val_iparm%iGPUSParm, val_iMesh%iMarkProp_d,        &
                        val_iMesh%nPoin, val_iMesh%nSate, val_iMesh%nBelv, val_iMesh%nMark,     &
                        val_iSolu%nNodeAuxv, val_iMesh%nPoin, is2ndOrder, UPWIND_SCHEME,        &
                        val_iParm%isWithPrec, val_iParm%isUnsteady,UPWIND_LIMITER               )

                call updSolu_RK_Global<<<nGridDim(IP_BP),nBlockDim>>>                       &
                        (val_iMesh%iCalc_Poin_d, val_iSolu%soluOld_d, val_iSolu%soluvar_d,  &
                        val_iSolu%primvar_d, val_iSolu%nodeAuxv_d, val_iSolu%Res_All_d,     &
                        val_iSolu%DT_d,val_iSolu%DTCoef_d, val_iParm%iGPUSParm,             &
                        val_iMesh%nPoin, val_iSolu%nNodeAuxv, val_iMesh%nPoin, iRK          )

                call bldBC_Global<<<nGridDim(IP_BB), nBlockDim>>>                               &
                        (val_iMesh%iCalc_Poin_d, val_iMesh%iBelvProp_d, val_iMesh%iMarkProp_d,  &
                        val_iMesh%iWDis_Poin_d, val_iMesh%iCoor_Poin_d, val_iMesh%iNvor_Belv_d, &
                        val_iMesh%iVelo_Poin_d, val_iMesh%iVelo_Belv_d, val_iSolu%soluvar_d,    &
                        val_iSolu%primvar_d, val_iSolu%soluBelv_d, val_iSolu%primBelv_d,        &
                        val_iSolu%soluMark_d, val_iSolu%primMark_d, val_iSolu%nodeAuxv_d,       &
                        val_iParm%iGPUSParm, val_iParm%isWithPrec, val_iMesh%nBelv,             &
                        val_iMesh%nMark, val_iMesh%nPoin, val_iSolu%nNodeAuxv,                  &
                        val_iMesh%nBelv, val_iParm%iIter                                        )

                istat = cudaDeviceSynchronize()
            end do
        end if

    endsubroutine
    !
endmodule
!
