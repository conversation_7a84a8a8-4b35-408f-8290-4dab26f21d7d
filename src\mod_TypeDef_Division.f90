
!---------------------------------------------
!
!---------------------------------------------
module mod_TypeDef_Division
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    implicit none
    
    
    !---------------------------------------------
    !剖分子块类型
    !---------------------------------------------
    type:: typ_DivBlock
        !基本点信息
        integer:: nPoin     = 0             !点数
        !logical:: isExp     = .false.      !
        integer,allocatable:: iPoin(:)      !节点编号 
        
        !边界信息
        real(kind=REALLEN):: minBnd(nDim)   !划分边界min
        real(kind=REALLEN):: maxBnd(nDim)   !划分边界max
        real(kind=REALLEN):: binBnd(nDim)   !实体边界min
        real(kind=REALLEN):: baxBnd(nDim)   !实体边界max
        
        !父子链表节点连接关系
        integer:: fartherID                 !父节点ID
        integer:: brotherID                 !兄弟节点ID
        integer:: sonID(2)                  !子节点ID
        !剖分信息
        logical:: isNeedDiv = .false.       !是否需要剖分
        integer:: iDivDim   = 0             !剖分维度
        real(kind=REALLEN):: iDivValue      !剖分取值
        
        !包含单元信息
        integer:: nElem = 0                 !单元数
        integer,allocatable:: iElem(:)      !单元编号列表
    endtype
    
    
    !---------------------------------------------
    !剖分区域类型
    !---------------------------------------------
    type:: typ_DivRegin
        integer:: nPoin                                 !总点数
        integer,allocatable:: iPoin(:)                  !点编号列表
        real(kind=REALLEN),allocatable:: iCoor_Orgn(:,:)!点原始坐标
        
        !
        integer,allocatable:: iFinalSort(:)             !最终排序
        integer,allocatable:: iNeID_Poin(:,:)           !分维排序编号
        integer,allocatable:: iPoin_Sort(:,:)           !分维排序编号
        real(kind=REALLEN),allocatable:: iCoor_Sort(:,:)!分维排序坐标
        
        !
        integer:: nPoinPerBlock = 8                 !子块包含的点数
        integer:: nDivLevel     = 0                 !划分层级
        integer:: nBlock        = 0                 !链表总块数
        type(typ_DivBlock),allocatable:: iBlock(:)  !子块链表
        
        !
        integer:: nFinalBlock                       !最终有效子块数
        integer,allocatable:: iFinalBlock(:)        !最终有效子块编号
    endtype
    !
contains
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine cleanDivRegin(val_iDivRegin)
        implicit none
        type(typ_DivRegin),intent(inout):: val_iDivRegin
        !
        integer:: i
        
        
        do i=1,val_iDivRegin%nBlock
            call cleanDivBlock(val_iDivRegin%iBlock(i))
        enddo
        
        if(allocated(val_iDivRegin%iPoin)) then
            deallocate(val_iDivRegin%iPoin)
        endif
        if(allocated(val_iDivRegin%iCoor_Orgn)) then
            deallocate(val_iDivRegin%iCoor_Orgn)
        endif
        if(allocated(val_iDivRegin%iFinalSort)) then
            deallocate(val_iDivRegin%iFinalSort)
        endif
        if(allocated(val_iDivRegin%iNeID_Poin)) then
            deallocate(val_iDivRegin%iNeID_Poin)
        endif
        if(allocated(val_iDivRegin%iPoin_Sort)) then
            deallocate(val_iDivRegin%iPoin_Sort)
        endif
        if(allocated(val_iDivRegin%iCoor_Sort)) then
            deallocate(val_iDivRegin%iCoor_Sort)
        endif
        if(allocated(val_iDivRegin%iBlock)) then
            deallocate(val_iDivRegin%iBlock)
        endif
        if(allocated(val_iDivRegin%iFinalBlock)) then
            deallocate(val_iDivRegin%iFinalBlock)
        endif
        
        val_iDivRegin%nPoin         = 0
        val_iDivRegin%nPoinPerBlock = 8
        val_iDivRegin%nDivLevel     = 0
        val_iDivRegin%nBlock        = 0
        val_iDivRegin%nFinalBlock   = 0
        
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine cleanDivBlock(val_iDivBlock)
        implicit none
        type(typ_DivBlock),intent(inout):: val_iDivBlock
        
        if(allocated(val_iDivBlock%iPoin)) then
            deallocate(val_iDivBlock%iPoin)
        endif
        if(allocated(val_iDivBlock%iElem)) then
            deallocate(val_iDivBlock%iElem)
        endif
        
        val_iDivBlock%nPoin = 0
        val_iDivBlock%nElem = 0
        
    endsubroutine
    !
endmodule
!
