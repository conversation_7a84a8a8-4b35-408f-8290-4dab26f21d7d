!
!---------------------------------------------
!
!---------------------------------------------
subroutine initElemProp
    implicit none
    
    call iniE<PERSON>Prop_NodeList
    
    call iniE<PERSON>Prop_EdgeList
    
    call iniElemProp_LineList
    
    call iniElemProp_DiviList
    
    return
    
contains
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine iniElemProp_NodeList
        use mod_PreDefine_Elem
        use mod_ElemProp
        implicit none
    
        ElemProp_nodeNum(:) = 0
        ElemProp_nodeList(:,:) = 0
    
        !线
        ElemProp_nodeNum(ELEM_LINE) = 2
        ElemProp_nodeList(1,ELEM_LINE) = 1
        ElemProp_nodeList(2,ELEM_LINE) = 2
        !三角形
        ElemProp_nodeNum(ELEM_TRIANGLE) = 3
        ElemProp_nodeList(1,ELEM_TRIANGLE) = 1
        ElemProp_nodeList(2,ELEM_TRIANGLE) = 2
        ElemProp_nodeList(3,ELEM_TRIANGLE) = 3
        !四边形
        ElemProp_nodeNum(ELEM_RECTANGLE) = 4
        ElemProp_nodeList(1,ELEM_RECTANGLE) = 1
        ElemProp_nodeList(2,ELEM_RECTANGLE) = 2
        ElemProp_nodeList(3,ELEM_RECTANGLE) = 3
        ElemProp_nodeList(4,ELEM_RECTANGLE) = 4
        !四面体
        ElemProp_nodeNum(ELEM_TETRAHEDRAL) = 4
        ElemProp_nodeList(1,ELEM_TETRAHEDRAL) = 1
        ElemProp_nodeList(2,ELEM_TETRAHEDRAL) = 2
        ElemProp_nodeList(3,ELEM_TETRAHEDRAL) = 3
        ElemProp_nodeList(4,ELEM_TETRAHEDRAL) = 5
        !六面体
        ElemProp_nodeNum(ELEM_HEXAHEDRAL) = 8
        ElemProp_nodeList(1,ELEM_HEXAHEDRAL) = 1
        ElemProp_nodeList(2,ELEM_HEXAHEDRAL) = 2
        ElemProp_nodeList(3,ELEM_HEXAHEDRAL) = 3
        ElemProp_nodeList(4,ELEM_HEXAHEDRAL) = 4
        ElemProp_nodeList(5,ELEM_HEXAHEDRAL) = 5
        ElemProp_nodeList(6,ELEM_HEXAHEDRAL) = 6
        ElemProp_nodeList(7,ELEM_HEXAHEDRAL) = 7
        ElemProp_nodeList(8,ELEM_HEXAHEDRAL) = 8
        !三棱柱
        ElemProp_nodeNum(ELEM_WEDGE) = 6
        ElemProp_nodeList(1,ELEM_WEDGE) = 1
        ElemProp_nodeList(2,ELEM_WEDGE) = 2
        ElemProp_nodeList(3,ELEM_WEDGE) = 3
        ElemProp_nodeList(4,ELEM_WEDGE) = 5
        ElemProp_nodeList(5,ELEM_WEDGE) = 6
        ElemProp_nodeList(6,ELEM_WEDGE) = 7
        !金字塔
        ElemProp_nodeNum(ELEM_PYRAMID) = 5
        ElemProp_nodeList(1,ELEM_PYRAMID) = 1
        ElemProp_nodeList(2,ELEM_PYRAMID) = 2
        ElemProp_nodeList(3,ELEM_PYRAMID) = 3
        ElemProp_nodeList(4,ELEM_PYRAMID) = 4
        ElemProp_nodeList(5,ELEM_PYRAMID) = 5
    
    endsubroutine
    
    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine iniElemProp_edgeList
        use mod_PreDefine_Elem
        use mod_ElemProp
        implicit none
        !要求右手法则外法向
    
        ElemProp_edgeNum(:) = 0
        ElemProp_edgeKind(:,:) = 0
        ElemProp_edgeList(:,:,:) = 0
        !线
    
        !三角形
        ElemProp_edgeNum(ELEM_TRIANGLE) = 3
        ElemProp_edgeKind(1,ELEM_TRIANGLE) = ELEM_LINE
        ElemProp_edgeList(1,1,ELEM_TRIANGLE) = 2
        ElemProp_edgeList(2,1,ELEM_TRIANGLE) = 1
        ElemProp_edgeKind(2,ELEM_TRIANGLE) = ELEM_LINE
        ElemProp_edgeList(1,2,ELEM_TRIANGLE) = 3
        ElemProp_edgeList(2,2,ELEM_TRIANGLE) = 2
        ElemProp_edgeKind(3,ELEM_TRIANGLE) = ELEM_LINE
        ElemProp_edgeList(1,3,ELEM_TRIANGLE) = 1
        ElemProp_edgeList(2,3,ELEM_TRIANGLE) = 3
    
        !四边形
        ElemProp_edgeNum(ELEM_RECTANGLE) = 4
        ElemProp_edgeKind(1,ELEM_RECTANGLE) = ELEM_LINE
        ElemProp_edgeList(1,1,ELEM_RECTANGLE) = 2
        ElemProp_edgeList(2,1,ELEM_RECTANGLE) = 1
        ElemProp_edgeKind(2,ELEM_RECTANGLE) = ELEM_LINE
        ElemProp_edgeList(1,2,ELEM_RECTANGLE) = 3
        ElemProp_edgeList(2,2,ELEM_RECTANGLE) = 2
    
        ElemProp_edgeKind(3,ELEM_RECTANGLE) = ELEM_LINE
        ElemProp_edgeList(1,3,ELEM_RECTANGLE) = 4
        ElemProp_edgeList(2,3,ELEM_RECTANGLE) = 3
        ElemProp_edgeKind(4,ELEM_RECTANGLE) = ELEM_LINE
        ElemProp_edgeList(1,4,ELEM_RECTANGLE) = 1
        ElemProp_edgeList(2,4,ELEM_RECTANGLE) = 4
    
        !四面体
        ElemProp_edgeNum(ELEM_TETRAHEDRAL) = 4
        ElemProp_edgeKind(1,ELEM_TETRAHEDRAL) = ELEM_TRIANGLE
        ElemProp_edgeList(1,1,ELEM_TETRAHEDRAL) = 2
        ElemProp_edgeList(2,1,ELEM_TETRAHEDRAL) = 1
        ElemProp_edgeList(3,1,ELEM_TETRAHEDRAL) = 3
        ElemProp_edgeList(4,1,ELEM_TETRAHEDRAL) = 3
        ElemProp_edgeKind(2,ELEM_TETRAHEDRAL) = ELEM_TRIANGLE
        ElemProp_edgeList(1,2,ELEM_TETRAHEDRAL) = 1
        ElemProp_edgeList(2,2,ELEM_TETRAHEDRAL) = 2
        ElemProp_edgeList(3,2,ELEM_TETRAHEDRAL) = 5
        ElemProp_edgeList(4,2,ELEM_TETRAHEDRAL) = 5
        ElemProp_edgeKind(3,ELEM_TETRAHEDRAL) = ELEM_TRIANGLE
        ElemProp_edgeList(1,3,ELEM_TETRAHEDRAL) = 2
        ElemProp_edgeList(2,3,ELEM_TETRAHEDRAL) = 3
        ElemProp_edgeList(3,3,ELEM_TETRAHEDRAL) = 5
        ElemProp_edgeList(4,3,ELEM_TETRAHEDRAL) = 5
        ElemProp_edgeKind(4,ELEM_TETRAHEDRAL) = ELEM_TRIANGLE
        ElemProp_edgeList(1,4,ELEM_TETRAHEDRAL) = 3
        ElemProp_edgeList(2,4,ELEM_TETRAHEDRAL) = 1
        ElemProp_edgeList(3,4,ELEM_TETRAHEDRAL) = 5
        ElemProp_edgeList(4,4,ELEM_TETRAHEDRAL) = 5
    
        !六面体
        ElemProp_edgeNum(ELEM_HEXAHEDRAL) = 6
        ElemProp_edgeKind(1,ELEM_HEXAHEDRAL) = ELEM_RECTANGLE
        ElemProp_edgeList(1,1,ELEM_HEXAHEDRAL) = 4
        ElemProp_edgeList(2,1,ELEM_HEXAHEDRAL) = 3
        ElemProp_edgeList(3,1,ELEM_HEXAHEDRAL) = 2
        ElemProp_edgeList(4,1,ELEM_HEXAHEDRAL) = 1
        ElemProp_edgeKind(2,ELEM_HEXAHEDRAL) = ELEM_RECTANGLE
        ElemProp_edgeList(1,2,ELEM_HEXAHEDRAL) = 5
        ElemProp_edgeList(2,2,ELEM_HEXAHEDRAL) = 6
        ElemProp_edgeList(3,2,ELEM_HEXAHEDRAL) = 7
        ElemProp_edgeList(4,2,ELEM_HEXAHEDRAL) = 8
    
        ElemProp_edgeKind(3,ELEM_HEXAHEDRAL) = ELEM_RECTANGLE
        ElemProp_edgeList(1,3,ELEM_HEXAHEDRAL) = 1
        ElemProp_edgeList(2,3,ELEM_HEXAHEDRAL) = 2
        ElemProp_edgeList(3,3,ELEM_HEXAHEDRAL) = 6
        ElemProp_edgeList(4,3,ELEM_HEXAHEDRAL) = 5
        ElemProp_edgeKind(4,ELEM_HEXAHEDRAL) = ELEM_RECTANGLE
        ElemProp_edgeList(1,4,ELEM_HEXAHEDRAL) = 3
        ElemProp_edgeList(2,4,ELEM_HEXAHEDRAL) = 4
        ElemProp_edgeList(3,4,ELEM_HEXAHEDRAL) = 8
        ElemProp_edgeList(4,4,ELEM_HEXAHEDRAL) = 7
    
        ElemProp_edgeKind(5,ELEM_HEXAHEDRAL) = ELEM_RECTANGLE
        ElemProp_edgeList(1,5,ELEM_HEXAHEDRAL) = 2
        ElemProp_edgeList(2,5,ELEM_HEXAHEDRAL) = 3
        ElemProp_edgeList(3,5,ELEM_HEXAHEDRAL) = 7
        ElemProp_edgeList(4,5,ELEM_HEXAHEDRAL) = 6
        ElemProp_edgeKind(6,ELEM_HEXAHEDRAL) = ELEM_RECTANGLE
        ElemProp_edgeList(1,6,ELEM_HEXAHEDRAL) = 4
        ElemProp_edgeList(2,6,ELEM_HEXAHEDRAL) = 1
        ElemProp_edgeList(3,6,ELEM_HEXAHEDRAL) = 5
        ElemProp_edgeList(4,6,ELEM_HEXAHEDRAL) = 8
    
        !三棱柱
        ElemProp_edgeNum(ELEM_WEDGE) = 5
        ElemProp_edgeKind(1,ELEM_WEDGE) = ELEM_TRIANGLE
        ElemProp_edgeList(1,1,ELEM_WEDGE) = 2
        ElemProp_edgeList(2,1,ELEM_WEDGE) = 1
        ElemProp_edgeList(3,1,ELEM_WEDGE) = 3
        ElemProp_edgeList(4,1,ELEM_WEDGE) = 3
        ElemProp_edgeKind(2,ELEM_WEDGE) = ELEM_TRIANGLE
        ElemProp_edgeList(1,2,ELEM_WEDGE) = 5
        ElemProp_edgeList(2,2,ELEM_WEDGE) = 6
        ElemProp_edgeList(3,2,ELEM_WEDGE) = 7
        ElemProp_edgeList(4,2,ELEM_WEDGE) = 7
        ElemProp_edgeKind(3,ELEM_WEDGE) = ELEM_RECTANGLE
        ElemProp_edgeList(1,3,ELEM_WEDGE) = 1
        ElemProp_edgeList(2,3,ELEM_WEDGE) = 2
        ElemProp_edgeList(3,3,ELEM_WEDGE) = 6
        ElemProp_edgeList(4,3,ELEM_WEDGE) = 5
        ElemProp_edgeKind(4,ELEM_WEDGE) = ELEM_RECTANGLE
        ElemProp_edgeList(1,4,ELEM_WEDGE) = 2
        ElemProp_edgeList(2,4,ELEM_WEDGE) = 3
        ElemProp_edgeList(3,4,ELEM_WEDGE) = 7
        ElemProp_edgeList(4,4,ELEM_WEDGE) = 6
        ElemProp_edgeKind(5,ELEM_WEDGE) = ELEM_RECTANGLE
        ElemProp_edgeList(1,5,ELEM_WEDGE) = 3
        ElemProp_edgeList(2,5,ELEM_WEDGE) = 1
        ElemProp_edgeList(3,5,ELEM_WEDGE) = 5
        ElemProp_edgeList(4,5,ELEM_WEDGE) = 7
    
        !金字塔
        ElemProp_edgeNum(ELEM_PYRAMID) = 5
        ElemProp_edgeKind(1,ELEM_PYRAMID) = ELEM_RECTANGLE
        ElemProp_edgeList(1,1,ELEM_PYRAMID) = 4
        ElemProp_edgeList(2,1,ELEM_PYRAMID) = 3
        ElemProp_edgeList(3,1,ELEM_PYRAMID) = 2
        ElemProp_edgeList(4,1,ELEM_PYRAMID) = 1
        ElemProp_edgeKind(2,ELEM_PYRAMID) = ELEM_TRIANGLE
        ElemProp_edgeList(1,2,ELEM_PYRAMID) = 1
        ElemProp_edgeList(2,2,ELEM_PYRAMID) = 2
        ElemProp_edgeList(3,2,ELEM_PYRAMID) = 5
        ElemProp_edgeList(4,2,ELEM_PYRAMID) = 5
        ElemProp_edgeKind(3,ELEM_PYRAMID) = ELEM_TRIANGLE
        ElemProp_edgeList(1,3,ELEM_PYRAMID) = 2
        ElemProp_edgeList(2,3,ELEM_PYRAMID) = 3
        ElemProp_edgeList(3,3,ELEM_PYRAMID) = 5
        ElemProp_edgeList(4,3,ELEM_PYRAMID) = 5
        ElemProp_edgeKind(4,ELEM_PYRAMID) = ELEM_TRIANGLE
        ElemProp_edgeList(1,4,ELEM_PYRAMID) = 3
        ElemProp_edgeList(2,4,ELEM_PYRAMID) = 4
        ElemProp_edgeList(3,4,ELEM_PYRAMID) = 5
        ElemProp_edgeList(4,4,ELEM_PYRAMID) = 5
        ElemProp_edgeKind(5,ELEM_PYRAMID) = ELEM_TRIANGLE
        ElemProp_edgeList(1,5,ELEM_PYRAMID) = 4
        ElemProp_edgeList(2,5,ELEM_PYRAMID) = 1
        ElemProp_edgeList(3,5,ELEM_PYRAMID) = 5
        ElemProp_edgeList(4,5,ELEM_PYRAMID) = 5
    
    endsubroutine
    
    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine iniElemProp_LineList
        use mod_PreDefine_Elem
        use mod_ElemProp
        implicit none
    
        ElemProp_lineNum(:) = 0
        ElemProp_lineList(:,:,:) = 0
        ElemProp_lineENUM(:,:,:) = 0
    
        !线
    
        !三角形
        ElemProp_lineNum(ELEM_TRIANGLE) = 3
        ElemProp_lineList(1,1,ELEM_TRIANGLE) = 2
        ElemProp_lineList(2,1,ELEM_TRIANGLE) = 1
        ElemProp_lineList(1,2,ELEM_TRIANGLE) = 3
        ElemProp_lineList(2,2,ELEM_TRIANGLE) = 2
        ElemProp_lineList(1,3,ELEM_TRIANGLE) = 1
        ElemProp_lineList(2,3,ELEM_TRIANGLE) = 3
        
        ElemProp_lineENUM(1,1,ELEM_TRIANGLE) = 1
        ElemProp_lineENUM(1,2,ELEM_TRIANGLE) = 2
        ElemProp_lineENUM(1,3,ELEM_TRIANGLE) = 3
    
        !四边形
        ElemProp_lineNum(ELEM_RECTANGLE) = 4
        ElemProp_lineList(1,1,ELEM_RECTANGLE) = 2
        ElemProp_lineList(2,1,ELEM_RECTANGLE) = 1
        ElemProp_lineList(1,2,ELEM_RECTANGLE) = 3
        ElemProp_lineList(2,2,ELEM_RECTANGLE) = 2
    
        ElemProp_lineList(1,3,ELEM_RECTANGLE) = 4
        ElemProp_lineList(2,3,ELEM_RECTANGLE) = 3
        ElemProp_lineList(1,4,ELEM_RECTANGLE) = 1
        ElemProp_lineList(2,4,ELEM_RECTANGLE) = 4
        
        ElemProp_lineENUM(1,1,ELEM_RECTANGLE) = 1
        ElemProp_lineENUM(1,2,ELEM_RECTANGLE) = 2
        ElemProp_lineENUM(1,3,ELEM_RECTANGLE) = 3
        ElemProp_lineENUM(1,4,ELEM_RECTANGLE) = 4
    
        !四面体
        ElemProp_lineNum(ELEM_TETRAHEDRAL) = 6
        ElemProp_lineList(1,1,ELEM_TETRAHEDRAL) = 1
        ElemProp_lineList(2,1,ELEM_TETRAHEDRAL) = 2
        ElemProp_lineList(1,2,ELEM_TETRAHEDRAL) = 2
        ElemProp_lineList(2,2,ELEM_TETRAHEDRAL) = 3
        ElemProp_lineList(1,3,ELEM_TETRAHEDRAL) = 3
        ElemProp_lineList(2,3,ELEM_TETRAHEDRAL) = 1
        ElemProp_lineList(1,4,ELEM_TETRAHEDRAL) = 1
        ElemProp_lineList(2,4,ELEM_TETRAHEDRAL) = 5
        ElemProp_lineList(1,5,ELEM_TETRAHEDRAL) = 2
        ElemProp_lineList(2,5,ELEM_TETRAHEDRAL) = 5
        ElemProp_lineList(1,6,ELEM_TETRAHEDRAL) = 3
        ElemProp_lineList(2,6,ELEM_TETRAHEDRAL) = 5
        
        ElemProp_lineENUM(1,1,ELEM_TETRAHEDRAL) = 1
        ElemProp_lineENUM(2,1,ELEM_TETRAHEDRAL) = 2
        ElemProp_lineENUM(1,2,ELEM_TETRAHEDRAL) = 1
        ElemProp_lineENUM(2,2,ELEM_TETRAHEDRAL) = 3
        ElemProp_lineENUM(1,3,ELEM_TETRAHEDRAL) = 1
        ElemProp_lineENUM(2,3,ELEM_TETRAHEDRAL) = 4
        ElemProp_lineENUM(1,4,ELEM_TETRAHEDRAL) = 2
        ElemProp_lineENUM(2,4,ELEM_TETRAHEDRAL) = 4
        ElemProp_lineENUM(1,5,ELEM_TETRAHEDRAL) = 3
        ElemProp_lineENUM(2,5,ELEM_TETRAHEDRAL) = 2
        ElemProp_lineENUM(1,6,ELEM_TETRAHEDRAL) = 4
        ElemProp_lineENUM(2,6,ELEM_TETRAHEDRAL) = 3
    
        !六面体
        ElemProp_lineNum(ELEM_HEXAHEDRAL) = 12
        ElemProp_lineList(1,1,ELEM_HEXAHEDRAL) = 1
        ElemProp_lineList(2,1,ELEM_HEXAHEDRAL) = 2
        ElemProp_lineList(1,2,ELEM_HEXAHEDRAL) = 4
        ElemProp_lineList(2,2,ELEM_HEXAHEDRAL) = 3
        ElemProp_lineList(1,3,ELEM_HEXAHEDRAL) = 8
        ElemProp_lineList(2,3,ELEM_HEXAHEDRAL) = 7
        ElemProp_lineList(1,4,ELEM_HEXAHEDRAL) = 5
        ElemProp_lineList(2,4,ELEM_HEXAHEDRAL) = 6
    
        ElemProp_lineList(1,5,ELEM_HEXAHEDRAL) = 1
        ElemProp_lineList(2,5,ELEM_HEXAHEDRAL) = 4
        ElemProp_lineList(1,6,ELEM_HEXAHEDRAL) = 2
        ElemProp_lineList(2,6,ELEM_HEXAHEDRAL) = 3
        ElemProp_lineList(1,7,ELEM_HEXAHEDRAL) = 6
        ElemProp_lineList(2,7,ELEM_HEXAHEDRAL) = 7
        ElemProp_lineList(1,8,ELEM_HEXAHEDRAL) = 5
        ElemProp_lineList(2,8,ELEM_HEXAHEDRAL) = 8
    
        ElemProp_lineList(1,9,ELEM_HEXAHEDRAL) = 1
        ElemProp_lineList(2,9,ELEM_HEXAHEDRAL) = 5
        ElemProp_lineList(1,10,ELEM_HEXAHEDRAL) = 2
        ElemProp_lineList(2,10,ELEM_HEXAHEDRAL) = 6
        ElemProp_lineList(1,11,ELEM_HEXAHEDRAL) = 3
        ElemProp_lineList(2,11,ELEM_HEXAHEDRAL) = 7
        ElemProp_lineList(1,12,ELEM_HEXAHEDRAL) = 4
        ElemProp_lineList(2,12,ELEM_HEXAHEDRAL) = 8
        
        ElemProp_lineENUM(1,1,ELEM_HEXAHEDRAL) = 1
        ElemProp_lineENUM(2,1,ELEM_HEXAHEDRAL) = 3
        ElemProp_lineENUM(1,2,ELEM_HEXAHEDRAL) = 4
        ElemProp_lineENUM(2,2,ELEM_HEXAHEDRAL) = 1
        ElemProp_lineENUM(1,3,ELEM_HEXAHEDRAL) = 2
        ElemProp_lineENUM(2,3,ELEM_HEXAHEDRAL) = 4
        ElemProp_lineENUM(1,4,ELEM_HEXAHEDRAL) = 3
        ElemProp_lineENUM(2,4,ELEM_HEXAHEDRAL) = 2
        
        ElemProp_lineENUM(1,5,ELEM_HEXAHEDRAL) = 6
        ElemProp_lineENUM(2,5,ELEM_HEXAHEDRAL) = 1
        ElemProp_lineENUM(1,6,ELEM_HEXAHEDRAL) = 1
        ElemProp_lineENUM(2,6,ELEM_HEXAHEDRAL) = 5
        ElemProp_lineENUM(1,7,ELEM_HEXAHEDRAL) = 5
        ElemProp_lineENUM(2,7,ELEM_HEXAHEDRAL) = 2
        ElemProp_lineENUM(1,8,ELEM_HEXAHEDRAL) = 2
        ElemProp_lineENUM(2,8,ELEM_HEXAHEDRAL) = 6
        
        ElemProp_lineENUM(1, 9,ELEM_HEXAHEDRAL) = 3
        ElemProp_lineENUM(2, 9,ELEM_HEXAHEDRAL) = 6
        ElemProp_lineENUM(1,10,ELEM_HEXAHEDRAL) = 5
        ElemProp_lineENUM(2,10,ELEM_HEXAHEDRAL) = 3
        ElemProp_lineENUM(1,11,ELEM_HEXAHEDRAL) = 4
        ElemProp_lineENUM(2,11,ELEM_HEXAHEDRAL) = 5
        ElemProp_lineENUM(1,12,ELEM_HEXAHEDRAL) = 6
        ElemProp_lineENUM(2,12,ELEM_HEXAHEDRAL) = 4
    
        !三棱柱
        ElemProp_lineNum(ELEM_WEDGE) = 9
        ElemProp_lineList(1,1,ELEM_WEDGE) = 1
        ElemProp_lineList(2,1,ELEM_WEDGE) = 2
        ElemProp_lineList(1,2,ELEM_WEDGE) = 2
        ElemProp_lineList(2,2,ELEM_WEDGE) = 3
        ElemProp_lineList(1,3,ELEM_WEDGE) = 3
        ElemProp_lineList(2,3,ELEM_WEDGE) = 1
        ElemProp_lineList(1,4,ELEM_WEDGE) = 5
        ElemProp_lineList(2,4,ELEM_WEDGE) = 6
        ElemProp_lineList(1,5,ELEM_WEDGE) = 6
        ElemProp_lineList(2,5,ELEM_WEDGE) = 7
        ElemProp_lineList(1,6,ELEM_WEDGE) = 7
        ElemProp_lineList(2,6,ELEM_WEDGE) = 5
        ElemProp_lineList(1,7,ELEM_WEDGE) = 1
        ElemProp_lineList(2,7,ELEM_WEDGE) = 5
        ElemProp_lineList(1,8,ELEM_WEDGE) = 2
        ElemProp_lineList(2,8,ELEM_WEDGE) = 6
        ElemProp_lineList(1,9,ELEM_WEDGE) = 3
        ElemProp_lineList(2,9,ELEM_WEDGE) = 7
        
        ElemProp_lineENUM(1,1,ELEM_WEDGE) = 1
        ElemProp_lineENUM(2,1,ELEM_WEDGE) = 3
        ElemProp_lineENUM(1,2,ELEM_WEDGE) = 1
        ElemProp_lineENUM(2,2,ELEM_WEDGE) = 4
        ElemProp_lineENUM(1,3,ELEM_WEDGE) = 1
        ElemProp_lineENUM(2,3,ELEM_WEDGE) = 5
        ElemProp_lineENUM(1,4,ELEM_WEDGE) = 3
        ElemProp_lineENUM(2,4,ELEM_WEDGE) = 2
        ElemProp_lineENUM(1,5,ELEM_WEDGE) = 4
        ElemProp_lineENUM(2,5,ELEM_WEDGE) = 2
        ElemProp_lineENUM(1,6,ELEM_WEDGE) = 5
        ElemProp_lineENUM(2,6,ELEM_WEDGE) = 2
        ElemProp_lineENUM(1,7,ELEM_WEDGE) = 3
        ElemProp_lineENUM(2,7,ELEM_WEDGE) = 5
        ElemProp_lineENUM(1,8,ELEM_WEDGE) = 4
        ElemProp_lineENUM(2,8,ELEM_WEDGE) = 3
        ElemProp_lineENUM(1,9,ELEM_WEDGE) = 5
        ElemProp_lineENUM(2,9,ELEM_WEDGE) = 4
    
        !金字塔
        ElemProp_lineNum(ELEM_PYRAMID) = 8
        ElemProp_lineList(1,1,ELEM_PYRAMID) = 1
        ElemProp_lineList(2,1,ELEM_PYRAMID) = 2
        ElemProp_lineList(1,2,ELEM_PYRAMID) = 2
        ElemProp_lineList(2,2,ELEM_PYRAMID) = 3
        ElemProp_lineList(1,3,ELEM_PYRAMID) = 3
        ElemProp_lineList(2,3,ELEM_PYRAMID) = 4
        ElemProp_lineList(1,4,ELEM_PYRAMID) = 4
        ElemProp_lineList(2,4,ELEM_PYRAMID) = 1
        ElemProp_lineList(1,5,ELEM_PYRAMID) = 1
        ElemProp_lineList(2,5,ELEM_PYRAMID) = 5
        ElemProp_lineList(1,6,ELEM_PYRAMID) = 2
        ElemProp_lineList(2,6,ELEM_PYRAMID) = 5
        ElemProp_lineList(1,7,ELEM_PYRAMID) = 3
        ElemProp_lineList(2,7,ELEM_PYRAMID) = 5
        ElemProp_lineList(1,8,ELEM_PYRAMID) = 4
        ElemProp_lineList(2,8,ELEM_PYRAMID) = 5
    
        ElemProp_lineENUM(1,1,ELEM_PYRAMID) = 1
        ElemProp_lineENUM(2,1,ELEM_PYRAMID) = 2
        ElemProp_lineENUM(1,2,ELEM_PYRAMID) = 1
        ElemProp_lineENUM(2,2,ELEM_PYRAMID) = 3
        ElemProp_lineENUM(1,3,ELEM_PYRAMID) = 1
        ElemProp_lineENUM(2,3,ELEM_PYRAMID) = 4
        ElemProp_lineENUM(1,4,ELEM_PYRAMID) = 1
        ElemProp_lineENUM(2,4,ELEM_PYRAMID) = 5
        ElemProp_lineENUM(1,5,ELEM_PYRAMID) = 2
        ElemProp_lineENUM(2,5,ELEM_PYRAMID) = 5
        ElemProp_lineENUM(1,6,ELEM_PYRAMID) = 3
        ElemProp_lineENUM(2,6,ELEM_PYRAMID) = 2
        ElemProp_lineENUM(1,7,ELEM_PYRAMID) = 4
        ElemProp_lineENUM(2,7,ELEM_PYRAMID) = 3
        ElemProp_lineENUM(1,8,ELEM_PYRAMID) = 5
        ElemProp_lineENUM(2,8,ELEM_PYRAMID) = 4
    
    endsubroutine
    
    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine iniElemProp_DiviList
        use mod_PreDefine_Elem
        use mod_ElemProp
        implicit none
    
        ElemProp_diviNum(:) = 0
        ElemProp_diviKind(:) = 0
        ElemProp_diviList(:,:,:) = 0
    
        !线
    
        !三角形
        ElemProp_diviNum(ELEM_TRIANGLE) = 1
        ElemProp_diviKind(ELEM_TRIANGLE) = ELEM_TRIANGLE
        ElemProp_diviList(1,1,ELEM_TRIANGLE) = 1
        ElemProp_diviList(2,1,ELEM_TRIANGLE) = 2
        ElemProp_diviList(3,1,ELEM_TRIANGLE) = 3
    
        !四边形
        ElemProp_diviNum(ELEM_RECTANGLE) = 2
        ElemProp_diviKind(ELEM_RECTANGLE) = ELEM_TRIANGLE
        ElemProp_diviList(1,1,ELEM_RECTANGLE) = 1
        ElemProp_diviList(2,1,ELEM_RECTANGLE) = 2
        ElemProp_diviList(3,1,ELEM_RECTANGLE) = 3
        ElemProp_diviList(1,2,ELEM_RECTANGLE) = 3
        ElemProp_diviList(2,2,ELEM_RECTANGLE) = 4
        ElemProp_diviList(3,2,ELEM_RECTANGLE) = 1
    
        !四面体
        ElemProp_diviNum(ELEM_TETRAHEDRAL) = 1
        ElemProp_diviKind(ELEM_TETRAHEDRAL) = ELEM_TETRAHEDRAL
        ElemProp_diviList(1,1,ELEM_TETRAHEDRAL) = 1
        ElemProp_diviList(2,1,ELEM_TETRAHEDRAL) = 2
        ElemProp_diviList(3,1,ELEM_TETRAHEDRAL) = 3
        ElemProp_diviList(4,1,ELEM_TETRAHEDRAL) = 5
    
        !六面体
        ElemProp_diviNum(ELEM_HEXAHEDRAL) = 6
        ElemProp_diviKind(ELEM_HEXAHEDRAL) = ELEM_TETRAHEDRAL
        ElemProp_diviList(1,1,ELEM_HEXAHEDRAL) = 1
        ElemProp_diviList(2,1,ELEM_HEXAHEDRAL) = 2
        ElemProp_diviList(3,1,ELEM_HEXAHEDRAL) = 3
        ElemProp_diviList(4,1,ELEM_HEXAHEDRAL) = 5
        ElemProp_diviList(1,2,ELEM_HEXAHEDRAL) = 2
        ElemProp_diviList(2,2,ELEM_HEXAHEDRAL) = 3
        ElemProp_diviList(3,2,ELEM_HEXAHEDRAL) = 5
        ElemProp_diviList(4,2,ELEM_HEXAHEDRAL) = 6
        ElemProp_diviList(1,3,ELEM_HEXAHEDRAL) = 3
        ElemProp_diviList(2,3,ELEM_HEXAHEDRAL) = 5
        ElemProp_diviList(3,3,ELEM_HEXAHEDRAL) = 6
        ElemProp_diviList(4,3,ELEM_HEXAHEDRAL) = 7
    
        ElemProp_diviList(1,4,ELEM_HEXAHEDRAL) = 1
        ElemProp_diviList(2,4,ELEM_HEXAHEDRAL) = 3
        ElemProp_diviList(3,4,ELEM_HEXAHEDRAL) = 4
        ElemProp_diviList(4,4,ELEM_HEXAHEDRAL) = 7
        ElemProp_diviList(1,5,ELEM_HEXAHEDRAL) = 1
        ElemProp_diviList(2,5,ELEM_HEXAHEDRAL) = 4
        ElemProp_diviList(3,5,ELEM_HEXAHEDRAL) = 7
        ElemProp_diviList(4,5,ELEM_HEXAHEDRAL) = 8
        ElemProp_diviList(1,6,ELEM_HEXAHEDRAL) = 1
        ElemProp_diviList(2,6,ELEM_HEXAHEDRAL) = 5
        ElemProp_diviList(3,6,ELEM_HEXAHEDRAL) = 7
        ElemProp_diviList(4,6,ELEM_HEXAHEDRAL) = 8
    
        !三棱柱
        ElemProp_diviNum(ELEM_WEDGE) = 3
        ElemProp_diviKind(ELEM_WEDGE) = ELEM_TETRAHEDRAL
        ElemProp_diviList(1,1,ELEM_WEDGE) = 1
        ElemProp_diviList(2,1,ELEM_WEDGE) = 2
        ElemProp_diviList(3,1,ELEM_WEDGE) = 3
        ElemProp_diviList(4,1,ELEM_WEDGE) = 5
        ElemProp_diviList(1,2,ELEM_WEDGE) = 2
        ElemProp_diviList(2,2,ELEM_WEDGE) = 3
        ElemProp_diviList(3,2,ELEM_WEDGE) = 5
        ElemProp_diviList(4,2,ELEM_WEDGE) = 6
        ElemProp_diviList(1,3,ELEM_WEDGE) = 3
        ElemProp_diviList(2,3,ELEM_WEDGE) = 5
        ElemProp_diviList(3,3,ELEM_WEDGE) = 6
        ElemProp_diviList(4,3,ELEM_WEDGE) = 7
    
        !金字塔
        ElemProp_diviNum(ELEM_PYRAMID) = 2
        ElemProp_diviKind(ELEM_PYRAMID) = ELEM_TETRAHEDRAL
        ElemProp_diviList(1,1,ELEM_PYRAMID) = 1
        ElemProp_diviList(2,1,ELEM_PYRAMID) = 2
        ElemProp_diviList(3,1,ELEM_PYRAMID) = 3
        ElemProp_diviList(4,1,ELEM_PYRAMID) = 5
        ElemProp_diviList(1,2,ELEM_PYRAMID) = 1
        ElemProp_diviList(2,2,ELEM_PYRAMID) = 3
        ElemProp_diviList(3,2,ELEM_PYRAMID) = 4
        ElemProp_diviList(4,2,ELEM_PYRAMID) = 5
    
    
    endsubroutine
    
endsubroutine
!
