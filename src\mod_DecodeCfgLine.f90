module mod_DecodeCfgLine
    use mod_PreDefine_Dimension
    use mod_PreDefine_Precision
    use mod_StringOP
    implicit none
    !
contains
    !
    subroutine getDomaID(val_DomaLine, val_DomaID)
        implicit none
        character(len=STRLEN),intent(in ):: val_DomaLine
        integer              ,intent(out):: val_DomaID
        !
        logical:: isExist
        character(len=STRLEN):: iLine, sLine

        iLine = adjustl(val_DomaLine)
        call getSubStringData(iLine, '#DOMA', sLine, isExist)
        if(isExist) then
            read(sLine,*) val_DomaID
        endif

    end subroutine getDomaID
    !
    subroutine getMarkID(val_MarkLine, val_MarkID)
        implicit none
        character(len=STRLEN),intent(in ):: val_MarkLine
        integer              ,intent(out):: val_MarkID
        !
        logical:: isExist
        character(len=STRLEN):: iLine, sLine

        iLine = adjustl(val_MarkLine)
        call getSubStringData(iLine, '#MARK', sLine, isExist)
        if(isExist) then
            read(sLine,*) val_MarkID
        endif

    end subroutine getMarkID
    !
    subroutine getMRFID(val_MRFLine, val_MRFID)
        implicit none
        character(len=STRLEN),intent(in ):: val_MRFLine
        integer              ,intent(out):: val_MRFID
        !
        logical:: isExist
        character(len=STRLEN):: iLine, sLine

        iLine = adjustl(val_MRFLine)
        call getSubStringData(iLine, '#MRF', sLine, isExist)
        if(isExist) then
            read(sLine,*) val_MRFID
        endif

    end subroutine getMRFID
    !
    subroutine decodeDomaLine(val_DomaLine, val_DomaInfo)
        use mod_PreDefine_Doma
        use mod_Config, only: typ_DomaInfo
        implicit none
        character(len=STRLEN),intent(in):: val_DomaLine
        type(typ_DomaInfo),intent(inout):: val_DomaInfo
        !
        logical:: isExist
        character(len=STRLEN):: iLine, sLine

        iLine = adjustl(val_DomaLine)

        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#DOMA', sLine, isExist)
        if(isExist) then
            read(sLine,*) val_DomaInfo%domaID
        endif

        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#INIT', sLine, isExist)
        if(isExist) then
            read(sLine,*) val_DomaInfo%markID
        endif

        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#TYPE', sLine, isExist)
        if(isExist) then
            if(sLine(1:4) == "FLUI") then
                val_DomaInfo%domaKD = DOMA_FLUI   !
            elseif(sLine(1:4) == "SOLI") then
                val_DomaInfo%domaKD = DOMA_SOLI   !
            else
            end if
        endif

    end subroutine decodeDomaLine
    !
    subroutine decodeMarkLine(val_MarkLine, val_MarkInfo)
        use mod_PreDefine_Mark
        use mod_Config, only: typ_MarkInfo
        implicit none
        character(len=STRLEN),intent(in):: val_MarkLine
        type(typ_MarkInfo),intent(inout):: val_MarkInfo
        !
        integer:: markKD
        logical:: isExist
        character(len=STRLEN):: iLine, sLine

        iLine = adjustl(val_MarkLine)

        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#MARK', sLine, isExist)
        if(isExist) then
            read(sLine,*) val_MarkInfo%markID
        endif

        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#TYPE', sLine, isExist)
        if(isExist) then
            if(sLine(1:4) == "NONE") then
                markKD = MARK_NONE   !空边界，用于重叠
            elseif(sLine(1:4) == "WALL") then
                markKD = MARK_WALL   !物面边界
            elseif(sLine(1:4) == "EQTW") then
                markKD = MARK_EQTW   !物面边界
            elseif(sLine(1:4) == "EULW") then
                markKD = MARK_EULW   !物面边界
            elseif(sLine(1:4) == "MOVW") then
                markKD = MARK_MOVW   !物面边界

            elseif(sLine(1:4) == "INLE") then
                markKD = MARK_INLE   !入流边界
            elseif(sLine(1:4) == "INLM") then
                markKD = MARK_INLM   !入流边界

            elseif(sLine(1:4) == "OUTL") then
                markKD = MARK_OUTL   !出流边界
            elseif(sLine(1:4) == "OUTP") then
                markKD = MARK_OUTP   !出流边界
            elseif(sLine(1:4) == "OUTM") then
                markKD = MARK_OUTM   !出流边界

            elseif(sLine(1:4) == "ATTA") then
                markKD = MARK_ATTA   !接触边界
            elseif(sLine(1:4) == "SYMM") then
                markKD = MARK_SYMM   !对称边界
            elseif(sLine(1:4) == "PFAR") then
                markKD = MARK_PFAR   !远场边界
            elseif(sLine(1:4) == "SLID") then
                markKD = MARK_SLID   !滑移边界
            elseif(sLine(1:4) == "OVST") then
                markKD = MARK_OVST   !嵌套边界
            elseif(sLine(1:4) == "LPRD") then
                markKD = MARK_LPRD   !周期边界
            elseif(sLine(1:4) == "CPRD") then
                markKD = MARK_CPRD   !周期边界

            elseif(sLine(1:4) == "UDBC") then
                markKD = MARK_UDBC   !自定义边界,未用到
            else
                pause "Wrong MarkType in parms.cfg"//sLine(1:4)
                markKD = MARK_NONE   !空边界，用于重叠
            end if
        endif

        val_MarkInfo%markKD = markKD

        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#AREA', sLine, isExist)
        if(isExist) then
            if(markKD /= MARK_INLM .and. markKD /= MARK_OUTM) write(*,*) "Wrong MarkLine:"//trim(adjustl(iLine))
            read(sLine,*) val_MarkInfo%surfArea
        endif

        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#PRES', sLine, isExist)
        if(isExist) then
            if(markKD /= MARK_INLM .and. markKD /= MARK_OUTP .and. markKD /= MARK_OUTM) write(*,*) "Wrong MarkLine:"//trim(adjustl(iLine))
            read(sLine,*) val_MarkInfo%surfPres
        endif

        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#TEMP', sLine, isExist)
        if(isExist) then
            if(markKD /= MARK_INLM .and. markKD /= MARK_OUTM) write(*,*) "Wrong MarkLine:"//trim(adjustl(iLine))
            read(sLine,*) val_MarkInfo%surfTemp
        endif

        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#MASS', sLine, isExist)
        if(isExist) then
            if(markKD /= MARK_INLM .and. markKD /= MARK_OUTM) write(*,*) "Wrong MarkLine:"//trim(adjustl(iLine))
            read(sLine,*) val_MarkInfo%surfMass
        endif

        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#NORM', sLine, isExist)
        if(isExist) then
            if(markKD /= MARK_INLM .and. markKD /= MARK_OUTM) write(*,*) "Wrong MarkLine:"//trim(adjustl(iLine))
            read(sLine,*) val_MarkInfo%surfNorm(1:nDim)
        endif


        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#VELO', sLine, isExist)
        if(isExist) then
            if(markKD /= MARK_MOVW) write(*,*) "Wrong MarkLine:"//trim(adjustl(iLine))
            read(sLine,*) val_MarkInfo%transVelo(1:nDim)
        endif

        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#ORGN', sLine, isExist)
        if(isExist) then
            if(markKD /= MARK_MOVW) write(*,*) "Wrong MarkLine:"//trim(adjustl(iLine))
            read(sLine,*) val_MarkInfo%rotatOrgn(1:nDim)
        endif

        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#AXIS', sLine, isExist)
        if(isExist) then
            if(markKD /= MARK_MOVW) write(*,*) "Wrong MarkLine:"//trim(adjustl(iLine))
            read(sLine,*) val_MarkInfo%rotatAxis(1:nDim)
        endif

        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#OMGA', sLine, isExist)
        if(isExist) then
            if(markKD /= MARK_MOVW) write(*,*) "Wrong MarkLine:"//trim(adjustl(iLine))
            read(sLine,*) val_MarkInfo%rotatOmga
        endif


        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#BPFW', sLine, isExist)
        if(isExist) then
            if(markKD /= MARK_OUTP) write(*,*) "Wrong MarkLine:"//trim(adjustl(iLine))
            read(sLine,*) val_MarkInfo%BackPresFact, val_MarkInfo%BackPrecIncr
        endif


        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#TOTP', sLine, isExist)
        if(isExist) then
            if(markKD /= MARK_OUTP) write(*,*) "Wrong MarkLine:"//trim(adjustl(iLine))
            read(sLine,*) val_MarkInfo%surfPtot
        endif


        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#TOTT', sLine, isExist)
        if(isExist) then
            if(markKD /= MARK_OUTP) write(*,*) "Wrong MarkLine:"//trim(adjustl(iLine))
            read(sLine,*) val_MarkInfo%surfTtot
        endif


    end subroutine decodeMarkLine
    !
    subroutine decodeMRFLine(val_MRFLine, val_MRFInfo)
        use mod_PreDefine_Mark
        use mod_Config, only: typ_MRFInfo
        implicit none
        character(len=STRLEN),intent(in):: val_MRFLine
        type(typ_MRFInfo),intent(inout):: val_MRFInfo
        !
        integer:: mKD
        logical:: isExist
        character(len=STRLEN):: iLine, sLine

        iLine = adjustl(val_MRFLine)

        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#MRF', sLine, isExist)
        if(isExist) then
            read(sLine,*) val_MRFInfo%mrfID
        endif

        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#ZONE', sLine, isExist)
        if(isExist) then
            read(sLine,*) val_MRFInfo%zoneID
        endif

        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#VELO', sLine, isExist)
        if(isExist) then
            read(sLine,*) val_MRFInfo%transVelo
        endif

        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#ORGN', sLine, isExist)
        if(isExist) then
            read(sLine,*) val_MRFInfo%rotatOrgn(1:3)
        endif

        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#AXIS', sLine, isExist)
        if(isExist) then
            read(sLine,*) val_MRFInfo%rotatAxis(1:3)
        endif

        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#OMGA', sLine, isExist)
        if(isExist) then
            read(sLine,*) val_MRFInfo%rotatOmga
        endif

        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#RADU', sLine, isExist)
        if(isExist) then
            read(sLine,*) val_MRFInfo%mrfRadu
        endif

        !write(*,*) trim(iLine)
        call getSubStringData(iLine, '#XLEN', sLine, isExist)
        if(isExist) then
            read(sLine,*) val_MRFInfo%mrfXLen
        endif

    end subroutine decodeMRFLine
    !
end module mod_DecodeCfgLine