!
!---------------------------------------------
!
!---------------------------------------------
module mod_calMeshMapp
    use mod_PreDefine_Flag
    use mod_Project
    use mod_Interface_AllocateArray
    implicit none
    !
contains
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine calMeshMapp(val_iPart)
        implicit none
        integer,intent(in):: val_iPart
    
    
        !---------------------------------------------
        if((val_iPart < 1).or.(val_ipart > nMeshPart)) then
            return
        endif


        !---------------------------------------------
        iMeshMapp(val_iPart)%nGPoin = iGlobalMesh%nPoin
        iMeshMapp(val_iPart)%nGElem = iGlobalMesh%nElem
        iMeshMapp(val_iPart)%nGSate = iGlobalMesh%nSate
        iMeshMapp(val_iPart)%nGBele = iGlobalMesh%nBele
        iMeshMapp(val_iPart)%nGBelv = iGlobalMesh%nBelv
    
        call allocateArray(iMeshMapp(val_iPart)%iFlag_GPoin, iMeshMapp(val_iPart)%nGPoin)
        call allocateArray(iMeshMapp(val_iPart)%iFlag_GElem, iMeshMapp(val_iPart)%nGElem)
        call allocateArray(iMeshMapp(val_iPart)%iFlag_GSate, iMeshMapp(val_iPart)%nGSate)
        call allocateArray(iMeshMapp(val_iPart)%iFlag_GBele, iMeshMapp(val_iPart)%nGBele)
        call allocateArray(iMeshMapp(val_iPart)%iFlag_GBelv, iMeshMapp(val_iPart)%nGBelv)

        call calIFlagInMeshMapp(val_iPart)

        call getNLocalInMeshMapp(val_iPart)


        !---------------------------------------------
        call allocateArray(iMeshMapp(val_iPart)%iMapLToG_Poin, iMeshMapp(val_iPart)%nLPoin)
        call allocateArray(iMeshMapp(val_iPart)%iMapLToG_Elem, iMeshMapp(val_iPart)%nLElem)
        call allocateArray(iMeshMapp(val_iPart)%iMapLToG_Sate, iMeshMapp(val_iPart)%nLSate)
        call allocateArray(iMeshMapp(val_iPart)%iMapLToG_Bele, iMeshMapp(val_iPart)%nLBele)
        call allocateArray(iMeshMapp(val_iPart)%iMapLToG_Belv, iMeshMapp(val_iPart)%nLBelv)
    
        call allocateArray(iMeshMapp(val_iPart)%iMapGToL_Poin, iMeshMapp(val_iPart)%nGPoin)
        call allocateArray(iMeshMapp(val_iPart)%iMapGToL_Elem, iMeshMapp(val_iPart)%nGElem)
        call allocateArray(iMeshMapp(val_iPart)%iMapGToL_Sate, iMeshMapp(val_iPart)%nGSate)
        call allocateArray(iMeshMapp(val_iPart)%iMapGToL_Bele, iMeshMapp(val_iPart)%nGBele)
        call allocateArray(iMeshMapp(val_iPart)%iMapGToL_Belv, iMeshMapp(val_iPart)%nGBelv)

        call calIMapInMeshMapp(val_iPart)

        deallocate(iMeshMapp(val_iPart)%iFlag_GPoin)
        deallocate(iMeshMapp(val_iPart)%iFlag_GElem)
        deallocate(iMeshMapp(val_iPart)%iFlag_GSate)
        deallocate(iMeshMapp(val_iPart)%iFlag_GBele)
        deallocate(iMeshMapp(val_iPart)%iFlag_GBelv)

        !---------------------------------------------
        call allocateArray(iMeshMapp(val_iPart)%iFlag_LPoin, iMeshMapp(val_iPart)%nLPoin)

        call getIFlag_LPoin(val_iPart)

        return
    
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine calIFlagInMeshMapp(val_iPart)
        implicit none
        integer,intent(in):: val_iPart
        !
        integer:: i,j
        integer:: poinID
        
        
        !---------------------------------------------
        !---------------------------------------------
        iMeshMapp(val_iPart)%iFlag_GPoin(:) = FLAG_INVAL
        iMeshMapp(val_iPart)%iFlag_GElem(:) = FLAG_INVAL
        iMeshMapp(val_iPart)%iFlag_GBele(:) = FLAG_INVAL
        iMeshMapp(val_iPart)%iFlag_GBelv(:) = FLAG_INVAL
        iMeshMapp(val_iPart)%iFlag_GSate(:) = FLAG_INVAL

        !---------------------------------------------
        !---------------------------------------------
        !$omp parallel do private(j)
        do i=1,iGlobalMesh%nPoin
            if(iGlobalMesh%iPart_Poin(i) == val_iPart) then
                iMeshMapp(val_iPart)%iFlag_GPoin(i) = FLAG_VALID
                
                do j=iGlobalMesh%kSate_Poin(i)+1,iGlobalMesh%kSate_Poin(i+1)
                    iMeshMapp(val_iPart)%iFlag_GSate(j) = FLAG_VALID
                enddo
            endif
        enddo
        !$omp end parallel do
        
        !---------------------------------------------
        !---------------------------------------------
        !$omp parallel do private(poinID,j)
        do i=1,iGLobalMesh%nElem
            do j=1,4*nDim-4
                poinID = iGlobalMesh%iElem(j,i)
                
                if(iMeshMapp(val_iPart)%iFlag_GPoin(poinID) == FLAG_VALID) then
                    iMeshMapp(val_iPart)%iFlag_GElem(i) = FLAG_VALID
                    exit
                endif
            enddo
        enddo
        !$omp end parallel do

        !$omp parallel do private(j,poinID)
        do i=1,iGLobalMesh%nElem
            if(iMeshMapp(val_iPart)%iFlag_GElem(i) == FLAG_VALID) then
                do j=1,4*nDim-4
                    poinID = iGlobalMesh%iElem(j,i)
                    
                    if(iMeshMapp(val_iPart)%iFlag_GPoin(poinID) == FLAG_INVAL) then
                        iMeshMapp(val_iPart)%iFlag_GPoin(poinID) = FLAG_VALID
                    endif
                enddo
            endif
        enddo
        !$omp end parallel do

        !---------------------------------------------
        !---------------------------------------------
        !$omp parallel do private(j,poinID)
        do i=1,iGlobalMesh%nBele
            do j=1,2*nDim-2
                poinID = iGlobalMesh%iBele(j,i)
                
                if(iMeshMapp(val_iPart)%iFlag_GPoin(poinID) == FLAG_VALID) then
                    iMeshMapp(val_iPart)%iFlag_GBele(i) = FLAG_VALID
                    exit
                endif
            enddo
        enddo
        !$omp end parallel do

        !---------------------------------------------
        !---------------------------------------------
        !$omp parallel do private(poinID)
        do i=1,iGlobalMesh%nBelv
            poinID = iGlobalMesh%iBelvProp(i,1)
            if(iGlobalMesh%iPart_Poin(poinID) == val_iPart) then
                iMeshMapp(val_iPart)%iFlag_GBelv(i) = FLAG_VALID
            endif
        enddo
        !$omp end parallel do

    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getNLocalInMeshMapp(val_iPart)
        implicit none
        integer,intent(in):: val_iPart
        !
        integer:: i,j
        integer:: iCount,nSate
        
        
        !---------------------------------------------
        !---------------------------------------------
        iCount = 0
        do i=1,iMeshMapp(val_ipart)%nGPoin
            if(iMeshMapp(val_iPart)%iFlag_GPoin(i) == FLAG_VALID)then
                iCount = iCount + 1
            endif
        enddo
        iMeshMapp(val_ipart)%nLPoin = iCount
    
        
        !---------------------------------------------
        !---------------------------------------------
        iCount = 0
        do i=1,iMeshMapp(val_iPart)%nGElem
            if(iMeshMapp(val_iPart)%iFlag_GElem(i) == FLAG_VALID) then
                iCount = iCount + 1
            endif
        enddo
        iMeshMapp(val_ipart)%nLElem = iCount
    
        
        !---------------------------------------------
        !---------------------------------------------
        iCount = 0
        do i=1,iMeshMapp(val_iPart)%nGBele
            if(iMeshMapp(val_iPart)%iFlag_GBele(i) == FLAG_VALID) then
                iCount = iCount + 1
            endif
        enddo
        iMeshMapp(val_ipart)%nLBele = iCount
        
        
        !---------------------------------------------
        !---------------------------------------------
        iCount = 0
        do i=1,iMeshMapp(val_iPart)%nGBelv
            if(iMeshMapp(val_iPart)%iFlag_GBelv(i) == FLAG_VALID) then
                iCount = iCount + 1
            endif
        enddo
        iMeshMapp(val_ipart)%nLBelv = iCount
    
        
        !---------------------------------------------
        !---------------------------------------------
        iCount = 0
        do i=1,iMeshMapp(val_iPart)%nGPoin
            if(iMeshMapp(val_iPart)%iFlag_GPoin(i) == FLAG_VALID) then
                nSate = iGlobalMesh%kSate_Poin(i+1) - iGlobalMesh%kSate_Poin(i)
                
                iCount = iCount + nSate
            endif
        enddo
        iMeshMapp(val_ipart)%nLSate = iCount
        
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine calIMapInMeshMapp(val_iPart)
        use mod_TypeDef_Mesh
        use mod_PreDefine_Flag
        use mod_PreDefine_IOPort
        use mod_Project
        use mod_Config
        implicit none
        integer,intent(in):: val_iPart
        !
        integer:: i,j
        integer:: iCount,nSate
    
    
        !---------------------------------------------
        iMeshMapp(val_iPart)%iMapGToL_Poin(:) = 0
        iMeshMapp(val_iPart)%iMapLToG_Poin(:) = 0
        iMeshMapp(val_iPart)%iMapGToL_Elem(:) = 0
        iMeshMapp(val_iPart)%iMapLToG_Elem(:) = 0
        iMeshMapp(val_iPart)%iMapGToL_Sate(:) = 0
        iMeshMapp(val_iPart)%iMapLToG_Sate(:) = 0
        iMeshMapp(val_iPart)%iMapGToL_Bele(:) = 0
        iMeshMapp(val_iPart)%iMapLToG_Bele(:) = 0
        iMeshMapp(val_iPart)%iMapGToL_Belv(:) = 0
        iMeshMapp(val_iPart)%iMapLToG_Belv(:) = 0
        
        !---------------------------------------------
        iCount = 0
        do i=1,iMeshMapp(val_ipart)%nGPoin
            if(iMeshMapp(val_iPart)%iFlag_GPoin(i) == FLAG_VALID)then
                iCount = iCount + 1
            
                iMeshMapp(val_iPart)%iMapGToL_Poin(i) = iCount
                iMeshMapp(val_iPart)%iMapLToG_Poin(iCount) = i
            
                nSate = iGlobalMesh%kSate_Poin(i+1) - iGlobalMesh%kSate_Poin(i)
            endif
        enddo
        
        !---------------------------------------------
        iCount = 0
        do i=1,iMeshMapp(val_iPart)%nGElem
            if(iMeshMapp(val_iPart)%iFlag_GElem(i) == FLAG_VALID) then
                iCount = iCount + 1
    
                iMeshMapp(val_iPart)%iMapGToL_Elem(i) = iCount
                iMeshMapp(val_iPart)%iMapLToG_Elem(iCount) = i
            endif
        enddo
        
        !---------------------------------------------
        iCount = 0
        do i=1,iMeshMapp(val_iPart)%nGBele
            if(iMeshMapp(val_iPart)%iFlag_GBele(i) == FLAG_VALID) then
                iCount = iCount + 1
    
                iMeshMapp(val_iPart)%iMapGToL_Bele(i) = iCount
                iMeshMapp(val_iPart)%iMapLToG_Bele(iCount) = i
            endif
        enddo
        
        !---------------------------------------------
        iCount = 0
        do i=1,iMeshMapp(val_iPart)%nGBelv
            if(iMeshMapp(val_iPart)%iFlag_GBelv(i) == FLAG_VALID) then
                iCount = iCount + 1
    
                iMeshMapp(val_iPart)%iMapGToL_Belv(i) = iCount
                iMeshMapp(val_iPart)%iMapLToG_Belv(iCount) = i
            endif
        enddo
        
        !---------------------------------------------
        iCount = 0
        do i=1,iMeshMapp(val_iPart)%nGPoin
            if(iMeshMapp(val_iPart)%iFlag_GPoin(i) == FLAG_VALID) then
                do j=iGlobalMesh%kSate_Poin(i)+1,iGLobalMesh%kSate_Poin(i+1)
                    iCount = iCount + 1
                
                    iMeshMapp(val_iPart)%iMapGToL_Sate(j) = iCount
                    iMeshMapp(val_iPart)%iMapLToG_Sate(iCount) = j
                enddo
            endif
        enddo
    
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getIFlag_LPoin(val_iPart)
        use mod_Config
        implicit none
        integer,intent(in):: val_iPart
        !
        integer:: i,poinOD
        
        
        iMeshMapp(val_iPart)%iFlag_LPoin(:) = FLAG_VALID

        !$omp parallel do private(poinOD)
        do i=1,iMeshMapp(val_iPart)%nLPoin
            poinOD = iMeshMapp(val_iPart)%iMapLToG_Poin(i)
            
            if(poinOD <= 0) then
                iMeshMapp(val_iPart)%iFlag_LPoin(i) = FLAG_EXTE
                cycle
            endif
            
            if(iGlobalMesh%iPart_Poin(poinOD) == val_iPart) then
                iMeshMapp(val_iPart)%iFlag_LPoin(i) = FLAG_VALID
            else
                iMeshMapp(val_iPart)%iFlag_LPoin(i) = FLAG_EXCH
            endif
        enddo
        !$omp end parallel do
            
    endsubroutine
    !
endmodule
!
    
    