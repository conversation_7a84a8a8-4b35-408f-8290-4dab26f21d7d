  
!-------------------------------------
!
!-------------------------------------
subroutine calILine(val_iMesh)
    use mod_TypeDef_Mesh
    use mod_PreDefine_IOPort
    use mod_PreDefine_Dimension
    implicit none
    type(typ_GlobalMesh),intent(inout):: val_iMesh
    !
    integer:: allLineCount
    integer:: nLineOnPoin(val_iMesh%nPoin+1)
    integer,allocatable:: allLineList(:,:)
    !
    integer:: i,j,elemID,lineIP
    logical:: isOk
    
    !
    call getNLineOnPoin(nDim                , &
                        val_iMesh%nElem     , &
                        val_iMesh%iElem     , &
                        val_iMesh%iElemKind , &
                        val_iMesh%nPoin     , &
                        nLineOnPoin         )
    
    !
    allLineCount = nLineOnPoin(val_iMesh%nPoin+1)
    
    allocate(allLineList(3 , allLineCount))
    allLineList(:,:) = 0
    
    !
    call getAllLine(nDim                , &
                    val_iMesh%nElem     , &
                    val_iMesh%iElem     , &
                    val_iMesh%iElemKind , &
                    val_iMesh%nPoin     , &
                    nLineOnPoin         , &
                    allLineCount        , &
                    allLineList         )
    
    !
    call getNLine(allLineCount,allLineList,val_iMesh%nLine)
    !
    val_iMesh%nElem_Line = allLineCount
    !
    allocate(val_iMesh%iLine(4 , val_iMesh%nLine))
    allocate(val_iMesh%kElem_Line(val_iMesh%nLine + 1))
    allocate(val_iMesh%iElem_Line(2 , val_iMesh%nElem_Line))
    val_iMesh%iLine(:,:)        = 0
    val_iMesh%kElem_Line(:)     = 0
    val_iMesh%iElem_Line(:,:)   = 0
    
    !
    call getILine(val_iMesh%nElem_Line  , &
                  allLineList           , &
                  val_iMesh%nLine       , &
                  val_iMesh%iLine       , &
                  val_iMesh%kElem_Line  , &
                  val_iMesh%iElem_Line  )
    !
    allocate(val_iMesh%iFlag_Line(val_iMesh%nLine))
    val_iMesh%iFlag_Line(:) = 0
    
    !
    call getIFlag_Line(val_iMesh , val_iMesh%iFlag_Line)
    
    !
    do i=1,val_iMesh%nLine
        do j=val_iMesh%kElem_Line(i)+1,val_iMesh%kElem_Line(i+1)
            elemID = val_iMesh%iElem_Line(1,j)
            
            call getLinePlaceInElem(nDim                        , &
                                    val_iMesh%iElem(:,elemID)   , &
                                    val_iMesh%iElemKind(elemID) , &
                                    val_iMesh%iLine(1:2,i)      , &
                                    lineIP                      )
            
            val_iMesh%iElem_Line(2,j) = lineIP
            
        enddo
    enddo
    
    deallocate(allLineList)
    !
    call chkILine(val_iMesh,isOk)
    if(.not.isOk) then
        write(ioPort_Out,*) 'chkILine: is not ok!'
        stop
    endif
    
    return
    
contains
    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getNLineOnPoin(val_nDim,val_nElem,val_iElem,val_elemKind,val_nPoin,val_nLineOnPoin)
        use mod_ElemProp
        implicit none
        integer,intent(in):: val_nDim
        integer,intent(in):: val_nElem
        integer,intent(in):: val_iElem(val_nDim*4-4,val_nElem)
        integer,intent(in):: val_elemKind(val_nElem)
        integer,intent(in):: val_nPoin
        integer,intent(inout):: val_nLineOnPoin(val_nPoin+1)
        !
        integer:: i,j,k
        integer:: ick,iCount
        integer:: n1,n2,nb,ne,icci(val_nDim*4-4)
        !
        val_nLineOnPoin(:) = 0
        !
        iCount = 0
        !!!$omp parallel do private(ick,icci,j,nb,ne)
        do i=1,val_nElem
            ick = val_elemKind(i)
            icci(:) = val_iElem(:,i)
            
            do j=1,elemProp_LineNum(ick)
                nb = icci(elemProp_LineList(1,j,ick))
                ne = icci(elemProp_LineList(2,j,ick))
                
                nb = min(nb,ne)
                !!!$omp atomic
                val_nLineOnPoin(nb+1) = val_nLineOnPoin(nb+1) + 1
            enddo
        enddo
        !!!$omp end parallel do
        !
        val_nLineOnPoin(1) = 0
        do i=2,val_nPoin+1
            val_nLineOnPoin(i) =  val_nLineOnPoin(i-1) + val_nLineOnPoin(i)
        enddo
        
    endsubroutine
    
    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getAllLine(val_nDim,val_nElem,val_iElem,val_elemKind,val_nPoin,val_nLineOnPoin,val_allLineCount,val_allLineList)
        use mod_ElemProp
        implicit none
        integer,intent(in):: val_nDim
        integer,intent(in):: val_nElem
        integer,intent(in):: val_iElem(val_nDim*4-4,val_nElem)
        integer,intent(in):: val_elemKind(val_nElem)
        integer,intent(in):: val_nPoin
        integer,intent(inout):: val_nLineOnPoin(val_nPoin+1)
        integer,intent(in)   :: val_allLineCount
        integer,intent(inout):: val_allLineList(3,val_allLineCount)
        !
        integer:: ii,i,j,k,iid
        integer:: ick,iCount
        integer:: n1,n2,nb,ne,icci(val_nDim*4-4)
        integer:: kb,ke,iTemp
        !
        iCount = 0
        !!!$omp parallel do private(j,ick,icci,n1,n2,nb,ne,iid)
        do i=1,val_nElem
            ick = val_elemKind(i)
            icci(:) = val_iElem(:,i)
            
            do j=1,elemProp_LineNum(ick)
                n1 = icci(elemProp_LineList(1,j,ick))
                n2 = icci(elemProp_LineList(2,j,ick))
                
                nb = min(n1,n2)
                ne = max(n1,n2)

                !!!$omp atomic capture
                iid = val_nLineOnPoin(nb) + 1
                val_nLineOnPoin(nb) = val_nLineOnPoin(nb) + 1
                !!!$omp end atomic

                val_allLineList(1, iid) = nb
                val_allLineList(2, iid) = ne
                val_allLineList(3, iid) = i
            enddo
        enddo
        !!!$omp end parallel do
        
        do i = val_nPoin,1,-1
            val_nLineOnPoin(i+1) = val_nLineOnPoin(i)
        enddo
        val_nLineOnPoin(1) = 0

        !$omp parallel do private(i,j,kb,ke,iTemp,ne)
        do ii=1,val_nPoin
            kb = val_nLineOnPoin(ii)+1
            ke = val_nLineOnPoin(ii+1)
            
            if(kb == ke) cycle
            
            do i=kb,ke-1
                do j=i+1,ke
                    if(val_allLineList(2,i) > val_allLineList(2,j)) then
                        iTemp                = val_allLineList(2,i)
                        val_allLineList(2,i) = val_allLineList(2,j)
                        val_allLineList(2,j) = iTemp
                        
                        iTemp                = val_allLineList(3,i)
                        val_allLineList(3,i) = val_allLineList(3,j)
                        val_allLineList(3,j) = iTemp
                        
                    endif
                enddo
            enddo   
            
            ne = val_allLineList(2,kb)
            do i=kb+1,ke
                if(val_allLineList(2,i) == ne) then
                    val_allLineList(1:2,i) = 0
                else
                    ne = val_allLineList(2,i)
                endif
            enddo
        enddo
        !$omp end parallel do
        
    endsubroutine
    
    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getNLine(val_allLineCount,val_allLineList,val_nLine)
        implicit none
        integer,intent(in):: val_allLineCount
        integer,intent(in):: val_allLineList(3,val_allLineCount)
        integer,intent(inout):: val_nLine
        !
        integer:: i
        
        val_nLine = 0
        do i=1,val_allLineCount
            if(val_allLineList(1,i) /= 0) then
                val_nLine = val_nLine + 1
            endif
        enddo
        
    endsubroutine
    
    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getILine(val_allLineCount,val_allLineList,val_nLine,val_iLine,val_kElem_Line,val_iElem_Line)
        implicit none
        integer,intent(in):: val_allLineCount
        integer,intent(in):: val_allLineList(3,val_allLineCount)
        integer,intent(in):: val_nLine
        integer,intent(inout):: val_iLine(4,val_nLine)
        integer,intent(inout):: val_kElem_Line(val_nLine+1)
        integer,intent(inout):: val_iElem_Line(2,val_allLineCount)
        !
        integer:: i,j
        integer:: icount
        
        
        val_iElem_Line(:,:) = 0
        val_iElem_Line(1,:) = val_allLineList(3,:)

        icount = 0
        do i=1,val_allLineCount
            if(val_allLineList(1,i) /= 0) then
                icount = icount + 1
                
                val_iLine(1,icount) = val_allLineList(1,i)
                val_iLine(2,icount) = val_allLineList(2,i)
                val_iLine(3,icount) = i
            endif
        enddo
        
        do i=1,val_nLine-1
            val_iLine(4,i) = val_iLine(3,i+1)-1
        enddo
        val_iLine(4,val_nLine) = val_allLineCount
        
        val_kElem_Line(1:val_nLine) = val_iLine(3,:)-1
        val_kElem_Line(val_nLine+1) = val_allLineCount

    endsubroutine
    
    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getIFlag_Line(val_iMesh,val_iFlag_Line)
        use mod_TypeDef_Mesh
        use mod_PreDefine_Flag
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        integer,intent(inout):: val_iFlag_Line(val_iMesh%nLine)
        !
        integer:: i,j
        integer:: erKind
        integer,allocatable:: iLocalFlag(:)
    
        allocate(iLocalFlag(val_iMesh%nPoin))
        iLocalFlag(:) = FLAG_INNER
        !
        val_iFlag_Line(:) = FLAG_INNER
        !
        do i=1,val_iMesh%nMark
            erKind = val_iMesh%iMark(i)%KIND
            
            do j=1,val_iMesh%iMark(i)%nPoin
                iLocalFlag(val_iMesh%iMark(i)%iPoin(j)) = erKind
            enddo
        enddo
    
        do i=1,val_iMesh%nLine
            if((iLocalFlag(val_iMesh%iLine(1,i)) /= FLAG_INNER).and. &
               (iLocalFlag(val_iMesh%iLine(2,i)) /= FLAG_INNER)) then
                
                val_iFlag_Line(i) = iLocalFlag(val_iMesh%iLine(1,i))
                ! Note that: LocalFlag(val_iMesh%iLine(1,i)) & iLocalFlag(val_iMesh%iLine(2,i)) maybe not equal.
                ! WALL & SYMM, SYMM & FAR
            endif
        enddo
    endsubroutine
    
    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getLinePlaceInElem(val_nDim,val_icci,val_ick,val_line,val_iPlace)
        use mod_ElemProp
        implicit none
        integer,intent(in):: val_nDim
        integer,intent(in):: val_icci(4*val_nDim-4)
        integer,intent(in):: val_ick
        integer,intent(in):: val_line(2)
        integer,intent(out):: val_iPlace
        !
        integer:: i
        integer:: p1,p2,k1,k2
        integer:: iLocalPlace
        !
        iLocalPlace = 0
        do i=1,elemProp_LineNum(val_ick)
            k1 = ElemProp_LineList(1,i,val_ick)
            k2 = ElemProp_LineList(2,i,val_ick)
            
            p1 = min(val_icci(k1),val_icci(k2))
            p2 = max(val_icci(k1),val_icci(k2))
            
            if((val_line(1) == p1).and. &
               (val_line(2) == p2)) then
                iLocalPlace = i
                exit
            endif
        enddo
        
        val_iPlace = iLocalPlace
        
        if(iLocalPlace == 0) then
            write(ioPort_Out,*) 'error : iLocalPlace == 0' 
            stop
        endif
        
    endsubroutine
    
    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine chkILine(val_iMesh,val_isOk)
        use mod_PreDefine_Flag
        use mod_PreDefine_IOPort
        use mod_ElemProp
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        logical,intent(inout):: val_isOk
        !
        integer:: i,j
        integer:: elemID,lineIP
        integer:: line_i(2),line_j(2),elemKind
        integer:: countOfElem(val_iMesh%nElem)
        logical:: iOk = .true.
        
        !
        val_isOk = .true.
        
        !
        countOfElem(:) = 0
        !$omp parallel do private(j,elemID,elemKind,lineIP,line_i,line_j)
        do i=1,val_iMesh%nLine
            line_i(:) = val_iMesh%iLine(1:2,i)
            
            if((val_iMesh%kElem_Line(i)+1 /= val_iMesh%iLine(3,i)).or. &
               (val_iMesh%kElem_Line(i+1) /= val_iMesh%iLine(4,i))) then
                iOk = .false.
                write(ioPort_Out,*) 'ERR0:'
            endif
               
            do j=val_iMesh%kElem_Line(i)+1,val_iMesh%kElem_Line(i+1)
                elemID   = val_iMesh%iElem_Line(1,j)
                elemKind = val_iMesh%iElemKind(elemID)
                
                lineIP   = val_iMesh%iElem_Line(2,j)
                
                line_j(:) = ElemProp_LineList(:,lineIP,elemKind)
                line_j(:) = val_iMesh%iElem(line_j(:),elemID)
                
                if((min(line_i(1),line_i(2)) /= min(line_j(1),line_j(2))).or. &
                   (max(line_i(1),line_i(2)) /= max(line_j(1),line_j(2))) )then
                    iOk = .false.
                    write(ioPort_Out,*) 'ERR1:',line_i(:),line_j(:)
                endif

                !$omp atomic
                countOfElem(elemID) = countOfElem(elemID) +1
            enddo
        enddo
        !$omp end parallel do
        val_isOk = iOk


        !$omp parallel do private(elemKind)
        do i=1,val_iMesh%nElem
            elemKind = val_iMesh%iElemKind(i)
            
            if(countOfElem(i) /= ElemProp_LineNum(elemKind)) then
                write(ioPort_Out,*) 'ERR2:',i,elemKind,countOfElem(i)
            endif
        enddo
        !$omp end parallel do
        !
        
    endsubroutine
    !
endsubroutine
!       