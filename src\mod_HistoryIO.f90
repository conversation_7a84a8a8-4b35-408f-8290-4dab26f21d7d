module mod_HistoryIO
    use mod_PreDefine_Dimension
    implicit none
    !
contains
    !
    subroutine HistoryFile_Open(val_ioPort, val_fileName,val_isAppend)
        use mod_WorkPath
        implicit none
        integer,intent(in):: val_ioPort
        character(len=*),intent(in):: val_fileName
        logical:: val_isAppend

        if(val_isAppend) then
            open(val_ioPort, file = trim(soluFullPath)//'/'//trim(val_fileName), status='unknown',position="append")
        else
            open(val_ioPort, file = trim(soluFullPath)//'/'//trim(val_fileName), status='unknown')

        end if

    end subroutine HistoryFile_Open
    !
    subroutine History_TotalAeroFMFile_WriteHeader(val_ioPort)
        use mod_PreDefine_Dimension
        implicit none
        integer,intent(in):: val_ioPort

        if(nDim == 2) then
            write(val_ioPort,*) 'VARIABLES = "iStep","CFx","CFy","CMz"'
        elseif(nDim == 3) then
            write(val_ioPort,*) 'VARIABLES = "iStep","CFx","CFy","CFz","CMx","CMy","CMz"'
        endif

    endsubroutine
    !
    subroutine History_ResErrFile_WriteHeader(val_ioPort)
        use mod_PreDefine_Dimension
        use mod_Config, only: PHYSICAL_PROBLEM, KIND_TURB_MODEL
        use mod_Options
        implicit none
        integer,intent(in):: val_ioPort
        !
        character(len=999):: headline


        headline = 'VARIABLES = "iStep","Density","DensityVx","DensityVy"'
        if(nDim == 3) then
            headline = trim(headline) // ',"DensityVz"'
        end if
        headline = trim(headline) // ',"DensityE"'
        if(PHYSICAL_PROBLEM >= _TURB) then
            if(KIND_TURB_MODEL == _TURB_SA) then
                headline = trim(headline) // ',"SA_vBar"'
            elseif(KIND_TURB_MODEL == _TURB_KE) then
                headline = trim(headline) // ',"KE_k","KE_e"'
            elseif(KIND_TURB_MODEL == _TURB_KW) then
                headline = trim(headline) // ',"KW_k","KW_w"'
            elseif(KIND_TURB_MODEL == _TURB_SST) then
                headline = trim(headline) // ',"SST_k","SST_w"'
            elseif(KIND_TURB_MODEL == _TURB_SAS) then
                headline = trim(headline) // ',"SAS_k","SAS_w"'
            elseif(KIND_TURB_MODEL == _TURB_RSM) then
            !    headline = trim(headline) // ',"RSM_k","RSM_w"'
            end if
        end if

        write(val_ioPort,*) trim(headline)

    endsubroutine
    !
    subroutine History_DetailedAeroFMFile_WriteHeader(val_ioPort, val_nMark, val_isVisc)
        use mod_strOfNumber
        implicit none
        integer,intent(in):: val_ioPort
        integer,intent(in):: val_nMark
        logical,intent(in):: val_isVisc
        !
        integer:: i,j,k
        character(len=8):: VarNames(18)
        character(len=9999):: varLine

        if(nDim == 2) then
            varNames(1)="CFx"
            varNames(2)="CFy"
            varNames(3)="CMz"

            varNames(4)="CFx_Pres"
            varNames(5)="CFy_Pres"
            varNames(6)="CMz_Pres"

            varNames(7)="CFx_Visc"
            varNames(8)="CFy_Visc"
            varNames(9)="CMz_Visc"

        else if(nDim == 3) then
            varNames(1)="CFx"
            varNames(2)="CFy"
            varNames(3)="CFz"
            varNames(4)="CMx"
            varNames(5)="CMy"
            varNames(6)="CMz"

            varNames(7) ="CFx_Pres"
            varNames(8) ="CFy_Pres"
            varNames(9) ="CFz_Pres"
            varNames(10)="CMx_Pres"
            varNames(11)="CMy_Pres"
            varNames(12)="CMz_Pres"

            varNames(13)="CFx_Visc"
            varNames(14)="CFy_Visc"
            varNames(15)="CFz_Visc"
            varNames(16)="CMx_Visc"
            varNames(17)="CMy_Visc"
            varNames(18)="CMz_Visc"
        end if

        varline = 'VARIABLES = "iter"'
        do i=1,3*nDim-3
            varline = trim(varline)// ', "'//trim(varNames(i))//'(total)"'
        end do

        do j=1,val_nMark
            do i=1,3*nDim-3
                varline = trim(varline)// ', "'//trim(varNames(i))//'(BC'//strTwo_Number(j)//')"'
            end do
            if(val_isVisc) then
                do i=3*nDim-2,9*nDim-9
                    varline = trim(varline)// ', "'//trim(varNames(i))//'(BC'//strTwo_Number(j)//')"'
                end do
            end if
        end do

        write(val_ioPort,*) trim(varLine)

    endsubroutine
    !
    subroutine History_DetailedAeroFMFile_WriteData(val_ioPort, val_iter, val_nMark, val_isVisc, val_aeroFM)
        use mod_TypeDef_Parm, only: typ_AeroFM
        implicit none
        integer,intent(in):: val_ioPort
        integer,intent(in):: val_iter
        integer,intent(in):: val_nMark
        logical,intent(in):: val_isVisc
        type(typ_AeroFM),intent(in):: val_aeroFM
        !
        integer:: i
        character(len=9999):: aline, bline

        write(bline, "(I8)") val_iter
        aline = trim(bline)
        write(bline, "(6E20.7)") val_aeroFM%totalCForce(1:nDim), val_aeroFM%totalCMomnt(1:2*nDim-3)
        aline = trim(aline)//trim(bline)
        do i=1,val_nMark
            if(val_isVisc) then
                write(bline, "(18E20.7)") &
                        val_aeroFM%surfaceCForce_total(1:nDim,i), val_aeroFM%surfaceCMomnt_total(1:2*nDim-3,i), &
                        val_aeroFM%surfaceCForce_pres(1:nDim,i), val_aeroFM%surfaceCMomnt_pres(1:2*nDim-3,i), &
                        val_aeroFM%surfaceCForce_visc(1:nDim,i), val_aeroFM%surfaceCMomnt_visc(1:2*nDim-3,i)
            else
                write(bline, "(6E20.7)") &
                        val_aeroFM%surfaceCForce_total(1:nDim,i), val_aeroFM%surfaceCMomnt_total(1:2*nDim-3,i)
            end if
            aline = trim(aline)//trim(bline)
        end do

        write(val_ioPort,*) trim(aline)

    endsubroutine
    !
    subroutine HistoryFile_Close(val_ioPort)
        implicit none
        integer,intent(in):: val_ioPort

        close(val_ioPort)

    end subroutine HistoryFile_Close

    !
end module mod_HistoryIO