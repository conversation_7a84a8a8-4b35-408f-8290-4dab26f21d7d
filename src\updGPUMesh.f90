
!---------------------------------------------
!
!---------------------------------------------
subroutine updGPUMesh(val_iMesh)
    use mod_TypeDef_Mesh
    use mod_Config
    use mod_Interface_AllocateArray
    implicit none
    type(typ_Mesh),intent(inout):: val_iMesh
    
    
    !---------------------------------------------
    val_iMesh%iFlag_Poin_d(:)   = val_iMesh%iFlag_Poin(:)
    val_iMesh%iIner_Poin_d(:)   = val_iMesh%iIner_Poin(:)
    val_iMesh%iCalc_Poin_d(:)   = val_iMesh%iCalc_Poin(:)
    val_iMesh%iClor_Poin_d(:)   = val_iMesh%iClor_Poin(:)
    val_iMesh%iCoor_Poin_d(:,:) = val_iMesh%iCoor_Poin(:,:)
    val_iMesh%iVolu_Poin_d(:)   = val_iMesh%iVolu_Poin(:)
    
    if(PHYSICAL_PROBLEM >= _TURB) then
        val_iMesh%iWDis_Poin_d(:) = val_iMesh%iWDis_Poin(:)
    endif
    
    !---------------------------------------------
    val_iMesh%iCoef_Sate_d(:,:) = val_iMesh%iCoef_Sate(:,:)
    
    !---------------------------------------------
    call allocateGPUArray(val_iMesh%iBelvProp_d , val_iMesh%nBelv, 2)
    call allocateGPUArray(val_iMesh%iArea_Belv_d, val_iMesh%nBelv)
    call allocateGPUArray(val_iMesh%iNvor_Belv_d, val_iMesh%nBelv, nDim)
    call allocateGPUArray(val_iMesh%iCoef_Belv_d, val_iMesh%nBelv, nDim)
    val_iMesh%iBelvProp_d(:,:)  = val_iMesh%iBelvProp(:,:)
    val_iMesh%iArea_Belv_d(:)   = val_iMesh%iArea_Belv(:)
    val_iMesh%iNvor_Belv_d(:,:) = val_iMesh%iNvor_Belv(:,:)
    val_iMesh%iCoef_Belv_d(:,:) = val_iMesh%iCoef_Belv(:,:)
    
    if(allocated(val_iMesh%iCoor_Belv)) then
        call allocateGPUArray(val_iMesh%iCoor_Belv_d, val_iMesh%nBelv, nDim)
        val_iMesh%iCoor_Belv_d(:,:) = val_iMesh%iCoor_Belv(:,:)
    endif

    
    val_iMesh%kPoin_Clor_d(:) = val_iMesh%kPoin_Clor(:)
    val_iMesh%iPoin_Clor_d(:) = val_iMesh%iPoin_Clor(:)
    
    call allocateGPUArray(val_iMesh%iBelv_Poin_d, val_iMesh%nBelv, 2)
    val_iMesh%kBelv_Poin_d(:)   = val_iMesh%kBelv_Poin(:)
    val_iMesh%iBelv_Poin_d(:,:) = val_iMesh%iBelv_Poin(:,:)
    
    !---------------------------------------------
    if(GRID_MOVEMENT == _YES) then
    val_iMesh%dCoor_Poin_d(:,:) = val_iMesh%dCoor_Poin(:,:)
    val_iMesh%iVelo_Poin_d(:,:) = val_iMesh%iVelo_Poin(:,:)
    val_iMesh%iVelo_Belv_d(:,:) = val_iMesh%iVelo_Belv(:,:)
    endif
    
endsubroutine
!
