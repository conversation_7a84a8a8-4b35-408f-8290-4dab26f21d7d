module mod_SoluComm
    use mod_PreDefine_IOPort
    !
contains
    !
    subroutine InitFlowField(val_iParm, val_iMesh, val_iSolu, val_iStep)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_Config, only: RESTART_SOL
        use mod_Options
        use mod_Configer, only: updConfig_FarField
        use mod_RestartIO, only: readForRestart
        use mod_SoluAdapter, only: updFieldWithBCond
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        integer       ,intent(in   ):: val_iStep
        !
        integer:: i,istat,did,mid

        call updateBoundCondInfo(val_iParm, val_iMesh, val_iSolu, val_iStep)

        istat = cudaDeviceSynchronize()
        val_iMesh%iMarkProp_d(:,:) = val_iMesh%iMarkProp(:,:)
        val_iSolu%soluInfty_d(:)   = val_iSolu%soluInfty(:)
        val_iSolu%primInfty_d(:)   = val_iSolu%primInfty(:)
        val_iSolu%soluMark_d(:,:)  = val_iSolu%soluMark(:,:)
        val_iSolu%primMark_d(:,:)  = val_iSolu%primMark(:,:)
        val_iSolu%soluUCRef_d(:)   = val_iSolu%soluUCRef(:)
        istat = cudaDeviceSynchronize()


        !---------------------------------------------
        if(RESTART_SOL == _YES) then
            write(*,*) 'RESTART_SOL == _YES'
            call readForRestart(val_iParm, val_iSolu)
        else
            do i=1,val_iMesh%nPoin
                did = val_iMesh%iDoma_Poin(i)
                if(did > 0) then
                    mid = val_iMesh%iDomaProp(did,3)
                    val_iSolu%soluvar(i,:) = val_iSolu%soluMark(mid,:)
                    val_iSolu%primvar(i,:) = val_iSolu%primMark(mid,:)
                else
                    val_iSolu%soluvar(i,:) = val_iSolu%soluInfty(:)
                    val_iSolu%primvar(i,:) = val_iSolu%primInfty(:)
                end if
            enddo
        endif

        val_iSolu%soluvar_d(:,:)  = val_iSolu%soluvar(:,:)
        val_iSolu%primvar_d(:,:)  = val_iSolu%primvar(:,:)
        val_iSolu%soluOld_d(:,:)  = val_iSolu%soluvar(:,:)
        val_iSolu%deltSolu_d(:,:) = 0.0
        val_iSolu%DTCoef_d(:)     = 1.0

        call updFieldWithBCond(val_iParm, val_iMesh, val_iSolu)

        istat = cudaDeviceSynchronize()
        val_iSolu%soluvar(:,:)   = val_iSolu%soluvar_d(:,:)
        val_iSolu%primvar(:,:)   = val_iSolu%primvar_d(:,:)
        val_iSolu%soluBelv(:,:)  = val_iSolu%soluBelv_d(:,:)
        val_iSolu%primBelv(:,:)  = val_iSolu%primBelv_d(:,:)
        istat = cudaDeviceSynchronize()

        call updFlowLimtBound(val_iParm, val_iMesh, val_iSolu)

    endsubroutine
    !
    subroutine updFlowLimtBound(val_iParm, val_iMesh, val_iSolu)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_FreeStream
        use mod_Config
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(in   ):: val_iMesh
        type(typ_Solu),intent(in   ):: val_iSolu
        !
        integer:: i,istat
        integer:: dMin,dMax,pMin,pMax,tMin,tMax

        dMin = flowInfty_Density
        dMax = flowInfty_Density
        pMin = flowInfty_Pressure
        pMax = flowInfty_Pressure
        tMin = flowInfty_Temperature
        tMax = flowInfty_Temperature

        do i=1,val_iMesh%nMark
            if((MarkInfos(i)%markKD ==MARK_INLE).or. &
               (MarkInfos(i)%markKD ==MARK_INLM).or. &
               (MarkInfos(i)%markKD ==MARK_OUTP) )then
                dMin = min(dMin, val_iSolu%soluMark(i,1))
                dMax = max(dMax, val_iSolu%soluMark(i,1))
                pMin = min(pMin, val_iSolu%primMark(i,1))
                pMax = max(pMax, val_iSolu%primMark(i,1))
                tMin = min(tMin, val_iSolu%primMark(i,nDim+2))
                tMax = max(tMax, val_iSolu%primMark(i,nDim+2))
            end if
        end do

        val_iParm%iHostParm(IP_LimMinD)      = FLOW_ALLOW_MIND* dMin
        val_iParm%iHostParm(IP_LimMaxD)      = FLOW_ALLOW_MAXD* dMax
        val_iParm%iHostParm(IP_LimMinP)      = FLOW_ALLOW_MINP* pMin
        val_iParm%iHostParm(IP_LimMaxP)      = FLOW_ALLOW_MAXP* pMax
        val_iParm%iHostParm(IP_LimMinT)      = FLOW_ALLOW_MINT* tMin
        val_iParm%iHostParm(IP_LimMaxT)      = FLOW_ALLOW_MAXT* tMax

        istat = cudaDeviceSynchronize()
        val_iParm%iGPUSParm(:) = val_iParm%iHostParm(:)
        istat = cudaDeviceSynchronize()

    endsubroutine
    !
    subroutine updateBoundCondInfo(val_iParm, val_iMesh, val_iSolu, val_iStep)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_Config, only: MarkInfos
        use mod_Configer, only: updConfig_FarField
        use mod_GetSoluParm, only: getSize_SoluPrim
        use mod_setSoluParm, only: setSoluParm_FreeFlow
        use mod_SoluAdapter, only: updFlowInftyInSolu,updMarkSoluAndPrim
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        integer       ,intent(in   ):: val_iStep
        !
        integer:: istat,i
        real(kind=REALLEN):: iTime = 0.0
        real(kind=REALLEN):: soluMi(20),primMi(20)
        integer:: nSoluX, nPrimX

        !write(*,*) "updateBoundCondInfo"
        call getSize_SoluPrim(nSoluX, nPrimX)

        !=======================================================
        !update mark type from infos in the config file
        do i=1,val_iMesh%nMark
            val_iMesh%iMarkProp(i,2) = MarkInfos(i)%markKD
        enddo

        !=======================================================
        !update infty flow condition
        call updConfig_FarField(val_iStep)

        call setSoluParm_FreeFlow

        call updFlowInftyInSolu(val_iParm, val_iSolu)

        istat = cudaDeviceSynchronize()
        val_iSolu%soluInfty_d(1:nSoluX) = val_iSolu%soluInfty(1:nSoluX)
        val_iSolu%primInfty_d(1:nPrimX) = val_iSolu%primInfty(1:nPrimX)
        istat = cudaDeviceSynchronize()

        !write(*,"(A, 10F20.10)") " soluInfty: ", val_iSolu%soluInfty(1:nSoluX)
        !write(*,"(A, 10F20.10)") " primInfty: ", val_iSolu%primInfty(:)

        !=======================================================
        !update mark condition
        val_iSolu%soluMark(:,:) = 0.0
        val_iSolu%primMark(:,:) = 0.0

        do i=1,val_iMesh%nMark
            if(MarkInfos(i)%markID == 0) then
                MarkInfos(i)%markID = i
                MarkInfos(i)%markKD = val_iMesh%iMarkProp(i,2)
            endif
        enddo

        call updMarkSoluAndPrim(val_iParm, val_iMesh, val_iSolu, val_iStep)

        istat = cudaDeviceSynchronize()
        val_iSolu%soluMark_d(:,:) = val_iSolu%soluMark(:,:)
        val_iSolu%primMark_d(:,:) = val_iSolu%primMark(:,:)
        istat = cudaDeviceSynchronize()
        !
        !write(*,*) "updateBoundCondInfo end"

    end subroutine updateBoundCondInfo
    !
    subroutine TimeMarching_Steady(val_iParm, val_iMesh, val_iSolu)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_Config, only: UNSTEADY_SIMULATION,CONVERT_OUTP2WALL
        use mod_RestartIO, only: saveForRestart
        use mod_SoluAdapter
        use mod_Options
        use mod_setSoluParm, only: updCFLNumber
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        !
        integer:: iInnerIter
        logical:: isNAN

        val_iParm%iHostParm(IP_OUTP2WALL) = -1.0
        if(CONVERT_OUTP2WALL) then
            if(UNSTEADY_SIMULATION == _DUAL_TIME_STEPPING .or. UNSTEADY_SIMULATION == _MULTI_STEADY_SIMULATION) then
                val_iParm%iHostParm(IP_OUTP2WALL) = 1.0
            end if
        end if
        val_iParm%iGPUSParm(:) = val_iParm%iHostParm(:)


        do iInnerIter=1, val_iParm%nIter_Steady
            val_iParm%iIter = val_iParm%iIter + 1

            val_iParm%iCurrInnerIter = iInnerIter

            call updCFLNumber(val_iParm%iHostParm(IP_CFLCurt), iInnerIter)
            val_iParm%iGPUSParm(:) = val_iParm%iHostParm(:)

            !---------------------------------------------
            !伪时间步迭代-单步
            call RunOneIteration(val_iParm, val_iMesh, val_iSolu)

            !---------------------------------------------
            !残差计算、判断及输出
            if((mod(iInnerIter,val_iParm%nIter_ResErrCalc) == 0).or.(val_iParm%iIter <= 1) ) then
                call CalResErrAndSave(val_iParm, val_iMesh, val_iSolu, iInnerIter, isNAN)

                call calAeroFMAndSave(val_iParm, val_iMesh, val_iSolu, val_iParm%iIter)

                !    if(isConverged(val_iParm)) exit INTERLOOP
            endif


            if(isNAN) then
                call SaveSolu(val_iParm, val_iMesh, val_iSolu, -8888)
                stop
            end if
            !---------------------------------------------
            !中间结果输出
            if((val_iParm%nUnstOuterIter == 0) .and. &
               (iInnerIter >= val_iParm%nIter_ResultSaveBgn) .and. &
               (mod(iInnerIter,val_iParm%nIter_ResultSave) == 0)) then
                call SaveSolu(val_iParm, val_iMesh, val_iSolu, iInnerIter)
            endif

            if((UNSTEADY_SIMULATION == _NONE .or. UNSTEADY_SIMULATION == _TIME_STEPPING).and. &
               (mod(iInnerIter,val_iParm%nIter_ResultSave) == 0)) then
                call saveForRestart(val_iParm, val_iSolu)
            end if

        enddo

        !---------------------------------------------
        !最终流场输出
        if(UNSTEADY_SIMULATION == _NONE) then
            call SaveSolu(val_iParm, val_iMesh, val_iSolu, -99999)
        end if
        
    end subroutine TimeMarching_Steady
    !
    subroutine TimeMarching_Unsteady(val_iParm, val_iMesh, val_iSolu)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_SoluAdapter
        use mod_saveSoluUnst
        use mod_Config, only: UNSTEADY_SIMULATION
        use mod_GetSoluParm, only: getSize_SoluPrim
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        !
        integer:: iOuterIter,iInnerIter,nSoluX,nPrimX
        logical:: isNAN


        call getSize_SoluPrim(nSoluX, nPrimX)

        val_iParm%iHostParm(IP_OUTP2WALL) = -1.0
        val_iParm%iGPUSParm(:) = val_iParm%iHostParm(:)

        !---------------------------------------------
        !OUTERLOOP: 物理时间推进
        val_iParm%iTimeCurrt = val_iParm%iTimeBegin

        OUTERLOOP: do iOuterIter=val_iParm%iOuterIterBegin,val_iParm%nUnstOuterIter
            val_iParm%iCurrOuterIter = iOuterIter
            write(*,"(A,I4,A,I4)") 'Physical Time Step :',iOuterIter,"/",val_iParm%nUnstOuterIter

            !if(val_iParm%iTimeCurrt > val_iParm%iTimeFinal) then
            !    exit OUTERLOOP
            !endif

            !if(GRID_MOVEMENT == _YES) then
            !    call saveMesh_Unst(val_iMesh, val_iParm, iOuterIter)
            !    call calMeshUpdating(val_iMesh, val_iParm, iOuterIter)
            !endif

            if(UNSTEADY_SIMULATION == _DUAL_TIME_STEPPING) then
                call saveSoluUnst(val_iParm, val_iMesh, val_iSolu, iOuterIter, nSoluX)
            endif

            call updateBoundCondInfo(val_iParm, val_iMesh, val_iSolu, iOuterIter)

            !---------------------------------------------
            !INTERLOOP: 伪时间步推进
            INTERLOOP: do iInnerIter=1, val_iParm%nUnstInnerIter
                val_iParm%iIter = val_iParm%iIter + 1

                val_iParm%iCurrInnerIter = iInnerIter

                !---------------------------------------------
                !伪时间步迭代-单步
                call RunOneIteration(val_iParm, val_iMesh, val_iSolu)

                !---------------------------------------------
                !残差计算、判断及输出
                if((mod(iInnerIter,val_iParm%nIter_ResErrCalc) == 0).or.(val_iParm%iIter <= 1) ) then
                    call CalResErrAndSave(val_iParm, val_iMesh, val_iSolu, iInnerIter, isNAN)

                    call calAeroFMAndSave(val_iParm, val_iMesh, val_iSolu, val_iParm%iIter)

                    !    if(isConverged(val_iParm)) exit INTERLOOP
                endif

                if(isNAN) then
                    call SaveSolu(val_iParm, val_iMesh, val_iSolu, -8888)
                    stop
                end if

            enddo INTERLOOP

            if((val_iParm%nUnstOuterIter > 1).and. &
               (iOuterIter >= val_iParm%nIter_ResultSaveBgn) .and. &
               mod(iOuterIter,val_iParm%nIter_ResultSave) == 0) then
                call SaveSolu(val_iParm, val_iMesh, val_iSolu, iOuterIter)
            endif

            val_iParm%iTimeCurrt = val_iParm%iTimeCurrt + val_iParm%iTimeDelta
        enddo OUTERLOOP

    end subroutine TimeMarching_Unsteady
    !
    subroutine RunOneIteration(val_iParm,val_iMesh,val_iSolu)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_Config, only: KIND_HARDWARE, CONV_NUM_METHOD_FLOW
        use mod_Options
        use mod_SoluAdapter, only: RunOneIteration_UPW_GPU
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu


        if(CONV_NUM_METHOD_FLOW == _JST) then
            if(KIND_HARDWARE == _CPU) then
                if(.not.val_iParm%isNeedExch) then
                    !call RunOneIteration_JST_CPU(val_iParm, val_iMesh, val_iSolu)
                elseif(val_iParm%isNeedExch) then
                    !call RunOneIteration_JST_Exch_CPU(val_iParm, val_iMesh, val_iSolu)
                endif
            elseif(KIND_HARDWARE == _GPU) then
                if(.not.val_iParm%isNeedExch) then
                    !call RunOneIteration_JST_GPU(val_iParm, val_iMesh, val_iSolu)
                elseif(val_iParm%isNeedExch) then
                    !call RunOneIteration_JST_Exch_GPU(val_iParm, val_iMesh, val_iSolu)
                endif
            end if

        elseif(CONV_NUM_METHOD_FLOW == _UPW) then
            if(KIND_HARDWARE == _CPU) then
                if(.not.val_iParm%isNeedExch) then
                    !call RunOneIteration_UPW_CPU(val_iParm, val_iMesh, val_iSolu)
                elseif(val_iParm%isNeedExch) then
                    !call RunOneIteration_UPW_Exch_CPU(val_iParm, val_iMesh, val_iSolu)
                endif
            elseif(KIND_HARDWARE == _GPU) then
                if(.not.val_iParm%isNeedExch) then
                    call RunOneIteration_UPW_GPU(val_iParm, val_iMesh, val_iSolu)
                elseif(val_iParm%isNeedExch) then
                    !call RunOneIteration_UPW_Exch_GPU(val_iParm, val_iMesh, val_iSolu)
                endif
            end if
        else

        end if

    endsubroutine
    !
end module mod_SoluComm