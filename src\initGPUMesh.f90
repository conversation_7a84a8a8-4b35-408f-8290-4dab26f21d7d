
!---------------------------------------------
!
!---------------------------------------------
subroutine initGPUMesh(val_iMesh)
    use mod_TypeDef_Mesh
    use mod_Config
    use mod_Interface_AllocateArray
    implicit none
    type(typ_Mesh),intent(inout):: val_iMesh
    
    
    !---------------------------------------------
    call allocateGPUArray(val_iMesh%iFlag_Poin_d, val_iMesh%nPoin)
    call allocateGPUArray(val_iMesh%iIner_Poin_d, val_iMesh%nPoin)
    call allocateGPUArray(val_iMesh%iCalc_Poin_d, val_iMesh%nPoin)
    call allocateGPUArray(val_iMesh%iClor_Poin_d, val_iMesh%nPoin)
    call allocateGPUArray(val_iMesh%iCoor_Poin_d, val_iMesh%nPoin, nDim)
    call allocateGPUArray(val_iMesh%iVolu_Poin_d, val_iMesh%nPoin)
    call allocateGPUArray(val_iMesh%iRadu_Poin_d, val_iMesh%nPoin)
    
    val_iMesh%iFlag_Poin_d(:)   = val_iMesh%iFlag_Poin(:)
    val_iMesh%iIner_Poin_d(:)   = val_iMesh%iIner_Poin(:)
    val_iMesh%iCalc_Poin_d(:)   = val_iMesh%iCalc_Poin(:)
    val_iMesh%iClor_Poin_d(:)   = val_iMesh%iClor_Poin(:)
    val_iMesh%iCoor_Poin_d(:,:) = val_iMesh%iCoor_Poin(:,:)
    val_iMesh%iVolu_Poin_d(:)   = val_iMesh%iVolu_Poin(:)
    val_iMesh%iRadu_Poin_d(:)   = val_iMesh%iRadu_Poin(:)
    
    call allocateGPUArray(val_iMesh%iProp_Infc_d, val_iMesh%nInfc, 5     )
    call allocateGPUArray(val_iMesh%iCoef_Infc_d, val_iMesh%nInfc, 2*nDim)
    val_iMesh%iProp_Infc_d(:,:) = val_iMesh%iProp_Infc(:,:)
    val_iMesh%iCoef_Infc_d(:,:) = val_iMesh%iCoef_Infc(:,:)
    
    
    if(allocated(val_iMesh%iWDis_Poin)) then
    call allocateGPUArray(val_iMesh%iWDis_Poin_d, val_iMesh%nPoin)
    val_iMesh%iWDis_Poin_d(:)   = val_iMesh%iWDis_Poin(:)
    endif
    
    !---------------------------------------------
    call allocateGPUArray(val_iMesh%kSate_Poin_d, val_iMesh%nPoin+1)
    call allocateGPUArray(val_iMesh%iSate_Poin_d, val_iMesh%nSate, 2)
    call allocateGPUArray(val_iMesh%iCoef_Sate_d, val_iMesh%nSate, nDim)
    val_iMesh%kSate_Poin_d(:)   = val_iMesh%kSate_Poin(:)
    val_iMesh%iSate_Poin_d(:,:) = val_iMesh%iSate_Poin(:,:)
    val_iMesh%iCoef_Sate_d(:,:) = val_iMesh%iCoef_Sate(:,:)
    
    
    if(NUMERICAL_METHOD == _NUMERICAL_MESHLESS) then
        call allocateGPUArray(val_iMesh%iCofR_Sate_d, val_iMesh%nSate, nDim)
        val_iMesh%iCofR_Sate_d(:,:) = val_iMesh%iCofR_Sate(:,:)
    endif
    
    !---------------------------------------------
    call allocateGPUArray(val_iMesh%iBelvProp_d, val_iMesh%nBelv, 2)
    call allocateGPUArray(val_iMesh%iMarkProp_d, val_iMesh%nMark, 2)
    call allocateGPUArray(val_iMesh%iArea_Belv_d, val_iMesh%nBelv)
    call allocateGPUArray(val_iMesh%iNvor_Belv_d, val_iMesh%nBelv, nDim)
    call allocateGPUArray(val_iMesh%iCoef_Belv_d, val_iMesh%nBelv, nDim)
    val_iMesh%iBelvProp_d(:,:)  = val_iMesh%iBelvProp(:,:)
    val_iMesh%iMarkProp_d(:,:)  = val_iMesh%iMarkProp(:,:)
    val_iMesh%iArea_Belv_d(:)   = val_iMesh%iArea_Belv(:)
    val_iMesh%iNvor_Belv_d(:,:) = val_iMesh%iNvor_Belv(:,:)
    val_iMesh%iCoef_Belv_d(:,:) = val_iMesh%iCoef_Belv(:,:)
    
    if(allocated(val_iMesh%iCoor_Belv)) then
    call allocateGPUArray(val_iMesh%iCoor_Belv_d, val_iMesh%nBelv, nDim)
    val_iMesh%iCoor_Belv_d(:,:) = val_iMesh%iCoor_Belv(:,:)
    endif
    
    !---------------------------------------------
    call allocateGPUArray(val_iMesh%kPoin_Clor_d, val_iMesh%nClor+1)
    call allocateGPUArray(val_iMesh%iPoin_Clor_d, val_iMesh%nPoin)
    val_iMesh%kPoin_Clor_d(:)   = val_iMesh%kPoin_Clor(:)
    val_iMesh%iPoin_Clor_d(:)   = val_iMesh%iPoin_Clor(:)
    
    call allocateGPUArray(val_iMesh%kBelv_Poin_d, val_iMesh%nPoin+1)
    call allocateGPUArray(val_iMesh%iBelv_Poin_d, val_iMesh%nBelv, 2)
    val_iMesh%kBelv_Poin_d(:)   = val_iMesh%kBelv_Poin(:)
    val_iMesh%iBelv_Poin_d(:,:) = val_iMesh%iBelv_Poin(:,:)
    
    !---------------------------------------------
    call allocateGPUArray(val_iMesh%iVelo_Poin_d, val_iMesh%nPoin, nDim)
    call allocateGPUArray(val_iMesh%iVelo_Belv_d, val_iMesh%nBelv, nDim)
    val_iMesh%iVelo_Poin_d(:,:) = val_iMesh%iVelo_Poin(:,:)
    val_iMesh%iVelo_Belv_d(:,:) = val_iMesh%iVelo_Belv(:,:)
    if(GRID_MOVEMENT == _YES) then
    call allocateGPUArray(val_iMesh%dCoor_Poin_d, val_iMesh%nPoin, nDim)
    call allocateGPUArray(val_iMesh%iVolu_Poin_n_d , val_iMesh%nPoin)
    call allocateGPUArray(val_iMesh%iVolu_Poin_n1_d, val_iMesh%nPoin)
    val_iMesh%dCoor_Poin_d(:,:) = val_iMesh%dCoor_Poin(:,:)
    endif
    
    call allocateGPUArray(val_iMesh%kSatL_Poin_d, val_iMesh%nPoin+1)
    call allocateGPUArray(val_iMesh%kSatR_Poin_d, val_iMesh%nPoin+1)
    call allocateGPUArray(val_iMesh%iSatL_Poin_d, val_iMesh%nSatL, 2)
    call allocateGPUArray(val_iMesh%iSatR_Poin_d, val_iMesh%nSatR, 2)
    call allocateGPUArray(val_iMesh%iCoef_SatL_d, val_iMesh%nSatL, nDim)
    call allocateGPUArray(val_iMesh%iCoef_SatR_d, val_iMesh%nSatR, nDim)
    val_iMesh%kSatL_Poin_d = val_iMesh%kSatL_Poin
    val_iMesh%kSatR_Poin_d = val_iMesh%kSatR_Poin
    val_iMesh%iSatL_Poin_d = val_iMesh%iSatL_Poin
    val_iMesh%iSatR_Poin_d = val_iMesh%iSatR_Poin
    val_iMesh%iCoef_SatL_d = val_iMesh%iCoef_SatL
    val_iMesh%iCoef_SatR_d = val_iMesh%iCoef_SatR
    
    
    call allocateGPUArray(val_iMesh%kSatL_Clor_d, val_iMesh%nClor+1)
    call allocateGPUArray(val_iMesh%kSatR_Clor_d, val_iMesh%nClor+1)
    call allocateGPUArray(val_iMesh%iSatL_Clor_d, val_iMesh%nSaCL, 3)
    call allocateGPUArray(val_iMesh%iSatR_Clor_d, val_iMesh%nSaCR, 3)
    val_iMesh%kSatL_Clor_d(:)   = val_iMesh%kSatL_Clor(:)
    val_iMesh%kSatR_Clor_d(:)   = val_iMesh%kSatR_Clor(:)
    val_iMesh%iSatL_Clor_d(:,:) = val_iMesh%iSatL_Clor(:,:)
    val_iMesh%iSatR_Clor_d(:,:) = val_iMesh%iSatR_Clor(:,:)
    
    
endsubroutine
!
