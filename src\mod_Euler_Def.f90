
!---------------------------------------------
!
!---------------------------------------------
module mod_Euler_Def
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    implicit none
    !
    integer,parameter:: nTurbEx = 0
    integer,parameter:: nConsEx = nDim + 2
    integer,parameter:: nPrimEx = nDim + 2 !+mul+mut
    integer,parameter:: nSoluEx = nDim + 2 !nCons+nTurb
    !
endmodule
!
