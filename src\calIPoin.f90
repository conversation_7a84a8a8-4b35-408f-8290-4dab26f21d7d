
!---------------------------------------------
!
!---------------------------------------------
subroutine calIPoin(val_iMesh)
    use mod_TypeDef_Mesh
    use mod_PreDefine_Flag
    use mod_PreDefine_IOPort
    implicit none
    type(typ_GlobalMesh),intent(inout):: val_iMesh
    !
    logical:: isOk
    
    
    allocate(val_iMesh%iFlag_Poin(val_iMesh%nPoin))
    val_iMesh%iFlag_Poin(:) = FLAG_INNER
    
    call getIFlag_Poin(val_iMesh,val_iMesh%iFlag_Poin)
    
    val_iMesh%nSate = 2*val_iMesh%nLine
    
    allocate(val_iMesh%kSate_Poin(val_iMesh%nPoin+1))
    allocate(val_iMesh%iSate_Poin(val_iMesh%nSate,2))
    val_iMesh%kSate_Poin(:)   = 0
    val_iMesh%iSate_Poin(:,:) = 0
    
    call getISate_Poin(val_iMesh%nLine      , &
                       val_iMesh%iLine      , &
                       val_iMesh%nPoin      , &
                       val_iMesh%nSate      , &
                       val_iMesh%kSate_Poin , &
                       val_iMesh%iSate_Poin )
    
    call chkIPoin(val_iMesh,isOk)
    if(.not.isOk) then
        write(ioPort_Out,*) 'chkILine: is not ok!'
        stop
    endif
    
contains

    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getIFlag_Poin(val_iMesh,val_iFlag_Poin)
        use mod_TypeDef_Mesh
        use mod_PreDefine_Flag
        use mod_PreDefine_Mark
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        integer,intent(inout):: val_iFlag_Poin(val_iMesh%nPoin)
        !
        integer:: i,j
        integer:: erKind
        
        
        val_iFlag_Poin(:) = FLAG_INNER
        !
        do i=1,val_iMesh%nMark
            erKind = val_iMesh%iMark(i)%KIND
            
            do j=1,val_iMesh%iMark(i)%nPoin
                val_iFlag_Poin(val_iMesh%iMark(i)%iPoin(j)) = erKind
            enddo
            
        enddo
        !优先级：物面》对称面》其他
        !对称面设置
        do i=1,val_iMesh%nMark
            erKind = val_iMesh%iMark(i)%KIND
            
            if(erKind == MARK_SYMM) then
                do j=1,val_iMesh%iMark(i)%nPoin
                    val_iFlag_Poin(val_iMesh%iMark(i)%iPoin(j)) = erKind
                enddo
            endif
            
        enddo
        
        !物面设置
        do i=1,val_iMesh%nMark
            erKind = val_iMesh%iMark(i)%KIND
            
            if(erKind == MARK_WALL .or. &
               erKind == MARK_EQTW .or. &
               erKind == MARK_EULW .or. &
               erKind == MARK_MOVW ) then
                do j=1,val_iMesh%iMark(i)%nPoin
                    val_iFlag_Poin(val_iMesh%iMark(i)%iPoin(j)) = erKind
                enddo
            endif
            
        enddo
        
        
    endsubroutine
    
    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getISate_Poin(val_nLine,val_iLine,val_nPoin,val_allSateCount,val_kSate_Poin,val_iSate_Poin)
        implicit none
        integer,intent(in):: val_nLine
        integer,intent(in):: val_iLine(4,val_nLine)
        integer,intent(in):: val_nPoin
        integer,intent(in):: val_allSateCount
        integer,intent(inout):: val_kSate_Poin(val_nPoin+1)
        integer,intent(inout):: val_iSate_Poin(val_allSateCount,2)
        !
        integer:: i,j
        integer:: nb,ne
        integer:: iKLocal(val_nPoin+1)
        
        
        val_kSate_Poin(:) = 0
        do i=1,val_nLine
            nb = val_iLine(1,i)
            ne = val_iLine(2,i)
            
            val_kSate_Poin(nb+1) = val_kSate_Poin(nb+1) + 1 
            val_kSate_Poin(ne+1) = val_kSate_Poin(ne+1) + 1 
        enddo
        !
        do i=2,val_nPoin+1
            val_kSate_Poin(i) = val_kSate_Poin(i-1) + val_kSate_Poin(i)
        enddo
        
        !============================
        iKLocal(:) = val_kSate_Poin(:)
        
        !
        do i=1,val_nLine
            nb = val_iLine(1,i)
            ne = val_iLine(2,i)
            
            iKLocal(nb) = iKLocal(nb) + 1 
            val_iSate_Poin(iKLocal(nb),1) = ne
            val_iSate_Poin(iKLocal(nb),2) = i
            
            iKLocal(ne) = iKLocal(ne) + 1 
            val_iSate_Poin(iKLocal(ne),1) = nb
            val_iSate_Poin(iKLocal(ne),2) = i
        enddo
        
    endsubroutine
    
    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine chkIPoin(val_iMesh,val_isOk)
        use mod_PreDefine_Flag
        use mod_PreDefine_IOPort
        use mod_ElemProp
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        logical,intent(inout):: val_isOk
        !
        integer:: i,j,k
        integer:: sateID,lineID,poinB,poinE
        integer:: countOfLine(val_iMesh%nLine)
        
        
        val_isOk = .true.
        !
        countOfLine(:) = 0
        do i=1,val_iMesh%nPoin
            do j=val_iMesh%kSate_Poin(i)+1,val_iMesh%kSate_Poin(i+1)
                sateID = val_iMesh%iSate_Poin(j,1)
                lineID = val_iMesh%iSate_Poin(j,2)
                
                poinB = val_iMesh%iLine(1,lineID)
                poinE = val_iMesh%iLine(2,lineID)
                
                if((min(i,sateID) /= min(poinB,poinE)).or. &
                   (max(i,sateID) /= max(poinB,poinE))) then
                    
                    write(ioPort_Out,*) 'ERR01:'
                    val_isOk = .false.
                    return
                    
                endif
                
                countOfLine(lineID) = countOfLine(lineID) +1
            enddo
        enddo
        !
        do i=1,val_iMesh%nLine
            if(countOfLine(i) /= 2) then
                write(ioPort_Out,*) 'ERR02:'
                val_isOk = .false.
                return
            endif
        enddo
        
    endsubroutine
    !
endsubroutine
!
