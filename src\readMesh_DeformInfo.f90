!
subroutine readMesh_DeformInfo
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    use mod_PreDefine_IOPort
    use mod_WorkPath
    use mod_Mesh_Deform
    implicit none
    !
    integer:: i,j,k,iCount
    integer:: istat
    character(len= 255):: aLine,bLine
    
    
    
    open(ioPort_FULL,file=trim(soluFullPath)//'/meshDeform_Info.dat',status='OLD',iostat=istat)

    if(istat /= 0) then
        write(ioPort_Out,*) 'readMesh_DeformInfo: open File error!'
        stop
    endif
    
    do
        read(ioPort_FULL,'(A)',iostat=istat) aLine
        if(istat /= 0) exit
        
        bLine = adjustl(aLine)
        
        if(bLine(1:11) == '#DEFORMZONE') then
            read(ioPort_FULL,*) nDeformZone
            read(ioPort_FULL,*) !Black Line
            
            allocate(iDeformZone(nDeformZone))
            
            do i=1,nDeformZone
                read(ioPort_FULL,*) !Zone ID
                read(ioPort_FULL,*) iDeformZone(i)%DeformKind
                read(ioPort_FULL,*) iDeformZone(i)%DeformMethod
                if(iDeformZone(i)%DeformMethod == DEFORMMETHOD_DWM) then
                    read(ioPort_FULL,*) iDeformZone(i)%iDefoCoef_DWM_Moved,iDeformZone(i)%iDefoCoef_DWM_Still
                endif
                read(ioPort_FULL,*) !Black Line
                
                read(ioPort_FULL,*) iDeformZone(i)%nRegn,iDeformZone(i)%nMark
                allocate(iDeformZone(i)%iRegnList(iDeformZone(i)%nRegn))
                allocate(iDeformZone(i)%iMarkList(iDeformZone(i)%nMark))
                read(ioPort_FULL,*) iDeformZone(i)%iRegnList(:)
                read(ioPort_FULL,*) iDeformZone(i)%iMarkList(:)
                read(ioPort_FULL,*) !Black Line
                
                SELECTCASE (iDeformZone(i)%DeformKind)
                CASE (DEFORMKIND_TRANS, DEFORMKIND_REGNT)
                    allocate(iDeformZone(i)%iTransDist(nDim,iDeformZone(i)%nMark))
                    
                CASE (DEFORMKIND_ROTAT, DEFORMKIND_REGNR)
                    allocate(iDeformZone(i)%iRotatAngle(iDeformZone(i)%nMark))
                    
                    read(ioPort_FULL,*) iDeformZone(i)%iRotatCent(:)
                    
                    if(nDim == 3) then
                        read(ioPort_FULL,*) iDeformZone(i)%iRotatAxle
                        
                        if(iDeformZone(i)%iRotatAxle == 0) then
                            read(ioPort_FULL,*) iDeformZone(i)%iRotatDirs
                        endif
                    endif
                        
                CASE (DEFORMKIND_SOFTT, DEFORMKIND_SOFTR)
                        
                ENDSELECT
            enddo
            
            exit
        endif
        
    enddo
    close(ioPort_FULL)
    
    
endsubroutine
!
