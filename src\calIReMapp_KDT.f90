!
module mod_calIReMapp_KDT
    use mod_PreDefine_Dimension
    use mod_PreDefine_Precision
    use mod_PreDefine_IOPort
    use mod_TypeDef_Division
    use mod_calDivRegin
    use mod_MeshReordering
    implicit none
    !
    type:: typ_ReList
        integer:: nPoin
        integer:: nBlock
        !
        integer,allocatable:: blockID(:)
        integer,allocatable:: iFlag(:,:)
        integer,allocatable:: iLinW(:)
        integer,allocatable:: iRowW(:)
        integer,allocatable:: iGrow(:)
        !
        integer:: nLine
        integer,allocatable:: reLine(:,:)
    endtype
    !
    integer:: nPoin
    integer,allocatable:: iMapOToN_Poin(:)
    integer,allocatable:: iMapNToO_Poin(:)
    real(kind=REALLEN),allocatable:: iCoor_Poin(:,:)
    type(typ_DivRegin):: iDivRegin  !重排的剖分二叉树
    !
    contains
    !
    !---------------------------------------------
    !---------------------------------------------
    subroutine calIReMapp_Poin_KDT(val_iPart,val_isOk)
        use mod_PreDefine_IOPort
        implicit none
        integer,intent(in):: val_iPart
        logical,intent(out):: val_isOk
        !
        integer:: i,j,iCount
        integer:: poinID,blockID,poinND
        integer:: nReBlock,nIcPoin,nBlock
        integer,allocatable:: kPoin_Block(:),iPoin(:),iBlock(:)
        integer:: iReBlock_Poin(iReMesh%nPoin)
        
        
        !---------------------------------------------
        !---------------------------------------------
        if(iReKind /= ReKind_Poin) then
            val_isOk = .false.
            return
        endif
        
        
        !---------------------------------------------
        !---------------------------------------------
        nReBlock = 0
        iReBlock_Poin(:) = 0
        do i=1,iReMesh%nClor
            iCount = 0
            do j=1,iReMesh%nPoin
                if(iReMesh%iClor_Poin(j) == i) then
                    iCount = iCount + 1
                endif
            enddo
            nIcPoin = iCount
            
            if(nIcPoin == 0) cycle
            allocate(iPoin(nIcPoin))
            allocate(iBlock(nIcPoin))
            
            iCount = 0
            do j=1,iReMesh%nPoin
                if(iReMesh%iClor_Poin(j) == i) then
                    iCount = iCount + 1
                    iPoin(iCount) = j
                endif
            enddo
            
            call calReordering_Poin(iReMesh%iCoor_Poin,         &
                        iPoin, iBlock, iReMesh%nPoin, nIcPoin,  &
                        nBlock, nPoinPerAccess, val_iPart*1000+i)
                    
            do j=1,nIcPoin
                poinID = iPoin(j)
                blockID = iBlock(j)
                
                iReBlock_Poin(poinID) = blockID + nReBlock
            enddo
            nReBlock = nReBlock + nBlock
                
            deallocate(iPoin,iBlock)
        enddo
    
        
        !---------------------------------------------
        !---------------------------------------------
        iReMapp%nGPoin = iReMesh%nPoin
        iReMapp%nLPoin = nReBlock*nPoinPerAccess
        allocate(iReMapp%iMapGToL_Poin(iReMapp%nGPoin))
        allocate(iReMapp%iMapLToG_Poin(iReMapp%nLPoin))
        iReMapp%iMapGToL_Poin(:) = 0
        iReMapp%iMapLToG_Poin(:) = 0
        
        
        allocate(kPoin_Block(nReBlock))
        kPoin_Block(:) = 0
        do i=1,iReMesh%nPoin
            blockID = iReBlock_Poin(i)
            
            kPoin_Block(blockID) = kPoin_Block(blockID) + 1
            
            poinND = (blockID - 1)*nPoinPerAccess + kPoin_Block(blockID)
            
            iReMapp%iMapGToL_Poin(i) = poinND
            iReMapp%iMapLToG_Poin(poinND) = i
        enddo
        
    
        !---------------------------------------------
        !---------------------------------------------
        val_isOk = .true.
        return
        
    endsubroutine
    !
    !---------------------------------------------
    subroutine calIReMapp_Sate_KDT(val_iPart,val_isOk)
        use mod_PreDefine_IOPort
        implicit none
        integer,intent(in):: val_iPart
        logical,intent(out):: val_isOk
        !
        integer:: i,j,k
        integer:: sateOP,sateNP,satePP
        integer:: iPoinBID,iPoinEID,nPoinNum
        integer:: iSateBID,iSateEID,nSateNum
        integer,allocatable:: kSatePart_Poin(:)
        integer,allocatable:: iSatePart_Poin(:)
        integer,allocatable:: iSateReOder(:)
        
        
        
        !---------------------------------------------
        !---------------------------------------------
        if(iReKind /= ReKind_Sate) then
            val_isOk = .false.
            return
        endif
        
        
        !---------------------------------------------
        !---------------------------------------------
        iReMapp%nGSate = iReMesh%nSate
        iReMapp%nLSate = iReMesh%nSate
        allocate(iReMapp%iMapGToL_Sate(iReMapp%nGSate))
        allocate(iReMapp%iMapLToG_Sate(iReMapp%nLSate))
        iReMapp%iMapGToL_Sate(:) = 0
        iReMapp%iMapLToG_Sate(:) = 0
        
        
        !---------------------------------------------
        !---------------------------------------------
        do i=1,iReMesh%nPoin,nPoinPerAccess
            iPoinBID = i
            iPoinEID = min(i + nPoinPerAccess - 1, iReMesh%nPoin)
            nPoinNum = iPoinEID - iPoinBID + 1
            
            iSateBID = iReMesh%kSate_Poin(iPoinBID) + 1
            iSateEID = iReMesh%kSate_Poin(iPoinEID + 1)
            nSateNum = iSateEID - iSateBID + 1
            
            if(nPoinNum <= 0) then
                write(ioPort_Out,*) 'nPoinNum <= 0'
                stop
            elseif(nPoinNum == 1) then
                do j=1,nSateNum
                    sateOP = j + iReMesh%kSate_Poin(iPoinBID)
                    sateNP = sateOP !j + iReMesh%kSate_Poin(iPoinBID)
                
                    iReMapp%iMapGToL_Sate(sateOP) = sateNP
                    iReMapp%iMapLToG_Sate(sateNP) = sateOP
                enddo
                
                cycle
            endif
            
            allocate(kSatePart_Poin(nPoinNum+1))
            allocate(iSatePart_Poin(nSateNum))
            allocate(iSateReOder(nSateNum))
            
            kSatePart_Poin(1:nPoinNum+1) = iReMesh%kSate_Poin(iPoinBID:iPoinEID+1) - &
                                           iReMesh%kSate_Poin(iPoinBID)
            iSatePart_Poin(:)            = iReMesh%iSate_Poin(iSateBID:iSateEID,1)
            iSateReOder(:)               = 0
            
            call calReordering_Sate(kSatePart_Poin, iSatePart_Poin,     &
                        iSateReOder, nPoinNum, nSateNum, nPoinPerAccess )
            
            do j=1,nSateNum
                satePP = iSateReOder(j)
                
                sateOP = satePP + iReMesh%kSate_Poin(iPoinBID)
                sateNP = j      + iReMesh%kSate_Poin(iPoinBID)
                
                iReMapp%iMapGToL_Sate(sateOP) = sateNP
                iReMapp%iMapLToG_Sate(sateNP) = sateOP
            enddo
            
            deallocate(kSatePart_Poin)
            deallocate(iSatePart_Poin)
            deallocate(iSateReOder)
        enddo
        
        
        !---------------------------------------------
        !---------------------------------------------
        val_isOk = .true.
        return
            
    endsubroutine
    !
    !---------------------------------------------
    !---------------------------------------------
    subroutine calReordering_Poin(val_iCoor,val_iPoin,  &
                val_iReBlock_Poin,val_nGPoin,val_nPoin, &
                val_nBlock,val_nPoinPerAccess,val_iFlag )
        implicit none
        real(kind=REALLEN),intent(in):: val_iCoor(nDim,val_nGPoin)
        integer,intent(in):: val_iPoin(val_nPoin)
        integer,intent(inout):: val_iReBlock_Poin(val_nPoin)
        integer,intent(in):: val_nGPoin
        integer,intent(in):: val_nPoin
        integer,intent(out):: val_nBlock
        integer,intent(in):: val_nPoinPerAccess
        integer,intent(in):: val_iFlag
        !
        integer:: i,j,k
        integer:: poinID,poinIP,blockID,nFinalPoin
        integer,allocatable:: iFinalPoin(:)
        logical:: isOk
        
        
        !---------------------------------------------
        nPoin = val_nPoin
        
        if(nPoin == 0) return
        
        
        allocate(iMapOToN_Poin(nPoin))
        allocate(iMapNToO_Poin(nPoin))
        allocate(iCoor_Poin(nDim,nPoin))
        iMapOToN_Poin(:) = 0
        iMapNToO_Poin(:) = 0
        iCoor_Poin(:,:)  = 0.0
        
        do i=1,nPoin
            poinID = val_iPoin(i)
            
            iCoor_Poin(:,i) = val_iCoor(:,poinID)
        enddo
        
        
        !---------------------------------------------
        iDivRegin%nPoin = nPoin
        allocate(iDivRegin%iPoin(nPoin))
        do i=1,nPoin
            iDivRegin%iPoin(i) = i
        enddo
        
        call initDivRegin(iDivRegin, iCoor_Poin, nPoin, val_nPoinPerAccess)
            
        call calDivRegin(iDivRegin,isOk)
        
        if(.not.isOk) then
            write(ioPort_Out,*) 'notOk in mod_Reordering_Poin.f90'
            stop
        endif
        
        val_nBlock = iDivRegin%nFinalBlock
        val_iReBlock_Poin(:) = 0
        do i=1,iDivRegin%nFinalBlock
            blockID = iDivRegin%iFinalBlock(i)
            
            do j=1,iDivRegin%iBlock(blockID)%nPoin
                poinIP = iDivRegin%iBlock(blockID)%iPoin(j)
                
                val_iReBlock_Poin(poinIP) = i
            enddo
        enddo
            
        call testIO_DivRegin(iDivRegin,"iRoRegin",val_iFlag)
        
        
        !cleanData
        nPoin = 0
        deallocate(iMapOToN_Poin)
        deallocate(iMapNToO_Poin)
        deallocate(iCoor_Poin)
        
        call cleanDivRegin(iDivRegin)
        
    endsubroutine
    !
    !---------------------------------------------
    subroutine calReordering_Sate(val_kSate,val_iSate,  &
                val_ReOrder,val_nPoin,val_nSate,val_nPBA)
        implicit none
        integer,intent(in):: val_kSate(val_nPoin+1)
        integer,intent(in):: val_iSate(val_nSate)
        integer,intent(out):: val_ReOrder(val_nSate)
        integer,intent(in):: val_nPoin
        integer,intent(in):: val_nSate
        integer,intent(in):: val_nPBA
        !
        integer:: i,j,k
        integer:: nCont,blockID
        logical:: isExist
        integer:: sateBlock(val_nSate),blockList(val_nSate)
        type(typ_ReList):: iRe
        
        
        
        !---------------------------------------------
        !---------------------------------------------
        iRe%nPoin = val_nPoin
        
        nCont = 0
        do i=1,val_nPoin
            nCont = max(nCont,val_kSate(i+1)-val_kSate(i))
        enddo
        iRe%nLine = nCont
        allocate(iRe%reLine(iRe%nLine, iRe%nPoin))
        
        
        !---------------------------------------------
        !---------------------------------------------
        nCont = 0
        do i=1,val_nSate
            blockID = (val_iSate(i) + val_nPBA - 1)/val_nPBA
            
            sateBlock(i) = blockID
            
            isExist = .false.
            do j=1,nCont
                if(blockID == blockList(j)) then
                    isExist = .true.
                    exit
                endif
            enddo
            
            if(.not.isExist) then
                nCont = nCont + 1
                blockList(nCont) = blockID
            endif
        enddo
        
        iRe%nBlock = nCont
        
        
        !---------------------------------------------
        !---------------------------------------------
        allocate(iRe%blockID(iRe%nBlock))
        allocate(iRe%iFlag(iRe%nBlock, iRe%nPoin))
        allocate(iRe%iLinW(iRe%nPoin))
        allocate(iRe%iRowW(iRe%nBlock))
        allocate(iRe%iGrow(iRe%nBlock))
        iRe%blockID(:) = blockList(1:iRe%nBlock)
        iRe%iFlag(:,:) = 0
        iRe%iLinW(:)   = 0
        iRe%iRowW(:)   = 0
        iRe%iGrow(:)   = 0
        
        
        !---------------------------------------------
        !---------------------------------------------
        do i=1,val_nPoin
            do j=val_kSate(i)+1,val_kSate(i+1)
                blockID = sateBlock(j)
                
                do k=1,iRe%nBlock
                    if(blockID == iRe%blockID(k)) then
                        iRe%iFlag(k,i) = iRe%iFlag(k,i) + 1
                        exit
                    endif
                enddo
            enddo
        enddo
        
        do i=1,iRe%nPoin
            nCont = 0
            do j=1,iRe%nBlock
                nCont = nCont + iRe%iFlag(j,i)
            enddo
            iRe%iLinW(i) = nCont
        enddo
        
        do i=1,iRe%nBlock
            nCont = 0
            do j=1,iRe%nPoin
                if(iRe%iFlag(i,j) > 0) then
                    nCont = nCont + 1
                endif
            enddo
            iRe%iRowW(i) = nCont
        enddo
        
        
        !---------------------------------------------
        !---------------------------------------------
        call reOrder_ReList(iRe)
        
        
        !---------------------------------------------
        !---------------------------------------------
        do i=1,iRe%nPoin
            do j=1,iRe%nLine
                if(iRe%reLine(j,i) > 0) then
                    do k=val_kSate(i)+1,val_kSate(i+1)
                        if(sateBlock(k) == iRe%reLine(j,i)) then
                            iRe%reLine(j,i) = k
                            sateBlock(k) = 0
                            exit
                        endif
                    enddo
                endif
            enddo
        enddo
        
        
        do i=1,val_nPoin
            do j=val_kSate(i)+1,val_kSate(i+1)
                val_ReOrder(j) = iRe%reLine(j-val_kSate(i),i)
            enddo
        enddo
              
        return
        
    endsubroutine
    !   
    !---------------------------------------------
    subroutine reOrder_ReList(val_iRe)
        use mod_PreDefine_IOPort
        implicit none
        type(typ_ReList),intent(inout):: val_iRe
        !
        integer:: i,j,k
        integer:: iWeight,maxIP,nCont,nZero
        integer:: tempFlag(val_iRe%nPoin)
        logical:: isOk
        
        
        !---------------------------------------------
        !---------------------------------------------
        do i=1,val_iRe%nLine
            !---------------------------------------------
            !s1:获得置空数
            nZero = 0
            do j=1,val_iRe%nPoin
                if(val_iRe%iLinW(j) == 0) then
                    nZero = nZero + 1
                endif
            enddo
            
            !---------------------------------------------
            !获得最长有效blockID
            iWeight = 0
            do j=1,val_iRe%nBlock
                if(val_iRe%irowW(j) > iWeight) then
                    iWeight = val_iRe%irowW(j)
                    maxIP   = j
                endif
            enddo
            
            write(IOPort_Full,*) nZero,iWeight,maxIP
            
            !---------------------------------------------
            !若完整，这取出该行
            if(iWeight + nZero == val_iRe%nPoin) then
                
                do j=1,val_iRe%nPoin
                    if(val_iRe%iLinW(j) > 0) then
                        val_iRe%reLine(i,j) = val_iRe%blockID(maxIP)
                
                        val_iRe%iFlag(maxIP,j) = val_iRe%iFlag(maxIP,j) - 1
                        
                        val_iRe%iLinW(j) = val_iRe%iLinW(j) - 1
                    endif
                    
                enddo
                
                nCont = 0
                do j=1,val_iRe%nPoin
                    if(val_iRe%iFlag(maxIP,j) > 0) then
                        nCont = nCont + 1
                    endif
                enddo
                val_iRe%iRowW(maxIP) = nCont
                
                !write(IOPort_Full,'(A,32I8)') '  M1    ',val_iRe%reLine(i,:)
                cycle
            endif
            
            
            !---------------------------------------------
            !若不全，这补全（补全，从对后续影响最小的备选中选取）
            tempFlag(:) = 0
            do j=1,val_iRe%nPoin
                tempFlag(j) = min(tempFlag(j),val_iRe%iFlag(maxIP,j))
            enddo
            
            do j=1,val_iRe%nBlock
                if(j == maxIP) cycle
                
                if(iWeight + nZero + val_iRe%iRowW(j) == val_iRe%nPoin) then
                    isOk = .true.
                    do k=1,val_iRe%nPoin
                        if(val_iRe%iFlag(maxIP,k)*val_iRe%iFlag(j,k) > 0) then
                            isOk = .false.
                            exit
                        endif
                    
                        if(val_iRe%iFlag(maxIP,k)+val_iRe%iFlag(j,k) == 0) then
                            isOk = .false.
                            exit
                        endif
                    enddo
                    
                elseif(iWeight + nZero + val_iRe%iRowW(j) > val_iRe%nPoin) then
                    isOk = .true.
                    do k=1,val_iRe%nPoin
                        if((val_iRe%iFlag(maxIP,k) == 0).and.    &
                           (val_iRe%iFlag(j    ,k) <= 1) ) then
                            isOk = .false.
                            exit
                        endif
                    enddo
                else
                    isOk = .false.    
                endif
                
                if(isOk) then
                    
                    do k=1,val_iRe%nPoin
                        if(val_iRe%iLinW(k) > 0) then
                            if(val_iRe%iFlag(maxIP,k) > 0) then
                                val_iRe%reLine(i,k) = val_iRe%blockID(maxIP)
                
                                val_iRe%iFlag(maxIP,k) = val_iRe%iFlag(maxIP,k) - 1
                            else
                                val_iRe%reLine(i,k) = val_iRe%blockID(j)
                
                                val_iRe%iFlag(j,k) = val_iRe%iFlag(j,k) - 1
                            endif
                        
                            val_iRe%iLinW(k) = val_iRe%iLinW(k) - 1
                        endif
                    
                    enddo
                    
                    
                    nCont = 0
                    do k=1,val_iRe%nPoin
                        if(val_iRe%iFlag(maxIP,k) > 0) then
                            nCont = nCont + 1
                        endif
                    enddo
                    val_iRe%iRowW(maxIP) = nCont
                    
                    nCont = 0
                    do k=1,val_iRe%nPoin
                        if(val_iRe%iFlag(j,k) > 0) then
                            nCont = nCont + 1
                        endif
                    enddo
                    val_iRe%iRowW(j) = nCont
                    
                    !write(IOPort_Full,'(A,32I8)') '  M2    ',val_iRe%reLine(i,:)
                    
                    exit
                endif
            enddo
            
            if(isOk) cycle
            
            !---------------------------------------------
            !若单次补不齐，就随意取？ or 先不管了，后期随意取？
            !直接随意取，因为后期补全可能会出错
            
            do k=1,val_iRe%nPoin
                if(val_iRe%iLinW(k) > 0) then
                    if(val_iRe%iFlag(maxIP,k) > 0) then
                        val_iRe%reLine(i,k) = val_iRe%blockID(maxIP)
                
                        val_iRe%iFlag(maxIP,k) = val_iRe%iFlag(maxIP,k) - 1
                        
                        if(val_iRe%iFlag(maxIP,k) == 0) then
                            val_iRe%iRowW(maxIP) = val_iRe%iRowW(maxIP) - 1
                        endif
                    else
                        do j=1,val_iRe%nBlock
                            if(val_iRe%iFlag(j,k) == 0) cycle
                            
                            val_iRe%reLine(i,k) = val_iRe%blockID(j)
                                
                            val_iRe%iFlag(j,k) = val_iRe%iFlag(j,k) - 1
                            
                            if(val_iRe%iFlag(j,k) == 0) then
                                val_iRe%iRowW(j) = val_iRe%iRowW(j) - 1
                            endif
                            
                            exit
                        enddo
                    endif
                        
                    val_iRe%iLinW(k) = val_iRe%iLinW(k) - 1
                endif
            enddo
                    
        enddo
        
    endsubroutine
    !
endmodule
! 
  