
!---------------------------------------------
!定义求解环境 MPI 和 GPU
!---------------------------------------------
module mod_PreDefine_Environment
    implicit none
    !
    logical,parameter:: USING_MPI           = .true.
    logical,parameter:: USING_GPU           = .true.
#ifdef GFLOW_VDOMAIN_INC
    logical,parameter:: Enable_Inc          = .true.
#else
    logical,parameter:: Enable_Inc          = .false.
#endif
endmodule


!---------------------------------------------
!定义实数类型（单精度、双精度）及字符串长度
!---------------------------------------------
module mod_PreDefine_Precision
    implicit none
    !
#ifdef GFLOW_PRECISION_DP
    integer,parameter:: REALLEN             = 8  !double precision
#endif
#ifdef GFLOW_PRECISION_SP
    integer,parameter:: REALLEN             = 4  !single precision
#endif
    integer,parameter:: STRLEN              = 256   !字符串长度
    !
    integer,parameter:: oneAccessBits       = 128
    integer,parameter:: nPrThreadPerBlock   = 64
endmodule

!---------------------------------------------
!定义维度及变量个数
!---------------------------------------------
module mod_PreDefine_Dimension
    implicit none
    !
    integer,parameter:: nElemProp           = 16
    !
#ifdef GFLOW_DIMENSION_3D
    integer,parameter:: nDim                = 3        !3D
#endif
#ifdef GFLOW_DIMENSION_2D
    integer,parameter:: nDim                = 2         !2D
#endif

    integer,parameter:: nCons               = nDim + 2  !consvar: (守恒变量)
    integer,parameter:: nPrim               = nDim + 2  !primvar: (原始变量)
    integer,parameter:: nSolu               = nDim + 2  !soluvar: (求解变量)
    !
    integer,parameter:: Dir_Forward         =  1
    integer,parameter:: Dir_Backward        = -1
    !
    integer,parameter:: NDP1                = nDim + 1
    integer,parameter:: NDP2                = nDim + 2
    integer,parameter:: NDP3                = nDim + 3
    integer,parameter:: NDP4                = nDim + 4
    !
endmodule

module mod_PreDefine_Doma
    implicit none
    integer,parameter:: DOMA_FLUI       = 0
    integer,parameter:: DOMA_SOLI       = 1
endmodule

!---------------------------------------------
!定义边界类型
!---------------------------------------------
module mod_PreDefine_Mark
    implicit none
    !所有边界定义为负值，未定义为0
    integer,parameter:: MARK_NONE         =  0  !空边界，用于重叠
    integer,parameter:: MARK_WALL         = -1  !物面边界
    integer,parameter:: MARK_EQTW         = -102
    integer,parameter:: MARK_EULW         = -103  ! Euler wall
    integer,parameter:: MARK_MOVW         = -104  ! Move wall (both Trans and Rotat)

    integer,parameter:: MARK_INLE         = -2    ! inlet
    integer,parameter:: MARK_INLM         = -202  ! mass flow inlet

    integer,parameter:: MARK_OUTL         = -3    ! outlet
    integer,parameter:: MARK_OUTP         = -302  ! pressure outlet
    integer,parameter:: MARK_OUTM         = -303  ! mass flow outlet

    integer,parameter:: MARK_ATTA         = -11   ! internal interface
    integer,parameter:: MARK_SYMM         = -12   ! symmetry surface
    integer,parameter:: MARK_PFAR         = -13   ! pressure far field
    integer,parameter:: MARK_SLID         = -14   ! slid boundary (pair)
    integer,parameter:: MARK_OVST         = -15   ! overset boundary
    integer,parameter:: MARK_LPRD         = -16   ! linear period boundary
    integer,parameter:: MARK_CPRD         = -17   ! circumferential period boundary

    integer,parameter:: MARK_UDBC       = -99   !自定义边界,未用到
    !
    integer,parameter:: BCPAIR_NONE         =  0
    integer,parameter:: BCPAIR_SLIP         = -21   !滑移边界对
    integer,parameter:: BCPAIR_PRDL         = -22   !周期边界对
    integer,parameter:: BCPAIR_PRDR         = -23   !周期边界对
    integer,parameter:: BCPAIR_INFC         = -24   !内部边界对
endmodule


!---------------------------------------------
!定义单元类型
!---------------------------------------------
module mod_PreDefine_Elem
    implicit none

    integer,parameter:: ELEM_NONE           = 0     !空单元   $$$
    integer,parameter:: ELEM_EXTE           = 100   !扩展单元 $$$
    integer,parameter:: ELEM_LINE           = 3     !线单元
    integer,parameter:: ELEM_TRIANGLE       = 5     !三角形单元
    integer,parameter:: ELEM_RECTANGLE      = 9     !四边形单元
    integer,parameter:: ELEM_TETRAHEDRAL    = 10    !四面体单元
    integer,parameter:: ELEM_HEXAHEDRAL     = 12    !六面体单元
    integer,parameter:: ELEM_WEDGE          = 13    !三棱柱单元
    integer,parameter:: ELEM_PYRAMID        = 14    !金字塔单元
endmodule


!---------------------------------------------
!定义通量面类型
!---------------------------------------------
module mod_PreDefine_Infc
    implicit none
    !
    integer,parameter:: INFC_NONE           = -100
    integer,parameter:: INFC_BND            = -1
    integer,parameter:: INFC_ONEWAY         = 0
    integer,parameter:: INFC_TWOWAY         = 1
    integer,parameter:: INFC_RECIPROCAL     = 2
endmodule


!---------------------------------------------
!定义标记类型
!---------------------------------------------
module mod_PreDefine_MeshDeform
    implicit none
    !
    integer,parameter:: DEFORMKIND_NONE     = -100
    integer,parameter:: DEFORMKIND_STILL    = 0 !静止
    integer,parameter:: DEFORMKIND_TRANS    = 1 !平移，位移
    integer,parameter:: DEFORMKIND_ROTAT    = 2 !转动，中心+轴向+转角
    integer,parameter:: DEFORMKIND_REGNT    = 3 !整块网格平移
    integer,parameter:: DEFORMKIND_REGNR    = 4 !整块网格转动
    integer,parameter:: DEFORMKIND_SOFTT    = 5 !弹性移动，位移（可合并到平移）
    integer,parameter:: DEFORMKIND_SOFTR    = 6 !弹性弯曲，中心+轴向+转角
    !
    integer,parameter:: DEFORMMETHOD_NONE   = 0 ! None
    integer,parameter:: DEFORMMETHOD_DWM    = 1 ! distance weight method
    integer,parameter:: DEFORMMETHOD_SM     = 2 ! spring method
    integer,parameter:: DEFORMMETHOD_DM     = 3 ! delaunay mapping method
    integer,parameter:: DEFORMMETHOD_RBF    = 4 ! radial based function
    integer,parameter:: DEFORMMETHOD_DMRBF  = 5 ! DM + RBF
    !
    integer,parameter:: DEFORMPOINT_NONE    = 0 !未定义
    integer,parameter:: DEFORMPOINT_INTER   = 1 !内部插值点
    integer,parameter:: DEFORMPOINT_DEFOM   = 2 !边界运动点
    integer,parameter:: DEFORMPOINT_STILL   = 3 !边界不动点
    !
endmodule


!---------------------------------------------
!定义标记类型
!---------------------------------------------
module mod_PreDefine_Flag
    implicit none
    !注意：一般Flag定义为负值，正值用于宿主单元插值
    !
    integer,parameter:: FLAG_NONE           =  0    !未定义
    integer,parameter:: FLAG_VALID          = -101  !有效
    integer,parameter:: FLAG_INVAL          = -100  !无效
    integer,parameter:: FLAG_INNER          = -102  !位于内部
    integer,parameter:: FLAG_EXCH           = -103  !位于交互块
    integer,parameter:: FLAG_BOUND          = -104  !位于边界
    integer,parameter:: FLAG_WALL           = -105  !位于物面
    integer,parameter:: FLAG_FORCED         = -108  !强制
    !
    !integer,parameter:: FLAG_HOID = hostID > 0
    integer,parameter:: FLAG_HOST           = -10
    integer,parameter:: FLAG_EXTE           = -11   !新增扩展点，不参与计算，只提供值，可归属无效点
endmodule

!---------------------------------------------
!定义标记类型
!---------------------------------------------
module mod_PreDefine_ExteKind
    implicit none
    !
    integer,parameter:: EXTE_NONE           = 0
    integer,parameter:: EXTE_SLIP           = 1
    integer,parameter:: EXTE_OVST           = 2
    integer,parameter:: EXTE_PART           = 3
endmodule


!---------------------------------------------
!定义网格块数据传递类型
!---------------------------------------------
module mod_PreDefine_ExchKind
    implicit none
    !
    integer,parameter:: EXCH_NONE           = 0     !未指定
    integer,parameter:: EXCH_SEND           = 1     !传出
    integer,parameter:: EXCH_RECV           = 2     !传入
    integer,parameter:: EXCH_INTE           = 3     !内部插值
endmodule


!---------------------------------------------
!定义输入输出端口号
!---------------------------------------------
module mod_PreDefine_IOPort
    implicit none
    !
    integer,parameter:: ioPort_In           = 5     !键盘
    integer,parameter:: ioPort_Out          = 6     !屏幕
    !
    integer,parameter:: ioPort_FULL         = 21
    integer,parameter:: ioPort_MESH         = 22
    integer,parameter:: ioPort_GEOM         = 23
    integer,parameter:: ioPort_TEST         = 24
    integer,parameter:: ioPort_TIME         = 25
    !
    integer,parameter:: ioPort_RSU          = 30
    integer,parameter:: ioPort_RES          = 31
    integer,parameter:: ioPort_AERO         = 35
    !
    integer,parameter:: ioPort_ERR          = 40


    !===========================
    !NBC:仅流场    PBC :流场+边界  OBC  :仅边界
    !MID:中间步    DUAL:双时间步   FINAL:最终
    integer,parameter:: SAVE_NOTHING        = 0
    integer,parameter:: SAVE_NBC_MID        = 1
    integer,parameter:: SAVE_NBC_DUAL       = 2
    integer,parameter:: SAVE_NBC_FINAL      = 3
    integer,parameter:: SAVE_PBC_MID        = 11
    integer,parameter:: SAVE_PBC_DUAL       = 12
    integer,parameter:: SAVE_PBC_FINAL      = 13
    integer,parameter:: SAVE_OBC_MID        = 21
    integer,parameter:: SAVE_OBC_DUAL       = 22
    integer,parameter:: SAVE_OBC_FINAL      = 23
endmodule


!---------------------------------------------
!杂项
!---------------------------------------------
module mod_PreDefine_Sundry
    implicit none
    !
endmodule


!---------------------------------------------
!
!---------------------------------------------
module mod_PreDefine_ALL
    use mod_PreDefine_Environment
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    use mod_PreDefine_Flag
    use mod_PreDefine_Elem
    use mod_PreDefine_Infc
    use mod_PreDefine_Mark
    use mod_PreDefine_ExchKind
    use mod_PreDefine_IOPort
    use mod_PreDefine_Sundry
    implicit none
    !
endmodule
!
