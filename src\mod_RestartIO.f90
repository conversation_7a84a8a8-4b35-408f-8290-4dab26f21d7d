!
module mod_RestartIO
    use mod_PreDefine_Precision
    use mod_PreDefine_IOPort
    use mod_MPIEnvironment
    use mod_Config
    use mod_WorkPath
    use mod_strOfNumber
    use mod_Interface_AllocateArray
    implicit none
    !
    real(kind=REALLEN),allocatable:: iOneVar(:)
    !
contains
    !
    subroutine saveForRestart(val_iParm,val_iSolu)
        use mod_TypeDef_Solu
        use mod_TypeDef_Parm
        implicit none
        type(typ_Parm),intent(in):: val_iParm
        type(typ_Solu),intent(in):: val_iSolu
        !
        integer:: i

        call allocateArray(iOneVar, size(val_iSolu%soluvar,1))
        
        open(ioPort_FULL, file=trim(adjustl(soluFullPath))//'/restart_Bit'//    &
                    strTwo_Number(myID+1)//'.dat', FORM='unformatted'           )

        write(ioPort_FULL) val_iParm%isDualTime
        if(val_iParm%isDualTime) then
            write(ioPort_FULL) val_iParm%iCurrOuterIter
            write(ioPort_FULL) val_iParm%iCurrInnerIter
            write(ioPort_FULL) val_iParm%iIter
        end if
        write(ioPort_FULL) val_iParm%ResErrRef

        do i=1,size(val_iSolu%soluvar,2)
            iOneVar(:) = val_iSolu%soluvar(:,i)
            write(ioPort_FULL) iOneVar
        enddo
        
        do i=1,size(val_iSolu%primvar,2)
            iOneVar(:) = val_iSolu%primvar(:,i)
            write(ioPort_FULL) iOneVar
        enddo
            
        close(ioPort_FULL)
        
    endsubroutine
    !
    subroutine readForRestart(val_iParm,val_iSolu)
        use mod_TypeDef_Solu
        use mod_TypeDef_Parm
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Solu),intent(inout):: val_iSolu
        !
        integer:: i
        integer:: iIter(3)
        logical:: isDual = .false.
        
        call allocateArray(iOneVar, size(val_iSolu%soluvar,1))
        
        open(ioPort_FULL,file=trim(adjustl(soluFullPath))//'/restart_Bit'//     &
                    strTwo_Number(myID+1)//'.dat', FORM='unformatted',status='old')

        read(ioPort_FULL) isDual
        if(isDual) then
            read(ioPort_FULL) val_iParm%iCurrOuterIter
            read(ioPort_FULL) val_iParm%iCurrInnerIter
            read(ioPort_FULL) val_iParm%iIter
        end if

        read(ioPort_FULL) val_iParm%ResErrRef
        val_iParm%isResErrRedCalculated = .true.

        do i=1,size(val_iSolu%soluvar,2)
            read(ioPort_FULL) iOneVar
            val_iSolu%soluvar(:,i) = iOneVar(:)
        enddo
        
        do i=1,size(val_iSolu%primvar,2)
            read(ioPort_FULL) iOneVar
            val_iSolu%primvar(:,i) = iOneVar(:)
        enddo
            
        close(ioPort_FULL)
        
    endsubroutine
    !
endmodule
!
    
    