module mod_Configer
    implicit none
    !
contains
    !
    subroutine readCfgFile_DomaLines
        use mod_Config
        use mod_WorkPath
        use mod_PreDefine_IOPort
        use mod_DecodeCfgLine
        implicit none
        integer:: istat,jstat,count
        character(len=STRLEN):: aLine
        !
        integer:: i,j,nDoma,icDoma,domaID
        character(len=999):: DomaLine(100)


        open(ioPort_FULL,file=trim(cfigFullPath),iostat = istat,status='old')
        if(istat /= 0) then
            write(ioPort_Out,*) 'ERROR: read parms file (parms.cfg) failed.'
            stop
        endif

        icDoma = 0
        do
            read(IOPort_FULL,"(A)",iostat=istat) aLine
            if(istat /= 0) exit
            if(len_trim(aLine) == 0) cycle

            aLine = adjustl(aLine)

            do i=1,len_trim(aLine)
                if(aLine(i:i) == '%') then
                    do j=i,len_trim(aLine)
                        aLine(j:j) = ' '
                    enddo
                    exit
                endif
            enddo

            if(len_trim(aLine) == 0) cycle
            aLine = adjustl(aLine)

            if(aLine(1:1) == '%') then
                cycle
            elseif(aline(1:5) == '#NDOM') then
                read(aLine(7:),*) nDoma
                allocate(DomaInfos(nDoma))
            elseif(aline(1:5) == '#DOMA') then
                icDoma = icDoma + 1
                DomaLine(icDoma) = aline
            end if
        end do

        do i=1,icDoma
            call getDomaID(DomaLine(i), DomaID)
            if(domaID > nDoma) then
                write(*,*) 'Wrong ID in domaLine: ', trim(DomaLine(i))
                cycle
            end if

            call decodeDomaLine(DomaLine(i), DomaInfos(domaID))
        end do

        close(ioPort_FULL)

    endsubroutine
    !
    subroutine readCfgFile_MarkLines
        use mod_Config
        use mod_WorkPath
        use mod_PreDefine_IOPort
        use mod_DecodeCfgLine
        implicit none
        integer:: istat,jstat,count
        character(len=STRLEN):: aLine
        !
        integer:: i,j,nMark,icMark,markID
        character(len=999):: MarkLine(100)


        open(ioPort_FULL,file=trim(cfigFullPath),iostat = istat,status='old')
        if(istat /= 0) then
            write(ioPort_Out,*) 'ERROR: read parms file (parms.cfg) failed.'
            stop
        endif

        icMark = 0
        do
            read(IOPort_FULL,"(A)",iostat=istat) aLine
            if(istat /= 0) exit
            if(len_trim(aLine) == 0) cycle

            aLine = adjustl(aLine)

            do i=1,len_trim(aLine)
                if(aLine(i:i) == '%') then
                    do j=i,len_trim(aLine)
                        aLine(j:j) = ' '
                    enddo
                    exit
                endif
            enddo

            if(len_trim(aLine) == 0) cycle
            aLine = adjustl(aLine)

            if(aLine(1:1) == '%') then
                cycle
            elseif(aline(1:5) == '#NMRK') then
                read(aLine(7:),*) nMark
                allocate(MarkInfos(nMark))
            elseif(aline(1:5) == '#MARK') then
                icMark = icMark + 1
                MarkLine(icMark) = aline
            end if
        end do

        do i=1,icMark
            call getMarkID(MarkLine(i), markID)
            if(markID > nMark) then
                write(*,*) 'Wrong ID in markLine: ', trim(MarkLine(i))
                cycle
            end if

            call decodeMarkLine(MarkLine(i), MarkInfos(markID))
        end do

        close(ioPort_FULL)

    endsubroutine
    !
    subroutine readCfgFile_MRFLines
        use mod_Config
        use mod_WorkPath
        use mod_PreDefine_IOPort
        use mod_DecodeCfgLine
        implicit none
        integer:: istat,jstat,count
        character(len=STRLEN):: aLine
        !
        integer:: i,j,nMRF,icMRF,mID
        character(len=999):: MRFLine(100)


        open(ioPort_FULL,file=trim(cfigFullPath),iostat = istat,status='old')
        if(istat /= 0) then
            write(ioPort_Out,*) 'ERROR: read parms file (parms.cfg) failed.'
            stop
        endif

        icMRF = 0
        do
            read(IOPort_FULL,"(A)",iostat=istat) aLine
            if(istat /= 0) exit
            if(len_trim(aLine) == 0) cycle

            aLine = adjustl(aLine)

            do i=1,len_trim(aLine)
                if(aLine(i:i) == '%') then
                    do j=i,len_trim(aLine)
                        aLine(j:j) = ' '
                    enddo
                    exit
                endif
            enddo

            if(len_trim(aLine) == 0) cycle
            aLine = adjustl(aLine)

            if(aLine(1:1) == '%') then
                cycle
            elseif(aline(1:5) == '#NMRF') then
                read(aLine(7:),*) nMRF
                allocate(MRFInfos(nMRF))
            elseif(aline(1:4) == '#MRF') then
                icMRF = icMRF + 1
                MRFLine(icMRF) = aline
            end if
        end do

        do i=1,icMRF
            call getMRFID(MRFLine(i), mID)
            if(mID > nMRF) then
                write(*,*) 'Wrong ID in MRFLine: ', trim(MRFLine(i))
                cycle
            end if

            call decodeMRFLine(MRFLine(i), MRFInfos(mID))
        end do

        close(ioPort_FULL)

    endsubroutine
    !
    subroutine readCfgFile_NameList
        use mod_Config
        use mod_WorkPath
        use mod_PreDefine_IOPort
        implicit none
        integer:: istat,jstat,count
        character(len=STRLEN):: aLine

        !---------------------------------------------
        ! 1. namelist中的参数，不一定要全部出现在相应文件中
        ! 2. 文件中 不可出现namelist中不存在的参数
        ! 3.
        namelist/config_List/ &
                KIND_HARDWARE             , NUMBER_PART               ,  &
                GRID_FILENAME             , GRID_SCALE_FACTOR         ,  &
                MESH_DYFIRST              , MESH_DOMALEN              , &
                PHYSICAL_DIMENSION        , PHYSICAL_PROBLEM          ,  &
                KIND_TURB_MODEL           , KIND_TURBSA , KIND_TURBSST,  &

                IS_INCOMPRESSIBLE         , IS_DIMENSIONLESS          ,  &
                RESTART_SOL               ,  &
                CONTAIN_MOVINGBC          , CONVERT_OUTP2WALL         ,  &

                AIR_GAMMA                 , AIR_GASCONSTANT           ,  &
                AIR_CP                    , AIR_VISCOSITY             ,  &
                AIR_PRANDTL_LAM           , AIR_PRANDTL_TURB          ,  &

                FLOW_PARMTYPE             , FLOW_PRESSURE             ,  &
                FLOW_TEMPERATURE          , FLOW_VELOCITY             ,  &
                FLOW_MACH                 , FLOW_DIR                  ,  &
                FLOW_AOA                  , FLOW_AOS                  ,  &
                FLOW_AOA_RANGE            , FLOW_AOS_RANGE            ,  &
                FLOW_REYNOLDS_NUMBER      , REYNOLDS_REFLENGTH        ,  &
                FLOW_LVPLANE              ,  &

                FLOW_ALLOW_CHANGES        , FLOW_ALLOW_MAXX           ,  &
                FLOW_ALLOW_MIND           , FLOW_ALLOW_MAXD           ,  &
                FLOW_ALLOW_MINP           , FLOW_ALLOW_MAXP           ,  &
                FLOW_ALLOW_MINT           , FLOW_ALLOW_MAXT           ,  &
                FLOW_ALLOW_MINX           ,                              &

                AEROFM_REFM               ,                              &
                AEROFM_REFS               , AEROFM_REFL               ,  &
                AEROFM_ORGN               , AEROFM_NAVGITER           ,  &

                CONV_NUM_METHOD_FLOW      , UPWIND_ORDER              ,  &
                NITER_O1TUNING            , UPWIND_SCHEME             ,  &
                UPWIND_LIMITER            , UPWIND_LIMITER_K          ,  &
                ARTDISS_K2                , ARTDISS_K4                ,  &

                TIME_DISCRE_FLOW          , TIME_DISCRE_TURB          ,  &
                CFL_NUMBER                , CFL_RAMP                  ,  &
                CFL_RAMP_KIND             , NITER_OF_MCGS             ,  &
                IMPLICIT_TREATMENT        , IMPLICIT_WEIGHT           ,  &

                UNSTEADY_SIMULATION       , MESH_DEFORM_METHOD        ,  &
                UNST_PHYSTIMEBGN          , UNST_PHYSTIMEBGN          ,  &
                UNST_PHYSTIMESTEP         ,                              &

                UNST_INNER_ITER           , UNST_OUTER_ITER           ,  &
                NITER_STEADY              ,                              &
                RESAVE_BGNITER            , &
                RESAVE_ITER               , RESERR_ITER               ,  &
                RESERR_SHOW               , RESERR_RESUCTION          ,  &
                RESERR_MINVAL

        !---------------------------------------------
        !打开文件，读取namelist
        open(ioPort_FULL,file=trim(cfigFullPath),iostat = istat,status='old')
        if(istat /= 0) then
            write(ioPort_Out,*) 'ERROR: read config file failed.',trim(cfigFullPath)
            stop
        endif

        read(ioPort_FULL,nml=config_List)

        close(ioPort_FULL)

    endsubroutine
    !
    subroutine checkAndCompleteConfig(val_isOk)
        use mod_Config
        use mod_MPIEnvironment
        use mod_PreDefine_ALL
        implicit none
        logical:: val_isOk

        val_isOk = .true.

        if(NUMERICAL_METHOD /= _NUMERICAL_FVM .and. NUMERICAL_METHOD /= _NUMERICAL_MESHLESS) then
            if(myID == MASTER_NODE) then
                write(*,*) "Unsupportted Numerical Method (Only FVM and Meshfree are supportted)."
            endif
            val_isOk = .false.
            return
        end if

        if(KIND_HARDWARE /= _CPU .and. KIND_HARDWARE /= _GPU) then
            if(myID == MASTER_NODE) then
                write(*,*) "Unsupportted Hardware Type (Only CPU and CPU are supportted)."
            endif
            val_isOk = .false.
            return
        end if

        if(NUMBER_PART > 1) then
            if(myID == MASTER_NODE) then
                write(*,*) "Only support : <NUMBER_PART = 1>."
            endif
            val_isOk = .false.
            return
        endif

        if(PHYSICAL_PROBLEM /= _EULER .and. PHYSICAL_PROBLEM /= _NS .and. PHYSICAL_PROBLEM /= _TURB) then
            if(myID == MASTER_NODE) then
                write(*,*) "Unsupportted Problem Type (Only Euler, NS and RANS are supportted)."
            endif
            val_isOk = .false.
            return
        end if

        if(PHYSICAL_DIMENSION /= nDim) then
            if(myID == MASTER_NODE) then
                write(ioPort_Out,*) 'ERROR: PHYSICAL_DIMENSION /= nDim'
            endif

            val_isOk = .false.
            return
        endif

        if(.not.USING_GPU .and. (KIND_HARDWARE == _GPU)) then
            if(myID == MASTER_NODE) then
                write(ioPort_Out,*) 'ERROR: NO_GPU.and.(KIND_HARDWARE == _GPU)'
            endif

            val_isOk = .false.
            return
        endif

        if((.not.USING_MPI) .and. (nProcess > 1)) then
            if(myID == MASTER_NODE) then
                write(ioPort_Out,*) 'ERROR: NO_MPI.and.(numprocs > 1)'
            endif

            val_isOk = .false.
            return
        endif

        if(KIND_HARDWARE == _GPU) then
            if(TIME_DISCRE_FLOW == _LUSGS_IMPLICIT) then
                write(ioPort_Out,*) "Wrong TIME_DISCRE_FLOW specified for GPU Implicit, and it's converted to MC-LUSGS."
                TIME_DISCRE_FLOW = _MCLUSGS_IMPLICIT
            end if
        end if

        if(PHYSICAL_PROBLEM >= _TURB) then
            if(KIND_TURB_MODEL == _TURB_BL) then
                write(ioPort_Out,*) "Wrong KIND_TURB_MODEL specified for RANS simulation, and it's converted to TURB_SA"
                KIND_TURB_MODEL = _TURB_SA
            end if
        else
            KIND_TURB_MODEL = _NONE
        end if

        if(UNSTEADY_SIMULATION == _NONE) then
            if(UNST_OUTER_ITER /= 0) then
                write(ioPort_Out,*) "Wrong UNST_OUTER_ITER specified for STEADY flow, ant it's conveted to 0"
                UNST_OUTER_ITER = 0
            end if
        end if

        if(FLOW_LVPLANE /= _XY) then
            FLOW_LVPLANE = _XZ
        endif

        if(AEROFM_NAVGITER /= 1) then
            write(ioPort_Out,*) "Now only support AEROFM_NAVGITER = 1"
            AEROFM_NAVGITER = 1
        end if

        !---------------------------------------------
        !采用单精度、预处理形式处理不可压流动时，因“精度不足”程序不收敛，故此提示
        if(IS_INCOMPRESSIBLE /= _YES) then
            IS_INCOMPRESSIBLE = _NO
        end if

        if((REALLEN == 4) .and. (IS_INCOMPRESSIBLE == _YES)) then
            if(myID == MASTER_NODE) then
                write(ioPort_Out,*) "Error: The treatment for incompressible flows "
                write(ioPort_Out,*) "       is not support in single-precision!    "
                write(ioPort_Out,*) "             (precision deficiency)           "
                write(ioPort_Out,*) " "
            endif
            val_isOk = .false.
            return
        endif

        !---------------------------------------------
        if(NUMBER_PART /= nProcess) then
            if(myID == MASTER_NODE) then
                write(ioPort_Out,*) "Error: NUMBER_PART /= nProcess."
            endif
            val_isOk = .false.
            return
        endif

        !---------------------------------------------
        !---------------------------------------------
        if(FLOW_PARMTYPE == _NON_DIMENSIONAL) then
            call setFlowFromMachAOA
        elseif(FLOW_PARMTYPE == _DIMENSIONAL) then
            call setFlowFromVelocityVector
        endif

    endsubroutine
    !
    subroutine setFlowFromMachAOA
        use mod_PreDefine_Dimension
        use mod_Config
        implicit none
        integer:: i
        real(kind=REALLEN):: veloEi,cEi


        if(abs(FLOW_AOA_RANGE(3)) > 1.0E-2) then
            FLOW_AOA = FLOW_AOA_RANGE(1)
        endif
        if(abs(FLOW_AOS_RANGE(3)) > 1.0E-2) then
            FLOW_AOS = FLOW_AOS_RANGE(1)
        end if

        FLOW_DENSITY = FLOW_PRESSURE/(FLOW_TEMPERATURE*AIR_GASCONSTANT)

        cEi = sqrt(AIR_GAMMA*AIR_GASCONSTANT*FLOW_TEMPERATURE)
        veloEi = cEi*FLOW_MACH

        if(nDim == 2) then
            FLOW_DIR(1) = cosd(FLOW_AOA)
            FLOW_DIR(2) = sind(FLOW_AOA)
            FLOW_DIR(3) = 0.0
        elseif(nDim == 3) then
            if(FLOW_LVPLANE == _XZ) then
                FLOW_DIR(1) = cosd(FLOW_AOS)*cosd(FLOW_AOA)
                FLOW_DIR(2) = sind(FLOW_AOS)
                FLOW_DIR(3) = cosd(FLOW_AOS)*sind(FLOW_AOA)
            else if(FLOW_LVPLANE == _XY) then
                FLOW_DIR(1) = cosd(FLOW_AOS)*cosd(FLOW_AOA)
                FLOW_DIR(2) = cosd(FLOW_AOS)*sind(FLOW_AOA)
                FLOW_DIR(3) = sind(FLOW_AOS)
            end if
        endif

        do i=1,3
            FLOW_VELOCITY(i) = veloEi*FLOW_DIR(i)
        enddo

        if(PHYSICAL_PROBLEM /= _EULER) then
            AIR_VISCOSITY = FLOW_DENSITY*veloEi*REYNOLDS_REFLENGTH/max(FLOW_REYNOLDS_NUMBER, 1.0E-3)
        endif

    endsubroutine
    !
    subroutine setFlowFromVelocityVector
        use mod_PreDefine_Dimension
        use mod_Config
        implicit none
        integer:: i
        real(kind=REALLEN):: veloEi,cEi


        FLOW_DENSITY = FLOW_PRESSURE/(FLOW_TEMPERATURE*AIR_GASCONSTANT)

        cEi = sqrt(AIR_GAMMA*AIR_GASCONSTANT*FLOW_TEMPERATURE)

        veloEi = 0.0
        do i=1,3
            veloEi = veloEi + FLOW_VELOCITY(i)*FLOW_VELOCITY(i)
        enddo
        veloEi = sqrt(veloEi)

        do i=1,3
            FLOW_DIR(i) = FLOW_VELOCITY(i)/veloEi
        enddo

        FLOW_MACH = veloEi/cEi

        if(PHYSICAL_PROBLEM /= _EULER) then
            FLOW_REYNOLDS_NUMBER = FLOW_DENSITY*veloEi*REYNOLDS_REFLENGTH/max(AIR_VISCOSITY, 1.0E-10)
        endif

    endsubroutine
    !
    subroutine trtConfig_DimensionLess
        use mod_Config
        use mod_parmDimLess
        use mod_PreDefine_Mark
        implicit none
        !
        integer:: i
        real(kind=REALLEN):: sq_vel


        !Basic independent variables
        if(IS_DIMENSIONLESS == _NO) then
            DLRef_length      = 1.0
            DLRef_density     = 1.0
            DLRef_pressure    = 1.0
            DLRef_temperature = 1.0
            !
        elseif(IS_DIMENSIONLESS == _YES) then
            DLRef_length      = 1.0
            DLRef_density     = FLOW_DENSITY
            DLRef_pressure    = FLOW_PRESSURE
            DLRef_temperature = FLOW_TEMPERATURE
        endif

        DLRef_velocity        = sqrt(DLRef_Pressure/DLRef_density)
        DLRef_time            = DLRef_length/DLRef_velocity
        DLRef_viscosity       = DLRef_density*DLRef_velocity*DLRef_length
        DLRef_bodyForce       = DLRef_density*DLRef_velocity**2 !/Ref_length==1

        !Derived variables
        !DLRef_KinVoscosity   = DLRef_viscosity/DLRef_density
        DLRef_energy         = DLRef_velocity**2
        DLRef_enthalpy       = DLRef_velocity**2
        !DLRef_entropy        = DLRef_energy/DLRef_temperature
        !DLRef_heatFlux       = DLRef_density*DLRef_energy*DLRef_velocity
        DLRef_gasConstant     = DLRef_pressure/(DLRef_density*DLRef_temperature)
        DLRef_Cp              = DLRef_gasConstant
        DLRef_Cv              = DLRef_gasConstant
        !DLRef_heatConduct    = DLRef_Cp*DLRef_viscosity
        !DLRef_turbK          = DLRef_velocity**2
        !DLRef_turbW          = DLRef_velocity/DLRef_temperature
        !
        FLOW_PRESSURE       = FLOW_PRESSURE     / DLRef_pressure
        FLOW_TEMPERATURE    = FLOW_TEMPERATURE  / DLRef_temperature
        FLOW_DENSITY        = FLOW_DENSITY      / DLRef_density
        FLOW_VELOCITY(:)    = FLOW_VELOCITY(:)  / DLRef_velocity
        AIR_GASCONSTANT     = AIR_GASCONSTANT   / DLRef_gasConstant
        AIR_CP              = AIR_CP            / DLRef_Cp
        AIR_VISCOSITY       = AIR_VISCOSITY     / DLRef_viscosity


    endsubroutine
    !
    subroutine updConfig_FarField(val_iIter)
        use mod_PreDefine_Dimension
        use mod_Config
        implicit none
        integer,intent(in):: val_iIter
        !
        integer:: i

        !---------------------------------------------
        if(abs(FLOW_AOA_RANGE(3)) > 1.0E-2) then
            FLOW_AOA = FLOW_AOA_RANGE(1) + FLOW_AOA_RANGE(3)*max(val_iIter-1, 0)
        else
            FLOW_AOA = FLOW_AOA
        endif

        !---------------------------------------------
        if(abs(FLOW_AOS_RANGE(3)) > 1.0E-2) then
            FLOW_AOS = FLOW_AOS_RANGE(1) + FLOW_AOS_RANGE(3)*max(val_iIter-1, 0)
        else
            FLOW_AOS = FLOW_AOS
        endif

        !---------------------------------------------
        if(nDim == 2) then
            FLOW_DIR(1) = cosd(FLOW_AOA)
            FLOW_DIR(2) = sind(FLOW_AOA)
            FLOW_DIR(3) = 0.0
        elseif(nDim == 3) then
            if(FLOW_LVPLANE == _XY) then
                FLOW_DIR(1) = cosd(FLOW_AOS)*cosd(FLOW_AOA)
                FLOW_DIR(2) = cosd(FLOW_AOS)*sind(FLOW_AOA)
                FLOW_DIR(3) = sind(FLOW_AOS)
            else !_XZ
                FLOW_DIR(1) = cosd(FLOW_AOS)*cosd(FLOW_AOA)
                FLOW_DIR(2) = sind(FLOW_AOS)
                FLOW_DIR(3) = cosd(FLOW_AOS)*sind(FLOW_AOA)
            end if
        endif

        do i=1,3
            FLOW_VELOCITY(i) = sqrt(AIR_GAMMA*AIR_GASCONSTANT*FLOW_TEMPERATURE)*FLOW_MACH*FLOW_DIR(i)
        enddo

    endsubroutine
    !
end module mod_Configer