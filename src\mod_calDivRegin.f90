!
module mod_calDivRegin
    use mod_Interface_AllocateArray
    implicit none
    !
contains
    !
    !---------------------------------------------
    !剖分对象的初始化
    !---------------------------------------------
    subroutine initDivRegin(val_iDivRegin,val_iCoor,val_nPoin,val_nPoinPerBlock)
        use mod_TypeDef_Division
        use mod_PreDefine_Dimension
        use mod_Interface_LOG2
        implicit none
        type(typ_DivRegin),intent(inout):: val_iDivRegin
        real(kind=REALLEN),intent(in):: val_iCoor(nDim,val_nPoin)
        integer,intent(in):: val_nPoin
        integer,intent(in):: val_nPoinPerBlock
        !
        integer:: i,poinID
    
    
        if(val_iDivRegin%nPoin <= 0) return
    
    
        !---------------------------------------------
        !初始化基本数据
        call allocateArray(val_iDivRegin%iCoor_Orgn, val_iDivRegin%nPoin, nDim)
        call allocateArray(val_iDivRegin%iFinalSort, val_iDivRegin%nPoin)
        call allocateArray(val_iDivRegin%iNeID_Poin, val_iDivRegin%nPoin, nDim)
        call allocateArray(val_iDivRegin%iPoin_Sort, val_iDivRegin%nPoin, nDim)
        call allocateArray(val_iDivRegin%iCoor_Sort, val_iDivRegin%nPoin, nDim)
    
        val_iDivRegin%iFinalSort(:) = 0
    
        do i=1,val_iDivRegin%nPoin
            poinID = val_iDivRegin%iPoin(i)
        
            val_iDivRegin%iCoor_Orgn(i,:) = val_iCoor(:,poinID)
        
            val_iDivRegin%iNeID_Poin(i,:) = i
            val_iDivRegin%iPoin_Sort(i,:) = i
            val_iDivRegin%iCoor_Sort(i,:) = val_iCoor(:,poinID)
        enddo
    
        
        !---------------------------------------------
        !指定分区块的点数
        if(val_nPoinPerBlock > 0) then
            val_iDivRegin%nPoinPerBlock = val_nPoinPerBlock
        else
            call getNPoinPerBlock(val_iDivRegin%nPoin,val_iDivRegin%nPoinPerBlock)
        endif
    
    
        !---------------------------------------------
        !指定分区块的层级数、块数等
        val_iDivRegin%nDivLevel = LOG2(val_iDivRegin%nPoin/val_iDivRegin%nPoinPerBlock + 0.5)
    
        val_iDivRegin%nBlock = 2**(val_iDivRegin%nDivLevel + 1)
    
        allocate(val_iDivRegin%iBlock(val_iDivRegin%nBlock))
    
    
    endsubroutine
    !
    !---------------------------------------------
    !网格剖分对象的计算
    !---------------------------------------------
    subroutine calDivRegin(val_iDivRegin,val_isOk)
        use mod_TypeDef_Division
        implicit none
        type(typ_DivRegin),intent(inout):: val_iDivRegin
        logical,intent(out):: val_isOk
        !
        integer:: i
        integer:: blockID
    
    
        !---------------------------------------------
        !剖分对象的点排序
        call sortPoinOfRagin(val_iDivRegin%iCoor_Sort,  &
                             val_iDivRegin%iPoin_Sort,  &
                             val_iDivRegin%iNeID_Poin,  &
                             val_iDivRegin%nPoin , nDim )
    
    
        !---------------------------------------------
        !根区块的初始化
        val_iDivRegin%iBlock(1)%nPoin = val_iDivRegin%nPoin
    
        call allocateArray(val_iDivRegin%iBlock(1)%iPoin, val_iDivRegin%iBlock(1)%nPoin)
        do i=1,val_iDivRegin%iBlock(1)%nPoin
            val_iDivRegin%iBlock(1)%iPoin(i) = i
        enddo
        val_iDivRegin%iBlock(1)%fartherID = 0
    
    
        !---------------------------------------------
        !逐层二分
        do i=0,val_iDivRegin%nDivLevel
        
            do blockID=2**i,2**(i+1)-1
                !---------------------------------------------
                !已知点列表，计算实体
                call getIBlockOfRagin(val_iDivRegin,blockID)
            
            
                !---------------------------------------------
                !获取子区点列表 sonL = 2*blockID; sonR = sonL+1
                call getSonBlockOfRagin(val_iDivRegin,blockID)
            enddo
        enddo
    
    
        !---------------------------------------------
        !获取最终有效块编号列表
        call getFinalBlocks(val_iDivRegin)
    
        val_isOk = .true.
        return
    
    endsubroutine
    !
    !---------------------------------------------
    !计算每个剖分块包含点数
    !---------------------------------------------
    subroutine getNPoinPerBlock(val_nPoin,val_nPoinPerBlock)
        use mod_Interface_LOG2
        implicit none
        integer,intent(in):: val_nPoin
        integer,intent(inout):: val_nPoinPerBlock
        !
        integer:: i,nLevel,nBase
    
    
        nLevel = LOG2(floor(sqrt(val_nPoin + 0.1)))
        if(2**nLevel > sqrt(val_nPoin + 0.0)) then
            nLevel = nLevel -1
        endif
    
        nBase = 2** nLevel
    
        do i=1,100
            if(nBase*i*2**i >= val_nPoin) exit
        enddo
    
        val_nPoinPerBlock = nBase*i
    
    endsubroutine
    !
    !---------------------------------------------
    !按坐标从小到大排序
    !---------------------------------------------
    subroutine sortPoinOfRagin(val_iCoor,val_iNumn,val_iNeID,val_nPoin,val_nDim)
        use mod_PreDefine_Precision
        use mod_Interface_LOG2
        implicit none
        real(kind=REALLEN),intent(inout):: val_iCoor(val_nPoin , val_nDim)
        integer           ,intent(inout):: val_iNumn(val_nPoin , val_nDim)
        integer           ,intent(inout):: val_iNeID(val_nPoin , val_nDim)
        integer,intent(in):: val_nPoin
        integer,intent(in):: val_nDim
        !
        integer:: i,j,k
        integer:: nLevel,iSize,poinID
        integer:: iBegin,iEnd,jBegin,jEnd
        integer:: tempNumn(val_nPoin)
        real(kind=REALLEN):: tempCoor(val_nPoin)
    
    
        nLevel = LOG2(val_nPoin)
    
        do i=1,val_nDim
            tempNumn(:) = val_iNumn(:,i)
            tempCoor(:) = val_iCoor(:,i)
        
            do j=1,nLevel
                iSize = 2**j
                do k=1,val_nPoin,iSize
                    iBegin = k
                    iEnd   = k + iSize/2 - 1
                    jBegin = k + iSize/2
                    jEnd   = k + iSize - 1
                
                    if(iEnd >= val_nPoin) cycle
                
                    jEnd = min(jEnd,val_nPoin)
                    call sort_Merge(tempCoor,tempNumn,val_nPoin,iBegin,iEnd,jBegin,jEnd)
                
                enddo
            enddo
        
            val_iNumn(:,i) = tempNumn(:)
            val_iCoor(:,i) = tempCoor(:)
        
            do j=1,val_nPoin
                poinID = val_iNumn(j,i)
                val_iNeID(poinID,i) = j
            enddo
        enddo
        !
    contains
    
        !---------------------------------------------
        !归并排序 Merge sort
        !---------------------------------------------
        subroutine sort_Merge(val_iCoor,val_iNumn,val_nPoin, &
                    val_iB,val_iE,val_jB,val_jE)
            use mod_PreDefine_Precision
            implicit none
            real(kind=REALLEN),intent(inout):: val_iCoor(val_nPoin)
            integer           ,intent(inout):: val_iNumn(val_nPoin)
            integer,intent(in):: val_nPoin
            integer,intent(in):: val_iB
            integer,intent(in):: val_iE
            integer,intent(in):: val_jB
            integer,intent(in):: val_jE
            !
            integer:: i,j
            integer:: iID,jID,iPoin
            integer:: tempNumn(val_iB:val_jE)
            real(kind=REALLEN):: tempCoor(val_iB:val_jE)
            !
            iID   = val_iB
            jID   = val_jB
            iPoin = val_iB - 1
            do i=val_iB,val_jE
                if(iID > val_iE) then
                    do j=jID,val_jE
                        iPoin = iPoin + 1
                        tempCoor(iPoin) = val_iCoor(j)
                        tempNumn(iPoin) = val_iNumn(j)
                    enddo
                    exit
                endif
            
                if(jID > val_jE) then
                    do j=iID,val_iE
                        iPoin = iPoin + 1
                        tempCoor(iPoin) = val_iCoor(j)
                        tempNumn(iPoin) = val_iNumn(j)
                    enddo
                    exit
                endif
            
                if(val_iCoor(iID) <= val_iCoor(jID)) then
                    iPoin = iPoin + 1
                    tempCoor(iPoin) = val_iCoor(iID)
                    tempNumn(iPoin) = val_iNumn(iID)
                    iID = iID + 1
                else
                    iPoin = iPoin + 1
                    tempCoor(iPoin) = val_iCoor(jID)
                    tempNumn(iPoin) = val_iNumn(jID)
                    jID = jID + 1
                endif
            enddo
        
            do i=val_iB,val_jE
                val_iCoor(i) = tempCoor(i)
                val_iNumn(i) = tempNumn(i)
            enddo
        
        endsubroutine
        !
    endsubroutine
    !
    !---------------------------------------------
    !计算当前块信息
    !---------------------------------------------
    subroutine getIBlockOfRagin(val_iDivRegin,val_blockID)
        use mod_Interface_LOG2
        use mod_TypeDef_Division
        implicit none
        type(typ_DivRegin),intent(inout):: val_iDivRegin
        integer,intent(in):: val_blockID
        !
        integer:: i,j,poinID,poinJD,poinIP
        integer:: nPoin,fartherID,iDivDim
        real(kind=REALLEN):: iDivValue
        real(kind=REALLEN):: minBnd,maxBnd
        real(kind=REALLEN):: deltaCoor(nDim)
        integer:: tempFlag(val_iDivRegin%nPoin)
    
    
        !---------------------------------------------
        !获取当前区块点数
        nPoin = val_iDivRegin%iBlock(val_blockID)%nPoin
    
    
        !---------------------------------------------
        !计算实体边界
        do i=1,nDim
            minBnd =  1.0E10
            maxBnd = -1.0E10
        
            do j=1,nPoin
                poinID = val_iDivRegin%iBlock(val_blockID)%iPoin(j)
                minBnd = min(minBnd,val_iDivRegin%iCoor_Orgn(poinID,i))
                maxBnd = max(maxBnd,val_iDivRegin%iCoor_Orgn(poinID,i))
            enddo
        
            val_iDivRegin%iBlock(val_blockID)%binBnd(i) = minBnd
            val_iDivRegin%iBlock(val_blockID)%baxBnd(i) = maxBnd
        
            deltaCoor(i) = maxBnd - minBnd
        enddo
    
    
        !---------------------------------------------
        !计算划分边界
        fartherID = val_iDivRegin%iBlock(val_blockID)%fartherID
        if(fartherID == 0) then
            val_iDivRegin%iBlock(val_blockID)%minBnd(:) = val_iDivRegin%iBlock(val_blockID)%binBnd(:)
            val_iDivRegin%iBlock(val_blockID)%maxBnd(:) = val_iDivRegin%iBlock(val_blockID)%baxBnd(:)
        else
            val_iDivRegin%iBlock(val_blockID)%minBnd(:) = val_iDivRegin%iBlock(fartherID)%minBnd(:)
            val_iDivRegin%iBlock(val_blockID)%maxBnd(:) = val_iDivRegin%iBlock(fartherID)%maxBnd(:)
        
            iDivDim   = val_iDivRegin%iBlock(fartherID)%iDivDim
            iDivValue = val_iDivRegin%iBlock(fartherID)%iDivValue
        
            if(val_blockID == val_iDivRegin%iBlock(fartherID)%sonID(1)) then
                val_iDivRegin%iBlock(val_blockID)%maxBnd(iDivDim) = iDivValue
            elseif(val_blockID == val_iDivRegin%iBlock(fartherID)%sonID(2)) then
                val_iDivRegin%iBlock(val_blockID)%minBnd(iDivDim) = iDivValue
            endif
        endif
        
    
        !---------------------------------------------
        !若无需分区，则返回
        if(nPoin <= val_iDivRegin%nPoinPerBlock) then
            val_iDivRegin%iBlock(val_blockID)%isNeedDiv = .false.
            return
        endif
    
        val_iDivRegin%iBlock(val_blockID)%isNeedDiv = .true.
    
    
        !---------------------------------------------
        !获得划分维度
        iDivDim   = 1
        iDivValue = deltaCoor(1)
        do j=2,nDim
            if(deltaCoor(j) > iDivValue) then
                iDivValue = deltaCoor(j)
                iDivDim   = j
            endif
        enddo
    
        val_iDivRegin%iBlock(val_blockID)%iDivDim   = iDivDim
    
    
        !---------------------------------------------
        !按划分维度重排节点
        call reBlockPoinInDivDim(val_iDivRegin%iBlock(val_blockID)%iPoin, &
                                 val_iDivRegin%iBlock(val_blockID)%nPoin, &
                                 iDivDim, val_iDivRegin)
    
        !---------------------------------------------
        !获得划分位置、划分取值
        call getDivIPlace(nPoin,val_iDivRegin%nPoinPerBlock,poinIP)
    
        poinID = val_iDivRegin%iBlock(val_blockID)%iPoin(poinIP)
        poinJD = val_iDivRegin%iBlock(val_blockID)%iPoin(poinIP+1)
    
        val_iDivRegin%iBlock(val_blockID)%iDivValue = 0.5*(val_iDivRegin%iCoor_Orgn(poinID,iDivDim) + &
                                                           val_iDivRegin%iCoor_Orgn(poinJD,iDivDim) )
    
    
    endsubroutine
    !
    !---------------------------------------------
    !获得子块信息
    !---------------------------------------------
    subroutine getSonBlockOfRagin(val_iDivRegin,val_blockID)
        use mod_TypeDef_Division
        implicit none
        type(typ_DivRegin),intent(inout):: val_iDivRegin
        integer,intent(in):: val_blockID
        !
        integer:: i,sonL,sonR,nPoinL,nPoinR
        integer:: nPoin,iDivDim,poinID
    
    
        !---------------------------------------------
        !若不需再分，则返回
        if(.not.(val_iDivRegin%iBlock(val_blockID)%isNeedDiv)) then
            val_iDivRegin%iBlock(val_blockID)%brotherID = 0
            val_iDivRegin%iBlock(val_blockID)%sonID(1)  = 0
            val_iDivRegin%iBlock(val_blockID)%sonID(2)  = 0
        
            return
        endif
    
    
        !---------------------------------------------
        !块与子块的连接关系
        sonL = 2*val_blockID
        sonR = sonL + 1
    
        val_iDivRegin%iBlock(val_blockID)%sonID(1) = sonL
        val_iDivRegin%iBlock(val_blockID)%sonID(2) = sonR
    
        val_iDivRegin%iBlock(sonL)%fartherID = val_blockID
        val_iDivRegin%iBlock(sonR)%fartherID = val_blockID
    
        val_iDivRegin%iBlock(sonL)%brotherID = sonR
        val_iDivRegin%iBlock(sonR)%brotherID = sonL
    
    
        !---------------------------------------------
        !申请子块内数据空间
        nPoin   = val_iDivRegin%iBlock(val_blockID)%nPoin
        iDivDim = val_iDivRegin%iBlock(val_blockID)%iDivDim
        !
        call getDivIPlace(nPoin,val_iDivRegin%nPoinPerBlock,poinID)
        nPoinL = poinID
        nPoinR = nPoin  - nPoinL
    
        val_iDivRegin%iBlock(sonL)%nPoin = nPoinL
        val_iDivRegin%iBlock(sonR)%nPoin = nPoinR
        call allocateArray(val_iDivRegin%iBlock(sonL)%iPoin, nPoinL)
        call allocateArray(val_iDivRegin%iBlock(sonR)%iPoin, nPoinR)
    
    
        !---------------------------------------------
        !赋予子块节点编号列表
        do i=1,nPoin
            if(i <= nPoinL) then
                poinID = i
                val_iDivRegin%iBlock(sonL)%iPoin(poinID) = val_iDivRegin%iBlock(val_blockID)%iPoin(i)
            else
                poinID = i - nPoinL
                val_iDivRegin%iBlock(sonR)%iPoin(poinID) = val_iDivRegin%iBlock(val_blockID)%iPoin(i)
        
            endif
        enddo
        !
    endsubroutine
    !
    !---------------------------------------------
    !获取最终区块列表
    !---------------------------------------------
    subroutine getFinalBlocks(val_iDivRegin)
        use mod_TypeDef_Division
        implicit none
        type(typ_DivRegin),intent(inout):: val_iDivRegin
        !
        integer:: i,j
        integer:: nBlock,blockID,nPoin
    
    
        nBlock = 0
        do i=1,val_iDivRegin%nBlock
            if(val_iDivRegin%iBlock(i)%nPoin > val_iDivRegin%nPoinPerBlock) cycle
        
            if(val_iDivRegin%iBlock(i)%nPoin <= 0) cycle
        
            nBlock = nBlock + 1
        enddo
    
        val_iDivRegin%nFinalBlock = nBlock
    
        if(nBlock <= 0) return
    
        call allocateArray(val_iDivRegin%iFinalBlock, val_iDivRegin%nFinalBlock)
    
        nBlock = 0
        do i=1,val_iDivRegin%nBlock
            if(val_iDivRegin%iBlock(i)%nPoin == val_iDivRegin%nPoinPerBlock) then
                nBlock = nBlock + 1
                val_iDivRegin%iFinalBlock(nBlock) = i
            endif
        enddo
        do i=1,val_iDivRegin%nBlock
            if((val_iDivRegin%iBlock(i)%nPoin > 0).and. &
               (val_iDivRegin%iBlock(i)%nPoin < val_iDivRegin%nPoinPerBlock) ) then
                nBlock = nBlock + 1
                val_iDivRegin%iFinalBlock(nBlock) = i
            endif
        enddo
    
    endsubroutine
    !
    !---------------------------------------------
    !将剖分块的节点按划分维度排序
    !---------------------------------------------
    subroutine reBlockPoinInDivDim(val_iPoinList,val_nPoin,val_iDivDim,val_iDivRegin)
        use mod_TypeDef_Division
        implicit none
        integer,intent(inout):: val_iPoinList(val_nPoin)
        integer,intent(in):: val_nPoin
        integer,intent(in):: val_iDivDim
        type(typ_DivRegin),intent(in):: val_iDivRegin
        !
        integer:: i
        integer:: poinID,poinIP
        integer:: tempFlag(val_iDivRegin%nPoin)
    
    
        tempFlag(:) = 0
        do i=1,val_nPoin
            poinID = val_iPoinList(i)
            poinID = val_iDivRegin%iNeID_Poin(poinID,val_iDivDim)
        
            tempFlag(poinID) = 1
        enddo
    
        poinIP = 0
        do i=1,val_iDivRegin%nPoin
            if(tempFlag(i) == 1) then
                poinIP = poinIP + 1
                poinID = val_iDivRegin%iPoin_Sort(i,val_iDivDim)
            
                val_iPoinList(poinIP) = poinID
            endif
        enddo
    
    endsubroutine
    !
    !---------------------------------------------
    !获取剖分位置
    !---------------------------------------------
    subroutine getDivIPlace(val_nPoin,val_nPoinPerBlock,val_iPlace)
        use mod_Interface_LOG2
        implicit none
        integer,intent(in):: val_nPoin
        integer,intent(in):: val_nPoinPerBlock
        integer,intent(out):: val_iPlace
        !
        integer:: i
        integer:: nFinal
    
        val_iPlace = 0
    
        nFinal = ceiling((val_nPoin+0.0)/val_nPoinPerBlock)
    
        val_iPlace = val_nPoinPerBlock*2**(LOG2(nFinal) - 1)
    
    endsubroutine
    !
    !---------------------------------------------
    !清空剖分对象
    !---------------------------------------------
    subroutine nullDivRegin(val_iDivRegin)
        use mod_TypeDef_Division
        implicit none
        type(typ_DivRegin),intent(inout):: val_iDivRegin
        !
        integer:: i
    
    
        !---------------------------------------------
        if(allocated(val_iDivRegin%iCoor_Orgn)) then
            deallocate(val_iDivRegin%iCoor_Orgn)
        endif
    
        if(allocated(val_iDivRegin%iFinalSort)) then
            deallocate(val_iDivRegin%iFinalSort)
        endif
    
        if(allocated(val_iDivRegin%iNeID_Poin)) then
            deallocate(val_iDivRegin%iNeID_Poin)
        endif
    
        if(allocated(val_iDivRegin%iPoin_Sort)) then
            deallocate(val_iDivRegin%iPoin_Sort)
        endif
    
        if(allocated(val_iDivRegin%iCoor_Sort)) then
            deallocate(val_iDivRegin%iCoor_Sort)
        endif
    
    
        !---------------------------------------------
        do i=1,val_iDivRegin%nBlock
            if(allocated(val_iDivRegin%iBlock(i)%iPoin)) then
                deallocate(val_iDivRegin%iBlock(i)%iPoin)
            endif
        
            if(allocated(val_iDivRegin%iBlock(i)%iElem)) then
                deallocate(val_iDivRegin%iBlock(i)%iElem)
            endif
        enddo
        !
        if(allocated(val_iDivRegin%iBlock)) then
            deallocate(val_iDivRegin%iBlock)
        endif
    
    endsubroutine
    !
endmodule
!
    
    