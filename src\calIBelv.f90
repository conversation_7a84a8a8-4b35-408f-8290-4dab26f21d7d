!
module mod_BelvType
    implicit none
    integer,parameter:: BelvType_Separate  = 0
    integer,parameter:: BelvType_Merged    = 1
    integer:: iBelvType = BelvType_Merged
    !
endmodule
!---------------------------------------------
!
!---------------------------------------------
subroutine calIBelv(val_iMesh)
    use mod_TypeDef_Mesh
    use mod_PreDefine_IOPort
    use mod_Interface_AllocateArray
    use mod_BelvType
    implicit none
    type(typ_GlobalMesh),intent(inout):: val_iMesh
    !
    integer:: i,j
    integer:: markID,iID,markKind,iBegin
    integer:: nBelv,nBelx,mBelv,mBelx
    integer:: kBelv_Mark(val_iMesh%nMark+1)
    integer:: kBelx_Mark(val_iMesh%nMark+1)
    !
    integer,allocatable:: iBelvMark(:,:)
    integer,allocatable:: kBelxMark(:)
    integer,allocatable:: iBelxMark(:,:)
    
    
    !---------------------------------------------
    nBelv = 0
    nBelx = 0
    kBelv_Mark(1) = 0
    kBelx_Mark(1) = 0
    do markID = 1,val_iMesh%nMark
        markKind = val_iMesh%iMark(markID)%KIND
        
        if((markKind /= MARK_NONE).and. & !重叠网格
           (markKind /= MARK_ATTA).and. & !滑移网格
           (markKind /= MARK_LPRD).and. & !周期边界
           (markKind /= MARK_CPRD) )then  !周期边界
            call getNBelvForMark(val_iMesh, markID, mBelv, mBelx)
        
            nBelv = nBelv + mBelv
            nBelx = nBelx + mBelx
        endif
        
        
        kBelv_Mark(markID+1) = nBelv
        kBelx_Mark(markID+1) = nBelx
    enddo
    
    
    !---------------------------------------------
    val_iMesh%nBelv = nBelv
    val_iMesh%nBelx = nBelx
    call allocateArray(val_iMesh%iBelvProp, nBelv, 2)
    call allocateArray(val_iMesh%kBelx_Belv, nBelv+1)
    call allocateArray(val_iMesh%iBelx_Belv, nBelx, 2)
    
    
    !---------------------------------------------
    val_iMesh%kBelx_Belv(:) = 0
    
    do markID = 1,val_iMesh%nMark
        markKind = val_iMesh%iMark(markID)%KIND
        
        if((markKind == MARK_NONE).or. & !重叠网格
           (markKind == MARK_ATTA).or. & !滑移网格
           (markKind == MARK_LPRD).and. & !周期边界
           (markKind == MARK_CPRD) )then  !周期边界
            cycle
        endif
    
        call getNBelvForMark(val_iMesh, markID, mBelv, mBelx)
        
        call allocateArray(iBelvMark, mBelv,2)
        call allocateArray(kBelxMark, mBelv+1)
        call allocateArray(iBelxMark, mBelx,2)
        iBelvMark(:,:) = 0
        kBelxMark(:)   = 0
        iBelxMark(:,:) = 0
        
        call getIBelvForMark(val_iMesh, markID, mBelv, mBelx, &
                            iBelvMark, kBelxMark, iBelxMark   )
        
        iBegin = kBelx_Mark(markID)
        
        do iID=1,mBelv
            val_iMesh%iBelvProp(kBelv_Mark(markID)+iID,1) = iBelvMark(iID,1)
            val_iMesh%iBelvProp(kBelv_Mark(markID)+iID,2) = markID !iBelvMark(iID,2)
            
            val_iMesh%kBelx_Belv(kBelv_Mark(markID)+iID+1) = iBegin + kBelxMark(iID+1)
        enddo
        
        do iID = 1,mBelx
            val_iMesh%iBelx_Belv(iBegin+ iID,1) = ibelxMark(iID,1)
            val_iMesh%iBelx_Belv(iBegin+ iID,2) = ibelxMark(iID,2)
        enddo
    enddo
    
    if(iBelvType == BelvType_Separate) then
        call allocateArray(iBelvMark, nBelv, 2)
        iBelvMark(:,:) = val_iMesh%iBelvProp(:,:)
        
        val_iMesh%nBelv = nBelx
        call allocateArray(val_iMesh%iBelvProp, val_iMesh%nBelv, 2)
        val_iMesh%iBelvProp(:,:) = 0
        
        iID = 0
        do i=1,nBelv
            do j=val_iMesh%kBelx_Belv(i)+1,val_iMesh%kBelx_Belv(i+1)
                iID = iID + 1
                val_iMesh%iBelvProp(iID,1) = iBelvMark(i,1)
                val_iMesh%iBelvProp(iID,2) = iBelvMark(i,2)
            enddo
        enddo
    endif
    
    
    deallocate(iBelvMark, kBelxMark, iBelxMark)
    return
    
contains
    !
    subroutine getNBelvForMark(val_iMesh,val_markID,val_nBelv,val_nBelx)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        integer,intent(in):: val_markID
        integer,intent(out):: val_nBelv
        integer,intent(out):: val_nBelx
        !
        integer:: i,j,iCount,jCount
        integer:: beleID,poinID,nodeNum
        integer:: iTempFlag(val_iMesh%nPoin)
        
        
        !---------------------------------------------
        iTempFlag(:) = 0
        
        do i=1,val_iMesh%iMark(val_markID)%nElem
            beleID = val_iMesh%iMark(val_markID)%iElem(i)
            
            nodeNum = val_iMesh%iBeleProp(beleID,1)
            do j=1,nodeNum
                poinID = val_iMesh%iBele(j,beleID)
                
                iTempFlag(poinID) = iTempFlag(poinID) + 1
            enddo
        enddo
        
        !---------------------------------------------
        iCount = 0
        jCount = 0
        do i=1,val_iMesh%nPoin
            if(iTempFlag(i) > 0) then
                iCount = iCount + 1
                jCount = jCount + iTempFlag(i)
            endif
        enddo
        
        val_nBelv = iCount
        val_nBelx = jCount
        
                
    endsubroutine
    !
    subroutine getIBelvForMark(val_iMesh,val_markID,val_nBelv,  &
                        val_nBelx,val_ibelv,val_kBelx,val_iBelx )
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        integer,intent(in):: val_markID
        integer,intent(in):: val_nBelv
        integer,intent(in):: val_nBelx
        integer,intent(out):: val_iBelv(val_nBelv,2)
        integer,intent(inout):: val_kBelx(val_nBelv+1)
        integer,intent(out):: val_iBelx(val_nBelx,2)
        !
        integer:: i,j,iCount
        integer:: beleID,poinID,nodeNum
        integer:: iTempFlag(val_iMesh%nPoin)
        integer:: kBelx_Poin(val_iMesh%nPoin+1)
        
        
        !---------------------------------------------
        iTempFlag(:)  = 0
        kBelx_Poin(:) = 0
        do i=1,val_iMesh%iMark(val_markID)%nElem
            beleID = val_iMesh%iMark(val_markID)%iElem(i)
            
            nodeNum = val_iMesh%iBeleProp(beleID,1)
            do j=1,nodeNum
                poinID = val_iMesh%iBele(j,beleID)
                
                iTempFlag(poinID) = iTempFlag(poinID) + 1
                kBelx_Poin(poinID+1) = kBelx_Poin(poinID+1) + 1
            enddo
        enddo
        
        do i=1,val_iMesh%nPoin
            kBelx_Poin(i+1) = kBelx_Poin(i) + kBelx_Poin(i+1)
        enddo
        
        !---------------------------------------------
        val_kBelx(:) = 0
        
        iCount = 0
        do i=1,val_iMesh%nPoin
            if(iTempFlag(i) > 0) then
                iCount = iCount + 1
                
                val_iBelv(iCount,1) = i
                val_iBelv(iCount,2) = val_markID
                
                val_kBelx(iCount + 1) = val_kBelx(iCount) + iTempFlag(i)
                
                if(val_kbelx(iCount+1) /= kBelx_Poin(i+1)) then
                    write(ioPort_Out,*) 'error' ,'E3'
                    stop
                endif
            endif
        enddo
        
        
        !---------------------------------------------
        do i=1,val_iMesh%iMark(val_markID)%nElem
            beleID = val_iMesh%iMark(val_markID)%iElem(i)
            
            nodeNum = val_iMesh%iBeleProp(beleID,1)
            do j=1,nodeNum
                poinID = val_iMesh%iBele(j,beleID)
                
                kBelx_Poin(poinID) = kBelx_Poin(poinID) + 1
                
                val_iBelx(kBelx_Poin(poinID),1) = beleID
                val_iBelx(kBelx_Poin(poinID),2) = j
            enddo
        enddo
        
    endsubroutine
    !
endsubroutine
!
