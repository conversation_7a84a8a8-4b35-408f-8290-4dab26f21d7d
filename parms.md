# Parameters


```c{.line-numbers}
#NMRK(15)
#MARK(1) #TYPE(WALL)
#MARK(2) #TYPE(EULW)
#MARK(3) #TYPE(EULW)
#MARK(4) #TYPE(MOVW) #ORGN(0.95569 -0.10299 -1.519) #AXIS(1 0 0) #OMGA(200) %F1
#MARK(5) #TYPE(MOVW) #ORGN(0.926982 -0.0597759 -0.705) #AXIS(0 -1 0) #OMGA(200) %F2
#MARK(6) #TYPE(WALL)
#MARK(7) #TYPE(PFAR)
#MARK(8) #TYPE(SYMM)
#MARK(9) #TYPE(EULW)
#MARK(10) #TYPE(EULW)
#MARK(11) #TYPE(MOVW) #ORGN(2.95443 0.492526 -1.51919) #AXIS(1 0 0) #OMGA(200) %T1
#MARK(12) #TYPE(MOVW) #ORGN(2.92394 -0.52866 -0.70478) #AXIS(0 1 0) #OMGA(200) %T2
#MARK(13) #TYPE(WALL)
#MARK(14) #TYPE(WALL)
#MARK(15) #TYPE(WALL)

```
| Key Workd | Option | Description                                                     |
|-----------|--------|-----------------------------------------------------------------|
| #NMRK     | int    | the number of BC marks, the value should be equal to BC counts. |
| #MARK     | int    | the index of BC mark                                            |
| #ORGN     | real*3 | the coordinates of the origin for BC rotating                   |
| #AXIS     | real*3 | the axis direction of BC rotating if 3D                         |
| #OMGA     | real   | the angular velocity of BC rotating (rad/s)                     |
| #VELO     | real*3 | the linear velocity of BC translation (m/s)                     |



```c{.line-numbers}
&config_List
KIND_HARDWARE               =            1

GRID_FILENAME               =      "EVTOL_AV-10"
GRID_SCALE_FACTOR           =  -0.001  0.001  0.001

PHYSICAL_DIMENSION          =            3
PHYSICAL_PROBLEM            =            3
KIND_TURB_MODEL             =            2
IS_INCOMPRESSIBLE           =            0
IS_DIMENSIONLESS            =            0
RESTART_SOL                 =            0
CONTAIN_MOVINGBC            =            1

FLOW_PARMTYPE               =            0
FLOW_LVPLANE                =            1
FLOW_PRESSURE               =     101325.0
FLOW_TEMPERATURE            =       287.15

FLOW_MACH                   =          0.1
FLOW_AOA                    =            0
FLOW_AOA_RANGE              =    -5  15  1
FLOW_AOS                    =            0
FLOW_REYNOLDS_NUMBER        =       1.4E+6

FLOW_ALLOW_CHANGES          =          0.1

FLOW_ALLOW_MIND             =        0.001
FLOW_ALLOW_MAXD             =        10.0
FLOW_ALLOW_MINP             =         10.0
FLOW_ALLOW_MAXP             =    1000000.0
FLOW_ALLOW_MINT             =         10.0
FLOW_ALLOW_MAXT             =       5000.0
FLOW_ALLOW_MINX             =          0.0
FLOW_ALLOW_MAXX             =      10000.0

AEROFM_REFM                 =        -1.0
AEROFM_REFS                 =         1.0
AEROFM_REFL                 =         1.0
AEROFM_ORGN                 =    0.0000 0.0000 0.0000
AEROFM_NAVGITER             =           1

CONV_NUM_METHOD_FLOW        =           1
UPWIND_ORDER                =           2
NITER_O1TUNING              =        1000
UPWIND_SCHEME               =          31
UPW_EPCILON_K               =      1.0000

TIME_DISCRE_FLOW            =          11
CFL_NUMBER                  =         1.0
CFL_RAMP                    =   1.0 100.0 100.00
CFL_RAMP_KIND               =           4
NITER_OF_MCGS               =          10
IMPLICIT_TREATMENT          =           0
IMPLICIT_WEIGHT             =         1.5

NITER_STEADY                =        1000

UNSTEADY_SIMULATION         =           2
INNER_ITER                  =         500
OUTER_ITER                  =           0

RESAVE_ITER                 =         1
RESERR_ITER                 =          10
RESERR_SHOW                 =          10

RESERR_RESUCTION            =        -3.0
RESERR_MINVAL               =        -8.0
/
```


| Parameter            | Options                                                  | Description                                       |
|----------------------|----------------------------------------------------------|---------------------------------------------------|
| KIND_HARDWARE        | int. 0:CPU 1:GPU                                         | the hardware type used                            |
| GRID_FILENAME        | string. default:""                                       | the grid file name                                |
| GRID_SCALE_FACTOR    | real*3. default: [1.0 1.0 1.0]                           | the grid scale factor                             |
| PHYSICAL_DIMENSION   | int. 2, 3                                                | the dimension of the problem                      |
| PHYSICAL_PROBLEM     | int. 0(None), 1(Euler), 2(Laminar), 3(RANS)              | the type of physical problem                      |                 
| KIND_TURB_MODEL      | int. 0(None), 1(BL), 2(SA), 3(SST)                       | tye type pf turbulent model                       |
| IS_INCOMPRESSIBLE    | int. 0(No), 1(Yes)                                       | flag whether incompressible                       |
| IS_DIMENSIONLESS     | int. 0(No), 1(Yes)                                       | flag whether dimensionless                        |      
| RESTART_SOL          | int. 0(No), 1(Yes)                                       | flag whether restart(continuous)                  |       
| CONTAIN_MOVINGBC     | int. 0(No), 1(Yes)                                       | flag whether contains moving BC                   |
| ---                  | ---                                                      | ---                                               |
| FLOW_PARMTYPE        | int. 0(using mach and AOA), 1(using velocity and dir)    | the type of flow variable given                   |            
| FLOW_LVPLANE         | int. 0(XZ), 1(XY)                                        | the type of longitudinal vertical plane           |            
| FLOW_PRESSURE        | real. default(101325)                                    | the pressure of the flow                          |            
| FLOW_TEMPERATURE     | real. default(287.15)                                    | the temperature of the flow                       |
| FLOW_MACH            | real. default(0.0)                                       | the mach number of the flow                       |           
| FLOW_AOA             | real. default(0.0)                                       | the angle of attack of the flow                   |
| FLOW_AOA_RANGE       | real*8. default: [0.0, 0.0, 0.0]                         | the angles of attack of the flow(begin,end,delta) | 
| FLOW_AOS             | real. default(0.0)                                       | the angle of side of the flow                     |          
| FLOW_REYNOLDS_NUMBER | the reynolds number of the fluid (unit reference length) |
| ---                  | ---                                                      | ---                                               |
| FLOW_ALLOW_CHANGES   | real. default: 0.1                                       | the allowed change factor for solution update     |
| FLOW_ALLOW_MIND      | real. default: 0.001                                     | the minimum density allowed                       |           
| FLOW_ALLOW_MAXD      | real. default: 1000.0                                    | the maximum density allowed                       |                  
| FLOW_ALLOW_MINP      | real. default: 1.0                                       | the minimum pressure allowed                      |                
| FLOW_ALLOW_MAXP      | real. default: 10000000.0                                | the maximum pressure allowed                      |               
| FLOW_ALLOW_MINT      | real. default: 1.0                                       | the minimum temperature allowed                   |             
| FLOW_ALLOW_MAXT      | real. default: 5000.0                                    | the maximum temperature allowed                   |              
| FLOW_ALLOW_MINX      | real. default: 0.0                                       | the minimum eddy viscosity allowed                |               
| FLOW_ALLOW_MAXX      | real. default: 10000.0                                   | the maximum eddy viscosity allowed                |
| ---                  | ---                                                      | ---                                               |
| AEROFM_REFM          | real. default: -1.0                                      | the reference mach number for zero velocity flows |           
| AEROFM_REFS          | real. default: 1.0                                       | the reference length for moment coefficient       |           
| AEROFM_REFL          | real. default: 1.0                                       | the reference length for force coefficient        |                  
| AEROFM_ORGN          | real*3. default: [0.0, 0.0, 0.0]                         | the reference center for moment calculation       |        
| AEROFM_NAVGITER      | int. default: 1                                          | the number of iterations for aeroFM averaging     |
| AEROFM_REFS          | real. default: 1.0                                       | the reference length for moment coefficient       |           
| CONV_NUM_METHOD_FLOW | int. 0:JST, 1:UPW                                        | the type of numerical method of convective term   |                    
| UPWIND_ORDER         | int. default: 0                                          | the order of upwind scheme (if UPW used)          |         
| NITER_O1TUNING       | int. default: 1000                                       | the number of1-st order iteration                 |            
| UPWIND_SCHEME        | int. 0:None, 1:Roe, 31:SLAU, 32:SLAU2                    | the type of upwind scheme                         |          
| UPW_EPCILON_K        | real. default:1.0                                        | the factor used in slope limiter                  |             
| ---                  | ---                                                      | ---                                               |
| TIME_DISCRE_FLOW     | int. 0:Runge-Kutta, 11:MC-LU-SGS                         | the type of time marching method                  |             
| CFL_NUMBER           | real. default: 1.0                                       | the CFL number                                    |             
| CFL_RAMP             | real*3. default [1.0 1.0 100.0]                          | the CFL tuning parameter                          |     
| CFL_RAMP_KIND        | int. default: 0                                          | CFL ramp type                                     |             
| NITER_OF_MCGS        | int. default:10                                          | the number of GS iterations                       |  
| IMPLICIT_TREATMENT   | int. 0:SR, 1:Full-Jacob                                  | the type of implicit jacob                        |             
| IMPLICIT_WEIGHT      | real. default(1.5)                                       | the implicit factor                               |
| ---                  | ---                                                      | ---                                               |

| NITER_STEADY        |int. default: 0 | the number of iterations for steady simulation |
| UNSTEADY_SIMULATION |int. 0(Steady), 1(global minimum time step), 2(Dual time stepping)| the type of unsteady simulation                                    |        
| UNST_INNER_ITER     |int. default: 0 | the number of iterations per unsteady step                                          |           
| UNST_OUTER_ITER     |int. default: 0 | the number of physical time steps                                  |     
| RESAVE_ITER         |int. default: 1| the step interval for result saving                                |              
| RESERR_ITER         |int. default: 1| the step interval for reserr calculation                           |      
| RESERR_SHOW         |int. default: 1| the step interval for reserr showing                               |
| RESERR_RESUCTION    |int. default: -3.0| the level of reserr declining                                      |            
| RESERR_MINVAL       |int. default: -8.0| the minimum level of reserr declining, not used                    |            

