!
!---------------------------------------------
!½öÖØÅÅPoinºÍSate
!---------------------------------------------
subroutine calMeshMapp_Reordering(val_iPart)
    use mod_Project
    use mod_Config
    use mod_MeshReordering
    use mod_calIReMapp_SIM
    use mod_calIReMapp_LBL
    use mod_calIReMapp_SFC
    use mod_calIReMapp_KDT
    use mod_calIReMapp_REC
    use mod_PreDefine_IOPort
    implicit none
    integer,intent(in):: val_iPart
    !
    logical:: isOk

    
    !---------------------------------------------
    call bldIReMesh_Poin(iGlobalMesh,iMeshMapp(val_iPart))
      
    iReKind = ReKind_Poin
    !call calIReMapp_Poin(val_iPart,isOk)
    if(KIND_POINTREORDERING     == _NONE          ) then !no-reordering
        !Do nothing
    elseif(KIND_POINTREORDERING == _REORDERING_SIM) then !simple reordering
        call calIReMapp_Poin_SIM(val_iPart,isOk)
    elseif(KIND_POINTREORDERING == _REORDERING_LBL) then !layer-by-layer reordering
    !    call bldIReMesh_Sate(iGlobalMesh,iMeshMapp(val_iPart))
        write(*,*) 'E111'
        call calIReMapp_Poin_LBL(val_iPart,isOk)
        write(*,*) 'E112'
    elseif(KIND_POINTREORDERING == _REORDERING_SFC) then !space curve fit reordering
        call calIReMapp_Poin_SFC(val_iPart,isOk)
    elseif(KIND_POINTREORDERING == _REORDERING_KDT) then !k-d tree reordering
        call calIReMapp_Poin_KDT(val_iPart,isOk)
    elseif(KIND_POINTREORDERING == _REORDERING_REC) then !reConstruction reordering
        call calIReMapp_Poin_REC(val_iPart,isOk)
    endif
        
    if(.not.isOk) then
        write(ioPort_Out,*) 'Error in caliReMapp_Poin.'
        stop
    endif
    
    call updIMeshMapp_Poin(val_iPart)
    
    call cleanIReMeshAndMapp
    
       
    !---------------------------------------------
    call bldIReMesh_Sate(iGlobalMesh,iMeshMapp(val_iPart))
    
    iReKind = ReKind_Sate
    !call calIReMapp_Sate(val_iPart,isOk)
    if(KIND_POINTREORDERING     == _NONE          ) then !no-reordering
        !Do nothing
    elseif(KIND_POINTREORDERING == _REORDERING_SIM) then !simple reordering
        !Do nothing
    elseif(KIND_POINTREORDERING == _REORDERING_LBL) then !layer-by-layer reordering
        call calIReMapp_Sate_LBL(val_iPart,isOk)
    elseif(KIND_POINTREORDERING == _REORDERING_SFC) then !space curve fit reordering
        call calIReMapp_Sate_SFC(val_iPart,isOk)
    elseif(KIND_POINTREORDERING == _REORDERING_KDT) then !k-d tree reordering
        call calIReMapp_Sate_KDT(val_iPart,isOk)
    elseif(KIND_POINTREORDERING == _REORDERING_REC) then !reConstruction reordering
        call calIReMapp_Sate_REC(val_iPart,isOk)
    endif
    
    if(.not.isOk) then
        write(ioPort_Out,*) 'Error in caliReMapp_Sate.'
        stop
    endif
    
    call updIMeshMapp_Sate(val_iPart)
    
    call cleanIReMeshAndMapp
    
    return
    
endsubroutine
!
