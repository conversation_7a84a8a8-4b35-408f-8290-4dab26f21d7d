!
subroutine readMesh_starCD(val_path,val_name,val_iMesh)
    use mod_Mesh_TypeDef
    implicit none
    character(len=*),intent(in):: val_path
    character(len=*),intent(in):: val_name
    type(typ_Mesh),intent(inout):: val_iMesh
    
    integer:: i
    character(len=100):: goal
    
    goal = val_name
    do i=1,len_trim(goal)
        if(goal(i:i) == '.') then
            goal(i:) = ' '
            exit
        endif
    enddo
    
    call readMesh_starCD_Inp(val_path,goal,val_iMesh)
    
    call readMesh_starCD_Vrt(val_path,goal,val_iMesh)
    
    call readMesh_starCD_Cel(val_path,goal,val_iMesh)
    
    call readMesh_starCD_Bnd(val_path,goal,val_iMesh)
    
endsubroutine
!
subroutine readMesh_starCD_Inp(val_path,val_goal,val_iMesh)
    use mod_Mesh_TypeDef
    implicit none
    character(len=*),intent(in):: val_path
    character(len=*),intent(in):: val_goal
    type(typ_Mesh),intent(inout):: val_iMesh
    
    integer:: i,j
    integer:: istat,jstat
    integer:: CTABLineNum,RDEFLineNum
    character(len=255),allocatable:: CTABLineList(:),RDEFLineList(:)
    character(len=255):: aLine,bLine
    integer:: icount,jcount
    
    !write(*,*)trim(val_path)//'/'//trim(val_goal)//'.inp'
    
    open(22,file=trim(val_path)//'/'//trim(val_goal)//'.inp',status='old')
        CTABLineNum = 0
        RDEFLineNum = 0
        
        do
            read(22,'(A)',iostat=istat) aLine
            if(istat /= 0) exit
            if(len_trim(aLine) == 0) cycle
            
            aLine = adjustl(aLine)
            
            if(aLine(1:4) == 'CTAB') then
                CTABLineNum = CTABLineNum+1
            elseif(aLine(1:4) == 'RDEF') then
                RDEFLineNum = RDEFLineNum+1
            elseif(aLine(1:4) == 'ctab') then
                CTABLineNum = CTABLineNum+1
            elseif(aLine(1:4) == 'rdef') then
                RDEFLineNum = RDEFLineNum+1
            endif
            
        enddo
        
        allocate(CTABLineList(CTABLineNum))
        allocate(RDEFLineList(RDEFLineNum))
        
        rewind(22)
        
        icount = 0
        jcount = 0
        
        do
            read(22,'(A)',iostat=istat) aLine
            if(istat /= 0) exit
            if(len_trim(aLine) == 0) cycle
            
            aLine = adjustl(aLine)
            
            if(aLine(1:4) == 'CTAB') then
                icount = icount+1
                CTABLineList(icount) = aLine
            elseif(aLine(1:4) == 'RDEF') then
                jcount = jcount+1
                RDEFLineList(jcount) = aLine
            elseif(aLine(1:4) == 'ctab') then
                icount = icount+1
                CTABLineNum = CTABLineNum+1
            elseif(aLine(1:4) == 'rdef') then
                jcount = jcount+1
                RDEFLineNum = RDEFLineNum+1
            endif
            
        enddo
        
    close(22)
    
    !
    !==========================================
    !
    do i=2,CTABLineNum
        do j=1,i-1
            if(trim(CTABLineList(i)) == trim(CTABLineList(j))) then
                CTABLineList(i) = ' '
                exit
            endif
        enddo
    enddo
    
    icount = 0
    do i=1,CTABLineNum
        if(len_trim(CTABLineList(i)) > 0) then
            icount = icount+1
        endif
    enddo
    val_iMesh%nDoma = icount
    
    allocate(val_iMesh%iDoma(val_iMesh%nDoma))
    
    icount = 0
    do i=1,CTABLineNum
        if(len_trim(CTABLineList(i)) > 0) then
            icount = icount+1
            val_iMesh%iDoma(icount)%DomaLine = CTABLineList(i)
        endif
    enddo
    
    do i=1,val_iMesh%nDoma
        aLine = val_iMesh%iDoma(i)%DomaLine
        !write(*,*) "aLine(6:)=", val_iMesh%iDoma(i)%DomaLine
        read(aLine(6:),*) val_iMesh%iDoma(i)%DomaID
        
        val_iMesh%iDoma(i)%DomaKind = "FLUI" !aLine(14:17)
        
        call getSubString(aLine,' ',-1,val_iMesh%iDoma(i)%DomaName)
        
    enddo
    
    !
    !==========================================
    !
    do i=2,RDEFLineNum
        do j=1,i-1
            if(trim(RDEFLineList(i)) == trim(RDEFLineList(j))) then
                RDEFLineList(i) = ' '
                exit
            endif
        enddo
    enddo
    
    icount = 0
    do i=1,RDEFLineNum
        if(len_trim(RDEFLineList(i)) > 0) then
            icount = icount+1
        endif
    enddo
    val_iMesh%nMark = icount
    
    allocate(val_iMesh%iMark(val_iMesh%nMark))
    
    icount = 0
    do i=1,RDEFLineNum
        if(len_trim(RDEFLineList(i)) > 0) then
            icount = icount+1
            val_iMesh%iMark(icount)%MarkLine = RDEFLineList(i)
        endif
    enddo
    
    do i=1,val_iMesh%nMark
        aLine = val_iMesh%iMark(i)%MarkLine
        
        call getSubString(aLine,',',2,bLine)

        !write(*,*) 'aLine=',trim(aLine)
        !write(*,*) 'bline=',trim(bline)
        read(bLine,*) val_iMesh%iMark(i)%MarkID
        
        call getSubString(aLine,',',3,val_iMesh%iMark(i)%MarkKind)
        
        call getSubString(aLine,',',4,val_iMesh%iMark(i)%MarkName)
        
    enddo
    
    !
    !==========================================
    !
    !do i=1,val_iMesh%nDoma
    !    write(*,*) trim(val_iMesh%iDoma(i)%DomaLine)
    !    write(*,*) val_iMesh%iDoma(i)%DomaID
    !    write(*,*) trim(val_iMesh%iDoma(i)%DomaName)
    !    write(*,*) trim(val_iMesh%iDoma(i)%DomaKind)
    !    write(*,*)
    !enddo
    !
    !do i=1,val_iMesh%nMark
    !    write(*,*) trim(val_iMesh%iMark(i)%MarkLine)
    !    write(*,*) val_iMesh%iMark(i)%MarkID
    !    write(*,*) trim(val_iMesh%iMark(i)%MarkName)
    !    write(*,*) trim(val_iMesh%iMark(i)%MarkKind)
    !    write(*,*)
    !enddo
    !
endsubroutine
!
subroutine readMesh_starCD_Vrt(val_path,val_goal,val_iMesh)
    use mod_Mesh_TypeDef
    implicit none
    character(len=*),intent(in):: val_path
    character(len=*),intent(in):: val_goal
    type(typ_Mesh),intent(inout):: val_iMesh
    
    
    integer:: i,j,temp
    integer:: istat,iCount,jCount
    character(len=255):: aLine,bLine,cLine
    
    open(22,file=trim(val_path)//'/'//trim(val_goal)//'.vrt',status='old')
        icount = 0
        do
            read(22,'(A)',iostat = istat) aLine
            if(istat /= 0) exit
            if(len_trim(aLine) == 0)cycle
            
            icount = icount+1
        enddo
        
        val_iMesh%nPoin = icount
        allocate(val_iMesh%iCoor(val_iMesh%nDime,val_iMesh%nPoin))
        val_iMesh%iCoor(:,:) = 0.0
        
        rewind(22)
        
        icount = 0
        do
            read(22,'(A)',iostat = istat) aLine
            if(istat /= 0) exit
            if(len_trim(aLine) == 0)cycle
            
            !
            !============================
            do i=1,len_trim(aLine)
                if(aLine(i:i) == 'e') then
                    aLine(i:i) = 'E'
                endif
            enddo

            aline = trim(aline)//"   "

            do i=len_trim(aLine),1,-1
                if(aLine(i:i) == 'E') then
                    if((aLine(i+4:i+4) == '+').or. &
                       (aLine(i+4:i+4) == '-') )then
                        bLine = trim(aLine(i+4:))
                        aLine(i+4:i+4) = ' '
                        aLine(i+5:) = trim(bLine)
                    elseif(aLine(i+4:i+4) == ' ') then
                        !do nothing
                    else
                        aLine(i+4:) = ' '
                        !pause 'error data. E+000'
                    endif
                endif
            enddo

            !write(*,*) trim(aline)
            
            !jCount = 0
            !do i=1,min(len_trim(bLine)+3,255)
            !    if((bLine(i:i) == 'e').or.(bLine(i:i) == 'E')) then
            !        jCount = jCount + 1
            !        
            !        if((bLine(i+4:i+4) == '+').or. &
            !           (bLine(i+4:i+4) == '-') ) then
            !            bLine(i+4:i+4) = ' '
            !            bLine(i+5:) = aLine(i+4:)
            !        
            !            aLine = bLine
            !        elseif(bLine(i+4:i+4) /= ' ') then
            !               
            !            if((jCount == 2).and.(val_iMesh%nDime == 2)) then
            !                bLine(i+4:) = ' '
            !                aLine = bLine
            !            endif
            !        endif    
            !    endif
            !    
            !    !
            !    !if(((bLine(i:i) == 'e').or.(bLine(i:i) == 'E')).and. &
            !    !   ((bLine(i+4:i+4) == '+').or.(bLine(i+4:i+4) == '-'))) then
            !    !    bLine(i+4:i+4) = ' '
            !    !    bLine(i+5:) = aLine(i+4:)
            !    !    
            !    !    aLine = bLine
            !    !endif
            !enddo
            
            
            !============================
            !
            
            icount = icount+1
            read(aLine,*) temp,(val_iMesh%iCoor(j,icount),j=1,val_iMesh%nDime)
            
        enddo
    close(22)
    
    
endsubroutine
!
subroutine readMesh_starCD_Cel(val_path,val_goal,val_iMesh)
    use mod_Mesh_TypeDef
    implicit none
    character(len=*),intent(in):: val_path
    character(len=*),intent(in):: val_goal
    type(typ_Mesh),intent(inout):: val_iMesh
    
    integer:: i,j,temp
    integer:: istat,icount
    character(len=255):: aLine
    
    open(22,file=trim(val_path)//'/'//trim(val_goal)//'.cel',status='old')
        icount = 0
        do
            read(22,'(A)',iostat = istat) aLine
            if(istat /= 0) exit
            if(len_trim(aLine) == 0)cycle
            
            icount = icount+1
        enddo
        
        val_iMesh%nElem = icount
        
        if(val_iMesh%nDime == 2) then
            allocate(val_iMesh%iElem(4,val_iMesh%nElem))
        elseif(val_iMesh%nDime == 3) then
            allocate(val_iMesh%iElem(8,val_iMesh%nElem))
        endif
        
        allocate(val_iMesh%elemDoma(val_iMesh%nElem))
        
        val_iMesh%iElem(:,:) = 0
        val_iMesh%elemDoma(:) = 0
        
        rewind(22)
        
        icount = 0
        do
            read(22,'(A)',iostat = istat) aLine
            if(istat /= 0) exit
            if(len_trim(aLine) == 0)cycle
            
            icount = icount+1
            read(aLine,*) temp,val_iMesh%iElem(:,icount),val_iMesh%elemDoma(icount)
        enddo
        
    close(22)
    !write(*,*) val_iMesh%nElem

    !write(*,*) val_iMesh%iElem(:,350)
    !write(*,*) val_iMesh%iElem(:,850)
    !pause
    
endsubroutine
!
subroutine readMesh_starCD_Bnd(val_path,val_goal,val_iMesh)
    use mod_Mesh_TypeDef
    implicit none
    character(len=*),intent(in):: val_path
    character(len=*),intent(in):: val_goal
    type(typ_Mesh),intent(inout):: val_iMesh
    
    integer:: i,j,temp,ni
    integer:: jstat
    integer:: icci(10)
    integer,allocatable:: icount(:)
    character(len=255):: aLine
    
    do i=1,val_iMesh%nMark
        val_iMesh%iMark(i)%nElem = 0
    enddo
    
    allocate(icount(val_iMesh%nMark))
    icount(:) = 0
    
    open(22,file=trim(val_path)//'/'//trim(val_goal)//'.bnd',status='old')
        icount(:) = 0
        do
            read(22,'(A)',iostat = jstat) aLine
            if(jstat /= 0) exit
            if(len_trim(aLine) == 0)cycle
            
            read(aLine,*) temp,temp,temp,temp,temp,ni
            
            icount(ni) = icount(ni)+1
        enddo
        
        do i=1,val_iMesh%nMark
            
            val_iMesh%iMark(i)%nElem = icount(i)
            
            if(val_iMesh%nDime == 2) then
                allocate(val_iMesh%iMark(i)%iElem(2,icount(i)))
            elseif(val_iMesh%nDime == 3) then
                allocate(val_iMesh%iMark(i)%iElem(4,icount(i)))
            endif
            val_iMesh%iMark(i)%iElem(:,:) = 0
            
        enddo
        
        rewind(22)
        
        icount(:) = 0
        do
            read(22,'(A)',iostat = jstat) aLine
            if(jstat /= 0) exit
            if(len_trim(aLine) == 0)cycle
            
            read(aLine,*) temp,(icci(i),i=1,4),ni
            
            icount(ni) = icount(ni)+1
            
            if(val_iMesh%nDime == 2) then
                val_iMesh%iMark(ni)%iElem(:,icount(ni)) = icci(1:2)
            
            elseif(val_iMesh%nDime == 3) then
                val_iMesh%iMark(ni)%iElem(:,icount(ni)) = icci(1:4)
                
            endif
            
        enddo
    close(22)
    
    
endsubroutine
!
