
!---------------------------------------------
!
!---------------------------------------------
module mod_calAeroFM
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    implicit none
    !
    private:: calCurrAeroFM,calAeroFMAvg,zeroAeroFM,aeroFM_Add
    !
contains
    !
    subroutine calIPresCoef(val_iPrim,val_iCoef,val_nPrim,val_nPoin)
        use mod_TypeDef_Solu
        use mod_Config
        use mod_preDefine_Flag
        use mod_FreeStream
        implicit none
        real(kind=REALLEN),intent(in):: val_iPrim(val_nPoin,val_nPrim)
        real(kind=REALLEN),intent(out):: val_iCoef(val_nPoin)
        integer,intent(in):: val_nPrim
        integer,intent(in):: val_nPoin
        !
        integer:: i
        real(kind=REALLEN):: coefEi

        do i=1,val_nPoin
            coefEi = (val_iPrim(i,1) - flowInfty_Pressure)/flowInfty_DynP

            val_iCoef(i) = coefEi
        enddo

    endsubroutine
    !
    subroutine calAeroFM(val_iMesh, val_iSolu, val_iParm, val_iIter, val_isVisc)
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_TypeDef_Parm
        use mod_calViscTau
        use mod_Config
        use cudafor
        implicit none
        type(typ_Mesh),intent(in   ):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        type(typ_Parm),intent(inout):: val_iParm
        integer,intent(in):: val_iIter
        logical,intent(in):: val_isVisc
        !
        integer:: istat


        istat = cudaDeviceSynchronize()
        val_iSolu%soluvar(:,:)    = val_iSolu%soluvar_d(:,:)
        val_iSolu%primvar(:,:)    = val_iSolu%primvar_d(:,:)
    
        if(val_isVisc) then
            if(KIND_HARDWARE == _CPU) then
                call calTau_CPU(val_iMesh, val_iSolu)
            elseif(KIND_HARDWARE == _GPU) then
                call calTau_GPU(val_iMesh, val_iSolu)
                istat = cudaDeviceSynchronize()
                val_iSolu%Tau(:,:,:) = val_iSolu%Tau_d(:,:,:)
            end if
        endif

        call calCurrAeroFM(val_iMesh, val_iSolu, val_iParm, val_iParm%currAeroFM, val_iParm%isMarkWall, val_isVisc)

        if(val_iIter > 0) then
            val_iParm%aeroFMHist(val_iIter) = val_iParm%currAeroFM
        endif

        call calAeroFMAvg(val_iParm, val_iParm%nIterFMAvg, val_iParm%iIter)

    endsubroutine
    !
    subroutine calCurrAeroFM(val_iMesh,val_iSolu,val_iParm,val_aeroFM,val_isMarkWall,val_isViscFlow)
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_TypeDef_Parm
        use mod_FreeStream
        use mod_parmDimLess
        implicit none
        type(typ_Mesh),intent(in):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        type(typ_Parm),intent(in):: val_iParm
        type(typ_AeroFM),intent(inout):: val_aeroFM
        logical,intent(in):: val_isMarkWall(:)
        logical,intent(in):: val_isViscFlow
        !
        integer:: i,j,k
        integer:: poinID,markID
        real(kind=REALLEN):: areaFi,nvorFi(nDim),PFi,iVector(nDim)
        real(kind=REALLEN):: forcePi(nDim),momntPi(nDim)
        real(kind=REALLEN):: forcePv(nDim),momntPv(nDim)
        real(kind=REALLEN):: tauTotal(nDim),tauNormal,tauTangent(nDim),WallShearStress
        !
        real(kind=REALLEN):: iForce(nDim),iMomnt(nDim),iCoefF(nDim),iCoefM(nDim)
        real(kind=REALLEN):: ForceRef,MomentRef


        !---------------------------------------------
        val_aeroFM%surfaceForce_pres(:,:) = 0.0
        val_aeroFM%surfaceMomnt_pres(:,:) = 0.0
        val_aeroFM%surfaceForce_visc(:,:) = 0.0
        val_aeroFM%surfaceMomnt_visc(:,:) = 0.0

        if(val_isViscFlow) then
            val_iSolu%iSurfCoef(:) = 0.0
        endif

        do i=1,val_iMesh%nBelv
            poinID = val_iMesh%iBelvProp(i,1)
            markID = val_iMesh%iBelvProp(i,2)

            if(markID <= 0) cycle

            areaFi    = val_iMesh%iArea_Belv(i)
            nvorFi(:) = val_iMesh%iNvor_Belv(i,:)

            PFi = val_iSolu%primvar(poinID,1)! - flowInfty_Pressure

            forcePi(:) = PFi*areaFi*nvorFi(:)

            iVector(:) = val_iMesh%iCoor_Poin(poinID,:) - val_iParm%aeroFM_RefO(:)

            if(nDim == 2) then
                momntPi(   1) = -forcePi(   1)*iVector(   2) + forcePi(   2)*iVector(   1)
                momntPi(2:  ) = 0.0
            elseif(nDim == 3) then
                momntPi(   1) = -forcePi(   2)*iVector(nDim) + forcePi(nDim)*iVector(   2)
                momntPi(   2) = -forcePi(nDim)*iVector(   1) + forcePi(   1)*iVector(nDim)
                momntPi(nDim) = -forcePi(   1)*iVector(   2) + forcePi(   2)*iVector(   1)
            endif

            val_aeroFM%surfaceForce_pres(:,markID) = val_aeroFM%surfaceForce_pres(:,markID) + forcePi(:)
            val_aeroFM%surfaceMomnt_pres(:,markID) = val_aeroFM%surfaceMomnt_pres(:,markID) + momntPi(:)

            !---------------------------------------------
            if(val_isViscFlow) then
                tauTotal(:) = 0.0
                do k=1,nDim
                    tauTotal(:) = tauTotal(:) + val_iSolu%Tau(poinID,1:nDim,k)*nvorFi(k) !*val_iSolu%primvar(poinID,nDim+3)
                enddo
                tauNormal = 0.0
                do k=1,nDim
                    tauNormal = tauNormal + tauTotal(k)*nvorFi(k)
                enddo
                tauTangent(:) = tauTotal(:) - tauNormal*nvorFi(:)

                val_iSolu%iSurfTau(poinID,:) = tauTangent(:)
                WallShearStress = 0.0
                do k=1,nDim
                    WallShearStress = WallShearStress + tauTangent(k)**2
                enddo
                val_iSolu%iSurfCoef(poinID) = sqrt(WallShearStress)/flowInfty_DynP

                forcePv(:) = -tauTangent(:)*areaFi !流体对物面的反作用力

                if(nDim == 2) then
                    momntPv(   1) = -forcePv(   1)*iVector(   2) + forcePv(   2)*iVector(   1)
                elseif(nDim == 3) then
                    momntPv(   1) = -forcePv(   2)*iVector(nDim) + forcePv(nDim)*iVector(   2)
                    momntPv(   2) = -forcePv(nDim)*iVector(   1) + forcePv(   1)*iVector(nDim)
                    momntPv(nDim) = -forcePv(   1)*iVector(   2) + forcePv(   2)*iVector(   1)
                endif

                val_aeroFM%surfaceForce_visc(:,markID) = val_aeroFM%surfaceForce_visc(:,markID) + forcePv(:)
                val_aeroFM%surfaceMomnt_visc(:,markID) = val_aeroFM%surfaceMomnt_visc(:,markID) + momntPv(:)
            endif
        enddo

        val_aeroFM%surfaceForce_pres(:,:) = val_aeroFM%surfaceForce_pres(:,:) * DLRef_bodyForce
        val_aeroFM%surfaceMomnt_pres(:,:) = val_aeroFM%surfaceMomnt_pres(:,:) * DLRef_bodyForce
        val_aeroFM%surfaceForce_visc(:,:) = val_aeroFM%surfaceForce_visc(:,:) * DLRef_bodyForce
        val_aeroFM%surfaceMomnt_visc(:,:) = val_aeroFM%surfaceMomnt_visc(:,:) * DLRef_bodyForce

        !---------------------------------------------
        ForceRef  = val_iParm%aeroFM_RefQ*val_iParm%aeroFM_RefS  * DLRef_bodyForce
        MomentRef = val_iParm%aeroFM_RefQ*val_iParm%aeroFM_RefS*val_iParm%aeroFM_RefL * DLRef_bodyForce

        !---------------------------------------------
        do i=1,val_iMesh%nMark
            iForce(:) = val_aeroFM%surfaceForce_pres(:,i)
            iMomnt(:) = val_aeroFM%surfaceMomnt_pres(:,i)
            iCoefF(:) = iForce(:)/ForceRef
            iCoefM(:) = iMomnt(:)/MomentRef

            val_aeroFM%surfaceCForce_pres(:,i) = iCoefF(:)
            val_aeroFM%surfaceCMomnt_pres(:,i) = iCoefM(:)
        enddo


        !---------------------------------------------
        if(val_isViscFlow) then
            do i=1,val_iMesh%nMark
                iForce(:) = val_aeroFM%surfaceForce_visc(:,i)
                iMomnt(:) = val_aeroFM%surfaceMomnt_visc(:,i)
                iCoefF(:) = iForce(:)/ForceRef
                iCoefM(:) = iMomnt(:)/MomentRef

                val_aeroFM%surfaceCForce_visc(:,i) = iCoefF(:)
                val_aeroFM%surfaceCMomnt_visc(:,i) = iCoefM(:)
            enddo
        endif

        !---------------------------------------------
        if(val_isViscFlow) then
            val_aeroFM%surfaceForce_total(:,:)  = val_aeroFM%surfaceForce_pres(:,:) + val_aeroFM%surfaceForce_visc(:,:)
            val_aeroFM%surfaceMomnt_total(:,:)  = val_aeroFM%surfaceMomnt_pres(:,:) + val_aeroFM%surfaceMomnt_visc(:,:)
            val_aeroFM%surfaceCForce_total(:,:) = val_aeroFM%surfaceCForce_pres(:,:) + val_aeroFM%surfaceCForce_visc(:,:)
            val_aeroFM%surfaceCMomnt_total(:,:) = val_aeroFM%surfaceCMomnt_pres(:,:) + val_aeroFM%surfaceCMomnt_visc(:,:)
        else
            val_aeroFM%surfaceForce_total(:,:)  = val_aeroFM%surfaceForce_pres(:,:)
            val_aeroFM%surfaceMomnt_total(:,:)  = val_aeroFM%surfaceMomnt_pres(:,:)
            val_aeroFM%surfaceCForce_total(:,:) = val_aeroFM%surfaceCForce_pres(:,:)
            val_aeroFM%surfaceCMomnt_total(:,:) = val_aeroFM%surfaceCMomnt_pres(:,:)
        endif

        !---------------------------------------------
        val_aeroFM%totalForce(:)  = 0.0
        val_aeroFM%totalMomnt(:)  = 0.0
        val_aeroFM%totalCForce(:) = 0.0
        val_aeroFM%totalCMomnt(:) = 0.0
        do i=1,val_iMesh%nMark
            if(val_isMarkWall(i)) then
                val_aeroFM%totalForce(:)  = val_aeroFM%totalForce(:)  + val_aeroFM%surfaceForce_total(:,i)
                val_aeroFM%totalMomnt(:)  = val_aeroFM%totalMomnt(:)  + val_aeroFM%surfaceMomnt_total(:,i)
                val_aeroFM%totalCForce(:) = val_aeroFM%totalCForce(:) + val_aeroFM%surfaceCForce_total(:,i)
                val_aeroFM%totalCMomnt(:) = val_aeroFM%totalCMomnt(:) + val_aeroFM%surfaceCMomnt_total(:,i)
            endif
        enddo

    endsubroutine
    !
    subroutine calAeroFMAvg(val_iParm,val_nIterAvg, val_currIter)
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_TypeDef_Parm
        !
        use mod_Config
        use mod_FreeStream
        use mod_parmDimLess
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        integer,intent(in):: val_nIterAvg
        integer,intent(in):: val_currIter
        !
        integer:: iterBgn, iterEnd, i
        real(kind=REALLEN):: ifactor

        iterBgn = max(1,val_currIter-val_nIterAvg+1)
        iterEnd = val_currIter
        ifactor = 1.0/(iterEnd-iterBgn+1)

        call zeroAeroFM(val_iParm%avgAeroFM)

        do i=iterBgn, iterEnd
            call aeroFM_Add(val_iParm%avgAeroFM, val_iParm%aeroFMHist(i), ifactor)
        end do

    endsubroutine
    !
    subroutine zeroAeroFM(val_aeroFM)
        use mod_TypeDef_Parm, only: typ_AeroFM
        implicit none
        type(typ_AeroFM),intent(inout):: val_aeroFM

        val_aeroFM%surfaceForce_pres(:,:)   = 0.0
        val_aeroFM%surfaceMomnt_pres(:,:)   = 0.0
        val_aeroFM%surfaceCForce_pres(:,:)  = 0.0
        val_aeroFM%surfaceCMomnt_pres(:,:)  = 0.0
        val_aeroFM%surfaceForce_visc(:,:)   = 0.0
        val_aeroFM%surfaceMomnt_visc(:,:)   = 0.0
        val_aeroFM%surfaceCForce_visc(:,:)  = 0.0
        val_aeroFM%surfaceCMomnt_visc(:,:)  = 0.0
        val_aeroFM%surfaceForce_total(:,:)  = 0.0
        val_aeroFM%surfaceMomnt_total(:,:)  = 0.0
        val_aeroFM%surfaceCForce_total(:,:) = 0.0
        val_aeroFM%surfaceCMomnt_total(:,:) = 0.0
        val_aeroFM%totalForce(:)            = 0.0
        val_aeroFM%totalMomnt(:)            = 0.0
        val_aeroFM%totalCForce(:)           = 0.0
        val_aeroFM%totalCMomnt(:)           = 0.0
    end subroutine zeroAeroFM
    !
    subroutine aeroFM_Add(aeroFMA, aeroFMB, val_factor)
        use mod_TypeDef_Parm, only: typ_AeroFM
        implicit none
        type(typ_AeroFM),intent(inout):: aeroFMA
        type(typ_AeroFM),intent(in):: aeroFMB
        real(kind=REALLEN),intent(in):: val_factor
        
        aeroFMA%surfaceForce_pres(:,:)   = aeroFMA%surfaceForce_pres(:,:)   + val_factor*aeroFMB%surfaceForce_pres(:,:)
        aeroFMA%surfaceMomnt_pres(:,:)   = aeroFMA%surfaceMomnt_pres(:,:)   + val_factor*aeroFMB%surfaceMomnt_pres(:,:)
        aeroFMA%surfaceCForce_pres(:,:)  = aeroFMA%surfaceCForce_pres(:,:)  + val_factor*aeroFMB%surfaceCForce_pres(:,:)
        aeroFMA%surfaceCMomnt_pres(:,:)  = aeroFMA%surfaceCMomnt_pres(:,:)  + val_factor*aeroFMB%surfaceCMomnt_pres(:,:)
        aeroFMA%surfaceForce_visc(:,:)   = aeroFMA%surfaceForce_visc(:,:)   + val_factor*aeroFMB%surfaceForce_visc(:,:)
        aeroFMA%surfaceMomnt_visc(:,:)   = aeroFMA%surfaceMomnt_visc(:,:)   + val_factor*aeroFMB%surfaceMomnt_visc(:,:)
        aeroFMA%surfaceCForce_visc(:,:)  = aeroFMA%surfaceCForce_visc(:,:)  + val_factor*aeroFMB%surfaceCForce_visc(:,:)
        aeroFMA%surfaceCMomnt_visc(:,:)  = aeroFMA%surfaceCMomnt_visc(:,:)  + val_factor*aeroFMB%surfaceCMomnt_visc(:,:)
        aeroFMA%surfaceForce_total(:,:)  = aeroFMA%surfaceForce_total(:,:)  + val_factor*aeroFMB%surfaceForce_total(:,:)
        aeroFMA%surfaceMomnt_total(:,:)  = aeroFMA%surfaceMomnt_total(:,:)  + val_factor*aeroFMB%surfaceMomnt_total(:,:)
        aeroFMA%surfaceCForce_total(:,:) = aeroFMA%surfaceCForce_total(:,:) + val_factor*aeroFMB%surfaceCForce_total(:,:)
        aeroFMA%surfaceCMomnt_total(:,:) = aeroFMA%surfaceCMomnt_total(:,:) + val_factor*aeroFMB%surfaceCMomnt_total(:,:)
        aeroFMA%totalForce(:)            = aeroFMA%totalForce(:)            + val_factor*aeroFMB%totalForce(:)
        aeroFMA%totalMomnt(:)            = aeroFMA%totalMomnt(:)            + val_factor*aeroFMB%totalMomnt(:)
        aeroFMA%totalCForce(:)           = aeroFMA%totalCForce(:)           + val_factor*aeroFMB%totalCForce(:)
        aeroFMA%totalCMomnt(:)           = aeroFMA%totalCMomnt(:)           + val_factor*aeroFMB%totalCMomnt(:)
    end subroutine aeroFM_Add
    !
end module mod_calAeroFM
