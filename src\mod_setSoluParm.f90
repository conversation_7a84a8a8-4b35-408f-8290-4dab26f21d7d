!
!---------------------------------------------
!
!---------------------------------------------
module mod_setSoluParm
    use mod_PreDefine_Dimension
    use mod_PreDefine_Precision
    implicit none
    !
contains
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine setSoluParm(val_iProj)
        use mod_TypeDef_Project
        use mod_Config
        implicit none
        type(typ_Project),intent(inout):: val_iProj
    

        call setSoluParm_CPU(val_iProj%iMesh, val_iProj%iParm)

        if(KIND_HARDWARE == _GPU) then
            call setSoluParm_GPU(val_iProj%iMesh, val_iProj%iParm)
        endif
       
    endsubroutine
    !
    subroutine setSoluParm_CPU(val_iMesh, val_iParm)
        use mod_TypeDef_Mesh
        use mod_TypeDef_Parm
        use mod_Config
        use mod_SoluAdapter, only: setSoluParm_Turb
        use mod_GetSoluParm, only: getSize_SoluPrim
        implicit none
        type(typ_Mesh),intent(in   ):: val_iMesh
        type(typ_Parm),intent(inout):: val_iParm
        !
        integer:: nSoluX, nPrimX
        
        call setSoluParm_FreeFlow
        
        call setSoluParm_Control(val_iMesh, val_iParm)
        
        call setSoluParm_IParm(val_iMesh, val_iParm)
        
        call setSoluParm_AeroFM(val_iMesh, val_iParm)

        if(PHYSICAL_PROBLEM >= _TURB) then
            call setSoluParm_Turb(val_iParm)
        end if

        call getSize_SoluPrim(nSoluX, nPrimX)

        call setupResErrArray(val_iParm, nSoluX)

    endsubroutine
    !
    subroutine setSoluParm_FreeFlow
        use mod_Config
        use mod_FreeStream
        implicit none
        !

        flowInfty_Density       = FLOW_DENSITY
        flowInfty_Pressure      = FLOW_PRESSURE
        flowInfty_Temperature   = FLOW_TEMPERATURE
        flowInfty_Viscosity     = AIR_VISCOSITY
        flowInfty_Velocity(:)   = FLOW_VELOCITY(1:nDim)
    
        flowInfty_VelS          = sqrt(AIR_GAMMA*AIR_GASCONSTANT*FLOW_TEMPERATURE)
        flowInfty_Mach          = FLOW_MACH
        flowInfty_ReyN          = FLOW_REYNOLDS_NUMBER
        flowInfty_Dir(:)        = FLOW_DIR(1:nDim)
    
        flowInfty_Ptotal        = FLOW_PRESSURE * (1.0+FLOW_MACH**2*(AIR_GAMMA-1)/2.0)**(AIR_GAMMA/(AIR_GAMMA-1.0))
        flowInfty_Ttotal        = FLOW_TEMPERATURE * (1.0+FLOW_MACH**2*(AIR_GAMMA-1)/2.0)

        if(AEROFM_REFM > 0) then
            flowInfty_DynP      = 0.5*FLOW_DENSITY*(flowInfty_VelS*AEROFM_REFM)**2
        else
            flowInfty_DynP      = 0.5*FLOW_DENSITY*(flowInfty_VelS*flowInfty_Mach)**2
        end if

        !write(*,*) "FlowInfty : ", flowInfty_Density, flowInfty_Pressure, flowInfty_Temperature, flowInfty_Velocity(:)

    endsubroutine
    !
    subroutine setSoluParm_Control(val_iMesh,val_iParm)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_Config
        implicit none
        type(typ_Mesh),intent(in   ):: val_iMesh
        type(typ_Parm),intent(inout):: val_iParm
    
    
        val_iParm%isMeshMove = .false.
        val_iParm%isViscFlow = .false.
        val_iParm%isTurbFlow = .false.
        val_iParm%isImplicit = .false.
        val_iParm%isLocalTim = .true.
        val_iParm%isWithPrec = .false.
        val_iParm%isUnsteady = .false.
        val_iParm%isDualTime = .false.
        val_iParm%isNeedExch = .false.
    
    
        if(GRID_MOVEMENT == _YES) then
            val_iParm%isMeshMove = .true.
        endif
    
        if(PHYSICAL_PROBLEM >= _NS) then
            val_iParm%isViscFlow = .true.
        endif
    
        if(PHYSICAL_PROBLEM >= _TURB) then
            val_iParm%isTurbFlow = .true.
            
            if(KIND_TURB_MODEL == _TURB_BL) then
                val_iParm%nTurbVar = 0
            elseif(KIND_TURB_MODEL == _TURB_SA) then
                val_iParm%nTurbVar = 1
            elseif(KIND_TURB_MODEL == _TURB_KE) then
                val_iParm%nTurbVar = 2
            elseif(KIND_TURB_MODEL == _TURB_KW) then
                val_iParm%nTurbVar = 2
            elseif(KIND_TURB_MODEL == _TURB_SST) then
                val_iParm%nTurbVar = 2
            else
                val_iParm%nTurbVar = 0
            endif
        endif
    
        if(TIME_DISCRE_FLOW >= _LUSGS_IMPLICIT) then
            val_iParm%isImplicit = .true.
        endif
    
        if(UNSTEADY_SIMULATION == _TIME_STEPPING) then
            val_iParm%isUnsteady = .true.
            val_iParm%isUnstDual = .false.
            val_iParm%isUnstUnit = .true.
            val_iParm%isLocalTim = .false.
            val_iParm%isDualTime = .false.
        elseif(UNSTEADY_SIMULATION == _DUAL_TIME_STEPPING) then
            val_iParm%isUnsteady = .true.
            val_iParm%isUnstDual = .true.
            val_iParm%isUnstUnit = .false.
            val_iParm%isLocalTim = .true.
            val_iParm%isDualTime = .true.
        elseif(UNSTEADY_SIMULATION == _MULTI_STEADY_SIMULATION) then
            val_iParm%isUnsteady = .false.
            val_iParm%isUnstDual = .false.
            val_iParm%isUnstUnit = .false.
            val_iParm%isLocalTim = .true.
            val_iParm%isDualTime = .true.
        endif
    
        if(IS_INCOMPRESSIBLE == _YES) then
            val_iParm%isWithPrec = .true.
        endif
        
        if(val_iMesh%iBelvProp(val_iMesh%nBelv,2) <= 0) then
            val_iparm%isNeedExch = .true.
        endif
    
    endsubroutine
    !
    subroutine setSoluParm_IParm(val_iMesh,val_iParm)
        use mod_TypeDef_Mesh
        use mod_typeDef_Parm
        use mod_Config
        use mod_FreeStream
        use mod_parmDimLess
        use mod_Interface_AllocateArray
        implicit none
        type(typ_Mesh),intent(in   ):: val_iMesh
        type(typ_Parm),intent(inout):: val_iParm
        !
        real(kind=REALLEN):: iCoefT,factB,vSound,eInfty
    
    
        !---------------------------------------------
        call allocateArray(val_iParm%iHostParm, NTotalParm)
        val_iParm%iHostParm(:) = 0.0
        
        !---------------------------------------------
        val_iParm%iHostParm(IP_Gamma)       = AIR_GAMMA
        val_iParm%iHostParm(IP_GamMO)       = AIR_GAMMA - 1.0
        val_iParm%iHostParm(IP_GasCO)       = AIR_GASCONSTANT
        val_iParm%iHostParm(IP_Cp   )       = AIR_CP
        val_iParm%iHostParm(IP_PrandtlL)    = AIR_PRANDTL_LAM
        val_iParm%iHostParm(IP_PrandtlT)    = AIR_PRANDTL_TURB
        if(val_iParm%isWithPrec) then
        val_iParm%iHostParm(IP_PrecBelt)    = max(flowInfty_Mach**2, 1.0E-6)
        else
        val_iParm%iHostParm(IP_PrecBelt)    = 1.00
        endif
        
        val_iParm%iHostParm(IP_SutherlandT0) = flowInfty_Temperature
        val_iParm%iHostParm(IP_SutherlandTs) = 110.4 / DLRef_temperature
        val_iParm%iHostParm(IP_SutherlandM0) = flowInfty_Viscosity
        
        val_iParm%iHostParm(IP_ArtDissK2)   = ARTDISS_K2
        val_iParm%iHostParm(IP_ArtDissK4)   = ARTDISS_K4
        val_iParm%iHostParm(IP_UpWRelaxF)   = UPWIND_LIMITER_K
        val_iParm%iHostParm(IP_ImplicitW)   = IMPLICIT_WEIGHT
        
        val_iParm%iHostParm(IP_CFLOrgn)     = CFL_NUMBER
        val_iParm%iHostParm(IP_CFLCurt)     = CFL_NUMBER
        val_iParm%iHostParm(IP_CFLTamp+1)   = CFL_RAMP(1)
        val_iParm%iHostParm(IP_CFLTamp+2)   = CFL_RAMP(2)
        val_iParm%iHostParm(IP_CFLTamp+3)   = CFL_RAMP(3)
        
        if(UNSTEADY_SIMULATION == _DUAL_TIME_STEPPING) then
            val_iParm%iHostParm(IP_DTLIMIT) = 2.0/3.0*UNST_PHYSTIMESTEP
        else
            val_iParm%iHostParm(IP_DTLIMIT) = -1.0
        endif
        
        val_iParm%iHostParm(IP_RKAlpha+1)   = 0.2500
        val_iParm%iHostParm(IP_RKAlpha+2)   = 0.3333
        val_iParm%iHostParm(IP_RKAlpha+3)   = 0.5000
        val_iParm%iHostParm(IP_RKAlpha+4)   = 1.0000
        
        val_iParm%iHostParm(IP_RKBelta)     = 0.00
        
        val_iParm%iHostParm(IP_InftyD)      = flowInfty_Density
        val_iParm%iHostParm(IP_InftyP)      = flowInfty_Pressure
        val_iParm%iHostParm(IP_InftyT)      = flowInfty_Temperature
        
        iCoefT = 1.0 + 0.5*(AIR_GAMMA - 1.0)*flowInfty_Mach**2
        
        val_iParm%iHostParm(IP_TotalD)      = flowInfty_Density*iCoefT**(1.0/(AIR_GAMMA - 1.0))
        val_iParm%iHostParm(IP_TotalP)      = flowInfty_Pressure*iCoefT**(AIR_GAMMA/(AIR_GAMMA - 1.0))
        val_iParm%iHostParm(IP_TotalT)      = flowInfty_Temperature*iCoefT

        FLOW_ALLOW_MAXD = 3.0*(1+0.5*(AIR_GAMMA-1.0)*FLOW_MACH*FLOW_MACH)**(1/(AIR_GAMMA-1.0))
        FLOW_ALLOW_MAXP = 3.0*(1+0.5*(AIR_GAMMA-1.0)*FLOW_MACH*FLOW_MACH)**(AIR_GAMMA/(AIR_GAMMA-1.0))
        FLOW_ALLOW_MAXT = 3.0*(1+0.5*(AIR_GAMMA-1.0)*FLOW_MACH*FLOW_MACH)

        val_iParm%iHostParm(IP_LimMinD)      = FLOW_ALLOW_MIND* flowInfty_Density
        val_iParm%iHostParm(IP_LimMaxD)      = FLOW_ALLOW_MAXD* flowInfty_Density
        val_iParm%iHostParm(IP_LimMinP)      = FLOW_ALLOW_MINP* flowInfty_Pressure
        val_iParm%iHostParm(IP_LimMaxP)      = FLOW_ALLOW_MAXP* flowInfty_Pressure
        val_iParm%iHostParm(IP_LimMinT)      = FLOW_ALLOW_MINT* flowInfty_Temperature
        val_iParm%iHostParm(IP_LimMaxT)      = FLOW_ALLOW_MAXT* flowInfty_Temperature
        val_iParm%iHostParm(IP_LimMinX)      = 0.0            * flowInfty_Viscosity
        val_iParm%iHostParm(IP_LimMaxX)      = 1.0E+6         * flowInfty_Viscosity
        val_iParm%iHostParm(IP_LimMinMuT)    = FLOW_ALLOW_MINX !* flowInfty_Viscosity
        val_iParm%iHostParm(IP_LimMaxMuT)    = FLOW_ALLOW_MAXX !* flowInfty_Viscosity
        !


        val_iParm%iHostParm(IP_ALLOWCHANGE)  = FLOW_ALLOW_CHANGES

        factB = 0.5
        if(flowInfty_Mach > 1.2) factB  = 0.3
        if(flowInfty_Mach > 1.5) factB  = 0.2
        if(flowInfty_Mach > 1.7) factB  = 0.1
        if(FlowInfty_Mach > 2.0) factB = 0.05
        if(FlowInfty_Mach > 2.5) factB = 0.02

        factB = min(factB, FLOW_ALLOW_CHANGES)

        vSound = sqrt(AIR_GAMMA*flowInfty_Pressure/flowInfty_Density)
        eInfty = flowInfty_Pressure/(AIR_GAMMA-1.0) + 0.5*flowInfty_Density*(flowInfty_Mach*vSound)**2

        val_iParm%iHostParm(IP_DDLIM)   = factB*flowInfty_Density
        val_iParm%iHostParm(IP_DULIM)   = factB*flowInfty_Density*flowInfty_Mach*vSound
        val_iParm%iHostParm(IP_DELIM)   = factB*eInfty

        val_iParm%iHostParm(IP_DTViscCoef)   = 2.0
        if(AEROFM_REFM > 0) then
        val_iParm%iHostParm(IP_RefMach) = AEROFM_REFM
        else
        val_iParm%iHostParm(IP_RefMach) = flowInfty_Mach
        end if

        val_iParm%iHostParm(IP_OUTP2WALL) = -1.0
        if(CONVERT_OUTP2WALL) then
            if(UNSTEADY_SIMULATION == _DUAL_TIME_STEPPING .or. UNSTEADY_SIMULATION == _MULTI_STEADY_SIMULATION) then
                val_iParm%iHostParm(IP_OUTP2WALL) = 1.0
            end if
        end if


        !---------------------------------------------
        val_iParm%nIter_Steady          = NITER_STEADY
        val_iParm%nUnstOuterIter        = UNST_OUTER_ITER
        val_iParm%nUnstInnerIter        = UNST_INNER_ITER

        val_iParm%iOuterIterBegin       = 1
        val_iParm%iInnerIterBegin       = 1
        val_iParm%iIter                 = 0
        val_iParm%nMCIter               = max(1,NITER_OF_MCGS)
        val_iParm%nIterFMAvg            = AEROFM_NAVGITER

        val_iParm%nTotalIter = val_iParm%nUnstInnerIter*val_iParm%nUnstOuterIter + val_iParm%nIter_Steady

        val_iParm%nIter_ResultSaveBgn   = RESAVE_BGNITER
       !write(*,*) " val_iParm%nIter_ResultSaveBgn ", val_iParm%nIter_ResultSaveBgn
        val_iParm%nIter_ResultSave      = RESAVE_ITER
        val_iParm%nIter_ResErrCalc      = RESERR_ITER
        val_iParm%nIter_ResErrShow      = RESERR_SHOW
    
        val_iParm%iTimeBegin            =  UNST_PHYSTIMEBGN  / DLRef_time
        val_iParm%iTimeFinal            =  UNST_PHYSTIMEEND  / DLRef_time
        val_iParm%iTimeDelta            =  UNST_PHYSTIMESTEP / DLRef_time
        val_iParm%iTimeCurrt            =  val_iParm%iTimeBegin
    
    endsubroutine
    !
    subroutine setSoluParm_AeroFM(val_iMesh,val_iParm)
        use mod_TypeDef_Mesh
        use mod_TypeDef_Parm
        use mod_Config
        use mod_FreeStream
        implicit none
        type(typ_Mesh),intent(in   ):: val_iMesh
        type(typ_Parm),intent(inout):: val_iParm
        !
        integer:: i
    

        val_iParm%nMark = val_iMesh%nMark

        !---------------------------------------------
        allocate(val_iParm%isMarkWall(val_iMesh%nMark))
        val_iParm%isMarkWall(:) = .false.
        do i=1,val_iMesh%nMark
            if(val_iMesh%iMarkProp(i,2) == MARK_WALL .or. &
               val_iMesh%iMarkProp(i,2) == MARK_EQTW .or. &
               val_iMesh%iMarkProp(i,2) == MARK_EULW .or. &
               val_iMesh%iMarkProp(i,2) == MARK_MOVW ) then
                val_iParm%isMarkWall(i) = .true.
            endif
        enddo

        !---------------------------------------------
        allocate(val_iParm%aeroFM_MarkWallFlag(val_iMesh%nMark))
        val_iParm%aeroFM_MarkWallFlag(:) = .false.
        do i=1,val_iMesh%nMark
            if(val_iMesh%iMarkProp(i,2) == MARK_WALL .or. &
               val_iMesh%iMarkProp(i,2) == MARK_EQTW .or. &
               val_iMesh%iMarkProp(i,2) == MARK_EULW .or. &
               val_iMesh%iMarkProp(i,2) == MARK_MOVW ) then
                val_iParm%aeroFM_MarkWallFlag(i) = .true.
            endif
        enddo
    
        !---------------------------------------------  
        val_iParm%aeroFM_RefQ    = flowInfty_DynP
        val_iParm%aeroFM_RefS    = AEROFM_REFS
        val_iParm%aeroFM_RefL    = AEROFM_REFL
        val_iParm%aeroFM_RefO(:) = AEROFM_ORGN(1:nDim)

        !---------------------------------------------
        write(*,*) "val_iParm%nIter = ", val_iParm%nTotalIter
        write(*,*) "val_iMesh%nMark = ", val_iMesh%nMark
        allocate(val_iParm%aeroFMHist(val_iParm%nTotalIter))

        call setupAeroFM(val_iParm%currAeroFM, val_iMesh%nMark)
        call setupAeroFM(val_iParm%avgAeroFM, val_iMesh%nMark)
        do i=1,val_iParm%nTotalIter
            call setupAeroFM(val_iParm%aeroFMHist(i), val_iMesh%nMark)
        end do

    endsubroutine
    !
    subroutine setupAeroFM(val_aeroFM,val_nMark)
        use mod_TypeDef_Parm, only: typ_AeroFM
        use mod_Interface_AllocateArray
        implicit none
        type(typ_AeroFM),intent(inout):: val_aeroFM
        integer         ,intent(in   ):: val_nMark


        allocate(val_aeroFM%surfaceForce_pres(nDim, val_nMark))
        allocate(val_aeroFM%surfaceMomnt_pres(nDim, val_nMark))
        allocate(val_aeroFM%surfaceCForce_pres(nDim, val_nMark))
        allocate(val_aeroFM%surfaceCMomnt_pres(nDim, val_nMark))
        val_aeroFM%surfaceForce_pres(:,:)  = 0.0
        val_aeroFM%surfaceMomnt_pres(:,:)  = 0.0
        val_aeroFM%surfaceCForce_pres(:,:) = 0.0
        val_aeroFM%surfaceCMomnt_pres(:,:) = 0.0

        allocate(val_aeroFM%surfaceForce_visc(nDim, val_nMark))
        allocate(val_aeroFM%surfaceMomnt_visc(nDim, val_nMark))
        allocate(val_aeroFM%surfaceCForce_visc(nDim, val_nMark))
        allocate(val_aeroFM%surfaceCMomnt_visc(nDim, val_nMark))
        val_aeroFM%surfaceForce_visc(:,:)  = 0.0
        val_aeroFM%surfaceMomnt_visc(:,:)  = 0.0
        val_aeroFM%surfaceCForce_visc(:,:) = 0.0
        val_aeroFM%surfaceCMomnt_visc(:,:) = 0.0

        allocate(val_aeroFM%surfaceForce_total(nDim, val_nMark))
        allocate(val_aeroFM%surfaceMomnt_total(nDim, val_nMark))
        allocate(val_aeroFM%surfaceCForce_total(nDim, val_nMark))
        allocate(val_aeroFM%surfaceCMomnt_total(nDim, val_nMark))
        val_aeroFM%surfaceForce_total(:,:)  = 0.0
        val_aeroFM%surfaceMomnt_total(:,:)  = 0.0
        val_aeroFM%surfaceCForce_total(:,:) = 0.0
        val_aeroFM%surfaceCMomnt_total(:,:) = 0.0

        val_aeroFM%totalForce(:)  = 0.0
        val_aeroFM%totalMomnt(:)  = 0.0
        val_aeroFM%totalCForce(:) = 0.0
        val_aeroFM%totalCMomnt(:) = 0.0

    endsubroutine
    !
    subroutine setupResErrArray(val_iParm,val_nResVar)
        use mod_PreDefine_IOPort
        use mod_TypeDef_Parm
        use mod_Config
        use mod_Interface_AllocateArray
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        integer       ,intent(in   ):: val_nResVar


        val_iParm%nResErrVar = val_nResVar
        if(val_iParm%nResErrVar <= 0) then
            write(ioPort_Out,*) 'val_iParm%nResErrVar <= 0'
            stop
        endif

        call allocateArray(val_iParm%ResErrLim, val_nResVar)
        call allocateArray(val_iParm%ResErrRef, val_nResVar)
        call allocateArray(val_iParm%ResErrAbs, val_nResVar)
        call allocateArray(val_iParm%ResErrMax, val_nResVar)
        call allocateArray(val_iParm%ResErrLog, val_nResVar)
        val_iParm%ResErrLim(:) = RESERR_RESUCTION
        val_iParm%ResErrRef(:) = 0.0
        val_iParm%ResErrAbs(:) = 0.0
        val_iParm%ResErrMax(:) = 0.0
        val_iParm%ResErrLog(:) = 0.0

    endsubroutine
    !
    subroutine setSoluParm_GPU(val_iMesh,val_iParm)
        use mod_TypeDef_Mesh
        use mod_TypeDef_Parm
        use mod_Config
        implicit none
        type(typ_Mesh),intent(in   ):: val_iMesh
        type(typ_Parm),intent(inout):: val_iParm

        call setSoluParm_GPUParameter(val_iParm)

        call setSoluParm_GPUThreadDim(val_iMesh)

    endsubroutine
    !
    subroutine setSoluParm_GPUParameter(val_iParm)
        use mod_Config
        use mod_TypeDef_Parm
        use mod_Interface_AllocateArray
        implicit none
        type(typ_Parm),intent(inout):: val_iParm

        call allocateGPUArray(val_iParm%iGPUSParm, nTotalParm)
        val_iParm%iGPUSParm = val_iParm%iHostParm


        !---------------------------------------------
        if(KIND_HARDWARE == _GPU) then
            call allocateGPUArray(val_iParm%ResErr_d   , val_iParm%nResErrVar)
            call allocateGPUArray(val_iParm%ResErrMax_d, val_iParm%nResErrVar)
            val_iParm%ResErr_d(:)    = 0.0
            val_iParm%ResErrMax_d(:) = 0.0
        endif

    endsubroutine
    !
    subroutine setSoluParm_GPUThreadDim(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_GPUThreadDim
        use mod_Config
        implicit none
        type(typ_Mesh),intent(in):: val_iMesh
        !
        integer:: i
        integer:: nPoin_Clor(val_iMesh%nClor)


        nThreadBlock(IP_BP, IP_NT24) = (val_iMesh%nPoin+16  -1)/16
        nThreadBlock(IP_BP, IP_NT25) = (val_iMesh%nPoin+32  -1)/32
        nThreadBlock(IP_BP, IP_NT26) = (val_iMesh%nPoin+64  -1)/64
        nThreadBlock(IP_BP, IP_NT27) = (val_iMesh%nPoin+128 -1)/128
        nThreadBlock(IP_BP, IP_NT28) = (val_iMesh%nPoin+256 -1)/256
        nThreadBlock(IP_BP, IP_NT29) = (val_iMesh%nPoin+512 -1)/512
        nThreadBlock(IP_BP, IP_NT2X) = (val_iMesh%nPoin+1024-1)/1024

        nThreadBlock(IP_BB, IP_NT24) = (val_iMesh%nBelv+16  -1)/16
        nThreadBlock(IP_BB, IP_NT25) = (val_iMesh%nBelv+32  -1)/32
        nThreadBlock(IP_BB, IP_NT26) = (val_iMesh%nBelv+64  -1)/64
        nThreadBlock(IP_BB, IP_NT27) = (val_iMesh%nBelv+128 -1)/128
        nThreadBlock(IP_BB, IP_NT28) = (val_iMesh%nBelv+256 -1)/256
        nThreadBlock(IP_BB, IP_NT29) = (val_iMesh%nBelv+512 -1)/512
        nThreadBlock(IP_BB, IP_NT2X) = (val_iMesh%nBelv+1024-1)/1024

        nThreadBlock(IP_BS, IP_NT24) = (val_iMesh%nSate+16  -1)/16
        nThreadBlock(IP_BS, IP_NT25) = (val_iMesh%nSate+32  -1)/32
        nThreadBlock(IP_BS, IP_NT26) = (val_iMesh%nSate+64  -1)/64
        nThreadBlock(IP_BS, IP_NT27) = (val_iMesh%nSate+128 -1)/128
        nThreadBlock(IP_BS, IP_NT28) = (val_iMesh%nSate+256 -1)/256
        nThreadBlock(IP_BS, IP_NT29) = (val_iMesh%nSate+512 -1)/512
        nThreadBlock(IP_BS, IP_NT2X) = (val_iMesh%nSate+1024-1)/1024

        nThreadBlock(IP_BE, IP_NT24) = (val_iMesh%nEdge+16  -1)/16
        nThreadBlock(IP_BE, IP_NT25) = (val_iMesh%nEdge+32  -1)/32
        nThreadBlock(IP_BE, IP_NT26) = (val_iMesh%nEdge+64  -1)/64
        nThreadBlock(IP_BE, IP_NT27) = (val_iMesh%nEdge+128 -1)/128
        nThreadBlock(IP_BE, IP_NT28) = (val_iMesh%nEdge+256 -1)/256
        nThreadBlock(IP_BE, IP_NT29) = (val_iMesh%nEdge+512 -1)/512
        nThreadBlock(IP_BE, IP_NT2X) = (val_iMesh%nEdge+1024-1)/1024

        nThreadBlock(IP_BI, IP_NT24) = (val_iMesh%nInfc+16  -1)/16
        nThreadBlock(IP_BI, IP_NT25) = (val_iMesh%nInfc+32  -1)/32
        nThreadBlock(IP_BI, IP_NT26) = (val_iMesh%nInfc+64  -1)/64
        nThreadBlock(IP_BI, IP_NT27) = (val_iMesh%nInfc+128 -1)/128
        nThreadBlock(IP_BI, IP_NT28) = (val_iMesh%nInfc+256 -1)/256
        nThreadBlock(IP_BI, IP_NT29) = (val_iMesh%nInfc+512 -1)/512
        nThreadBlock(IP_BI, IP_NT2X) = (val_iMesh%nInfc+1024-1)/1024

        do i=1,val_iMesh%nClor
            nPoin_Clor(i) = val_iMesh%kPoin_Clor(i+1) - val_iMesh%kPoin_Clor(i)

            nThreadBlock(IP_BM+i, IP_NT24) = (nPoin_Clor(i)+16  -1)/16
            nThreadBlock(IP_BM+i, IP_NT25) = (nPoin_Clor(i)+32  -1)/32
            nThreadBlock(IP_BM+i, IP_NT26) = (nPoin_Clor(i)+64  -1)/64
            nThreadBlock(IP_BM+i, IP_NT27) = (nPoin_Clor(i)+128 -1)/128
            nThreadBlock(IP_BM+i, IP_NT28) = (nPoin_Clor(i)+256 -1)/256
            nThreadBlock(IP_BM+i, IP_NT29) = (nPoin_Clor(i)+512 -1)/512
            nThreadBlock(IP_BM+i, IP_NT2X) = (nPoin_Clor(i)+1024-1)/1024
        enddo

        do i=1,val_iMesh%nClor
            nPoin_Clor(i) = val_iMesh%kSatL_Clor(i+1) - val_iMesh%kSatL_Clor(i)

            nThreadBlock(IP_BL+i, IP_NT24) = (nPoin_Clor(i)+16  -1)/16
            nThreadBlock(IP_BL+i, IP_NT25) = (nPoin_Clor(i)+32  -1)/32
            nThreadBlock(IP_BL+i, IP_NT26) = (nPoin_Clor(i)+64  -1)/64
            nThreadBlock(IP_BL+i, IP_NT27) = (nPoin_Clor(i)+128 -1)/128
            nThreadBlock(IP_BL+i, IP_NT28) = (nPoin_Clor(i)+256 -1)/256
            nThreadBlock(IP_BL+i, IP_NT29) = (nPoin_Clor(i)+512 -1)/512
            nThreadBlock(IP_BL+i, IP_NT2X) = (nPoin_Clor(i)+1024-1)/1024
        enddo

        do i=1,val_iMesh%nClor
            nPoin_Clor(i) = val_iMesh%kSatR_Clor(i+1) - val_iMesh%kSatR_Clor(i)

            nThreadBlock(IP_BR+i, IP_NT24) = (nPoin_Clor(i)+16  -1)/16
            nThreadBlock(IP_BR+i, IP_NT25) = (nPoin_Clor(i)+32  -1)/32
            nThreadBlock(IP_BR+i, IP_NT26) = (nPoin_Clor(i)+64  -1)/64
            nThreadBlock(IP_BR+i, IP_NT27) = (nPoin_Clor(i)+128 -1)/128
            nThreadBlock(IP_BR+i, IP_NT28) = (nPoin_Clor(i)+256 -1)/256
            nThreadBlock(IP_BR+i, IP_NT29) = (nPoin_Clor(i)+512 -1)/512
            nThreadBlock(IP_BR+i, IP_NT2X) = (nPoin_Clor(i)+1024-1)/1024
        enddo


        nBlockDim = 32
        if(NTHREADPERBLOCK == 16) then
            nBlockDim = 16
            nGridDim(:) = nThreadBlock(:,IP_NT24)
        elseif(NTHREADPERBLOCK == 32) then
            nBlockDim = 32
            nGridDim(:) = nThreadBlock(:,IP_NT25)
        elseif(NTHREADPERBLOCK == 64) then
            nBlockDim = 64
            nGridDim(:) = nThreadBlock(:,IP_NT26)
        elseif(NTHREADPERBLOCK == 128) then
            nBlockDim = 128
            nGridDim(:) = nThreadBlock(:,IP_NT27)
        elseif(NTHREADPERBLOCK == 256) then
            nBlockDim = 256
            nGridDim(:) = nThreadBlock(:,IP_NT28)
        elseif(NTHREADPERBLOCK == 512) then
            nBlockDim = 512
            nGridDim(:) = nThreadBlock(:,IP_NT29)
        elseif(NTHREADPERBLOCK == 1024) then
            nBlockDim = 1024
            nGridDim(:) = nThreadBlock(:,IP_NT2X)
        else
            nBlockDim = 32
            nGridDim(:) = nThreadBlock(:,IP_NT25)
        endif

    endsubroutine
    !
    subroutine updCFLNumber(val_CFL,val_iStep)
        use mod_Config
        implicit none
        real(kind=REALLEN),intent(inout):: val_CFL
        integer,intent(in):: val_iStep
        !
        real(kind=REALLEN):: CFLOrgn,CFLCurt


        if(TIME_DISCRE_FLOW == _RUNGE_KUTTA_EXPLICIT) then
            val_CFL = CFL_NUMBER
            return
        endif

        if(CFL_RAMP_KIND == _CFL_RAMP_LINEAR) then
            CFLCurt = CFL_NUMBER + CFL_RAMP(1)*((val_iStep - 1.0)/CFL_RAMP(2))
            CFLCurt = min(CFLCurt , CFL_RAMP(3))
        elseif(CFL_RAMP_KIND == _CFL_RAMP_EXP) then
            CFLCurt = CFL_NUMBER * CFL_RAMP(1)**((val_iStep - 1.0)/CFL_RAMP(2))
            CFLCurt = min(CFLCurt , CFL_RAMP(3))
        elseif(CFL_RAMP_KIND == _CFL_RAMP_LSTAIR) then
            CFLCurt = CFL_NUMBER + CFL_RAMP(1)*floor((val_iStep - 1)/CFL_RAMP(2))
            CFLCurt = min(CFLCurt , CFL_RAMP(3))
        elseif(CFL_RAMP_KIND == _CFL_RAMP_ESTAIR) then
            CFLCurt = CFL_NUMBER * CFL_RAMP(1)**(floor((val_iStep - 1)/CFL_RAMP(2)))
            CFLCurt = min(CFLCurt , CFL_RAMP(3))
        else
            CFLCurt = CFL_NUMBER
        endif

        val_CFL = CFLCurt

    endsubroutine
    !
endmodule
!   
    
    