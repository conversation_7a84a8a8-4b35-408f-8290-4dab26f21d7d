!-----------------------------------------
!	author : <PERSON><PERSON><PERSON><PERSON>@pku.edu.cn
!	purpose : generate tecplot binary files
!-----------------------------------------
module mod_TECIO
    !include "tecio.f90"
    implicit none
    integer(kind=4), parameter:: nullchar           = 0
    character(len=8),parameter:: magicnumber_ver    = '#!TDV112'
    integer(kind=4), parameter:: byteorder          = 1
    integer(kind=4), parameter:: variabledatafmt    = 1 !2=double
    integer(kind=4), parameter:: fequadrilateral    = 3
    integer(kind=4), parameter:: febrick            = 5
    real(kind=4),    parameter:: eohmarker          = 357.0
    real(kind=4),    parameter:: zonemarker         = 299.0

    type:: typ_TecFile
        character(len=999):: fpath
        integer:: fileTag
        !
        integer(kind=4):: filetype, parentzone, strandid, notused
        integer(kind=4):: zonetype, npoints, nelements, nvariables
        integer(kind=4):: icelldim, jcelldim, kcelldim
        integer(kind=4):: auxiliarypair, specifyvarlocation, localfaceneighbor, userdefinedfaceneighbor
        integer(kind=4):: passivevariable, variablesharing, shareconnectivityfromzone
        character(len=256):: titlename, zonename, varnames
        real(kind=8):: solutiontime
        real(kind=8):: minBnd(100), maxBnd(100)
    endtype
    !
contains
    subroutine plt_open_file(val_tecfile, val_tag, val_fpath)
        type(typ_TecFile):: val_tecfile
        integer:: val_tag
        character(len=*):: val_fpath

        val_tecfile%fileTag = val_tag
        val_tecfile%fpath = trim(adjustl(val_fpath))

        open(val_tecfile%fileTag, file=trim(val_tecfile%fpath),form="unformatted", access="stream")

    endsubroutine

    subroutine plt_zone_init(val_tecfile, val_dim, val_nvariables, val_npoints, val_nelements, val_varnames, val_minbnd, val_maxbnd, val_iter)
        use mod_PreDefine_Precision
        implicit none
        type(typ_TecFile):: val_tecfile
        integer:: val_dim
        integer:: val_nvariables
        integer:: val_npoints
        integer:: val_nelements
        character(len=*):: val_varnames
        real(kind=REALLEN):: val_minbnd(:)
        real(kind=REALLEN):: val_maxbnd(:)
        integer,intent(in):: val_iter
        !
        integer:: i

        ! FileType: 0 = Full, 1 = GRID, 2 = SOLUTION
        val_tecfile%filetype = 0

        if(val_dim == 2) then
            val_tecfile%zonetype = fequadrilateral
        elseif(val_dim == 3) then
            val_tecfile%zonetype = febrick
        end if

        val_tecfile%titlename = "GFlow Export"
        val_tecfile%zonename = "Field"
        val_tecfile%varnames = trim(adjustl(val_varnames))

        val_tecfile%npoints = val_npoints
        val_tecfile%nelements = val_nelements
        val_tecfile%nvariables = val_nvariables

        do i=1,val_nvariables
            val_tecfile%minBnd(i) = val_minbnd(i)
            val_tecfile%maxBnd(i) = val_maxbnd(i)
        end do

        ! Parent zone
        val_tecfile%parentzone = -1

        ! StrandID
        ! -1 = static strand ID
        val_tecfile%strandid = -1

        ! Solution time
        val_tecfile%solutiontime = 0.0 + val_iter

        ! Not used. Set to -1
        val_tecfile%notused = -1

        ! Specify Var Location
        ! 0 = Don't specify, all data is located at the nodes
        val_tecfile%specifyvarlocation = 0

        ! Are raw local 1-to-1 face neighbor supplied?
        ! 0 = FALSE 1 = TRUE
        val_tecfile%localfaceneighbor = 0

        ! Number of miscellaneous user-defined face neighbor connections
        val_tecfile%userdefinedfaceneighbor = 0


        ! ICellDim, JCellDim, KCellDim (for future use; set to zero)
        val_tecfile%icelldim = 0; val_tecfile%jcelldim = 0; val_tecfile%kcelldim = 0

        ! 0 = No more Auxiliary name/value pair
        val_tecfile%auxiliarypair = 0

        val_tecfile%passivevariable = 0

        val_tecfile%variablesharing = 0

        val_tecfile%shareconnectivityfromzone = -1

    endsubroutine

    subroutine plt_write_header(val_tecfile)
        implicit none
        type(typ_TecFile):: val_tecfile
        !
        integer:: i

        !============================================================================
        !head section

        ! Magic number, Version number
        write(val_tecfile%fileTag) magicnumber_ver

        ! Integer value of 1
        ! This is used to determine the byte order
        ! of the reader, relative to the writer
        write(val_tecfile%fileTag) byteorder

        ! FileType: 0 = Full, 1 = GRID, 2 = SOLUTION
        write(val_tecfile%fileTag) val_tecfile%filetype

        ! Title
        do i=1,len_trim(val_tecfile%titlename)
            write(val_tecfile%fileTag) ichar(val_tecfile%titlename(i:i))
        enddo
        write(val_tecfile%fileTag) nullchar

        !variable num and names
        write(val_tecfile%fileTag) val_tecfile%nvariables
        do i=1,len_trim(val_tecfile%varnames)
            if(val_tecfile%varnames(i:i) /= ',') then
                write(val_tecfile%fileTag) ichar(val_tecfile%varnames(i:i))
            else
                write(val_tecfile%fileTag) nullchar
            end if
        end do
        write(val_tecfile%fileTag) nullchar

        ! Zone marker. Value = 299.0
        write(val_tecfile%fileTag) zonemarker

        ! Zone name
        do i=1,len_trim(val_tecfile%zonename)
            write(val_tecfile%fileTag) ichar(val_tecfile%zonename(i:i))
        end do
        write(val_tecfile%fileTag) nullchar

        write(val_tecfile%fileTag) val_tecfile%parentzone

        write(val_tecfile%fileTag) val_tecfile%strandid

        write(val_tecfile%fileTag) val_tecfile%solutiontime

        write(val_tecfile%fileTag) val_tecfile%notused

        write(val_tecfile%fileTag) val_tecfile%zonetype

        write(val_tecfile%fileTag) val_tecfile%specifyvarlocation

        write(val_tecfile%fileTag) val_tecfile%localfaceneighbor

        write(val_tecfile%fileTag) val_tecfile%userdefinedfaceneighbor

        write(val_tecfile%fileTag) val_tecfile%nPoints

        write(val_tecfile%fileTag) val_tecfile%nElements

        write(val_tecfile%fileTag) val_tecfile%icelldim, val_tecfile%jcelldim, val_tecfile%kcelldim

        write(val_tecfile%fileTag) val_tecfile%auxiliarypair

        ! separate the header from the data with an EOHMARKER
        ! eohmarker = 357.0
        write(val_tecfile%fileTag) eohmarker

        !============================================================================
        !data section

        write(val_tecfile%fileTag) zonemarker

        do i = 1,val_tecfile%nvariables
            write(val_tecfile%fileTag) variableDataFmt
        end do

        write(val_tecfile%fileTag) val_tecfile%passivevariable

        write(val_tecfile%fileTag) val_tecfile%variablesharing

        write(val_tecfile%fileTag) val_tecfile%shareconnectivityfromzone

        do i=1,val_tecfile%nvariables
            write(val_tecfile%fileTag) val_tecfile%minBnd(i), val_tecfile%maxBnd(i)
        enddo

    endsubroutine

    subroutine plt_write_variable(val_tecfile,val_variable)
        use mod_PreDefine_Precision
        implicit none
        type(typ_TecFile):: val_tecfile
        real(kind=REALLEN):: val_variable(:)
        !
        integer:: i
        real(kind=4),allocatable:: tmpr(:)


        allocate(tmpr(val_tecfile%npoints))

        do i=1,val_tecfile%npoints
            tmpr(i) = val_variable(i)
        end do
        write(val_tecfile%fileTag) tmpr

        deallocate(tmpr)

    endsubroutine

    subroutine plt_write_elements(val_tecfile, val_ielem,val_dimX, val_dimY)
        implicit none
        type(typ_TecFile):: val_tecfile
        integer:: val_ielem(val_dimX, val_dimY)
        integer:: val_dimX, val_dimY
        !
        integer:: i,j
        integer(kind=4),allocatable:: ielem(:,:)

        allocate(ielem(val_dimX, val_tecfile%nelements))

        do i=1,val_tecfile%nelements
            ielem(:,i) = val_ielem(:,i)-1
        end do

        write(val_tecfile%fileTag) ielem

        deallocate(ielem)

    endsubroutine

    subroutine plt_close_file(val_tecfile)
        implicit none
        type(typ_TecFile):: val_tecfile

        close(val_tecfile%fileTag)

    end subroutine plt_close_file

end module mod_TECIO