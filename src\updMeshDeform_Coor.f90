! 
subroutine updMeshDeform_Coor(val_iMesh)
    use mod_TypeDef_Mesh
    use mod_Mesh_Deform
    use mod_Interface_AllocateArray
    implicit none
    type(typ_GlobalMesh),intent(inout):: val_iMesh
    !
    integer:: i,j,k,s
    integer:: poinID,deformKind
    real(kind=REALLEN):: dCoor(nDim,val_iMesh%nPoin)
    real(kind=REALLEN):: ddCoor(nDim,val_iMesh%nPoin)
    real(kind=REALLEN):: dpCoor(nDim),orgnA,orgnR,deltA
    
    
    dCoor(:,:) = 0.0
    do i=1,nDeformZone
        deformKind = iDeformZone(i)%deformKind
        
        ddCoor(:,:) = 0.0
        
        SELECT CASE (iDeformZone(i)%deformKind)
        CASE (DEFORMKIND_TRANS,DEFORMKIND_REGNT,DEFORMKIND_SOFTT)
            do j=1,iDeformZone(i)%nPoinBC
                poinID = iDeformZone(i)%iPoinPropBC(j,1)
            
                ddCoor(:,poinID) = iDeformZone(i)%iDeltPoinBC(:,j)
            enddo
            
            do j=1,iDeformZone(i)%nPoin
                poinID = iDeformZone(i)%iPoinProp(j,1)
            
                ddCoor(:,poinID) = iDeformZone(i)%iDeltPoin(:,j)
            enddo
            
        CASE (DEFORMKIND_ROTAT,DEFORMKIND_REGNR,DEFORMKIND_SOFTR)
            do j=1,iDeformZone(i)%nPoinBC
                poinID = iDeformZone(i)%iPoinPropBC(j,1)
                
                orgnA = iDeformZone(i)%iOrgnAndRBC(j,1)
                orgnR = iDeformZone(i)%iOrgnAndRBC(j,2)
                deltA = iDeformZone(i)%iDeltPoinBC(1,j)
                
                if(nDim == 2) then
                dpCoor(1) = orgnR*(cosd(orgnA + deltA) - cosd(orgnA))
                dpCoor(2) = orgnR*(sind(orgnA + deltA) - sind(orgnA))
                endif
                
                iDeformZone(i)%iOrgnAndRBC(j,1) = orgnA + deltA
                
                ddCoor(:,poinID) = dpCoor(:)
            enddo
            
            do j=1,iDeformZone(i)%nPoin
                poinID = iDeformZone(i)%iPoinProp(j,1)
                
                orgnA = iDeformZone(i)%iOrgnAndR(j,1)
                orgnR = iDeformZone(i)%iOrgnAndR(j,2)
                deltA = iDeformZone(i)%iDeltPoin(1,j)
                
                if(nDim == 2) then
                dpCoor(1) = orgnR*(cosd(orgnA + deltA) - cosd(orgnA))
                dpCoor(2) = orgnR*(sind(orgnA + deltA) - sind(orgnA))
                endif
                
                iDeformZone(i)%iOrgnAndR(j,1) = orgnA + deltA
                
                ddCoor(:,poinID) = dpCoor(:)
            enddo
            
        END SELECT
        
        dCoor(:,:) = dCoor(:,:) + ddCoor(:,:)
    enddo
    
    val_iMesh%dCoor_Poin(:,:) = dCoor(:,:)
    val_iMesh%iCoor_Poin(:,:) = val_iMesh%iCoor_Poin(:,:) + dCoor(:,:)
    
endsubroutine
!
    
    
