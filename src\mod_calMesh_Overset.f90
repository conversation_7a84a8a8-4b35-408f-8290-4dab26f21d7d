!   
!---------------------------------------------
!
!---------------------------------------------
module mod_calMesh_Overset
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    use mod_PreDefine_Flag
    use mod_PreDefine_IOPort
    use mod_TypeDef_Division
    implicit none
    !
    real(kind=REALLEN):: viscLayerHeight = 1.0       !边界层高度上限
    real(kind=REALLEN):: iViscPoinSensor = 10.0      !粘性底层网格捕捉器
    integer           ,allocatable:: iHost_Poin(:)   !(nPoin) 宿主信息
    integer           ,allocatable:: iFordHost(:)    !(nPoin) 强制Host
    integer           ,allocatable:: iOrgnHost(:)    !(nPoin) 初始Host
    !
    integer           ,allocatable:: iRegn_Poin(:)   !(nPoin) +-RegnID
    integer           ,allocatable:: kRDis_Poin(:,:) !(nPoin,nRegn)
    real(kind=REALLEN),allocatable:: iRDis_Poin(:,:) !(nPoin,nRegn)
    real(kind=REALLEN),allocatable:: iRadu_Poin(:,:) !(nPoin,3) avg,min,max
    type(typ_DivRegin)            :: iDivRegnFull    !all points divided
    type(typ_DivRegin),allocatable:: iDivRegnWall(:) !(nRegn) wall points divided
    !
contains
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine calMeshReform_Overset(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_Interface_AllocateArray
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
        !
        integer:: i,nPoin,nSucc,nearID
        integer:: iTempFlag(val_iMesh%nPoin)
        real(kind=REALLEN):: iDis
        logical:: isOk
        
        
        call allocateArray(val_iMesh%iHost_Poin, val_iMesh%nPoin)
        call allocateArray(val_iMesh%iHostAlte_Poin, val_iMesh%nPoin, 10)
        
        
        call setupMeshReform_Overset(val_iMesh)
        
        call calIPoinProp(val_iMesh)
        
        call calIForcedHost(val_iMesh)
        
        call calIOrgnHost(val_iMesh)
        
        !call chkIOrgnHost(val_iMesh)
        
        call iniITempFlag
        
        do 
            call bldIPoinFront(val_iMesh, iTempFlag, nPoin)
            
            if(nPoin == 0) exit
            
            call updIPoinFront(val_iMesh, iTempFlag, nSucc)
            
            if(nSucc == nPoin) exit
        enddo
        
        call endIOrgnHost
        
        
        val_iMesh%iHost_Poin(:) = iOrgnHost(:)
        
        call calIHostAlte_Poin(val_iMesh)
        
        call calIExteRegnOvst(val_iMesh)
        
        call testIO_GlobalMesh_Valid(val_iMesh,'Ovst')
        
        return
        
    contains
        !
        subroutine iniITempFlag 
            implicit none
            
            
            iTempFlag(:) = iOrgnHost(:)
            
            do i=1,val_iMesh%nPoin
                if(iFordHost(i) == FLAG_FORCED) then
                    iTempFlag(i) = FLAG_EXCH
                endif
            enddo
            
        endsubroutine
        !
        subroutine endIOrgnHost 
            !
            integer:: i,j
            integer:: sateID
            
            do i=1,val_iMesh%nPoin
                if(iOrgnHost(i) /= FLAG_VALID) then
                    iOrgnHost(i) = FLAG_INVAL
                endif
            enddo
            
            do i=1,val_iMesh%nPoin
                if(iOrgnHost(i) == FLAG_VALID) then
                    do j=val_iMesh%kSate_Poin(i)+1, &
                         val_iMesh%kSate_Poin(i+1)
                        sateID = val_iMesh%iSate_Poin(j,1)
                        
                        if(iOrgnHost(sateID) /= FLAG_VALID) then
                            iOrgnHost(sateID) = FLAG_HOST
                        endif
                    enddo
                endif
            enddo
            
        endsubroutine
        !
    endsubroutine
    !
    !---------------------------------------------
    subroutine setupMeshReform_Overset(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_calDivRegin
        use mod_Interface_AllocateArray
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        !
        integer:: i
        
        
        call allocateArray(iHost_Poin, val_iMesh%nPoin)
        call allocateArray(iFordHost , val_iMesh%nPoin)
        call allocateArray(iOrgnHost , val_iMesh%nPoin)
        iHost_Poin(:) = FLAG_NONE
        iFordHost(:)  = FLAG_NONE
        iOrgnHost(:)  = FLAG_NONE
        
        call allocateArray(iRegn_Poin, val_iMesh%nPoin)
        call allocateArray(kRDis_Poin, val_iMesh%nPoin, val_iMesh%nRegn)
        call allocateArray(iRDis_Poin, val_iMesh%nPoin, val_iMesh%nRegn)
        call allocateArray(iRadu_Poin, val_iMesh%nPoin, 3)
        iRegn_Poin(:)   = 0
        kRDis_Poin(:,:) = 0
        iRDis_Poin(:,:) = 0.0
        iRadu_Poin(:,:) = 0.0
        
        
        if(allocated(iDivRegnWall)) then
            do i=1,size(iDivRegnWall)
                call nullDivRegin(iDivRegnWall(i))
            enddo
            deallocate(iDivRegnWall)
        endif
        
        allocate(iDivRegnWall(val_iMesh%nRegn))
        
        call iniIDivRegnFull(val_iMesh)
        
    contains
        !
        subroutine iniIDivRegnFull(val_iMesh)
            use mod_TypeDef_Mesh
            use mod_calDivRegin
            implicit none
            type(typ_GlobalMesh),intent(in):: val_iMesh
            !
            integer:: i,nPoinPerFullBlock
            logical:: isOk
        
            
            iDivRegnFull%nPoin = val_iMesh%nPoin
            call allocateArray(iDivRegnFull%iPoin, iDivRegnFull%nPoin)
            do i=1,val_iMesh%nPoin
                iDivRegnFull%iPoin(i) = i
            enddo
        
            nPoinPerFullBlock = ceiling((val_iMesh%nPoin+0.0)**(1.0/nDim))
            nPoinPerFullBlock = ceiling(sqrt(val_iMesh%nPoin+0.0))
            
            call initDivRegin(iDivRegnFull, val_iMesh%iCoor_Poin,   &
                            val_iMesh%nPoin, nPoinPerFullBlock      )
                
            call calDivRegin(iDivRegnFull, isOk)
        
            if(.not.isOk) then
                write(ioPort_Out,*) 'calDivRegin(iDivRegnFull, isOk) is not OK.'
                stop
            endif
            
        endsubroutine
        !
    endsubroutine
    !
    !---------------------------------------------
    subroutine calIPoinProp(val_iMesh)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        !
        call calIRegn_Poin(val_iMesh)
        
        call calIRadu_Poin(val_iMesh)
        
        call calIRDis_Poin(val_iMesh)
        !
        call wrtIRDis_Poin(val_iMesh)
        !
    contains
        !
        subroutine calIRegn_Poin(val_iMesh)
            use mod_TypeDef_Mesh
            implicit none
            type(typ_GlobalMesh),intent(in):: val_iMesh
            !
            integer:: i,j,k
            integer:: domaID,markID,poinID
            
            
            iRegn_Poin(:) = 0
            do i=1,val_iMesh%nRegn
                do j=1,val_iMesh%iRegn(i)%nDoma
                    domaID = val_iMesh%iRegn(i)%domaList(j)
                    
                    iRegn_Poin(val_iMesh%iDoma(domaID)%iPoin(:)) = i
                enddo
                
                do j=1,val_iMesh%iRegn(i)%nMark
                    markID = val_iMesh%iRegn(i)%markList(j)
                    
                    if(val_iMesh%iMark(markID)%KIND /= MARK_WALL) cycle
                    
                    iRegn_Poin(val_iMesh%iMark(markID)%iPoin(:)) = -i
                enddo  
            enddo
            
        endsubroutine
        !
        subroutine calIRadu_Poin(val_iMesh)
            use mod_TypeDef_Mesh
            use mod_vectorAlgebra
            implicit none
            type(typ_GlobalMesh),intent(in):: val_iMesh
            !
            integer:: i,j,k
            integer:: sateID
            real(kind=REALLEN):: dCoor(nDim)
            real(kind=REALLEN):: iDis,minDis,maxDis
            
            
            do i=1,val_iMesh%nPoin
                minDis = 0.0
                maxDis = 0.0
                
                do j=val_iMesh%kSate_Poin(i)+1,val_iMesh%kSate_Poin(i+1)
                    sateID = val_iMesh%iSate_Poin(j,1)
                    
                    dCoor(:) = val_iMesh%iCoor_Poin(:,     i) - &
                               val_iMesh%iCoor_Poin(:,sateID)
                    
                    call vector_length(nDim, dCoor, iDis)
                    
                    if(j == val_iMesh%kSate_Poin(i)+1) then
                        minDis = iDis
                        maxDis = iDis
                    else
                        minDis = min(minDis, iDis)
                        maxDis = max(maxDis, iDis)
                    endif
                enddo
                
                iRadu_Poin(i,1) = 0.5*(minDis + maxDis)
                iRadu_Poin(i,2) = minDis
                iRadu_Poin(i,3) = maxDis
            enddo
        
            !write(ioPort_Out,*) iRadu_Poin(:,1)
            
        endsubroutine
        !
        subroutine calIRDis_Poin(val_iMesh)
            use mod_TypeDef_Mesh
            use mod_calDivRegin
            use mod_vectorAlgebra
            implicit none
            type(typ_GlobalMesh),intent(in):: val_iMesh
            !
            integer:: i,j,k
            integer:: regnID,poinID
            integer:: nPoinPerWallBlock
            real(kind=REALLEN):: iDis
            logical:: isOk
            
            nPoinPerWallBlock = ceiling((val_iMesh%nPoin+0.0)**(1.0/nDim))
            nPoinPerWallBlock = ceiling(sqrt(val_iMesh%nPoin+0.0)/10)
            
            do i=1,val_iMesh%nRegn
                call setupIDivRegnWall(iDivRegnWall(i),i)
                if(iDivRegnWall(i)%nPoin == 0) then
                    kRDis_Poin(:,i) = 0
                    iRDis_Poin(:,i) = 1.0E+10
                    cycle
                endif
                
                call initDivRegin(iDivRegnWall(i),  &
                            val_iMesh%iCoor_Poin,   &
                            val_iMesh%nPoin,        &
                            nPoinPerWallBlock       )
                
                call calDivRegin(iDivRegnWall(i), isOk)
                
                if(.not.isOk) then
                    write(ioPort_Out,*) 'calDivRegin(iDivRegnWall(i)) is not OK. '
                    stop
                endif
                
                do j=1,val_iMesh%nPoin
                    regnID = iRegn_Poin(j)
                    
                    if(regnID == -i) then
                        kRDis_Poin(j,i) = j
                        iRDis_Poin(j,i) = 0.0
                    else
                        call getMinDisInDivRegn(val_iMesh%iCoor_Poin(:,j),  &
                                            iDivRegnWall(i), poinID, iDis   )
                        
                        kRDis_Poin(j,i) = poinID
                        iRDis_Poin(j,i) = iDis
                    endif
                enddo
            enddo
            
        endsubroutine
        !
        subroutine wrtIRDis_Poin(val_iMesh)
            use mod_TypeDef_Mesh
            use mod_WorkPath
            use mod_PreDefine_IOPort
            implicit none
            type(typ_GlobalMesh),intent(in):: val_iMesh
            !
            integer:: i,j,k
            character(len=64):: VStr
            character(len=32):: zoneTypeStr
            
            if(val_iMesh%nRegn == 1) then
                VStr = '"RDis1"'
            elseif(val_iMesh%nRegn == 2) then
                VStr = '"RDis1","RDis2"'
            elseif(val_iMesh%nRegn == 3) then
                VStr = '"RDis1","RDis2","RDis3"'
            elseif(val_iMesh%nRegn == 4) then
                VStr = '"RDis1","RDis2","RDis3","RDis4"'
            elseif(val_iMesh%nRegn == 5) then
                VStr = '"RDis1","RDis2","RDis3","RDis4","RDis5"'
            else
            endif
            !
            
            open(ioPort_FULL,file=trim(testFullPath)//'/testIO_GlobalMesh_RDis.plt',asynchronous='yes',status='unknown')
    
            if(nDim == 2) then
                write(ioPort_FULL,*)'VARIABLES="X","Y",',trim(VStr)
                zoneTypeStr = 'FEQUADRILATERAL'
            elseif(nDim == 3) then
                write(ioPort_FULL,*)'VARIABLES="X","Y","X",',trim(VStr)
                zoneTypeStr = 'FEBRICK'
            endif
                write(ioPort_FULL,*)'ZONE DATAPACKING = POINT,',        &
                                        ' NODES =', val_iMesh%nPoin,    &
                                        ' ELEMENTS =', val_iMesh%nElem, &
                                        ' ZONETYPE = ',trim(adjustl(zoneTypeStr))
    
                do i=1,val_iMesh%nPoin
                    write(ioPort_FULL,*) val_iMesh%iCoor_Poin(:,i),iRDis_Poin(i,:)
                enddo
    
                do i=1,val_iMesh%nElem
                    write(ioPort_FULL,*) val_iMesh%iElem(:,i)
                enddo
            close(ioPort_FULL)
            
        endsubroutine
        !
        subroutine setupIDivRegnWall(val_iDivRegn,val_regnID)
            implicit none
            type(typ_DivRegin),intent(inout):: val_iDivRegn
            integer,intent(in):: val_regnID
            !
            integer:: i,iCount,nPoin,poinID
            
            
            iCount = 0
            do i=1,size(iRegn_Poin)
                if(iRegn_Poin(i) == -val_regnID) then
                    iCount = iCount + 1
                endif
            enddo
            
            val_iDivRegn%nPoin = iCount
            if(val_iDivRegn%nPoin == 0) return
            
            allocate(val_iDivRegn%iPoin(val_iDivRegn%nPoin))
            
            iCount = 0
            do i=1,size(iRegn_Poin)
                if(iRegn_Poin(i) == -val_regnID) then
                    iCount = iCount + 1
                    
                    val_iDivRegn%iPoin(iCount) = i
                endif
            enddo
            
        endsubroutine
        !
        subroutine getMinDisInDivRegn(val_iCoor, &
                    val_iDivRegn,val_poinID,val_iDis)
            use mod_calDivRegin
            implicit none
            real(kind=REALLEN),intent(in):: val_iCoor(nDim)
            type(typ_DivRegin),intent(in):: val_iDivRegn
            integer,intent(out):: val_poinID
            real(kind=REALLEN),intent(out):: val_iDis
            !
            integer:: i,blockID,nearBD,poinID,minID
            real(kind=REALLEN):: iDis,minDis
            
            if(val_iDivRegn%nPoin <= 0) then
                val_poinID = 0
                val_iDis   = 1.0E+10
                return
            endif
            
            call getNearestBlockID(val_iCoor, val_iDivRegn, nearBD)
            call getIDisInBlock(val_iCoor, val_iDivRegn, nearBD, iDis, poinID)
            minDis = iDis
            minID  = poinID
            
            do i=1,val_iDivRegn%nFinalBlock
                blockID = val_iDivRegn%iFinalBlock(i)
                if(blockID == nearBD) cycle
                
                call getIDisToBlock(val_iCoor, val_iDivRegn, blockID, iDis)
                
                if(iDis < minDis) then
                    call getIDisInBlock(val_iCoor, val_iDivRegn, blockID, iDis, poinID)
                    
                    if(iDis < minDis) then
                        minDis = iDis
                        minID  = poinID
                    endif
                endif
            enddo
            
            val_poinID = val_iDivRegn%iPoin(minID)
            val_iDis   = minDis
            
        endsubroutine
        !
    endsubroutine
    !
    !---------------------------------------------
    subroutine calIForcedHost(val_iMesh)
        use mod_PreDefine_Flag
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        !
        integer:: i,regnID
        
        iFordHost(:) = FLAG_NONE
        do i=1,val_iMesh%nPoin
            regnID = iRegn_Poin(i)
            
            if(regnID < 0) then !物面点
                iFordHost(i) = FLAG_VALID
                
            elseif(isPoinInViscLayer(iRadu_Poin(i,2),       &
                                     iRadu_Poin(i,3),       &
                                     iRDis_Poin(i,regnID),  &
                                     iViscPoinSensor,       &
                                     viscLayerHeight ) ) then
                iFordHost(i) = FLAG_VALID
                
            endif
        enddo
        
        do i=1,val_iMesh%nMark
            if(val_iMesh%iMark(i)%KIND == MARK_NONE) then
                iFordHost(val_iMesh%iMark(i)%iPoin(:)) = FLAG_FORCED
            endif
        enddo
        
    endsubroutine
    !
    !---------------------------------------------
    subroutine calIOrgnHost(val_iMesh)
        use mod_PreDefine_Flag
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
        !
        integer:: i,regnID
        
        
        iOrgnHost(:) = FLAG_NONE
        do i=1,val_iMesh%nPoin
            if(iFordHost(i) == FLAG_VALID) then
                iOrgnHost(i) = FLAG_VALID
            elseif(iFordHost(i) /= FLAG_FORCED) then
                call getNearestRegnID(iRDis_Poin(i,:), val_iMesh%nRegn, regnID)
                
                if(regnID == iRegn_Poin(i)) then
                    iOrgnHost(i) = FLAG_VALID
                elseif(isPoinUnLapped(val_iMesh,i)) then
                    iOrgnHost(i) = FLAG_VALID
                endif
            endif
        enddo
        
        !val_iMesh%iHost_Poin(:) = iOrgnHost(:)
        !call testIO_GlobalMesh_Valid(val_iMesh,'O1')
        
        call trtOrgnHost_BCInfection(val_iMesh)
        
        !val_iMesh%iHost_Poin(:) = iOrgnHost(:)
        !call testIO_GlobalMesh_Valid(val_iMesh,'O2')
        
        call trtOrgnHost_BadPoint(val_iMesh)
        
        !val_iMesh%iHost_Poin(:) = iOrgnHost(:)
        !call testIO_GlobalMesh_Valid(val_iMesh,'O3')
        
    contains
        !
        subroutine trtOrgnHost_BCInfection(val_iMesh)
            use mod_TypeDef_Mesh
            implicit none
            type(typ_GlobalMesh),intent(in):: val_iMesh
            !
            integer:: i,j
            integer:: poinID,sateID,nAdd
            integer:: iInfe_Poin(val_iMesh%nPoin)
            
            
            iInfe_Poin(:) = 0
            do i=1,val_iMesh%nMark
                if(val_iMesh%iMark(i)%KIND == MARK_NONE) cycle
                
                do j=1,val_iMesh%iMark(i)%nPoin
                    poinID = val_iMesh%iMark(i)%iPoin(j)
                    
                    if(iOrgnHost(poinID) == FLAG_VALID) then
                        iInfe_Poin(poinID) = 1
                    endif
                enddo
            enddo
            
            do 
                nAdd = 0
                
                do i=1,val_iMesh%nPoin
                    if(iInfe_Poin(i) == 1) then
                        do j=val_iMesh%kSate_Poin(i)+1, val_iMesh%kSate_Poin(i+1)
                            sateID = val_iMesh%iSate_Poin(j,1)
                            
                            if((iInfe_Poin(sateID) == 0).and.   &
                               (iOrgnHost(sateID) == FLAG_VALID)) then
                                iInfe_Poin(sateID) = 1
                                nAdd = nAdd + 1
                            endif
                        enddo
                    endif
                enddo
                
                if(nAdd == 0) exit
            enddo
            
            do i=1,val_iMesh%nPoin
                if((iOrgnHost(i) == FLAG_VALID).and. &
                   (iInfe_Poin(i) == 0)         ) then 
                    iOrgnHost(i) = FLAG_NONE
                endif
            enddo
            
        endsubroutine
        !
        subroutine trtOrgnHost_BadPoint(val_iMesh)
            use mod_TypeDef_Mesh
            implicit none
            type(typ_GlobalMesh),intent(in):: val_iMesh
            
            integer:: i,j
            integer:: nTrt,nSate,nInvl,sateID
            
            
            do
                nTrt = 0
                do i=1,val_iMesh%nPoin
                    if(iOrgnHost(i) == FLAG_VALID) then
                        nSate = val_iMesh%kSate_Poin(i+1) - val_iMesh%kSate_Poin(i)
                        
                        nInvl = 0
                        do j=val_iMesh%kSate_Poin(i)+1,val_iMesh%kSate_Poin(i+1)
                            sateID = val_iMesh%iSate_Poin(j,1)
                            
                            if(iOrgnHost(sateID) /= FLAG_VALID) then
                                nInvl = nInvl + 1
                            endif
                        enddo
                
                        if(nInvl > nSate/2) then
                            iOrgnHost(i) = FLAG_NONE
                            nTrt = nTrt + 1
                        endif
                    endif
                enddo
                
                if(nTrt == 0) exit
            enddo
        
        endsubroutine
        !
    endsubroutine
    !
    !---------------------------------------------
    subroutine bldIPoinFront(val_iMesh,val_iFlag,val_nFront)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        integer,intent(inout):: val_iFlag(val_iMesh%nPoin)
        integer,intent(out):: val_nFront
        !
        integer:: i,j
        integer:: sateID,nPoin
        
        
        nPoin = 0
        do i=1,val_iMesh%nPoin
            if(val_iFlag(i) == FLAG_VALID) then
                do j=val_iMesh%kSate_Poin(i)+1,val_iMesh%kSate_Poin(i+1)
                    sateID = val_iMesh%iSate_Poin(j,1)
                    
                    if(val_iFlag(sateID) == FLAG_NONE) then
                        val_iFlag(sateID) = FLAG_HOST
                        nPoin = nPoin + 1
                    endif
                enddo
            endif
        enddo
        
        val_nFront = nPoin
        
    endsubroutine
    !
    !---------------------------------------------
    subroutine updIPoinFront(val_iMesh,val_iFlag,val_nSucc)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        integer,intent(inout):: val_iFlag(val_iMesh%nPoin)
        integer,intent(out):: val_nSucc
        !
        integer:: i
        integer:: nearID,nSucc
        real(kind=REALLEN):: iDis
        
        nSucc = 0
        do i=1,val_iMesh%nPoin
            if(val_iFlag(i) == FLAG_HOST) then
                call getNearPoinID(val_iMesh, i, nearID, iDis)
                    
                if(nearID == 0) then
                    iOrgnHost(i) = FLAG_VALID
                    val_iFlag(i) = FLAG_VALID
                        
                elseif(iDis < 0.5*iRadu_Poin(i,2)) then
                    iOrgnHost(i) = FLAG_EXCH
                    val_iFlag(i) = FLAG_EXCH
                        
                    nSucc = nSucc + 1
                    
                elseif(isPoinInnerValid(val_iMesh, nearID)) then
                    iOrgnHost(i) = FLAG_EXCH
                    val_iFlag(i) = FLAG_EXCH
                        
                    nSucc = nSucc + 1
            
                else
                    iOrgnHost(i) = FLAG_VALID
                    val_iFlag(i) = FLAG_VALID
                        
                endif
                    
            endif
        enddo
        
        val_nSucc = nSucc
        
    endsubroutine
    !
    !---------------------------------------------
    subroutine calIHostAlte_Poin(val_iMesh)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
        !
        integer,parameter:: maxSate = 100
        integer:: i,j
        integer:: nearID,nAlte
        real(kind=REALLEN):: iDis,jDis
        integer:: iAltePoin(maxSate)
        real(kind=REALLEN):: iAlteDist(maxSate)
        
        
        val_iMesh%iHostAlte_Poin(:,:) = 0
        
        do i=1,val_iMesh%nPoin
            if(val_iMesh%iHost_Poin(i) == FLAG_HOST) then
                call getNearPoinID(val_iMesh, i, nearID, iDis)
                
                call getAltePoin(val_iMesh, i, nearID, iAltePoin,&
                            iAlteDist, nAlte, maxSate           )
                
                val_iMesh%iHostAlte_Poin(i,1) = min(9,nAlte)
                
                do j=1,min(9,nAlte)
                    val_iMesh%iHostAlte_Poin(i,j+1) = iAltePoin(j)
                enddo
                
            endif
        enddo
        
    endsubroutine
    !
    !---------------------------------------------
    subroutine calIExteRegnOvst(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_Interface_AllocateArray
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
        !
        integer:: i,j,k
        integer:: sateID,nAdd,iAdd,jAdd,nSate
        integer:: iFlag_Poin(val_iMesh%nPoin)
        integer:: iFlag_Sate(val_iMesh%nSate)
        integer:: iSate_Add(val_iMesh%nSate,2)
        
        
        !---------------------------------------------
        val_iMesh%iExteRegnSANO%KIND    = EXTE_OVST
        val_iMesh%iExteRegnSANO%nPoin   = 0
        
        val_iMesh%iExteRegnSANO%nOrgnPoin = val_iMesh%nPoin
        call allocateArray(val_iMesh%iExteRegnSANO%kSate_Poin, val_iMesh%iExteRegnSANO%nOrgnPoin+1)
        val_iMesh%iExteRegnSANO%kSate_Poin(:) = 0
        
        
        !---------------------------------------------
        iFlag_Poin(:) = 0
        iFlag_Sate(:) = 0
        do i=1,val_iMesh%nPoin
            if(val_iMesh%iHost_Poin(i) == FLAG_VALID) then
                do j=val_iMesh%kSate_Poin(i)+1, &
                     val_iMesh%kSate_Poin(i+1)
                    sateID = val_iMesh%iSate_Poin(j,1)
                    
                    if(val_iMesh%iHost_Poin(sateID) == FLAG_HOST) then
                        iFlag_Poin(i) = 1
                        iFlag_Sate(j) = 1
                    endif
                enddo
            endif
        enddo
        
        
        !---------------------------------------------
        iSate_Add(:,:) = 0
        
        nAdd = 0
        do i=1,val_iMesh%nPoin
            if(iFlag_Poin(i) == 1) then
                iAdd = 0
                do j=val_iMesh%kSate_Poin(i)+1, &
                     val_iMesh%kSate_Poin(i+1)
                    if(iFlag_Sate(j) == 1) then
                        sateID = val_iMesh%iSate_Poin(j,1)
                        
                        call getIAddSate(val_iMesh, i, sateID, iSate_Add, nAdd+iAdd, jAdd)
                        
                        iAdd = iAdd + jAdd
                    endif
                enddo
                     
                val_iMesh%iExteRegnSANO%kSate_Poin(i+1) = iAdd
                nAdd = nAdd + iAdd
            endif
            
            val_iMesh%iExteRegnSANO%kSate_Poin(i+1) = val_iMesh%iExteRegnSANO%kSate_Poin(i) + &
                                                      val_iMesh%iExteRegnSANO%kSate_Poin(i+1)
        enddo
        
        !---------------------------------------------
        val_iMesh%iExteRegnSANO%nSate = nAdd
        call allocateArray(val_iMesh%iExteRegnSANO%iSate_Poin, nAdd, 2)
        val_iMesh%iExteRegnSANO%iSate_Poin(:,:) = 0
        
        val_iMesh%iExteRegnSANO%iSate_Poin(1:nAdd,:) = iSate_Add(1:nAdd,:)
        
    contains
        !
        subroutine getIAddSate(val_iMesh,val_poinL,val_poinR, &
                    val_iSate,val_iBegin,val_nAdd)
            use mod_TypeDef_Mesh
            use mod_Config
            implicit none
            type(typ_GlobalMesh),intent(in):: val_iMesh
            integer,intent(in):: val_poinL
            integer,intent(in):: val_poinR
            integer,intent(out):: val_iSate(val_iMesh%nSate,2)
            integer,intent(in):: val_iBegin
            integer,intent(out):: val_nAdd
            !
            integer:: i,j,iCount,jCount
            integer:: poinID,nSate
            integer:: nPoinForReplace = 3
            
            nSate = min(val_iMesh%iHostAlte_poin(val_poinR,1), nPoinForReplace)
            
            do i=1,nSate
                poinID = val_iMesh%iHostAlte_poin(val_poinR,i+1)
                
                val_iSate(val_iBegin+i,1) = poinID
                val_iSate(val_iBegin+i,2) = val_poinL
            enddo
            
            val_nAdd = nSate
            
        endsubroutine
        !
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    !
    subroutine getNearestValidPoinIDInDivBlock(val_iMesh,&
                val_poinID,val_blockID,val_nearID,      &
                val_minDis,val_iFlag                    )
        use mod_TypeDef_Mesh
        use mod_PreDefine_Flag
        use mod_vectorAlgebra
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        integer,intent(in):: val_poinID
        integer,intent(in):: val_blockID
        integer,intent(out):: val_nearID
        real(kind=REALLEN),intent(out):: val_minDis
        integer,intent(in):: val_iFlag
        !
        integer:: i,j
        integer:: poinIP,poinID,minID
        real(kind=REALLEN):: minDis,iDis,iVector(nDim)
        !
        
        minID  = 0
        minDis = 1.0E10
        
        do i=1,iDivRegnFull%iBlock(val_blockID)%nPoin
            poinIP = iDivRegnFull%iBlock(val_blockID)%iPoin(i)
            poinID = iDivRegnFull%iPoin(poinIP)
            
            if(iRegn_Poin(poinID) == iRegn_Poin(val_poinID)) cycle
            if((val_iFlag == 1).and.(iOrgnHost(poinID) /= FLAG_VALID)) cycle
            
            iVector(:) = val_iMesh%iCoor_Poin(:,poinID) - &
                         val_iMesh%iCoor_Poin(:,val_poinID)
             
            call vector_length(nDim, iVector, iDis)
                
            if(iDis < minDis) then
                minDis = iDis
                minID  = poinID
            endif
        enddo
        
        val_nearID = minID
        val_minDis = minDis
        
    endsubroutine
    !
    subroutine getNearestRegnID(val_iRDis,val_nRegn,val_regnID)
        implicit none
        real(kind=REALLEN),intent(in):: val_iRDis(val_nRegn)
        integer,intent(in):: val_nRegn
        integer,intent(out):: val_regnID
        !
        integer:: i,iID
            
        iID = 1
        do i=2,val_nRegn
            if(val_iRDis(i) < val_iRDis(iID)) then
                iID = i
            endif
        enddo
            
        val_regnID = iID
            
    endsubroutine
    !
    subroutine getNearPoinID(val_iMesh,val_poinID,val_nearID,val_minDis)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        integer,intent(out):: val_poinID
        integer,intent(out):: val_nearID
        real(kind=REALLEN),intent(out):: val_minDis
        !
        integer:: i,j
        integer:: blockID,nearID,minID
        real(kind=REALLEN):: iDis,jDis,minDis,iRadu
        real(kind=REALLEN):: iCoor(nDim)
        
        
        val_nearID = 0
        
        iRadu = iRadu_Poin(val_poinID,1)
        
        iCoor(:) = val_iMesh%iCoor_Poin(:,val_poinID)
        
        call getNearestBlockID(iCoor, iDivRegnFull, blockID)
        
        call getNearestValidPoinIDInDivBlock(val_iMesh, &
                    val_poinID, blockID, nearID, iDis, 1)
        
        minDis = iDis
        minID  = nearID
        
        
        !---------------------------------------------
        do i=1,iDivRegnFull%nFinalBlock
            blockID = iDivRegnFull%iFinalBlock(i)
            
            if(blockID == minID) cycle
            
            call getIDisToBlock(iCoor, iDivRegnFull, blockID, iDis)
            
            if(iDis < minDis) then
                call getNearestValidPoinIDInDivBlock(val_iMesh, &
                            val_poinID, blockID, nearID, jDis, 1)
                if(jDis < minDis) then
                    minDis = jDis
                    minID  = nearID
                endif
            endif
            
        enddo
        
        val_nearID = minID
        val_minDis = minDis
        
    endsubroutine
    !
    subroutine getAltePoin(val_iMesh,val_poinID,val_nearID, &
                val_iAltePoin,val_iAlteDist,val_nAlte,val_nAlteSize)
        use mod_TypeDef_Mesh
        use mod_vectorAlgebra
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        integer,intent(in):: val_poinID
        integer,intent(in):: val_nearID
        integer,intent(out):: val_iAltePoin(val_nAlteSize)
        real(kind=REALLEN),intent(out):: val_iAlteDist(val_nAlteSize)
        integer,intent(out):: val_nAlte
        integer,intent(in):: val_nAlteSize
        !
        integer:: i,j,k,iCount
        integer:: sateID,sateJD,sateKD
        integer:: iAlteList(val_nAlteSize)
        real(kind=REALLEN):: iAlteDist(val_nAlteSize)
        real(kind=REALLEN):: iVector(nDim),iDis
        logical:: isOk
        
        
        if(val_iMesh%iHost_Poin(val_nearID) /= FLAG_VALID) then
            val_nAlte = 0
            return
        endif
        
        iAlteList(:) = 0
        iAlteDist(:) = 0.0
        
        iAlteList(1) = val_nearID
        iVector(:) = val_iMesh%iCoor_Poin(:,val_poinID) - &
                     val_iMesh%iCoor_Poin(:,val_nearID)
        
        call vector_length(nDim, iVector, iDis) 
        iAlteDist(1) = iDis
        
        do i=val_iMesh%kSate_Poin(val_nearID)+1,val_iMesh%kSate_Poin(val_nearID+1)
            sateID = val_iMesh%iSate_Poin(i,1)
            if(val_iMesh%iHost_Poin(sateID) /= FLAG_VALID) cycle
            
            do j=val_iMesh%kSate_Poin(sateID)+1,val_iMesh%kSate_Poin(sateID+1)
                sateJD = val_iMesh%iSate_Poin(j,1)
                if(val_iMesh%iHost_Poin(sateJD) /= FLAG_VALID) cycle
                
                do k=val_iMesh%kSate_Poin(sateJD)+1,val_iMesh%kSate_Poin(sateJD+1)
                    sateKD = val_iMesh%iSate_Poin(j,1)
                    if(val_iMesh%iHost_Poin(sateKD) /= FLAG_VALID) cycle
                    
                    call insertPoin(val_iMesh, val_poinID, sateKD, val_nAlteSize, isOk)
                enddo
                
                call insertPoin(val_iMesh, val_poinID, sateJD, val_nAlteSize, isOk)
            enddo
            
            call insertPoin(val_iMesh, val_poinID, sateID, val_nAlteSize, isOk)
        enddo
        !
        iCount = 0
        do i=1,val_nAlteSize
            if(iAlteList(i) > 0) then
                iCount = iCount + 1
            endif
        enddo
        val_nAlte = iCount
        
        val_iAltePoin(:) = iAlteList(:)
        val_iAlteDist(:) = iAlteDist(:)
        
        return
        
    contains
        !
        subroutine insertPoin(val_iMesh,val_poinID,val_instID,val_nAlteSize,val_isOk)
            implicit none
            type(typ_GlobalMesh),intent(in):: val_iMesh
            integer,intent(in):: val_poinID
            integer,intent(in):: val_instID
            integer,intent(in):: val_nAlteSize
            logical,intent(out):: val_isOk
            !
            integer:: ii,jj
            
            iVector(:) = val_iMesh%iCoor_Poin(:,val_poinID) - &
                         val_iMesh%iCoor_Poin(:,val_instID)
            
            call vector_length(nDim, iVector, iDis) 
            
            do ii=1,val_nAlteSize
                if(val_instID == iAlteList(ii)) then
                    val_isOk = .false.
                    return
                endif
            enddo
            
            do ii=1,val_nAlteSize
                if(iAlteList(ii) == 0) then
                    iAlteList(ii) = val_instID
                    iAlteDist(ii) = iDis
                    
                    val_isOk = .true.
                    return
                elseif(iDis < iAlteDist(ii)) then
                    do jj=val_nAlteSize,ii+1,-1
                        iAlteList(jj) = iAlteList(jj-1)
                        iAlteDist(jj) = iAlteDist(jj-1)
                    enddo
                    
                    iAlteList(ii) = val_instID
                    iAlteDist(ii) = iDis
                
                    val_isOk = .true.
                    return
                endif
            enddo
            
            val_isOk = .false.
            return
            
        endsubroutine
        !
    endsubroutine
    !
    logical function isPoinUnLapped(val_iMesh,val_poinID)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        integer,intent(in):: val_poinID
        !
        integer:: i, blockID, nearID
        real(kind=REALLEN):: iDis, jDis, iCoor(nDim),iLimt
            
            
        !---------------------------------------------
        iCoor(:) = val_iMesh%iCoor_Poin(:,val_poinID)
        iLimt    = iRadu_Poin(val_poinID,1)
            
        !---------------------------------------------
        call getNearestBlockID(iCoor, iDivRegnFull, blockID)
            
        call getNearestValidPoinIDInDivBlock(val_iMesh, &
                    val_poinID, blockID, nearID, iDis, 0)
            
        if((nearID > 0).and.(iDis < iLimt)) then
            isPoinUnLapped = .false.
            return
        endif
            
        do i = 1,iDivRegnFull%nFinalBlock
            blockID = iDivRegnFull%iFinalBlock(i)
                
            call getIDisToBlock(iCoor, iDivRegnFull, blockID, iDis)
                
            if(iDis < iRadu_Poin(val_poinID,1)) then
                call getNearestValidPoinIDInDivBlock(val_iMesh, &
                            val_poinID, blockID, nearID, jDis, 0)
            
                if((nearID > 0).and.(jDis < iLimt)) then
                    isPoinUnLapped = .false.
                    return
                endif
            endif
        enddo
            
        isPoinUnLapped = .true.
        return
            
    endfunction
    !
    logical function isPoinInnerValid(val_iMesh,val_poinID)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        integer,intent(out):: val_poinID
        !
        integer:: i,sateID
            
        do i=val_iMesh%kSate_Poin(val_poinID)+1,    &
                    val_iMesh%kSate_Poin(val_poinID+1)
            sateID = val_iMesh%iSate_Poin(i,1)
                
            if(iOrgnHost(sateID) /= FLAG_VALID) then
                isPoinInnerValid = .false.
                return
            endif
        enddo
                 
        isPoinInnerValid = .true.
        return
            
    endfunction
    !
    logical function isPoinInViscLayer(val_minR,val_maxR,&
                val_iRDis,val_sensor,val_viscLayerThick )
        implicit none
        real(kind=REALLEN),intent(in):: val_minR
        real(kind=REALLEN),intent(in):: val_maxR
        real(kind=REALLEN),intent(in):: val_iRDis
        real(kind=REALLEN),intent(in):: val_sensor
        real(kind=REALLEN),intent(in):: val_viscLayerThick
            
            
        if(val_minR*val_sensor > val_maxR) then
            isPoinInViscLayer = .false.
        elseif(val_iRDis > val_viscLayerThick) then
            isPoinInViscLayer = .false.
        else
            isPoinInViscLayer = .true.
        endif
               
    endfunction
    !
endmodule
!
