!
module mod_calResErr
    use mod_PreDefine_Precision
    use mod_PreDefine_Flag
    use cudafor
    implicit none
    !
contains
    !
    subroutine calResErr(val_iParm, val_iMesh, val_iSolu,val_nSolu,val_iIter,val_isNAN)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_Config
        use mod_GPUThreadDim
        use mod_Interface_ArrayNAN
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        integer,intent(in):: val_nSolu
        integer,intent(in):: val_iIter
        logical,intent(inout):: val_isNAN
        !
        integer:: i,j,istat
        real(kind=REALLEN):: ResErrH(val_nSolu),ResMaxH(val_nSolu),ResRefi
        real(kind=REALLEN):: delV

        if(KIND_HARDWARE == _CPU) then
            ResErrH(:) = 0.0
            ResMaxH(:) = 0.0

            do i=1,val_iSolu%nNode
                if(val_iMesh%iCalc_Poin(i) /= FLAG_VALID) cycle

                do j=1,val_nSolu
                    delV = abs(val_iSolu%Res_All(i,j))

                    ResErrH(j) = ResErrH(j) + delV**2
                    ResMaxH(j) = max(ResMaxH(j) ,delV)
                end do
            end do

        elseif(KIND_HARDWARE == _GPU) then
            istat = cudaDeviceSynchronize()
            val_iParm%ResErr_d(:) = 0.0
            val_iParm%ResErrMax_d(:) = 0.0
            istat = cudaDeviceSynchronize()

            call calResErr_Global<<<nThreadBlock(IP_BP,IP_NT26),64>>>                    &
                    (val_iMesh%iCalc_Poin_d, val_iSolu%Res_All_d,&
                    val_iParm%ResErr_d, val_iParm%ResErrMax_d, val_nSolu, val_iSolu%nNode)

            !===================
            istat = cudaDeviceSynchronize()

            ResErrH(:) = val_iParm%ResErr_d(:)
            ResMaxH(:) = val_iParm%ResErrMax_d(:)
        end if

        !===================
        val_isNAN = isArrayNAN(ResErrH, val_nSolu)

        do i=1,val_nSolu
            ResErrH(i) = max(ResErrH(i),1.0e-36)
        enddo

        if(val_iParm%isResErrRedCalculated == .false.) then
            val_iParm%ResErrRef(1       ) = ResErrH(1)
            val_iParm%ResErrRef(2:nDim+1) = max(max(ResErrH(2),ResErrH(3)),ResErrH(nDim+1))
            val_iParm%ResErrRef(nDim+2: ) = ResErrH(nDim+2:)
            val_iParm%isResErrRedCalculated = .true.
        end if

        do i=1,val_nSolu
            ResRefi = val_iParm%ResErrRef(i)

            val_iParm%ResErrAbs(i) = ResErrH(i)
            val_iParm%ResErrMax(i) = ResMaxH(i)
            val_iParm%ResErrLog(i) =  0.5*log10(ResErrH(i)/ResRefi)
        enddo

    endsubroutine
    !
    attributes(global) subroutine calResErr_Global(val_iCalc_Poin,&
            val_deltSolu,val_ResErr,val_ResErrMax,val_nSolu,val_nPoin)
        implicit none
        integer           ,device:: val_iCalc_Poin(val_nPoin)
        real(kind=REALLEN),device:: val_deltSolu(val_nPoin,val_nSolu)
        real(kind=8      ),device:: val_ResErr(val_nSolu)
        real(kind=8      ),device:: val_ResErrMax(val_nSolu)
        integer           , value:: val_nSolu
        integer           , value:: val_nPoin

        integer:: i,j,iID
        real(kind=8),shared:: iSRes(16),iMRes(16)
        real(kind=8):: iRes,rstat,dVar

        iID = threadIdx%x
        i = (blockIdx%x-1)*blockDim%x+threadIdx%x

        if(iID <= val_nSolu) then
            iSRes(iID) = 0.0
            iMRes(iID) = 0.0
        endif
        call syncthreads()

        if(i <= val_nPoin .and. &
           val_iCalc_Poin(i) == FLAG_VALID) then
            do j=1,val_nSolu
                dVar = val_deltSolu(i,j)
                iRes = dVar*dVar

                rstat = atomicAdd(iSRes(j),      iRes)
                rstat = atomicMax(iMRes(j), abs(dVar))
            enddo
        endif

        call syncthreads()
        if(iID <= val_nSolu) then
            iSRes(iID) = iSRes(iID)/val_nPoin
            rstat = atomicAdd(val_ResErr(   iID), iSRes(iID))
            rstat = atomicMax(val_ResErrMax(iID), iMRes(iID))
        endif

    endsubroutine
    !
    logical function isConverged(val_iParm)
        use mod_TypeDef_Parm
        implicit none
        type(typ_Parm    ),intent(in):: val_iParm
        !
        integer:: i

        do i=1,val_iParm%nResErrVar
            if(val_iParm%ResErrLog(i) > val_iParm%ResErrLim(i)) then
                isConverged = .false.
                return
            endif
        enddo

        isConverged = .true.
        return

    endfunction
    !
endmodule
!
