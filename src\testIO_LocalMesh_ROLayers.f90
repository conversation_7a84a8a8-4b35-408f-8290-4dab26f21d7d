!
subroutine calILayer_Poin(val_iMesh,val_iLayer)
    use mod_TypeDef_Mesh
    implicit none
    type(typ_Mesh),intent(in   ):: val_iMesh
    integer       ,intent(inout):: val_iLayer(val_iMesh%nPoin)
    !
    integer:: ii,i,j,k
    integer:: beleID,sateID
    
    val_iLayer(:) = 0
    
    do i=1,val_iMesh%nMark
        if(val_iMesh%iMarkProp(i,2) == MARK_WALL .or. &
           val_iMesh%iMarkProp(i,2) == MARK_EQTW .or. &
           val_iMesh%iMarkProp(i,2) == MARK_EULW .or. &
           val_iMesh%iMarkProp(i,2) == MARK_MOVW ) then
            do j=1,val_iMesh%iMark(i)%nElem
                beleID = val_iMesh%iMark(i)%iElem(j)
                do k=1,2*nDim-2
                    val_iLayer(val_iMesh%iBele(k,beleID)) = 1
                enddo
            enddo
        endif
    enddo
    
    do ii=1,1000
        do i=1,val_iMesh%nPoin
            if(val_iLayer(i) == ii) then
                do j=val_iMesh%kSate_Poin(i)+1,val_iMesh%kSate_Poin(i+1)
                    sateID = val_iMesh%iSate_Poin(j,1)
                    if(val_iLayer(sateID) == 0) then
                        val_iLayer(sateID) = ii+1
                    endif
                enddo
            endif
        enddo
    enddo
    
endsubroutine
!   
subroutine testIO_LocalMesh_ROLayers(val_iMesh,val_iPart,val_iIter)
    use mod_PreDefine_Dimension
    use mod_PreDefine_IOPort
    use mod_PreDefine_Flag
    use mod_TypeDef_Mesh
    use mod_WorkPath
    use mod_Config
    use mod_strOfNumber
    use mod_Interface_AllocateArray
    implicit none
    type(typ_Mesh),intent(in):: val_iMesh
    integer,intent(in):: val_iPart
    integer,intent(in):: val_iIter
    !
    integer:: ii,i,j,elemID,poinID,beleID
    character(len=STRLEN):: string_varList
    character(len=STRLEN):: string_ZoneType
    character(len=STRLEN):: string_MarkType
    character(len=STRLEN):: string_VarShare
    character(len=STRLEN):: fileName
    integer,allocatable:: iLayer_Poin(:)
    
    
    if(nDim == 2) then
        string_varList  = 'VARIABLES="X","Y","Index","iLayer" '
        string_ZoneType = 'FEQUADRILATERAL'
        string_MarkType = 'FELINESEG'
        string_VarShare = '([1-4]=1)'
    elseif(nDim == 3) then
        string_varList  = 'VARIABLES="X","Y","Z","Index","iLayer" '
        string_ZoneType = 'FEBRICK'
        string_MarkType = 'FEQUADRILATERAL'
        string_VarShare = '([1-5]=1)'
    else
        return
    endif
    
    fileName = strTwo_Number(val_iPart)//'_'//strSix_Number(val_iIter)//'.plt'
    
    !=================================
    !=================================
    call allocateArray(iLayer_Poin, val_iMesh%nPoin)
    call calILayer_Poin(val_iMesh, iLayer_Poin)
    
    
    !=================================
    !=================================
    open(ioPort_FULL,file=trim(testFullPath)//'/LocalMesh_Re_'//trim(fileName),asynchronous='yes',status='unknown')
    
        write(ioPort_FULL,*) trim(adjustl(string_varList))
        
        write(ioPort_FULL,"(A,I10,A,I10,A)")'ZONE DATAPACKING = POINT, NODES =', val_iMesh%nPoin,   &
                                ', ELEMENTS =', val_iMesh%nPoin-1,', ZONETYPE = FELINESEG'
            
        do j=1,val_iMesh%nPoin
            if(nDim == 2) then
            write(ioPort_FULL,"(2E14.5,2I10)") val_iMesh%iCoor_Poin(j,:),j,iLayer_Poin(j)
            elseif(nDim == 3) then
            write(ioPort_FULL,"(3E14.5,2I10)") val_iMesh%iCoor_Poin(j,:),j,iLayer_Poin(j)
            endif
        enddo
            
        do j=1,val_iMesh%nPoin-1
            write(ioPort_FULL,"(2I10)") j,j+1
        enddo
    
    close(ioPort_FULL)
    
    !=================================
    !=================================
    open(ioPort_FULL,file=trim(testFullPath)//'/LocalMesh_ReLayers_'//trim(fileName),asynchronous='yes',status='unknown')
    
    do i = 1,val_iMesh%nDoma
        if(val_iMesh%iDoma(i)%nValdElem <= 0) cycle
        
        write(ioPort_FULL,*) trim(adjustl(string_varList))
        if(i == 1) then
            write(ioPort_FULL,*)'ZONE DATAPACKING = POINT, NODES =', val_iMesh%nPoin,   &
                                    ', ELEMENTS =', val_iMesh%iDoma(i)%nValdElem,       &
                                    ', ZONETYPE = ',trim(adjustl(string_ZoneType))
            
            do j=1,val_iMesh%nPoin
                if(nDim == 2) then
                write(ioPort_FULL,"(2E14.5,2I10)") val_iMesh%iCoor_Poin(j,:),j,iLayer_Poin(j)
                elseif(nDim == 3) then
                write(ioPort_FULL,"(3E14.5,2I10)") val_iMesh%iCoor_Poin(j,:),j,iLayer_Poin(j)
                endif
            enddo
            
        else
            write(ioPort_FULL,*)'ZONE DATAPACKING = POINT, NODES =', val_iMesh%nPoin,   &
                                    ', ELEMENTS =', val_iMesh%iDoma(i)%nValdElem,       &
                                    ', ZONETYPE = ',trim(adjustl(string_ZoneType)),     &
                                    ', VARSHARELIST=',trim(adjustl(string_VarShare))
        endif
        
        do j=1,val_iMesh%iDoma(i)%nElem
            elemID = val_iMesh%iDoma(i)%iElem(j)
            
            if(val_iMesh%iFlag_Elem(elemID) /= FLAG_VALID) cycle
            
            write(ioPort_FULL,*) val_iMesh%iElem(:,elemID)
        enddo
    enddo
    
    close(ioPort_FULL)
    
    !=================================
    !=================================
    open(ioPort_FULL,file=trim(testFullPath)//'/LocalBC_ReLayers_'//trim(fileName),asynchronous='yes',status='unknown')
    
    ii = 0
    do i = 1,val_iMesh%nMark
        if(val_iMesh%iMark(i)%nElem <= 0) cycle
        if(val_iMesh%iMarkProp(i,2) == MARK_NONE) cycle
        if(val_iMesh%iMarkProp(i,2) == MARK_ATTA) cycle
                
        ii = ii + 1
        write(ioPort_FULL,*) trim(adjustl(string_varList))
        if(i == 1) then
            write(ioPort_FULL,*)'ZONE DATAPACKING = POINT, NODES =', val_iMesh%nBCPoin,     &
                                    ', ELEMENTS =', val_iMesh%iMark(i)%nElem,               &
                                    ', ZONETYPE = ',trim(adjustl(string_MarkType))
            
        else
            write(ioPort_FULL,*)'ZONE DATAPACKING = POINT, NODES =', val_iMesh%nBCPoin,     &
                                    ', ELEMENTS =', val_iMesh%iMark(i)%nElem,               &
                                    ', ZONETYPE = ',trim(adjustl(string_MarkType)),         &
                                    ', VARSHARELIST=',trim(adjustl(string_VarShare))
        endif
        
        if(ii == 1) then
            do j=1,val_iMesh%nBCPoin
                poinID = val_iMesh%iMapBToG_Poin(j)
                if(nDim == 2) then
                write(ioPort_FULL,"(2E14.5,2I10)") val_iMesh%iCoor_Poin(poinID,:),j,iLayer_Poin(poinID)
                elseif(nDim == 3) then
                write(ioPort_FULL,"(3E14.5,2I10)") val_iMesh%iCoor_Poin(poinID,:),j,iLayer_Poin(poinID)
                endif
            enddo
            
        endif
        
        do j=1,val_iMesh%iMark(i)%nElem
            beleID = val_iMesh%iMark(i)%iElem(j)
            write(ioPort_FULL,*) val_iMesh%iMapGToB_Poin(val_iMesh%iBele(1:2*nDim-2,beleID))
        enddo
        
    enddo
    
    close(ioPort_FULL)
    
endsubroutine
!
subroutine testIO_LocalMesh_ReOrdering(val_iMesh,val_iPart,val_iIter)
    use mod_Config
    use mod_TypeDef_Mesh
    implicit none
    type(typ_Mesh),intent(in):: val_iMesh
    integer,intent(in):: val_iPart
    integer,intent(in):: val_iIter
endsubroutine