!
subroutine testIO_GlobalMesh_WDis(val_iMesh,val_GDis,val_iIter)
    use mod_PreDefine_Dimension
    use mod_PreDefine_IOPort
    use mod_TypeDef_Mesh
    use mod_WorkPath
    use mod_strOfNumber
    implicit none
    type(typ_GlobalMesh),intent(in):: val_iMesh
    real(kind=REALLEN),intent(in):: val_GDis(val_iMesh%nPoin)
    integer,intent(in):: val_iIter
    !
    integer:: i,j,elemID
    character(len=STRLEN):: string_varList
    character(len=STRLEN):: string_ZoneType
    character(len=STRLEN):: string_VarShare
    character(len=STRLEN):: fileName
    
    
    if(nDim == 2) then
        string_varList  = 'VARIABLES="X","Y","iWDis" '
        string_ZoneType = 'FEQUADRILATERAL'
        string_VarShare = '([1-3]=1)'
    elseif(nDim == 3) then
        string_varList  = 'VARIABLES="X","Y","Z","iWDis" '
        string_ZoneType = 'FEBRICK'
        string_VarShare = '([1-4]=1)'
    else
        return
    endif
    
    fileName = 'GlobalWDis_'//trim(strSix_Number(val_iIter))//'.plt'
    
    open(ioPort_FULL,file=trim(testFullPath)//'/'//trim(fileName),asynchronous='yes',status='unknown')
    
    do i = 1,val_iMesh%nDoma
        write(ioPort_FULL,*) trim(adjustl(string_varList))
        if(i == 1) then
            write(ioPort_FULL,*)'ZONE DATAPACKING = POINT, NODES =', val_iMesh%nPoin,   &
                                    ', ELEMENTS =', val_iMesh%iDoma(i)%nElem,           &
                                    ', ZONETYPE = ',trim(adjustl(string_ZoneType))
            
            do j=1,val_iMesh%nPoin
                write(ioPort_FULL,*) val_iMesh%iCoor_Poin(:,j),val_GDis(j)
            enddo
            
        else
            write(ioPort_FULL,*)'ZONE DATAPACKING = POINT, NODES =', val_iMesh%nPoin,   &
                                    ', ELEMENTS =', val_iMesh%iDoma(i)%nElem,           &
                                    ', ZONETYPE = ',trim(adjustl(string_ZoneType)),     &
                                    ', VARSHARELIST=',trim(adjustl(string_VarShare))
        endif
        
        do j=1,val_iMesh%iDoma(i)%nElem
            elemID = val_iMesh%iDoma(i)%iElem(j)
            
            write(ioPort_FULL,*) val_iMesh%iElem(:,elemID)
        enddo
    enddo
    
    close(ioPort_FULL)
    
endsubroutine
!