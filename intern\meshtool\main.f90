!
program main
    use mod_<PERSON>sh
    implicit none
    
    call initWorkPath
    
    write(*,*) 'Doing: iniIn<PERSON>esh'
    call iniIn<PERSON><PERSON>
    
    write(*,*) 'Doing: transformToGlobalMesh'
    call transformToGlobalMesh
    
    write(*,*) 'Doing: iniGlobalMesh'
    call iniGlobalMesh
    
    write(*,*) 'Doing: writeGlobalMesh'
    call writeGlobalMesh

    !write(*,*) 'iGlobalMesh%nElem = ',iGlobalMesh%nElem

    write(*,*) 'All is over~'
    
end
