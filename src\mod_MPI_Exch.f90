!
!---------------------------------------------
!
!---------------------------------------------
module mod_MPI_Exch
    use mod_PreDefine_Environment
    use mod_PreDefine_Precision
    use mod_PreDefine_ExchKind
    use mod_PreDefine_IOPort
    use mod_MPIEnvironment
    implicit none
    !
contains
    !---------------------------------------------
    !parallel computation init
    !---------------------------------------------
    subroutine mimd_init
        use mod_PreDefine_IOPort
        implicit none

#ifdef GFLOW_WITH_MPI

        if(.not.USING_MPI) then
            myID     = 0
            nProcess = 1
            
            return
        endif
        
        !��ʼ��MPI����
        call MPI_INIT(iError)
        if(iError /= 0) then
            write(ioPort_Out,*) 'MPI_INIT error!'
            stop
        endif
    
        !��ȡ�����̱��
        call MPI_COMM_RANK(MPI_COMM_WORLD,myID,iError)
        if(iError /= 0) then
            write(ioPort_Out,*) 'MPI_COMM_RANK error!'
            stop
        endif
    
        !��ȡ�ܽ�����
        call MPI_COMM_SIZE(MPI_COMM_WORLD,nProcess,iError)
        if(iError /= 0) then
            write(ioPort_Out,*) 'MPI_COMM_SIZE error!'
            stop
        endif
#endif
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine mimd_DataExch(val_nExch,val_iExch,val_isInteger)
        use mod_TypeDef_MeshComponent
        implicit none
        integer,intent(in):: val_nExch
        type(typ_ExchRegn),intent(inout):: val_iExch(val_nExch)
        integer,intent(in),optional:: val_isInteger
        !
        integer:: i
        integer:: dType,nData
        integer:: nTag
        integer,allocatable:: iTag(:),iStat(:,:)


#ifdef GFLOW_WITH_MPI
        if(.not.USING_MPI) return
        !---------------------------------------------
        !---------------------------------------------
        if(present(val_isInteger)) then
            dType = MPI_INTEGER
        else
            if(REALLEN == 4) then
                dType = MPI_REAL
            elseif(REALLEN == 8) then
                dType = MPI_REAL8
            else
                call MPI_ABORT(MPI_COMM_WORLD,MPI_ERROR,iError)
                    
                stop
            endif
        endif
        
        if(val_nExch <= 0) return
        
        
        !---------------------------------------------
        !---------------------------------------------
        allocate(iTag(val_nExch),iStat(MPI_STATUS_SIZE,val_nExch))
        nTag = 0
        do i=1,val_nExch
            if(val_iExch(i)%KIND == EXCH_SEND) then
                nData = val_iExch(i)%nDataPerNode * val_iExch(i)%nPoin
                
                if(dType == MPI_INTEGER) then
                call MPI_ISEND(val_iExch(i)%iExchData, nData, dType, val_iExch(i)%EXID,  &
                               val_iExch(i)%iExchTag, MPI_COMM_WORLD, iError )
                else
                call MPI_ISEND(val_iExch(i)%rExchData, nData, dType, val_iExch(i)%EXID,  &
                               val_iExch(i)%iExchTag, MPI_COMM_WORLD, iError )
                endif
                
                if(iError /= 0) then
                    write(ioPort_Out,"(2X,A)") 'mpi_isend error!'
                
                    call MPI_ABORT(MPI_COMM_WORLD,MPI_ERROR,iError)
                    
                    stop
                endif
                
                nTag = nTag + 1
                iTag(nTag) = val_iExch(i)%iExchTag
                
            elseif(val_iExch(i)%KIND == EXCH_RECV) then
                nData = val_iExch(i)%nDataPerNode * val_iExch(i)%nPoin
                
                if(dType == MPI_INTEGER) then
                call MPI_IRECV(val_iExch(i)%iExchData, nData, dType, val_iExch(i)%EXID,  &
                               val_iExch(i)%iExchTag, MPI_COMM_WORLD, iError )
                else
                call MPI_IRECV(val_iExch(i)%rExchData, nData, dType, val_iExch(i)%EXID,  &
                               val_iExch(i)%iExchTag, MPI_COMM_WORLD, iError )
                endif
                
                if(iError /= 0) then
                    write(ioPort_Out,"(2X,A)") 'mpi_irecv error!'
                    write(ioPort_Out,"(2X,A,I3,A,I3)") 'myID =',myID,";targetID =",val_iExch(i)%EXID
                
                    call mimd_exit
                    stop
                endif
                
                nTag = nTag + 1
                iTag(nTag) = val_iExch(i)%iExchTag
                
            endif
            
        enddo
        
        
        !---------------------------------------------
        !---------------------------------------------
        call MPI_WAITALL(nTag,iTag,iStat,iError)
        if(iError /= 0) then
            write(ioPort_Out,"(2X,A)") 'mpi_waitall error!'
                
            call MPI_ABORT(MPI_COMM_WORLD,MPI_ERROR,iError)
                    
            stop
        endif
        
        deallocate(iTag,iStat)
#endif
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine mimd_Exit
        implicit none

#ifdef GFLOW_WITH_MPI
        if(.not.USING_MPI) return
        
        call MPI_BARRIER(MPI_COMM_WORLD,iError)
        
        if(iError /= 0) then
            write(ioPort_Out,"(2X,A)") 'mpi_barrier error!'
            
            stop
        endif
        
        call MPI_FINALIZE(iError)
        
        if(iError /= 0) then
            write(ioPort_Out,"(2X,A)") 'mpi_finalize error!'
            
            stop
        endif
#endif
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine mimd_Reduce(val_sum)
        real(kind=REALLEN):: val_sum
        !
        integer:: dType
        real(kind=REALLEN):: tmp

#ifdef GFLOW_WITH_MPI
        if(.not.USING_MPI) return
        
        if(REALLEN == 4) then
            dType = MPI_REAL
        elseif(REALLEN == 8) then
            dType = MPI_REAL8
        endif
        
        call MPI_ALLREDUCE(val_sum,tmp,1,dType,MPI_SUM,MPI_COMM_WORLD,iError)
        
        if(iError /= 0) then
            write(ioPort_Out,"(2X,A)") 'mpi_AllReduce error!'
            
            stop
        endif
#endif
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine mimd_Sync
        implicit none

#ifdef GFLOW_WITH_MPI
        if(.not.USING_MPI) return
        
        call MPI_BARRIER(MPI_COMM_WORLD,iError)
        
        if(iError /= 0) then
            write(ioPort_Out,"(2X,A)") 'mpi_barrier error!'
            
            stop
        endif
#endif
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    real(kind=REALLEN) function mimd_Time
        implicit none

#ifdef GFLOW_WITH_MPI
        if(.not.USING_MPI) return
        
        mimd_Time = MPI_WTIME()
#endif
    endfunction
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine mimd_Abort
        implicit none

#ifdef GFLOW_WITH_MPI
        if(.not.USING_MPI) return
        
        call MPI_ABORT(MPI_COMM_WORLD,MPI_ERROR,iError)
        
        stop
#endif
    endsubroutine
    !
endmodule
!
