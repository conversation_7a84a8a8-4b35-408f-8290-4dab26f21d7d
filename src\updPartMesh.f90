!
subroutine updPartMesh(val_iIter)
    use mod_Project
    use mod_WorkPath
    use mod_PartMeshIO
    use mod_calPartMesh
    use mod_addExteRegnToBelv, only:addExteRegnToBelv
    use mod_bldExchLink, only:bldExchLink
    implicit none
    integer,intent(in):: val_iIter
    !
    integer:: i
    
    do i=1,nMeshPart
        call calPartMesh_Updating(i)
        
        if(iGlobalMesh%iExteRegnSANO%KIND /= EXTE_NONE) then
            call addExteRegnToBelv(i)
        endif
    enddo
    
    call bldExchLink
    
    do i=1,nMeshPart
        call savePartMesh_Updating(iPartMesh(i), geomFullPath, i) 
    enddo
    
endsubroutine
!
