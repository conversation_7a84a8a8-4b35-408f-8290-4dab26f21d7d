#pragma once

#include <cstdint>
#include <string>

namespace gflow {

//---------------------------------------------
// Environment definitions (MPI and GPU)
//---------------------------------------------
namespace environment {
    constexpr bool USING_MPI = true;
    constexpr bool USING_GPU = true;
    
#ifdef GFLOW_VDOMAIN_INC
    constexpr bool Enable_Inc = true;
#else
    constexpr bool Enable_Inc = false;
#endif
}

//---------------------------------------------
// Precision definitions (single/double precision) and string length
//---------------------------------------------
namespace precision {
#ifdef GFLOW_PRECISION_DP
    using real_t = double;  // double precision
    constexpr int REALLEN = 8;
#else
    using real_t = float;   // single precision  
    constexpr int REALLEN = 4;
#endif
    
    constexpr int STRLEN = 256;  // string length
    constexpr int oneAccessBits = 128;
    constexpr int nPrThreadPerBlock = 64;
}

//---------------------------------------------
// Dimension and variable count definitions
//---------------------------------------------
namespace dimension {
    constexpr int nElemProp = 16;
    
#ifdef GFLOW_DIMENSION_3D
    constexpr int nDim = 3;  // 3D
#else
    constexpr int nDim = 2;  // 2D
#endif

    constexpr int nCons = nDim + 2;  // conservative variables
    constexpr int nPrim = nDim + 2;  // primitive variables
    constexpr int nSolu = nDim + 2;  // solution variables
    
    constexpr int Dir_Forward = 1;
    constexpr int Dir_Backward = -1;
    
    constexpr int NDP1 = nDim + 1;
    constexpr int NDP2 = nDim + 2;
    constexpr int NDP3 = nDim + 3;
    constexpr int NDP4 = nDim + 4;
}

//---------------------------------------------
// Domain definitions
//---------------------------------------------
namespace domain {
    constexpr int DOMA_FLUI = 0;  // fluid domain
    constexpr int DOMA_SOLI = 1;  // solid domain
}

//---------------------------------------------
// Boundary type definitions
//---------------------------------------------
namespace mark {
    // All boundaries defined as negative values, undefined as 0
    constexpr int MARK_NONE = 0;    // empty boundary, for overlap
    constexpr int MARK_WALL = -1;   // wall boundary
    constexpr int MARK_EQTW = -102;
    constexpr int MARK_EULW = -103; // Euler wall
    constexpr int MARK_MOVW = -104; // Move wall (both Trans and Rotat)

    constexpr int MARK_INLE = -2;   // inlet
    constexpr int MARK_INLM = -202; // mass flow inlet

    constexpr int MARK_OUTL = -3;   // outlet
    constexpr int MARK_OUTP = -302; // pressure outlet
    constexpr int MARK_OUTM = -303; // mass flow outlet

    constexpr int MARK_ATTA = -11;  // internal interface
    constexpr int MARK_SYMM = -12;  // symmetry surface
    constexpr int MARK_PFAR = -13;  // pressure far field
    constexpr int MARK_SLID = -14;  // slid boundary (pair)
    constexpr int MARK_OVST = -15;  // overset boundary
    constexpr int MARK_LPRD = -16;  // linear period boundary
    constexpr int MARK_CPRD = -17;  // circumferential period boundary

    constexpr int MARK_UDBC = -99;  // user defined boundary, unused
    
    constexpr int BCPAIR_NONE = 0;
    constexpr int BCPAIR_SLIP = -21; // slip boundary pair
    constexpr int BCPAIR_PRDL = -22; // periodic boundary pair
    constexpr int BCPAIR_PRDR = -23; // periodic boundary pair
    constexpr int BCPAIR_INFC = -24; // internal boundary pair
}

//---------------------------------------------
// Element type definitions
//---------------------------------------------
namespace element {
    constexpr int ELEM_NONE = 0;         // empty element
    constexpr int ELEM_EXTE = 100;       // extended element
    constexpr int ELEM_LINE = 3;         // line element
    constexpr int ELEM_TRIANGLE = 5;     // triangle element
    constexpr int ELEM_RECTANGLE = 9;    // quadrilateral element
    constexpr int ELEM_TETRAHEDRAL = 10; // tetrahedral element
    constexpr int ELEM_HEXAHEDRAL = 12;  // hexahedral element
    constexpr int ELEM_WEDGE = 13;       // wedge element
    constexpr int ELEM_PYRAMID = 14;     // pyramid element
}

//---------------------------------------------
// Interface type definitions
//---------------------------------------------
namespace interface {
    constexpr int INFC_NONE = -100;
    constexpr int INFC_BND = -1;
    constexpr int INFC_ONEWAY = 0;
    constexpr int INFC_TWOWAY = 1;
    constexpr int INFC_RECIPROCAL = 2;
}

//---------------------------------------------
// Mesh deformation definitions
//---------------------------------------------
namespace mesh_deform {
    constexpr int DEFORMKIND_NONE = -100;
    constexpr int DEFORMKIND_STILL = 0;  // static
    constexpr int DEFORMKIND_TRANS = 1;  // translation, displacement
    constexpr int DEFORMKIND_ROTAT = 2;  // rotation, center+axis+angle
    constexpr int DEFORMKIND_REGNT = 3;  // whole mesh translation
    constexpr int DEFORMKIND_REGNR = 4;  // whole mesh rotation
    constexpr int DEFORMKIND_SOFTT = 5;  // elastic movement, displacement
    constexpr int DEFORMKIND_SOFTR = 6;  // elastic bending, center+axis+angle

    constexpr int DEFORMMETHOD_NONE = 0;   // None
    constexpr int DEFORMMETHOD_DWM = 1;    // distance weight method
    constexpr int DEFORMMETHOD_SM = 2;     // spring method
    constexpr int DEFORMMETHOD_DM = 3;     // delaunay mapping method
    constexpr int DEFORMMETHOD_RBF = 4;    // radial based function
    constexpr int DEFORMMETHOD_DMRBF = 5;  // DM + RBF

    constexpr int DEFORMPOINT_NONE = 0;   // undefined
    constexpr int DEFORMPOINT_INTER = 1;  // internal interpolation point
    constexpr int DEFORMPOINT_DEFOM = 2;  // boundary moving point
    constexpr int DEFORMPOINT_STILL = 3;  // boundary static point
}

//---------------------------------------------
// Flag definitions
//---------------------------------------------
namespace flag {
    // Note: Flags are generally defined as negative values, positive values for host element interpolation
    constexpr int FLAG_NONE = 0;     // undefined
    constexpr int FLAG_VALID = -101; // valid
    constexpr int FLAG_INVAL = -100; // invalid
    constexpr int FLAG_INNER = -102; // located inside
    constexpr int FLAG_EXCH = -103;  // located in exchange block
    constexpr int FLAG_BOUND = -104; // located at boundary
    constexpr int FLAG_WALL = -105;  // located at wall
    constexpr int FLAG_FORCED = -108; // forced

    constexpr int FLAG_HOST = -10;
    constexpr int FLAG_EXTE = -11;   // new extended point, not participating in calculation
}

//---------------------------------------------
// Extended kind definitions
//---------------------------------------------
namespace exte_kind {
    constexpr int EXTE_NONE = 0;
    constexpr int EXTE_SLIP = 1;
    constexpr int EXTE_OVST = 2;
    constexpr int EXTE_PART = 3;
}

//---------------------------------------------
// Exchange kind definitions
//---------------------------------------------
namespace exch_kind {
    constexpr int EXCH_NONE = 0; // unspecified
    constexpr int EXCH_SEND = 1; // send out
    constexpr int EXCH_RECV = 2; // receive in
    constexpr int EXCH_INTE = 3; // internal interpolation
}

//---------------------------------------------
// IO Port definitions
//---------------------------------------------
namespace io_port {
    constexpr int ioPort_In = 5;   // keyboard
    constexpr int ioPort_Out = 6;  // screen

    constexpr int ioPort_FULL = 21;
    constexpr int ioPort_MESH = 22;
    constexpr int ioPort_GEOM = 23;
    constexpr int ioPort_TEST = 24;
    constexpr int ioPort_TIME = 25;

    constexpr int ioPort_RSU = 30;
    constexpr int ioPort_RES = 31;
    constexpr int ioPort_AERO = 35;

    constexpr int ioPort_ERR = 40;

    // Save options
    constexpr int SAVE_NOTHING = 0;
    constexpr int SAVE_NBC_MID = 1;
    constexpr int SAVE_NBC_DUAL = 2;
    constexpr int SAVE_NBC_FINAL = 3;
    constexpr int SAVE_PBC_MID = 11;
    constexpr int SAVE_PBC_DUAL = 12;
    constexpr int SAVE_PBC_FINAL = 13;
    constexpr int SAVE_OBC_MID = 21;
    constexpr int SAVE_OBC_DUAL = 22;
    constexpr int SAVE_OBC_FINAL = 23;
}

} // namespace gflow
