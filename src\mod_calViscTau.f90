
module mod_calViscTau
    implicit none
    !
contains
    !
    subroutine calTau_CPU(val_iMesh, val_iSolu)
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        implicit none
        type(typ_Mesh),intent(in):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        !

        !call calTau_Host(val_iMesh%iCalc_Poin, val_iSolu%primvar,   &
        !        val_iSolu%gradPrim, val_iSolu%Tau, val_iMesh%nPoin  )

    end subroutine calTau_CPU
    !
    !
    subroutine calTau_GPU(val_iMesh, val_iSolu)
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_GPUThreadDim
        use mod_TurbSA_GPUFunc
        use cudafor
        implicit none
        type(typ_Mesh),intent(in):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        !
        integer:: istat

        call calTau_Global<<<nGridDim(IP_BP),nBlockDim>>>(      &
                val_iMesh%iCalc_Poin_d, val_iSolu%primvar_d,    &
                val_iSolu%gradSolu_d, val_iSolu%limiter_d,      &
                val_iSolu%Tau_d,val_iMesh%nPoin, val_iMesh%nPoin)

        istat = cudaDeviceSynchronize()

    end subroutine calTau_GPU
    !
end module mod_calViscTau