!
module mod_calMeshTopo_Func
    use mod_PreDefine_Dimension
    use mod_PreDefine_Elem
    use mod_PreDefine_IOPort
    use mod_ElemProp
    implicit none
    !
contains
    !
    !-------------------------------------------------
    !
    !-------------------------------------------------
    subroutine getElemKind(val_iSize,val_icci,val_ick)
        implicit none
        integer,intent(in):: val_iSize
        integer,intent(in),dimension(val_iSize):: val_icci
        integer,intent(out):: val_ick
    
        if(val_iSize == 2) then
            val_ick = ELEM_LINE
        
        elseif(val_iSize == 4) then
            if(val_icci(3) == val_icci(4)) then
                val_ick = ELEM_TRIANGLE
            else
                val_ick = ELEM_RECTANGLE
            endif
        
        elseif(val_iSize == 8) then
            if(val_icci(3) == val_icci(4)) then
                if(val_icci(5) == val_icci(8)) then
                    val_ick = ELEM_TETRAHEDRAL
                else
                    val_ick = ELEM_WEDGE
                endif
            else
                if(val_icci(5) == val_icci(8)) then
                    val_ick = ELEM_PYRAMID
                else
                    val_ick = ELEM_HEXAHEDRAL
                endif
            endif
        
        else
            val_ick = ELEM_NONE
        endif
    
    endsubroutine
    !
    !-------------------------------------------------
    !
    !-------------------------------------------------
    logical function isEqual_Edge(val_size,val_edgei,val_edgej)
        implicit none
        integer,intent(in):: val_size
        integer,intent(in),dimension(val_size):: val_edgei
        integer,intent(in),dimension(val_size):: val_edgej
    
        integer:: i,iok
        integer,dimension(val_size):: edgei,edgej
    
    
        edgei(:) = val_edgei(:)
        edgej(:) = val_edgej(:)
    
        if(val_size == 4) then
            if(edgei(3) == edgei(4)) then
                edgei(4) = 0
            endif
        
            if(edgej(3) == edgej(4)) then
                edgej(4) = 0
            endif
        endif
    
        !subroutine reNum_Int(val_size,val_arri)
        call reNum_Int(val_size,edgei)
    
        call reNum_Int(val_size,edgej)
    
        do i=1,val_size
            if(edgei(i) /= edgej(i)) then
                isEqual_Edge = .false.
                return
            endif
        enddo
    
        isEqual_Edge = .true.
        return
    
    endfunction
    !
    !-------------------------------------------------
    !
    !-------------------------------------------------
    subroutine reNum_Int(val_size,val_arri)
        implicit none
        integer,intent(in):: val_size
        integer,intent(inout),dimension(val_size):: val_arri
    
        integer:: i,j,temp,iok
    
        do i=1,val_size
            iok = 0
        
            do j=1,val_size-1
                if(val_arri(j) > val_arri(j+1)) then
                    temp = val_arri(j)
                    val_arri(j) = val_arri(j+1)
                    val_arri(j+1) = temp
                
                    iok = 1
                endif
            enddo
        
            if(iok == 0) exit
        
        enddo
    
    endsubroutine
    !
    !-------------------------------------------------
    !
    !-------------------------------------------------
    integer function getEqual_Edge(val_size,val_edgei,val_edgej)
        implicit none
        integer,intent(in):: val_size
        integer,intent(in),dimension(val_size):: val_edgei
        integer,intent(in),dimension(val_size):: val_edgej
        !logical function
        logical:: isEqual_Edge
        !
        integer:: i,j,nSize,iIP
    
        getEqual_Edge = 0
    
        if(.not.isEqual_Edge(val_size,val_edgei,val_edgej)) then
            getEqual_Edge = 0
            return
        endif
    
        if(val_size == 2) then
            if(val_edgei(1) == val_edgej(1)) then
                getEqual_Edge = 1
            else
                getEqual_Edge = -1
            endif
        
        elseif(val_size == 4) then
            if(val_edgei(3) == val_edgei(4)) then
                nSize = 3
            else
                nSize = 4
            endif
        
            do i=1,nSize
                if(val_edgej(i) == val_edgei(1)) then
                    iIP = mod(i,nSize) + 1
                
                    if(val_edgej(iIP) == val_edgei(2)) then
                        getEqual_Edge = 1
                    else
                        getEqual_Edge = -1
                    endif
                endif
            enddo
        
        endif
    
    endfunction
    !
    !-------------------------------------------------
    !
    !-------------------------------------------------
    subroutine getEdgeOfElem(val_ick,val_sizec,val_icci,val_nk,val_iek,val_sizee,val_ieei)
        implicit none
        integer,intent(in):: val_ick
        integer,intent(in):: val_sizec
        integer,intent(in),dimension(val_sizec):: val_icci
        integer,intent(in):: val_nk
        integer,intent(out):: val_iek
        integer,intent(in):: val_sizee
        integer,intent(out),dimension(val_sizee):: val_ieei
    
        if((val_ick /= ELEM_TRIANGLE).and. &
           (val_ick /= ELEM_RECTANGLE).and. &
           (val_ick /= ELEM_TETRAHEDRAL).and. &
           (val_ick /= ELEM_HEXAHEDRAL).and. &
           (val_ick /= ELEM_WEDGE).and. &
           (val_ick /= ELEM_PYRAMID)) then
            val_iek = ELEM_NONE
            val_ieei(:) = 0
        
            return
        endif
       
        if((val_nk <= 0).or.(val_nk > ElemProp_edgeNum(val_ick))) then
            val_iek = ELEM_NONE
            val_ieei(:) = 0
        
            return
        endif
    
        val_iek = ElemProp_edgeKind(val_nk,val_ick)
        val_ieei(:) = val_icci(ElemProp_edgeList(1:val_sizee,val_nk,val_ick))
    
    endsubroutine
    !
    !-------------------------------------------------
    !
    !-------------------------------------------------
    subroutine getMinValueOfIntArray(val_size,val_array,val_minValue)
        implicit none
        integer,intent(in):: val_size
        integer,intent(in),dimension(val_size):: val_array
        integer,intent(inout):: val_minValue
    
        integer:: i
    
        val_minValue = val_array(1)
        do i=2,val_size
            val_minValue = min(val_minValue,val_array(i))
        enddo
    
    endsubroutine
    !
    !-------------------------------------------------
    !
    !-------------------------------------------------
    subroutine chkIElemLink(val_iMesh)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        !
        integer:: i,j
        integer:: er,kr,ick,jck
        integer:: ieei(2*nDim-2),jeej(2*nDim-2)
    
        !
        !!$omp parallel do private(i,j,er,kr,ick,jck,ieei,jeej)
        do i=1,val_iMesh%nElem
            ick = val_imesh%iElemKind(i)
            do j=1,ElemProp_EdgeNum(ick)
                er = val_iMesh%iElemLink(i,j,1)
                kr = val_iMesh%iElemLink(i,j,2)
            
                if(er <= 0) cycle
                jck = val_iMesh%iElemKind(er)
            
                ieei(:) = val_iMesh%iElem(ElemProp_EdgeList(1:2*nDim-2,j ,ick),i )
                jeej(:) = val_iMesh%iElem(ElemProp_EdgeList(1:2*nDim-2,kr,jck),er)
            
            
                SELECT CASE (getEqual_Edge(2*nDim-2,ieei,jeej))
                CASE (0)
                    write(ioPort_Out,*) 'ERROR, not Equal',i,j,er,kr,ieei,jeej
                CASE(1)
                    write(ioPort_Out,*) 'ERROR, same dir'
                END SELECT
            
            enddo
        enddo
        !!$omp end parallel do

        !pause 'XXXX'
    endsubroutine
    !
    !
endmodule
!
