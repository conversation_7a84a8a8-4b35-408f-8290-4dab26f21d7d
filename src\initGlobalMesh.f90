
!---------------------------------------------
!
!---------------------------------------------
subroutine initGlobalMesh(val_iMesh)
    use mod_TypeDef_Mesh
    use mod_Config
    use mod_calMesh_Topology
    use mod_calMesh_Geometric
    use mod_calMesh_WallDistance
    implicit none
    type(typ_GlobalMesh),intent(inout):: val_iMesh
    !
    
    
    !-------------------------------------------
    !基本网格参数配置
    call setParmOfGlobalMesh(val_iMesh)
    
    
    !-------------------------------------------
    !网格块和网格区
    write(ioPort_Out,"(5X,A)") ":: calGlobalMesh_Topology..."
    call calGlobalMesh_Topology(val_iMesh)
    
    
    !-------------------------------------------
    !几何信息
    write(ioPort_Out,"(5X,A)") ":: calGlobalMesh_Geometric..."
    call calGlobalMesh_Geometric(val_iMesh)
    
    
    !-------------------------------------------
    !
    if(PHYSICAL_PROBLEM >= _TURB) then
        write(ioPort_Out,"(5X,A)") ":: calGlobalMesh_WallDistance..."
        call calGlobalMesh_WallDistance(val_iMesh)
        
        !call testIO_GlobalMesh_WDis(val_iMesh, val_iMesh%iWDis_Poin, 0)
        !
        !write(ioPort_Out,*) 'testIO_GDis in iniGlobalMesh.f90'
        !stop
    endif
    
endsubroutine
!
