!
!---------------------------------------------
!    
!---------------------------------------------
module mod_TypeDef_MeshMapping
    !---------------------------------------------
    !主从网格映射关系
    !---------------------------------------------
    type:: typ_meshMapping
        integer:: nGPoin,nGElem,nGSate,nGBele,nGBelv
        integer:: nLPoin,nLElem,nLSate,nLBele,nLBelv
        !
        integer,allocatable:: iFlag_GPoin(:)
        integer,allocatable:: iFlag_GElem(:)
        integer,allocatable:: iFlag_GSate(:)
        integer,allocatable:: iFlag_GBele(:)
        integer,allocatable:: iFlag_GBelv(:)
        !
        integer,allocatable:: iFlag_LPoin(:)
        integer,allocatable:: iFlag_LElem(:)
        integer,allocatable:: iFlag_LSate(:)
        integer,allocatable:: iFlag_LBele(:)
        integer,allocatable:: iFlag_LBelv(:)
        
        !+:对应的 0:无效点
        integer,allocatable:: iMapLToG_Poin(:) !(nLPoin) 
        integer,allocatable:: iMapLToG_Elem(:)
        integer,allocatable:: iMapLToG_Sate(:)
        integer,allocatable:: iMapLToG_Bele(:)
        integer,allocatable:: iMapLToG_Belv(:)
        !
        integer,allocatable:: iMapGToL_Poin(:) !(nGPoin)
        integer,allocatable:: iMapGToL_Elem(:)
        integer,allocatable:: iMapGToL_Sate(:)
        integer,allocatable:: iMapGToL_Bele(:)
        integer,allocatable:: iMapGToL_Belv(:)
        !
        integer:: nRSate,nBBelv !iExteRegnSANO%iSate <=对应关系=> iMesh%iBelv
        integer,allocatable:: iMapRToB_SaBv(:) 
        integer,allocatable:: iMapBToR_SaBv(:) 
        !
    endtype
    !
endmodule
!
    
    