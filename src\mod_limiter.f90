module mod_Limiter
    use cudafor
    use mod_PreDefine_Precision
    implicit none
    real(kind=REALLEN),parameter:: RSmall = 1.0E-10
    !r=src/dst
    !https://www.cnblogs.com/li12242/p/5062354.html
contains
    !
    attributes(host,device) subroutine limit_Adapter(src,dst,phii,eps,ltype)
        use mod_Options
        implicit none
        real(kind=REALLEN):: src
        real(kind=REALLEN):: dst
        real(kind=REALLEN):: phii
        real(kind=REALLEN):: eps
        integer:: ltype

        if(ltype == _VENKATAKRISHNAN)then
            call limit_<PERSON><PERSON><PERSON><PERSON><PERSON>an(src, dst, phii, eps)
        elseif(ltype == _MINMOD)then
            call limit_Minmod(src, dst, phii)
        elseif(ltype == _KOREN) then
            call limit_Koren(src, dst, phii)
        elseif(ltype == _Superbee) then
            call limit_Superbee(src, dst, phii)
        elseif(ltype == _VanAlbada) then
            call limit_VanAlbadaR(src, dst, phii)
        elseif(ltype == _VANLEER) then
            call limit_VanLeer(src, dst, phii)
        elseif(ltype == _VANLEERAVG) then
            call limit_VanLeerAvg(src, dst, phii)
        else
            phii = 0.0
        endif

    end subroutine limit_Adapter
    !===================
    ![1]CHARM: phi = max(0,r(3r+1)/(r+1)^2), not 2nd order TVD
    attributes(host,device) subroutine limit_CHARM(src,dst,phii)
        implicit none
        real(kind=REALLEN):: src
        real(kind=REALLEN):: dst
        real(kind=REALLEN):: phii
        !
        real(kind=REALLEN):: r

        phii = 0.0
        if(abs(dst) > RSmall) then
            r = src/dst
            if(r > 0) then
                phii = r*(3.0*r+1.0)/(r+1.0)**2
            end if
        endif

    endsubroutine
    !
    !===================
    ![2]HCUS: phi = 1.5*(r+|r|)/(r+2), not 2nd order TVD
    attributes(host,device) subroutine limit_HCUS(src,dst,phii)
        implicit none
        real(kind=REALLEN):: src
        real(kind=REALLEN):: dst
        real(kind=REALLEN):: phii
        !
        real(kind=REALLEN):: r

        phii = 0.0
        if(abs(dst) > RSmall) then
            r = src/dst
            if(r > 0) then
                phii =1.5*(r+abs(r))/(r+2.0)
            end if
        endif

    endsubroutine
    !
    !===================
    ![3]HQUICK: phi = 2.0*(r+|r|)/(r+3), not 2nd order TVD
    attributes(host,device) subroutine limit_HQUICK(src,dst,phii)
        implicit none
        real(kind=REALLEN):: src
        real(kind=REALLEN):: dst
        real(kind=REALLEN):: phii
        !
        real(kind=REALLEN):: r

        phii = 0.0
        if(abs(dst) > RSmall) then
            r = src/dst
            if(r > 0) then
                phii =2.0*(r+abs(r))/(r+3.0)
            end if
        endif

    endsubroutine
    !
    !===================
    ![4]Koren: phi = max(0,min(2r,(2+r)/3,2)), third-order accurate for sufficiently smooth data
    attributes(host,device) subroutine limit_Koren(src,dst,phii)
        implicit none
        real(kind=REALLEN):: src
        real(kind=REALLEN):: dst
        real(kind=REALLEN):: phii
        !
        real(kind=REALLEN):: r

        phii = 0.0
        if(abs(dst) > RSmall) then
            r = src/dst
            phii = max(0.0, min(2.0, min(2.0*r,(2.0+r)/3.0)))
        endif

    endsubroutine
    !
    !===================
    ![5]minmod: phi = max(0,min(1,r))
    attributes(host,device) subroutine limit_MinMod(src,dst,phii)
        implicit none
        real(kind=REALLEN):: src
        real(kind=REALLEN):: dst
        real(kind=REALLEN):: phii
        !
        real(kind=REALLEN):: r

        phii = 0.0
        if(abs(dst) > RSmall) then
            r = src/dst
            phii = max(0.0, min(1.0, r))
        endif

    endsubroutine
    !
    !===================
    ![6]MC(Monotonized central): phi = max(0,min(2,2r,(r+1)/2))
    attributes(host,device) subroutine limit_MC(src,dst,phii)
        implicit none
        real(kind=REALLEN):: src
        real(kind=REALLEN):: dst
        real(kind=REALLEN):: phii
        !
        real(kind=REALLEN):: r

        phii = 0.0
        if(abs(dst) > RSmall) then
            r = src/dst
            phii = max(0.0, min(2.0, min(2.0*r, 0.5*(r+1.0))))
        endif

    endsubroutine
    !
    !===================
    ![7]Osher: phi = max(0,min(r,b))
    attributes(host,device) subroutine limit_Osher(src,dst,phii,limC)
        implicit none
        real(kind=REALLEN):: src
        real(kind=REALLEN):: dst
        real(kind=REALLEN):: phii
        real(kind=REALLEN):: limC !1<=lim<=2
        !
        real(kind=REALLEN):: r

        phii = 0.0
        if(abs(dst) > RSmall) then
            r = src/dst
            phii = max(0.0, min(limC, r))
        endif

    endsubroutine
    !
    !===================
    ![8]Ospre: phi = 1.5*r(r+1)/(r^2+r+1)
    attributes(host,device) subroutine limit_Ospre(src,dst,phii)
        implicit none
        real(kind=REALLEN):: src
        real(kind=REALLEN):: dst
        real(kind=REALLEN):: phii
        !
        real(kind=REALLEN):: r

        phii = 0.0
        if(abs(dst) > RSmall) then
            r = src/dst
            phii = 1.5*r*(r+1)/(r*r+r+1.0)
        endif

    endsubroutine
    !
    !===================
    ![9]Smart: phi = max(0,min(2r,4,(1+3r)/4)), not 2nd order TVD
    attributes(host,device) subroutine limit_Smart(src,dst,phii)
        implicit none
        real(kind=REALLEN):: src
        real(kind=REALLEN):: dst
        real(kind=REALLEN):: phii
        !
        real(kind=REALLEN):: r

        phii = 0.0
        if(abs(dst) > RSmall) then
            r = src/dst
            phii = max(0.0, min(4.0, min(2.0*r, 0.25+0.75*r)))
        endif

    endsubroutine
    !
    !===================
    ![10]Superbee: phi = max(0,min(1,2r),min(r,2))
    attributes(host,device) subroutine limit_Superbee(src,dst,phii)
        implicit none
        real(kind=REALLEN):: src
        real(kind=REALLEN):: dst
        real(kind=REALLEN):: phii
        !
        real(kind=REALLEN):: r

        phii = 0.0
        if(abs(dst) > RSmall) then
            r = src/dst
            phii = max(0.0, max(min(1.0, 2.0*r),min(r,2.0)))
        endif

    endsubroutine
    !
    !===================
    ![11]Sweby: phi = max(0,min(1,br),min(r,b))
    attributes(host,device) subroutine limit_Sweby(src,dst,phii,limC)
        implicit none
        real(kind=REALLEN):: src
        real(kind=REALLEN):: dst
        real(kind=REALLEN):: phii
        real(kind=REALLEN):: limC
        !
        real(kind=REALLEN):: r

        phii = 0.0
        if(abs(dst) > RSmall) then
            r = src/dst
            phii = max(0.0, max(min(1.0, limC*r), min(r,limC)))
        endif

    endsubroutine
    !
    !===================
    ![12]UMIST: phi = max(0,min(2r,(1+3r)/4,(3+r)/4,2))
    attributes(host,device) subroutine limit_UMIST(src,dst,phii)
        implicit none
        real(kind=REALLEN):: src
        real(kind=REALLEN):: dst
        real(kind=REALLEN):: phii
        !
        real(kind=REALLEN):: r

        phii = 0.0
        if(abs(dst) > RSmall) then
            r = src/dst
            phii = max(0.0, min(min(2.0,2.0*r),min(0.25+0.75*r,0.75+0.25*r)))
        endif

    endsubroutine
    !
    !===================
    ![13]VanAlbada: phi = (r^2+r)/(r^2+1)
    attributes(host,device) subroutine limit_VanAlbada(src,dst,phii)
        implicit none
        real(kind=REALLEN):: src
        real(kind=REALLEN):: dst
        real(kind=REALLEN):: phii
        !
        real(kind=REALLEN):: r

        phii = 0.0
        if(abs(dst) > RSmall) then
            r = src/dst
            phii = (r*r+r)/(r*r+1.0)
        endif

    endsubroutine
    !
    !===================
    ![14]VanAlbadaR: phi = 2r/(r^2+1), not 2nd order TVD, used on high-order schemes
    attributes(host,device) subroutine limit_VanAlbadaR(src,dst,phii)
        implicit none
        real(kind=REALLEN):: src
        real(kind=REALLEN):: dst
        real(kind=REALLEN):: phii
        !
        real(kind=REALLEN):: r

        phii = 0.0
        if(abs(dst) > RSmall) then
            r = src/dst
            phii = 2.0*r/(r*r+1.0)
        endif

    endsubroutine
    !
    !===================
    ![15]VanLeer: phi = (r+|r|)/(1+|r|)
    attributes(host,device) subroutine limit_VanLeer(src,dst,phii)
        implicit none
        real(kind=REALLEN):: src
        real(kind=REALLEN):: dst
        real(kind=REALLEN):: phii
        real(kind=REALLEN):: limC
        !
        real(kind=REALLEN):: r

        phii = 0.0
        if(abs(dst) > RSmall) then
            r = src/dst
            phii = (r+abs(r))/(1.0+abs(r))
        endif

    endsubroutine
    !
    !===================
    ![15]VanLeerR: phi = max(0, min(br,b,(1+r)/2) 1<=b<=2
    attributes(host,device) subroutine limit_VanLeerR(src,dst,phii,limC)
        implicit none
        real(kind=REALLEN):: src
        real(kind=REALLEN):: dst
        real(kind=REALLEN):: phii
        real(kind=REALLEN):: limC
        !
        real(kind=REALLEN):: r

        phii = 0.0
        if(abs(dst) > RSmall) then
            r = src/dst
            phii = max(0.0, min(limC*r, min(limC,0.5*(1+r))))
        endif

    endsubroutine
    !
    !===================
    ![16]VanLeerAvg: phi = max(0, min(2.0r,2.0,(1+r)/2)
    attributes(host,device) subroutine limit_VanLeerAvg(src,dst,phii)
        implicit none
        real(kind=REALLEN):: src
        real(kind=REALLEN):: dst
        real(kind=REALLEN):: phii
        real(kind=REALLEN):: limC
        !
        real(kind=REALLEN):: r

        phii = 0.0
        if(abs(dst) > RSmall) then
            r = src/dst
            phii = max(0.0, min(2.0*r, min(2.0,0.5*(1+r))))
        endif

    endsubroutine
    !
    attributes(host,device) subroutine limit_Venkatakrishnan(src,dst,phii,eps2)
        implicit none
        real(kind=REALLEN):: src
        real(kind=REALLEN):: dst
        real(kind=REALLEN):: phii
        real(kind=REALLEN):: eps2
        !
        real(kind=REALLEN):: a,b

        a = src*src + eps2 + 2*src*dst
        b = src*src + eps2 + 2*dst*dst + src*dst

        if(src*dst < 0) then
            phii = 0.0
        else
            phii = a/b
        endif

    end subroutine limit_Venkatakrishnan
    !
end module mod_Limiter