
!---------------------------------------------
!
!---------------------------------------------
subroutine launchSolu(val_iProj)
    use mod_PreDefine_IOPort
    use mod_TypeDef_Project
    use mod_Config
    use mod_HistoryIO
    use mod_setSoluParm, only: setSoluParm
    use mod_SoluMAlloc, only: SoluMAlloc
    use mod_SoluAdapter, only: SaveSolu
    use mod_SoluComm, only: InitFlowField,TimeMarching_Steady,TimeMarching_Unsteady
    implicit none
    type(typ_Project),intent(inout):: val_iProj
    !
    integer:: iOuterIter,iInterIter
    real(kind=4):: iTimeBegin,iTimeFinal
    integer:: ivar,istat,nInnerIter,iInnerIterBegin
    logical:: isDual = .false.
    logical:: isUnst = .false.

    !write(*,*) val_iProj%iMesh%iDomaProp(:,3)
    !pause 'sssc'


    call setSoluParm(val_iProj)

    call SoluMAlloc(val_iProj%iParm, val_iProj%iMesh, val_iProj%iSolu)

    !---------------------------------------------
    !流场初始化及初始流场输出
    call InitFlowField(val_iProj%iParm, val_iProj%iMesh, val_iProj%iSolu, 0)

    call SaveSolu(val_iProj%iParm, val_iProj%iMesh, val_iProj%iSolu, 0)

    !---------------------------------------------
    !Open History files and write headers
    call HistoryFile_Open(ioPort_RES, "History_ResErr.plt", (RESTART_SOL == _YES))
    call HistoryFile_Open(ioPort_RES+1, "History_TotalAeroFM.plt", (RESTART_SOL == _YES))
    call HistoryFile_Open(ioPort_RES+2, "History_DetailedAeroFM.plt", (RESTART_SOL == _YES))

    call History_ResErrFile_WriteHeader(ioPort_RES)
    call History_TotalAeroFMFile_WriteHeader(ioPort_RES+1)
    call History_DetailedAeroFMFile_WriteHeader(ioPort_RES+2, val_iProj%iMesh%nMark, val_iProj%iParm%isViscFlow)

    !---------------------------------------------
    !OUTERLOOP: 物理时间推进
    call CPU_TIME(iTimeBegin)

    val_iProj%iParm%iTimeCurrt = val_iProj%iParm%iTimeBegin

    !Steady flow marching
    isUnst = val_iProj%iParm%isUnsteady
    isDual = val_iProj%iParm%isDualTime
    val_iProj%iParm%isUnsteady = .false.
    val_iProj%iParm%isDualTime = .false.

    call TimeMarching_Steady(val_iProj%iParm, val_iProj%iMesh, val_iProj%iSolu)

    !Unsteady flow marching
    val_iProj%iParm%isUnsteady = isUnst
    val_iProj%iParm%isDualTime = isDual
    if(val_iProj%iParm%isDualTime) then
        call SaveSolu(val_iProj%iParm, val_iProj%iMesh, val_iProj%iSolu, 0)


        call TimeMarching_Unsteady(val_iProj%iParm, val_iProj%iMesh, val_iProj%iSolu)
    end if

    call CPU_TIME(iTimeFinal)

    !---------------------------------------------
    !Close History files
    call HistoryFile_Close(ioPort_RES)
    call HistoryFile_Close(ioPort_RES+1)
    call HistoryFile_Close(ioPort_RES+2)

    !---------------------------------------------
    !计算耗时统计输出
    call saveComputingTime(val_iProj%iParm, iTimeBegin, iTimeFinal, val_iProj%iMesh%nPoin)

endsubroutine
!
