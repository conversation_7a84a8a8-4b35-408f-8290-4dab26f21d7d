!
subroutine iniGlobalMesh_Deform(val_iMesh)
    use mod_TypeDef_Mesh
    use mod_Mesh_Deform
    use mod_PreDefine_IOPort
    use mod_Interface_AllocateArray
    implicit none
    type(typ_GlobalMesh),intent(in):: val_iMesh
    !
    integer:: i
    
    
    call allocateArray(val_iMesh%dCoor_Poin, nDim, val_iMesh%nPoin)
    
    call readMesh_DeformInfo
    
    call bldMeshDeformZone(val_iMesh)
    
    
    do i=1,nDeformZone
        if((iDeformZone(i)%DeformKind == DEFORMKIND_ROTAT).or. &
           (iDeformZone(i)%DeformKind == DEFORMKIND_REGNR).or. &
           (iDeformZone(i)%DeformKind == DEFORMKIND_SOFTR) ) then
            call calMeshDeform_OrgnAndR(iDeformZone(i),val_iMesh)
        endif
        
        if(iDeformZone(i)%DeformMethod  == DEFORMMETHOD_DWM) then !distance weighting
            call iniMeshDeform_DWM(iDeformZone(i),val_iMesh)
        
        elseif(iDeformZone(i)%DeformMethod  == DEFORMMETHOD_SM) then !spring method
        !    call iniMeshDeform_SM(iDeformZone(i),val_iMesh)
        
        elseif(iDeformZone(i)%DeformMethod  == DEFORMMETHOD_DM) then !delaunay mapping method
        !    call iniMeshDeform_DM(iDeformZone(i),val_iMesh)
        
        elseif(iDeformZone(i)%DeformMethod  == DEFORMMETHOD_RBF) then !radial based function
        !    call iniMeshDeform_RBF(iDeformZone(i),val_iMesh)
        
        elseif(iDeformZone(i)%DeformMethod  == DEFORMMETHOD_DMRBF) then !DM+RBF
        !    call iniMeshDeform_DMRBF(iDeformZone(i),val_iMesh)
        
        endif
    enddo
    
endsubroutine
!
