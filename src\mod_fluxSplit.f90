module mod_FluxSplit
    use cudafor
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    implicit none
    !
contains
    !
    attributes(host,device) subroutine fluxSplit_Roe(soluFl,soluFr,coefFi,VgnFi,fluxFi,Gamma,GasCo,iDir)
        implicit none
        real(kind=REALLEN):: soluFl(nCons)
        real(kind=REALLEN):: soluFr(nCons)
        real(kind=REALLEN):: coefFi(nDim)
        real(kind=REALLEN):: VgnFi
        real(kind=REALLEN):: fluxFi(nCons)
        real(kind=REALLEN):: Gamma,GasCo
        integer:: iDir
        !
        integer:: i
        real(kind=REALLEN):: nvorFi(nDim),iLenFi
        real(kind=REALLEN):: DFl,HFl,cFl,VnFl,sqFl
        real(kind=REALLEN):: DFr,HFr,cFr,VnFr,sqFr
        real(kind=REALLEN):: DFi,HFi,cFi,VnFi,sqFi,PFi,veloFi(nDim)
        real(kind=REALLEN):: deltaV(nDim),deltaD,deltaP,deltaVn
        real(kind=REALLEN):: Rd,Rdm,EV,EVP,EVM,iLimt,T1,T2,T3,T4,T5,Tm(nDim),Ts


        iLenFi = sqrt(coefFi(1)**2+coefFi(2)**2+coefFi(nDim)**2*(nDim-2))

        nvorFi(   1) = coefFi(   1)/iLenFi
        nvorFi(   2) = coefFi(   2)/iLenFi
        nvorFi(nDim) = coefFi(nDim)/iLenFi

        DFl = soluFl(1)/(GasCo*soluFl(nDim+2))
        DFr = soluFr(1)/(GasCo*soluFr(nDim+2))

        Rd  = sqrt(DFr/DFl)
        Rdm = 1.0/(1.0 + Rd)

        veloFi(   1) = (soluFl(     2) + Rd*soluFr(     2))*Rdm
        veloFi(   2) = (soluFl(     3) + Rd*soluFr(     3))*Rdm
        veloFi(nDim) = (soluFl(nDim+1) + Rd*soluFr(nDim+1))*Rdm

        sqFl = soluFl(2)**2 + soluFl(3)**2 + soluFl(4   )**2*(nDim-2)
        sqFr = soluFr(2)**2 + soluFr(3)**2 + soluFr(4   )**2*(nDim-2)
        sqFi = veloFi(1)**2 + veloFi(2)**2 + veloFi(nDim)**2*(nDim-2)
        VnFl = soluFl(2)*nvorFi(1) + soluFl(3)*nvorFi(2) + soluFl(nDim+1)*nvorFi(nDim)*(nDim-2) - vgnFi*DFl
        VnFr = soluFr(2)*nvorFi(1) + soluFr(3)*nvorFi(2) + soluFr(nDim+1)*nvorFi(nDim)*(nDim-2) - vgnFi*DFr
        VnFi = veloFi(1)*nvorFi(1) + veloFi(2)*nvorFi(2) + veloFi(nDim  )*nvorFi(nDim)*(nDim-2) - vgnFi*Rd

        HFl = Gamma/(Gamma - 1.0)*soluFl(1)/DFl + 0.5*sqFl
        HFr = Gamma/(Gamma - 1.0)*soluFr(1)/DFr + 0.5*sqFr

        DFi = DFl*Rd
        HFi = (HFl + Rd*HFr)*Rdm
        cFi = sqrt((Gamma - 1.0)*(HFi - 0.5*sqFi))

        deltaV(   1) = soluFr(     2) - soluFl(     2)
        deltaV(   2) = soluFr(     3) - soluFl(     3)
        deltaV(nDim) = soluFr(nDim+1) - soluFl(nDim+1)
        deltaD       = DFr            - DFl
        deltaVn      = VnFr           - VnFl
        deltaP       = soluFr(1)      - soluFl(1)

        EV  = abs(VnFi)
        EVP = abs(VnFi + cFi)
        EVM = abs(VnFi - cFi)

        iLimt = 0.1*cFi
        !if(EV < iLimt) then
        !    EV  = 0.5*(EV *EV /iLimt + iLimt)
        !endif
        if(EVP < iLimt) then
            EVP = 0.5*(EVP*EVP/iLimt + iLimt)
        endif
        if(EVM < iLimt) then
            EVM = 0.5*(EVM*EVM/iLimt + iLimt)
        endif

        T1 = EV *(deltaD - deltaP/(cFi*cFi))
        T2 = EVP*(deltaP + DFi*cFi*deltaVn)/(2.0*cFi*cFi)
        T3 = EVM*(deltaP - DFi*cFi*deltaVn)/(2.0*cFi*cFi)

        T4 = T1 + T2 + T3
        T5 = (T2 - T3)*cFi

        Ts = 0.0
        do i=1,nDim
            Tm(i) = EV*DFi*(deltaV(i) - deltaVn*nvorFi(i))
            Ts = Ts + veloFi(i)*Tm(i)
        enddo

        fluxFi(     1) = VnFl*DFl     + VnFr*DFr
        fluxFi(     2) = VnFl*DFl*soluFl(     2) + VnFr*DFr*soluFr(     2) + (soluFr(1) + soluFl(1))*nvorFi(   1)
        fluxFi(     3) = VnFl*DFl*soluFl(     3) + VnFr*DFr*soluFr(     3) + (soluFr(1) + soluFl(1))*nvorFi(   2)
        fluxFi(nDim+1) = VnFl*DFl*soluFl(nDim+1) + VnFr*DFr*soluFr(nDim+1) + (soluFr(1) + soluFl(1))*nvorFi(nDim)
        fluxFi(nDim+2) = VnFl*DFl*HFl + VnFr*DFr*HFr

        fluxFi(     1) = fluxFi(1) - T4*1.0
        fluxFi(     2) = fluxFi(2) - (T4*veloFi(1) + T5*nvorFi(1) + Tm(1))
        fluxFi(     3) = fluxFi(3) - (T4*veloFi(2) + T5*nvorFi(2) + Tm(2))
        if(nDim==3) then
            fluxFi( 4) = fluxFi(4) - (T4*veloFi(nDim) + T5*nvorFi(nDim) + Tm(nDim))
        endif
        fluxFi(nDim+2) = fluxFi(nDim+2) - (T4*HFi - T1*cFi*cFi/(Gamma - 1.0) + T5*VnFi + Ts)

        do i=1,nCons
            fluxFi(i) = 0.5*fluxFi(i)*iLenFi
        enddo

        if(VnFi >= 0.0) then
            iDir = 1
        else
            iDir = -1
        endif

    endsubroutine
    !
    attributes(host,device) subroutine fluxSplit_SLAU(soluFl,soluFr,coefFi,VgnFi,fluxFi,Gamma,GasCo,iDir)
        implicit none
        real(kind=REALLEN):: soluFl(nCons)
        real(kind=REALLEN):: soluFr(nCons)
        real(kind=REALLEN):: coefFi(nDim)
        real(kind=REALLEN):: VgnFi
        real(kind=REALLEN):: fluxFi(nCons)
        real(kind=REALLEN):: Gamma,GasCo
        integer:: iDir
        !
        real(kind=REALLEN):: lenFi,nvorFi(nDim)
        real(kind=REALLEN):: DFl,sqL,VnL,HFl,cFl,MLP,gLP,VnLP,beltaLP
        real(kind=REALLEN):: DFr,sqR,VnR,HFr,cFr,MRM,gRM,VnRM,beltaRM
        real(kind=REALLEN):: delP,delD,VnFi,cFi,MKar,xhi,fp,fd,PBar,mdot,DFi


        lenFi = sqrt(coefFi(1)*coefFi(1)+coefFi(2)*coefFi(2)+coefFi(nDim)*coefFi(nDim)*(nDim-2))
        nvorFi(   1) = coefFi(   1)/lenFi
        nvorFi(   2) = coefFi(   2)/lenFi
        nvorFi(nDim) = coefFi(nDim)/lenFi

        DFl = soluFl(1)/(GasCo*soluFl(nDim+2))
        DFr = soluFr(1)/(GasCo*soluFr(nDim+2))
        sqL = soluFl(2)*soluFl(2) + soluFl(3)*soluFl(3) + soluFl(4)*soluFl(4   )*(nDim-2)
        sqR = soluFr(2)*soluFr(2) + soluFr(3)*soluFr(3) + soluFr(4)*soluFr(4   )*(nDim-2)
        VnL = soluFl(2)*nvorFi(1) + soluFl(3)*nvorFi(2) + soluFl(4)*nvorFi(nDim)*(nDim-2) - vgnFi
        VnR = soluFr(2)*nvorFi(1) + soluFr(3)*nvorFi(2) + soluFr(4)*nvorFi(nDim)*(nDim-2) - vgnFi
        HFl = Gamma/(Gamma-1.0)*soluFl(1)/DFl + 0.5*sqL
        HFr = Gamma/(Gamma-1.0)*soluFr(1)/DFr + 0.5*sqR
        cFl = sqrt(Gamma*soluFl(1)/DFl)
        cFr = sqrt(Gamma*soluFr(1)/DFr)

        delP = soluFr(1) - soluFl(1)
        delD = DFr       - DFl

        DFi  = 0.5*(DFl + DFr)
        cFi  = 0.5*(cFl + cFr)
        VnFi = (DFl*VnL + DFr*VnR)/(DFl+DFr)
        MLP  = VnL/cFi
        MRM  = VnR/cFi

        MKar = min(1.0, sqrt(0.5*(sqL+sqR))/cFi)
        xhi  = (1.0 - MKar)**2
        fp   = 1.0 - (1.0 - MKar)**2

        gLP = -max(-1.0, min(MLP, 0.0))
        gRM =  min( 1.0, max(MRM, 0.0))
        fd  = 1.0 - gLP*gRM

        VnLP = fd*abs(VnFi) + (1.0-fd)*abs(VnL)
        VnRM = fd*abs(VnFi) + (1.0-fd)*abs(VnR)

        if(MLP >= 1.0) then
            beltaLP = 1.0
        elseif(MLP <= -1.0) then
            beltaLP = 0.0
        else
            beltaLP = 0.25*(2-MLP)*(MLP+1)*(MLP+1)
        endif

        if(MRM >= 1.0) then
            beltaRM = 0.0
        elseif(MRM <= -1.0) then
            beltaRM = 1.0
        else
            beltaRM = 0.25*(2+MRM)*(MRM-1)*(MRM-1)
        end if

        PBar = 0.5*(soluFl(1) + soluFr(1) + (soluFl(1)-soluFr(1)) * (beltaLP-beltaRM) + &
                    (soluFl(1)+soluFr(1)) * (beltaLP+beltaRM-1.0) * fp)
        mdot = 0.5*(DFl*(VnL+abs(VnLP)) + DFr*(VnR-abs(VnRM)) - xhi*delP/cFi)*lenFi

        beltaLP = 0.5*(mdot + abs(mdot))
        beltaRM = 0.5*(mdot - abs(mdot))

        fluxFi(     1) = beltaLP                + beltaRM
        fluxFi(     2) = beltaLP*soluFl(     2) + beltaRM*soluFr(     2) + Pbar*coefFi(   1)
        fluxFi(     3) = beltaLP*soluFl(     3) + beltaRM*soluFr(     3) + Pbar*coefFi(   2)
        fluxFi(nDim+1) = beltaLP*soluFl(nDim+1) + beltaRM*soluFr(nDim+1) + Pbar*coefFi(nDim)
        fluxFi(nDim+2) = beltaLP*hFl            + beltaRM*hFr

        if(VnFi >= 0.0) then
            iDir = 1
        else
            iDir = -1
        endif

    endsubroutine
    !
    attributes(host,device) subroutine fluxSplit_SLAU2(soluFl,soluFr,coefFi,VgnFi,fluxFi,Gamma,GasCo,iDir)
        implicit none
        real(kind=REALLEN):: soluFl(nCons)
        real(kind=REALLEN):: soluFr(nCons)
        real(kind=REALLEN):: coefFi(nDim)
        real(kind=REALLEN):: VgnFi
        real(kind=REALLEN):: fluxFi(nCons)
        real(kind=REALLEN):: Gamma,GasCo
        integer:: iDir
        !
        real(kind=REALLEN):: iLenFi,nvorFi(nDim)
        real(kind=REALLEN):: DFl,DFr,sqFl,sqFr,VnFl,VnFr,hFl,hFr,cFl,cFr
        real(kind=REALLEN):: deltD,deltP,VnAvg,DAvg,cAvg,MLP,MRM,Mkar,xhi,fp,gLP,gRM,fd
        real(kind=REALLEN):: beltaLP,beltaRM,Pbar,mdot,FFi(5),VnLP,VnRM


        iLenFi = sqrt(coefFi(1)*coefFi(1)+coefFi(2)*coefFi(2)+coefFi(nDim)*coefFi(nDim)*(nDim-2))
        nvorFi(   1) = coefFi(   1)/iLenFi
        nvorFi(   2) = coefFi(   2)/iLenFi
        nvorFi(nDim) = coefFi(nDim)/iLenFi

        DFl  = soluFl(1)/(GasCo*soluFl(nDim+2))
        DFr  = soluFr(1)/(GasCo*soluFr(nDim+2))
        sqFl = soluFl(2)*soluFl(2) + soluFl(3)*soluFl(3) + soluFl(4)*soluFl(4   )*(nDim-2)
        sqFr = soluFr(2)*soluFr(2) + soluFr(3)*soluFr(3) + soluFr(4)*soluFr(4   )*(nDim-2)
        VnFl = soluFl(2)*nvorFi(1) + soluFl(3)*nvorFi(2) + soluFl(4)*nvorFi(nDim)*(nDim-2) - vgnFi
        VnFr = soluFr(2)*nvorFi(1) + soluFr(3)*nvorFi(2) + soluFr(4)*nvorFi(nDim)*(nDim-2) - vgnFi
        hFl  = Gamma/(Gamma-1.0)*soluFl(1)/DFl + 0.5*sqFl
        hFr  = Gamma/(Gamma-1.0)*soluFr(1)/DFr + 0.5*sqFr
        cFl  = sqrt(Gamma*soluFl(1)/DFl)
        cFr  = sqrt(Gamma*soluFr(1)/DFr)

        deltD = DFr       - DFl
        deltP = soluFr(1) - soluFl(1)

        DAvg  = 0.5*(DFl + DFr)
        cAvg  = 0.5*(cFl + cFr)
        VnAvg = (DFl*abs(VnFl) + DFr*abs(VnFr))/(DFl+DFr)
        MLP   = VnFl/cAvg
        MRM   = VnFr/cAvg

        Mkar = min(1.0, sqrt(0.5*(sqFl+sqFr))/cAvg)
        xhi  = (1.0 - Mkar)**2
        fp   = 1.0 - (1.0 - Mkar)**2

        gLP = -max(-1.0, min(MLP, 0.0))
        gRM =  min( 1.0, max(MRM, 0.0))
        fd  = 1.0 - gLP*gRM

        VnLP = fd*abs(VnAvg)+(1-fd)*abs(VnFl)
        VnRM = fd*abs(VnAvg)+(1-fd)*abs(VnFr)

        if(MLP >= 1.0) then
            beltaLP = 1.0
        elseif(MLP <= -1.0) then
            beltaLP = 0.0
        else
            beltaLP = 0.25*(2-MLP)*(MLP+1)*(MLP+1)
        endif

        if(MRM >= 1.0) then
            beltaRM = 0.0
        elseif(MRM <= -1.0) then
            beltaRM = 1.0
        else
            beltaRM = 0.25*(2+MRM)*(MRM-1)*(MRM-1)
        end if

        Pbar = 0.5*((soluFl(1) + soluFr(1)) + (soluFl(1) - soluFr(1))*(beltaLP - beltaRM) + &
                    DAvg * cAvg * (beltaLP + beltaRM - 1.0) * sqrt(0.5*(sqFl+sqFr)))
        mdot = 0.5*(DFl*(VnFl+abs(VnLP)) + DFr*(VnFr-abs(VnRM)) - xhi*deltP/cavg)*iLenFi

        beltaLP = 0.5*(mdot + abs(mdot))
        beltaRM = 0.5*(mdot - abs(mdot))

        fluxFi(     1) = beltaLP                + beltaRM
        fluxFi(     2) = beltaLP*soluFl(     2) + beltaRM*soluFr(     2) + Pbar*coefFi(   1)
        fluxFi(     3) = beltaLP*soluFl(     3) + beltaRM*soluFr(     3) + Pbar*coefFi(   2)
        fluxFi(nDim+1) = beltaLP*soluFl(nDim+1) + beltaRM*soluFr(nDim+1) + Pbar*coefFi(nDim)
        fluxFi(nDim+2) = beltaLP*hFl            + beltaRM*hFr

        if(MLP+MRM >= 0.0) then
            iDir = 1
        else
            iDir = -1
        endif

    endsubroutine
    !
    attributes(host,device) subroutine fluxSplit_SLAU2VL(soluFl,soluFr,coefFi,fluxFi,Gamma,GasCo,SVLFact,iDir)
        implicit none
        real(kind=REALLEN):: soluFl(nCons)
        real(kind=REALLEN):: soluFr(nCons)
        real(kind=REALLEN):: coefFi(nDim)
        real(kind=REALLEN):: fluxFi(nCons)
        real(kind=REALLEN):: Gamma,GasCo,SVLFact
        integer:: iDir
        !
        real(kind=REALLEN):: iLenFi,nvorFi(nDim)
        real(kind=REALLEN):: DFl,DFr,sqFl,sqFr,VnFl,VnFr,hFl,hFr,cFl,cFr
        real(kind=REALLEN):: deltD,deltP,VnAvg,DAvg,cAvg,MLP,MRM,Mkar,xhi,fp,gLP,gRM,fd
        real(kind=REALLEN):: beltaLP,beltaRM,Pbar,mdot,FFi(5),VnAvgLP,VnAvgRM
        real(kind=REALLEN):: MBar,xhi0,xhi1,omega,MLP_VL,MRM_VL,Dm_VL


        iLenFi = sqrt(coefFi(1)*coefFi(1)+coefFi(2)*coefFi(2)+coefFi(nDim)*coefFi(nDim)*(nDim-2))
        nvorFi(   1) = coefFi(   1)/iLenFi
        nvorFi(   2) = coefFi(   2)/iLenFi
        nvorFi(nDim) = coefFi(nDim)/iLenFi

        DFl  = soluFl(1)/(GasCo*soluFl(nDim+2))
        DFr  = soluFr(1)/(GasCo*soluFr(nDim+2))
        sqFl = soluFl(2)*soluFl(2) + soluFl(3)*soluFl(3) + soluFl(4)*soluFl(4)*(nDim-2)
        sqFr = soluFr(2)*soluFr(2) + soluFr(3)*soluFr(3) + soluFr(4)*soluFr(4)*(nDim-2)
        VnFl = soluFl(2)*nvorFi(1) + soluFl(3)*nvorFi(2) + soluFl(4)*nvorFi(nDim)*(nDim-2)
        VnFr = soluFr(2)*nvorFi(1) + soluFr(3)*nvorFi(2) + soluFr(4)*nvorFi(nDim)*(nDim-2)
        hFl  = Gamma/(Gamma-1.0)*soluFl(1)/DFl + 0.5*sqFl
        hFr  = Gamma/(Gamma-1.0)*soluFr(1)/DFr + 0.5*sqFr
        cFl  = sqrt(Gamma*soluFl(1)/DFl)
        cFr  = sqrt(Gamma*soluFr(1)/DFr)

        deltD = DFr       - DFl
        deltP = soluFr(1) - soluFl(1)

        DAvg  = 0.5*(DFl + DFr)
        cAvg  = 0.5*(cFl + cFr)
        VnAvg = (DFl*VnFl + DFr*VnFr)/(DFl+DFr)
        MLP   = VnFl/cAvg
        MRM   = VnFr/cAvg

        Mkar  = min(1.0, sqrt(0.5*(sqFl+sqFr))/cAvg)
        xhi  = (1.0 - Mkar)**2
        fp   = 1.0 - xhi

        gLP = -max(-1.0, min(MLP, 0.0))
        gRM =  min( 1.0, max(MRM, 0.0))
        fd  = 1.0 - gLP*gRM

        VnAvgLP = fd*abs(VnAvg)+(1-fd)*abs(VnFl)
        VnAvgRM = fd*abs(VnAvg)+(1-fd)*abs(VnFr)

        if(MLP >= 1.0) then
            beltaLP = 1.0
            MLP_VL = MLP
        elseif(MLP <= -1.0) then
            beltaLP = 0.0
            MLP_VL = 0.0
        else
            MLP_VL  = 0.25*(MLP+1)*(MLP+1)
            beltaLP = 0.25*(2-MLP)*(MLP+1)*(MLP+1)
        endif

        if(MRM >= 1.0) then
            beltaRM = 0.0
            MRM_VL  = 0.0
        elseif(MRM <= -1.0) then
            beltaRM = 1.0
            MRM_VL  = MRM
        else
            MRM_VL  = -0.25*(MRM-1)*(MRM-1)
            beltaRM = 0.25*(2+MRM)*(MRM-1)*(MRM-1)
        end if

        Dm_VL = abs(DFl*cFl*MLP_VL - DFr*cFr*MRM_VL)

        Pbar = 0.5*((soluFl(1) + soluFr(1)) + (soluFl(1) - soluFr(1))*(beltaLP - beltaRM) + &
                    DAvg * cAvg * (beltaLP + beltaRM - 1.0) * sqrt(0.5*(sqFl+sqFr)))
        mdot = 0.5*(DFl*(VnFl+abs(VnAvgLP)) + DFr*(VnFr-abs(VnAvgRM)) - xhi*deltP/cavg)*iLenFi

        beltaLP = 0.5*(mdot + abs(mdot))
        beltaRM = 0.5*(mdot - abs(mdot))

        beltaLP = 0.5*(mdot + abs(mdot)*SVLFact + (1.0-SVLFact)*Dm_VL)
        beltaRM = 0.5*(mdot - abs(mdot)*SVLFact - (1.0-SVLFact)*Dm_VL)

        fluxFi(     1) = beltaLP                + beltaRM
        fluxFi(     2) = beltaLP*soluFl(     2) + beltaRM*soluFr(     2) + Pbar*coefFi(   1)
        fluxFi(     3) = beltaLP*soluFl(     3) + beltaRM*soluFr(     3) + Pbar*coefFi(   2)
        fluxFi(nDim+1) = beltaLP*soluFl(nDim+1) + beltaRM*soluFr(nDim+1) + Pbar*coefFi(nDim)
        fluxFi(nDim+2) = beltaLP*hFl            + beltaRM*hFr

        if(VnAvg >= 0.0) then
            iDir = 1
        else
            iDir = -1
        endif

    endsubroutine
    !
    attributes(host,device) subroutine fluxSplit_SLAU2R1(soluFl,soluFr,coefFi,fluxFi,Gamma,GasCo,ratio,iDir)
        implicit none
        real(kind=REALLEN):: soluFl(nCons)
        real(kind=REALLEN):: soluFr(nCons)
        real(kind=REALLEN):: coefFi(nDim)
        real(kind=REALLEN):: fluxFi(nCons)
        real(kind=REALLEN):: Gamma,GasCo,ratio
        integer:: iDir
        !
        real(kind=REALLEN):: iLenFi,nvorFi(nDim)
        real(kind=REALLEN):: DFl,DFr,sqFl,sqFr,VnFl,VnFr,hFl,hFr,cFl,cFr
        real(kind=REALLEN):: deltD,deltP,VnAvg,DAvg,cAvg,MLP,MRM,Mkar,xhi,fp,gLP,gRM,fd
        real(kind=REALLEN):: beltaLP,beltaRM,Pbar,mdot,FFi(5),VnAvgLP,VnAvgRM
        real(kind=REALLEN):: MBar,xhi0,xhi1,omega


        iLenFi = sqrt(coefFi(1)*coefFi(1)+coefFi(2)*coefFi(2)+coefFi(nDim)*coefFi(nDim)*(nDim-2))
        nvorFi(   1) = coefFi(   1)/iLenFi
        nvorFi(   2) = coefFi(   2)/iLenFi
        nvorFi(nDim) = coefFi(nDim)/iLenFi

        DFl  = soluFl(1)/(GasCo*soluFl(nDim+2))
        DFr  = soluFr(1)/(GasCo*soluFr(nDim+2))
        sqFl = soluFl(2)*soluFl(2) + soluFl(3)*soluFl(3) + soluFl(4)*soluFl(4)*(nDim-2)
        sqFr = soluFr(2)*soluFr(2) + soluFr(3)*soluFr(3) + soluFr(4)*soluFr(4)*(nDim-2)
        VnFl = soluFl(2)*nvorFi(1) + soluFl(3)*nvorFi(2) + soluFl(4)*nvorFi(nDim)*(nDim-2)
        VnFr = soluFr(2)*nvorFi(1) + soluFr(3)*nvorFi(2) + soluFr(4)*nvorFi(nDim)*(nDim-2)
        hFl  = Gamma/(Gamma-1.0)*soluFl(1)/DFl + 0.5*sqFl
        hFr  = Gamma/(Gamma-1.0)*soluFr(1)/DFr + 0.5*sqFr
        cFl  = sqrt(Gamma*soluFl(1)/DFl)
        cFr  = sqrt(Gamma*soluFr(1)/DFr)

        deltD = DFr       - DFl
        deltP = soluFr(1) - soluFl(1)

        DAvg  = 0.5*(DFl + DFr)
        cAvg  = 0.5*(cFl + cFr)
        VnAvg = (DFl*VnFl + DFr*VnFr)/(DFl+DFr)
        MLP   = VnFl/cAvg
        MRM   = VnFr/cAvg

        Mkar  = min(1.0, sqrt(0.5*(sqFl+sqFr))/cAvg)
        xhi0  = (1.0 - Mkar)**2
        MBar  = min(1.0, sqrt(0.5*(MLP*MLP+MRM*MRM)))
        xhi1  = (1.0 - MBar)**2
        omega = 1.0/(1.0+exp(2.5*ratio))
        xhi  = omega*xhi1 + (1.0-omega)*xhi0
        fp   = 1.0 - xhi

        gLP = -max(-1.0, min(MLP, 0.0))
        gRM =  min( 1.0, max(MRM, 0.0))
        fd  = 1.0 - gLP*gRM

        VnAvgLP = fd*abs(VnAvg)+(1-fd)*abs(VnFl)
        VnAvgRM = fd*abs(VnAvg)+(1-fd)*abs(VnFr)

        if(MLP >= 1.0) then
            beltaLP = 1.0
        elseif(MLP <= -1.0) then
            beltaLP = 0.0
        else
            beltaLP = 0.25*(2-MLP)*(MLP+1)*(MLP+1)
        endif

        if(MRM >= 1.0) then
            beltaRM = 0.0
        elseif(MRM <= -1.0) then
            beltaRM = 1.0
        else
            beltaRM = 0.25*(2+MRM)*(MRM-1)*(MRM-1)
        end if

        Pbar = 0.5*((soluFl(1) + soluFr(1)) + (soluFl(1) - soluFr(1))*(beltaLP - beltaRM) + &
                    DAvg * cAvg * (beltaLP + beltaRM - 1.0) * sqrt(0.5*(sqFl+sqFr)))
        mdot = 0.5*(DFl*(VnFl+abs(VnAvgLP)) + DFr*(VnFr-abs(VnAvgRM)) - xhi*deltP/cavg)*iLenFi

        beltaLP = 0.5*(mdot + abs(mdot))
        beltaRM = 0.5*(mdot - abs(mdot))

        fluxFi(     1) = beltaLP                + beltaRM
        fluxFi(     2) = beltaLP*soluFl(     2) + beltaRM*soluFr(     2) + Pbar*coefFi(   1)
        fluxFi(     3) = beltaLP*soluFl(     3) + beltaRM*soluFr(     3) + Pbar*coefFi(   2)
        fluxFi(nDim+1) = beltaLP*soluFl(nDim+1) + beltaRM*soluFr(nDim+1) + Pbar*coefFi(nDim)
        fluxFi(nDim+2) = beltaLP*hFl            + beltaRM*hFr

        if(VnAvg >= 0.0) then
            iDir = 1
        else
            iDir = -1
        endif

    endsubroutine
    !
    attributes(host,device) subroutine fluxSplit_SLAU2R2(soluFl,soluFr,coefFi,fluxFi,Gamma,GasCo,iDir)
        implicit none
        real(kind=REALLEN):: soluFl(nCons)
        real(kind=REALLEN):: soluFr(nCons)
        real(kind=REALLEN):: coefFi(nDim)
        real(kind=REALLEN):: fluxFi(nCons)
        real(kind=REALLEN):: Gamma,GasCo
        integer:: iDir
        !
        real(kind=REALLEN):: iLenFi,nvorFi(nDim)
        real(kind=REALLEN):: DFl,DFr,sqFl,sqFr,VnFl,VnFr,hFl,hFr,cFl,cFr
        real(kind=REALLEN):: deltD,deltP,VnAvg,DAvg,cAvg,MLP,MRM,Mkar,xhi,fp,gLP,gRM,fd
        real(kind=REALLEN):: beltaLP,beltaRM,Pbar,mdot,FFi(5),VnAvgLP,VnAvgRM


        iLenFi = sqrt(coefFi(1)*coefFi(1)+coefFi(2)*coefFi(2)+coefFi(nDim)*coefFi(nDim)*(nDim-2))
        nvorFi(   1) = coefFi(   1)/iLenFi
        nvorFi(   2) = coefFi(   2)/iLenFi
        nvorFi(nDim) = coefFi(nDim)/iLenFi

        DFl  = soluFl(1)/(GasCo*soluFl(nDim+2))
        DFr  = soluFr(1)/(GasCo*soluFr(nDim+2))
        sqFl = soluFl(2)*soluFl(2) + soluFl(3)*soluFl(3) + soluFl(4)*soluFl(4)*(nDim-2)
        sqFr = soluFr(2)*soluFr(2) + soluFr(3)*soluFr(3) + soluFr(4)*soluFr(4)*(nDim-2)
        VnFl = soluFl(2)*nvorFi(1) + soluFl(3)*nvorFi(2) + soluFl(4)*nvorFi(nDim)*(nDim-2)
        VnFr = soluFr(2)*nvorFi(1) + soluFr(3)*nvorFi(2) + soluFr(4)*nvorFi(nDim)*(nDim-2)
        hFl  = Gamma/(Gamma-1.0)*soluFl(1)/DFl + 0.5*sqFl
        hFr  = Gamma/(Gamma-1.0)*soluFr(1)/DFr + 0.5*sqFr
        cFl  = sqrt(Gamma*soluFl(1)/DFl)
        cFr  = sqrt(Gamma*soluFr(1)/DFr)

        deltD = DFr       - DFl
        deltP = soluFr(1) - soluFl(1)

        DAvg  = 0.5*(DFl + DFr)
        cAvg  = 0.5*(cFl + cFr)
        VnAvg = (DFl*VnFl + DFr*VnFr)/(DFl+DFr)
        MLP   = VnFl/cAvg
        MRM   = VnFr/cAvg

        Mkar = min(1.0, sqrt(0.5*(sqFl+sqFr))/cAvg)
        xhi  = (1.0 - Mkar)**2
        fp   = 1.0 - (1.0 - Mkar)**2

        gLP = -max(-1.0, min(MLP, 0.0))
        gRM =  min( 1.0, max(MRM, 0.0))
        fd  = 1.0 - gLP*gRM

        VnAvgLP = fd*abs(VnAvg)+(1-fd)*abs(VnFl)
        VnAvgRM = fd*abs(VnAvg)+(1-fd)*abs(VnFr)

        if(MLP >= 1.0) then
            beltaLP = 1.0
        elseif(MLP <= -1.0) then
            beltaLP = 0.0
        else
            beltaLP = 0.25*(2-MLP)*(MLP+1)*(MLP+1)
        endif

        if(MRM >= 1.0) then
            beltaRM = 0.0
        elseif(MRM <= -1.0) then
            beltaRM = 1.0
        else
            beltaRM = 0.25*(2+MRM)*(MRM-1)*(MRM-1)
        end if

        Pbar = 0.5*((soluFl(1) + soluFr(1)) + (soluFl(1) - soluFr(1))*(beltaLP - beltaRM) + &
                    DAvg * cAvg * (beltaLP + beltaRM - 1.0) * sqrt(0.5*(sqFl+sqFr)))
        mdot = 0.5*(DFl*(VnFl+abs(VnAvgLP)) + DFr*(VnFr-abs(VnAvgRM)) - xhi*deltP/cavg)*iLenFi

        beltaLP = 0.5*(mdot + abs(mdot))
        beltaRM = 0.5*(mdot - abs(mdot))

        fluxFi(     1) = beltaLP                + beltaRM
        fluxFi(     2) = beltaLP*soluFl(     2) + beltaRM*soluFr(     2) + Pbar*coefFi(   1)
        fluxFi(     3) = beltaLP*soluFl(     3) + beltaRM*soluFr(     3) + Pbar*coefFi(   2)
        fluxFi(nDim+1) = beltaLP*soluFl(nDim+1) + beltaRM*soluFr(nDim+1) + Pbar*coefFi(nDim)
        fluxFi(nDim+2) = beltaLP*hFl            + beltaRM*hFr

        if(MLP+MRM >= 0.0) then
            iDir = 1
        else
            iDir = -1
        endif

    endsubroutine
    !
end module mod_FluxSplit