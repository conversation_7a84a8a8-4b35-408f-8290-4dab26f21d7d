!
module mod_Interface_SortArray
    implicit none
    !
    interface sortArray1D
        MODULE PROCEDURE sortArray1D_Int
        MODULE PROCEDURE sortArray1D_Real4
        MODULE PROCEDURE sortArray1D_Real8
    endinterface
    !
    interface sortArray1DWithOrder
        MODULE PROCEDURE sortArray1DWO_Int
        MODULE PROCEDURE sortArray1DWO_Real4
        MODULE PROCEDURE sortArray1DWO_Real8
    endinterface
    !
contains
    ! 
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine sortArray1D_Int(val_iArray,val_nSize)
        use mod_Interface_LOG2
        implicit none
        integer,intent(inout):: val_iArray(val_nSize)
        integer,intent(in   ):: val_nSize
        !
        integer:: i,j,iSize,nLevel
        integer:: iBegin,iEnd,jBegin,jEnd
        integer:: tempArray(val_nSize)
        
        
        tempArray(:) = val_iArray(:)
        
        nLevel = LOG2(val_nSize)
        
        do i=1,nLevel
            iSize = 2**i
            do j=1,val_nSize,iSize
                iBegin = j
                iEnd   = j + iSize/2 - 1
                jBegin = j + iSize/2
                jEnd   = j + iSize - 1
                
                if(iEnd >= val_nSize) cycle
                jEnd = min(jEnd, val_nSize)
                
                call sortMerge_Int(iBegin,iEnd,jBegin,jEnd)
                
            enddo
        enddo
        
        val_iArray(:) = tempArray(:)
        
    contains
        !
        subroutine sortMerge_Int(val_iB,val_iE,val_jB,val_jE)
            implicit none
            integer,intent(in):: val_iB
            integer,intent(in):: val_iE
            integer,intent(in):: val_jB
            integer,intent(in):: val_jE
            !
            integer:: ii,jj
            integer:: iID,jID,iPoin
            integer:: subArray(val_iB:val_jE)
            
            !
            iID   = val_iB
            jID   = val_jB
            iPoin = val_iB - 1
            do ii=val_iB,val_jE
                if(iID > val_iE) then
                    do jj=jID,val_jE
                        iPoin = iPoin + 1
                        subArray(iPoin) = tempArray(jj)
                    enddo
                    exit
                endif
            
                if(jID > val_jE) then
                    do jj=iID,val_iE
                        iPoin = iPoin + 1
                        subArray(iPoin) = tempArray(jj)
                    enddo
                    exit
                endif
            
                if(tempArray(iID) <= tempArray(jID)) then
                    iPoin = iPoin + 1
                    subArray(iPoin) = tempArray(iID)
                    iID = iID + 1
                else
                    iPoin = iPoin + 1
                    subArray(iPoin) = tempArray(jID)
                    jID = jID + 1
                endif
            enddo
        
            do ii=val_iB,val_jE
                tempArray(ii) = subArray(ii)
            enddo
            
        endsubroutine
        !
    endsubroutine
    !
    subroutine sortArray1D_Real4(val_iArray,val_nSize)
        use mod_Interface_LOG2
        implicit none
        real*4 ,intent(inout):: val_iArray(val_nSize)
        integer,intent(in   ):: val_nSize
        !
        integer:: i,j,iSize,nLevel
        integer:: iBegin,iEnd,jBegin,jEnd
        real*4 :: tempArray(val_nSize)
        
        
        tempArray(:) = val_iArray(:)
        
        nLevel = LOG2(val_nSize)
        
        do i=1,nLevel
            iSize = 2**i
            do j=1,val_nSize,iSize
                iBegin = j
                iEnd   = j + iSize/2 - 1
                jBegin = j + iSize/2
                jEnd   = j + iSize - 1
                
                if(iEnd >= val_nSize) cycle
                
                jEnd = min(jEnd, val_nSize)
                
                call sortMerge_Real4(iBegin,iEnd,jBegin,jEnd)
                
            enddo
        enddo
        
        val_iArray(:) = tempArray(:)
        
    contains
        !
        subroutine sortMerge_Real4(val_iB,val_iE,val_jB,val_jE)
            implicit none
            integer,intent(in):: val_iB
            integer,intent(in):: val_iE
            integer,intent(in):: val_jB
            integer,intent(in):: val_jE
            !
            integer:: ii,jj
            integer:: iID,jID,iPoin
            real*4 :: subArray(val_iB:val_jE)
            
            !
            iID   = val_iB
            jID   = val_jB
            iPoin = val_iB - 1
            do ii=val_iB,val_jE
                if(iID > val_iE) then
                    do jj=jID,val_jE
                        iPoin = iPoin + 1
                        subArray(iPoin) = tempArray(jj)
                    enddo
                    exit
                endif
            
                if(jID > val_jE) then
                    do jj=iID,val_iE
                        iPoin = iPoin + 1
                        subArray(iPoin) = tempArray(jj)
                    enddo
                    exit
                endif
            
                if(tempArray(iID) <= tempArray(jID)) then
                    iPoin = iPoin + 1
                    subArray(iPoin) = tempArray(iID)
                    iID = iID + 1
                else
                    iPoin = iPoin + 1
                    subArray(iPoin) = tempArray(jID)
                    jID = jID + 1
                endif
            enddo
        
            do ii=val_iB,val_jE
                tempArray(ii) = subArray(ii)
            enddo
            
        endsubroutine
        !
    endsubroutine
    !
    subroutine sortArray1D_Real8(val_iArray,val_nSize)
        use mod_Interface_LOG2
        implicit none
        real*8 ,intent(inout):: val_iArray(val_nSize)
        integer,intent(in   ):: val_nSize
        !
        integer:: i,j,iSize,nLevel
        integer:: iBegin,iEnd,jBegin,jEnd
        real*8 :: tempArray(val_nSize)
        
        
        tempArray(:) = val_iArray(:)
        
        nLevel = LOG2(val_nSize)
        
        do i=1,nLevel
            iSize = 2**i
            do j=1,val_nSize,iSize
                iBegin = j
                iEnd   = j + iSize/2 - 1
                jBegin = j + iSize/2
                jEnd   = j + iSize - 1
                
                if(iEnd >= val_nSize) cycle
                jEnd = min(jEnd, val_nSize)
                
                call sortMerge_Real8(iBegin,iEnd,jBegin,jEnd)
                
            enddo
        enddo
        
        val_iArray(:) = tempArray(:)
        
    contains
        !
        subroutine sortMerge_Real8(val_iB,val_iE,val_jB,val_jE)
            implicit none
            integer,intent(in):: val_iB
            integer,intent(in):: val_iE
            integer,intent(in):: val_jB
            integer,intent(in):: val_jE
            !
            integer:: ii,jj
            integer:: iID,jID,iPoin
            real*8 :: subArray(val_iB:val_jE)
            
            !
            iID   = val_iB
            jID   = val_jB
            iPoin = val_iB - 1
            do ii=val_iB,val_jE
                if(iID > val_iE) then
                    do jj=jID,val_jE
                        iPoin = iPoin + 1
                        subArray(iPoin) = tempArray(jj)
                    enddo
                    exit
                endif
            
                if(jID > val_jE) then
                    do jj=iID,val_iE
                        iPoin = iPoin + 1
                        subArray(iPoin) = tempArray(jj)
                    enddo
                    exit
                endif
            
                if(tempArray(iID) <= tempArray(jID)) then
                    iPoin = iPoin + 1
                    subArray(iPoin) = tempArray(iID)
                    iID = iID + 1
                else
                    iPoin = iPoin + 1
                    subArray(iPoin) = tempArray(jID)
                    jID = jID + 1
                endif
            enddo
        
            do ii=val_iB,val_jE
                tempArray(ii) = subArray(ii)
            enddo
            
        endsubroutine
        !
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine sortArray1DWO_Int(val_iArray,val_iOrder,val_nSize)
        use mod_Interface_LOG2
        implicit none
        integer,intent(inout):: val_iArray(val_nSize)
        integer,intent(out  ):: val_iOrder(val_nSize)
        integer,intent(in   ):: val_nSize
        !
        integer:: i,j,iSize,nLevel
        integer:: iBegin,iEnd,jBegin,jEnd
        integer:: tempArray(val_nSize)
        integer:: tempOrder(val_nSize)
        
        
        do i=1,val_nSize
            tempArray(i) = val_iArray(i)
            tempOrder(i) = i
        enddo
        
        nLevel = LOG2(val_nSize)
        
        do i=1,nLevel
            iSize = 2**i
            do j=1,val_nSize,iSize
                iBegin = j
                iEnd   = j + iSize/2 - 1
                jBegin = j + iSize/2
                jEnd   = j + iSize - 1
                
                if(iEnd >= val_nSize) cycle
                jEnd = min(jEnd, val_nSize)
                
                call sortMerge_Int(iBegin,iEnd,jBegin,jEnd)
                
            enddo
        enddo
        
        do i=1,val_nSize
            val_iArray(i) = tempArray(i)
            val_iOrder(i) = tempOrder(i)
        enddo
        
    contains
        !
        subroutine sortMerge_Int(val_iB,val_iE,val_jB,val_jE)
            implicit none
            integer,intent(in):: val_iB
            integer,intent(in):: val_iE
            integer,intent(in):: val_jB
            integer,intent(in):: val_jE
            !
            integer:: ii,jj
            integer:: iID,jID,iPoin
            integer:: subArray(val_iB:val_jE)
            integer:: subOrder(val_iB:val_jE)
            
            !
            iID   = val_iB
            jID   = val_jB
            iPoin = val_iB - 1
            do ii=val_iB,val_jE
                if(iID > val_iE) then
                    do jj=jID,val_jE
                        iPoin = iPoin + 1
                        subArray(iPoin) = tempArray(jj)
                        subOrder(iPoin) = tempOrder(jj)
                    enddo
                    exit
                endif
            
                if(jID > val_jE) then
                    do jj=iID,val_iE
                        iPoin = iPoin + 1
                        subArray(iPoin) = tempArray(jj)
                        subOrder(iPoin) = tempOrder(jj)
                    enddo
                    exit
                endif
            
                if(tempArray(iID) <= tempArray(jID)) then
                    iPoin = iPoin + 1
                    subArray(iPoin) = tempArray(iID)
                    subOrder(iPoin) = tempOrder(iID)
                    iID = iID + 1
                else
                    iPoin = iPoin + 1
                    subArray(iPoin) = tempArray(jID)
                    subOrder(iPoin) = tempOrder(jID)
                    jID = jID + 1
                endif
            enddo
        
            do ii=val_iB,val_jE
                tempArray(ii) = subArray(ii)
                tempOrder(ii) = subOrder(ii)
            enddo
            
        endsubroutine
        !
    endsubroutine
    !
    !---------------------------------------------
    subroutine sortArray1DWO_Real4(val_iArray,val_iOrder,val_nSize)
        use mod_Interface_LOG2
        implicit none
        real*4 ,intent(inout):: val_iArray(val_nSize)
        integer,intent(out  ):: val_iOrder(val_nSize)
        integer,intent(in   ):: val_nSize
        !
        integer:: i,j,iSize,nLevel
        integer:: iBegin,iEnd,jBegin,jEnd
        real*4 :: tempArray(val_nSize)
        integer:: tempOrder(val_nSize)
        
        
        do i=1,val_nSize
            tempArray(i) = val_iArray(i)
            tempOrder(i) = i
        enddo
        
        nLevel = LOG2(val_nSize)
        
        do i=1,nLevel
            iSize = 2**i
            do j=1,val_nSize,iSize
                iBegin = j
                iEnd   = j + iSize/2 - 1
                jBegin = j + iSize/2
                jEnd   = j + iSize - 1
                
                if(iEnd >= val_nSize) cycle
                jEnd = min(jEnd, val_nSize)
                
                call sortMerge_Real4(iBegin,iEnd,jBegin,jEnd)
                
            enddo
        enddo
        
        do i=1,val_nSize
            val_iArray(i) = tempArray(i)
            val_iOrder(i) = tempOrder(i)
        enddo
        
    contains
        !
        subroutine sortMerge_Real4(val_iB,val_iE,val_jB,val_jE)
            implicit none
            integer,intent(in):: val_iB
            integer,intent(in):: val_iE
            integer,intent(in):: val_jB
            integer,intent(in):: val_jE
            !
            integer:: ii,jj
            integer:: iID,jID,iPoin
            real*4 :: subArray(val_iB:val_jE)
            integer:: subOrder(val_iB:val_jE)
            
            !
            iID   = val_iB
            jID   = val_jB
            iPoin = val_iB - 1
            do ii=val_iB,val_jE
                if(iID > val_iE) then
                    do jj=jID,val_jE
                        iPoin = iPoin + 1
                        subArray(iPoin) = tempArray(jj)
                        subOrder(iPoin) = tempOrder(jj)
                    enddo
                    exit
                endif
            
                if(jID > val_jE) then
                    do jj=iID,val_iE
                        iPoin = iPoin + 1
                        subArray(iPoin) = tempArray(jj)
                        subOrder(iPoin) = tempOrder(jj)
                    enddo
                    exit
                endif
            
                if(tempArray(iID) <= tempArray(jID)) then
                    iPoin = iPoin + 1
                    subArray(iPoin) = tempArray(iID)
                    subOrder(iPoin) = tempOrder(iID)
                    iID = iID + 1
                else
                    iPoin = iPoin + 1
                    subArray(iPoin) = tempArray(jID)
                    subOrder(iPoin) = tempOrder(jID)
                    jID = jID + 1
                endif
            enddo
        
            do ii=val_iB,val_jE
                tempArray(ii) = subArray(ii)
                tempOrder(ii) = subOrder(ii)
            enddo
            
        endsubroutine
        !
    endsubroutine
    !
    !---------------------------------------------
    subroutine sortArray1DWO_Real8(val_iArray,val_iOrder,val_nSize)
        use mod_Interface_LOG2
        implicit none
        real*8 ,intent(inout):: val_iArray(val_nSize)
        integer,intent(out  ):: val_iOrder(val_nSize)
        integer,intent(in   ):: val_nSize
        !
        integer:: i,j,iSize,nLevel
        integer:: iBegin,iEnd,jBegin,jEnd
        real*8 :: tempArray(val_nSize)
        integer:: tempOrder(val_nSize)
        
        
        do i=1,val_nSize
            tempArray(i) = val_iArray(i)
            tempOrder(i) = i
        enddo
        
        nLevel = LOG2(val_nSize)
        
        do i=1,nLevel
            iSize = 2**i
            do j=1,val_nSize,iSize
                iBegin = j
                iEnd   = j + iSize/2 - 1
                jBegin = j + iSize/2
                jEnd   = j + iSize - 1
                
                if(iEnd >= val_nSize) cycle
                jEnd = min(jEnd, val_nSize)
                
                call sortMerge_Real8(iBegin,iEnd,jBegin,jEnd)
                
            enddo
        enddo
        
        do i=1,val_nSize
            val_iArray(i) = tempArray(i)
            val_iOrder(i) = tempOrder(i)
        enddo
        
    contains
        !
        subroutine sortMerge_Real8(val_iB,val_iE,val_jB,val_jE)
            implicit none
            integer,intent(in):: val_iB
            integer,intent(in):: val_iE
            integer,intent(in):: val_jB
            integer,intent(in):: val_jE
            !
            integer:: ii,jj
            integer:: iID,jID,iPoin
            real*8 :: subArray(val_iB:val_jE)
            integer:: subOrder(val_iB:val_jE)
            
            !
            iID   = val_iB
            jID   = val_jB
            iPoin = val_iB - 1
            do ii=val_iB,val_jE
                if(iID > val_iE) then
                    do jj=jID,val_jE
                        iPoin = iPoin + 1
                        subArray(iPoin) = tempArray(jj)
                        subOrder(iPoin) = tempOrder(jj)
                    enddo
                    exit
                endif
            
                if(jID > val_jE) then
                    do jj=iID,val_iE
                        iPoin = iPoin + 1
                        subArray(iPoin) = tempArray(jj)
                        subOrder(iPoin) = tempOrder(jj)
                    enddo
                    exit
                endif
            
                if(tempArray(iID) <= tempArray(jID)) then
                    iPoin = iPoin + 1
                    subArray(iPoin) = tempArray(iID)
                    subOrder(iPoin) = tempOrder(iID)
                    iID = iID + 1
                else
                    iPoin = iPoin + 1
                    subArray(iPoin) = tempArray(jID)
                    subOrder(iPoin) = tempOrder(jID)
                    jID = jID + 1
                endif
            enddo
        
            do ii=val_iB,val_jE
                tempArray(ii) = subArray(ii)
                tempOrder(ii) = subOrder(ii)
            enddo
            
        endsubroutine
        !
    endsubroutine
    !
endmodule
!
    
   
