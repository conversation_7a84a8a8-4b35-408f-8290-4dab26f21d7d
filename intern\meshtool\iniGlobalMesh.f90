!
subroutine iniGlobalMesh
    use mod_Mesh
    implicit none
    
    iGlobalMesh%nDime = nDime
    
    call iniGlobalMesh_poin
    
    call iniGlobalMesh_doma
    
    call iniGlobalMesh_elem
    
    call iniGlobalMesh_mark
    
    call iniGlobalMesh_Bele
    
endsubroutine
!
subroutine iniGlobalMesh_poin
    use mod_Mesh
    implicit none
    integer:: i,j,ni
    integer:: count
    
    count = 0
    do i=1,nMesh
        count = count+ iInMesh(i)%nPoin
    enddo
    iGlobalMesh%nPoin = count
    
    allocate(iGlobalMesh%iCoor(iGlobalMesh%nDime,iGlobalMesh%nPoin))
    
    do i=1,nMesh
        do j=1,iInMesh(i)%nPoin
            ni = iInMesh(i)%globalPoinID(j)
            iGlobalMesh%iCoor(:,ni) = iInMesh(i)%iCoor(:,j)
        enddo
    enddo
    
endsubroutine
!
subroutine iniGlobalMesh_doma
    use mod_Mesh
    implicit none
    integer:: i,j,k,l,ni
    integer:: count
    integer:: icci(8)
    
    count = 0
    do i=1,nMesh
        count = count+ iInMesh(i)%nDoma
    enddo
    iGlobalMesh%nDoma = count
    
    allocate(iGlobalMesh%iDoma(iGlobalMesh%nDoma))
    
    do i=1,nMesh
        do j=1,iInMesh(i)%nDoma
            ni = iInMesh(i)%globalDomaID(j)
            iGlobalMesh%iDoma(ni) = iInMesh(i)%iDoma(j)
            do k=1,iInMesh(i)%iDoma(j)%nElem
                icci(1:4*nDime-4) = iInMesh(i)%iDoma(j)%iElem(:,k)
                do l=1,4*nDime-4
                    iGlobalMesh%iDoma(ni)%iElem(l,k) = iInMesh(i)%globalPoinID(icci(l))
                enddo
            enddo
        enddo
    enddo
    
endsubroutine
!
subroutine iniGlobalMesh_elem
    use mod_Mesh
    implicit none
    integer:: i,j,k
    integer:: count
    integer:: ielemsize
    
    count = 0
    do i=1,iGlobalMesh%nDoma
        count = count+iGlobalMesh%iDoma(i)%nElem
    enddo
    
    ielemsize = iGlobalMesh%nDime*4-4
    iGlobalMesh%nElem = count
    allocate(iGlobalMesh%iElem(ielemsize,iGlobalMesh%nElem))
    
    count = 0
    do i=1,iGlobalMesh%nDoma
        do j=1,iGlobalMesh%iDoma(i)%nElem
            iGlobalMesh%iElem(:,count+j) = iGlobalMesh%iDoma(i)%iElem(:,j)
        enddo
        count = count+iGlobalMesh%iDoma(i)%nElem
    enddo
    
endsubroutine
!
subroutine iniGlobalMesh_mark
    use mod_Mesh
    implicit none
    integer:: i,j,k,l,ni
    integer:: count
    integer:: icci(3)
    
    count = 0
    do i=1,nMesh
        count = count+ iInMesh(i)%nMark
    enddo
    iGlobalMesh%nMark = count
    
    allocate(iGlobalMesh%iMark(iGlobalMesh%nMark))
    
    do i=1,nMesh
        do j=1,iInMesh(i)%nMark
            ni = iInMesh(i)%globalMarkID(j)
            iGlobalMesh%iMark(ni) = iInMesh(i)%iMark(j)
            do k=1,iInMesh(i)%iMark(j)%nElem
                icci(1:2*nDime-2) = iInMesh(i)%iMark(j)%iElem(:,k)
                do l=1,2*nDime-2
                    iGlobalMesh%iMark(ni)%iElem(l,k) = iInMesh(i)%globalPoinID(icci(l))
                enddo
            enddo
        enddo
    enddo
    
endsubroutine
!
subroutine iniGlobalMesh_Bele
    use mod_Mesh
    implicit none
    integer:: i,j,k
    integer:: count
    integer:: ielemsize
    
    count = 0
    do i=1,iGlobalMesh%nMark
        count = count+iGlobalMesh%iMark(i)%nElem
    enddo
    
    ielemsize = iGlobalMesh%nDime*2-2
    iGlobalMesh%nBEle = count
    allocate(iGlobalMesh%iBEle(ielemsize,iGlobalMesh%nBEle))
    
    count = 0
    do i=1,iGlobalMesh%nMark
        do j=1,iGlobalMesh%iMark(i)%nElem
            iGlobalMesh%iBEle(:,count+j) = iGlobalMesh%iMark(i)%iElem(:,j)
        enddo
        count = count+iGlobalMesh%iMark(i)%nElem
    enddo
    
endsubroutine
