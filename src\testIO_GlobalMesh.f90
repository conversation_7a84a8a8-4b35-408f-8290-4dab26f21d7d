!
subroutine testIO_GlobalMesh(val_iMesh,val_iIter)
    use mod_PreDefine_Dimension
    use mod_PreDefine_IOPort
    use mod_TypeDef_Mesh
    use mod_WorkPath
    use mod_strOfNumber
    implicit none
    type(typ_GlobalMesh),intent(in):: val_iMesh
    integer,intent(in):: val_iIter
    !
    integer:: i,j,elemID
    character(len=STRLEN):: string_varList
    character(len=STRLEN):: string_ZoneType
    character(len=STRLEN):: string_VarShare
    character(len=STRLEN):: fileName
    
    
    if(nDim == 2) then
        string_varList  = 'VARIABLES="X","Y","Index","iPart" '
        string_ZoneType = 'FEQUADRILATERAL'
        string_VarShare = '([1-4]=1)'
    elseif(nDim == 3) then
        string_varList  = 'VARIABLES="X","Y","Z","Index","iPart" '
        string_ZoneType = 'FEBRICK'
        string_VarShare = '([1-5]=1)'
    else
        return
    endif
    
    if(val_iIter >= 0) then
        fileName = 'GlobalDeform_'//trim(strTen_Number(val_iIter))//'.plt'
    else
        fileName = 'GlobalMesh.plt'
    endif
    
    open(ioPort_FULL,file=trim(testFullPath)//'/'//trim(fileName),asynchronous='yes',status='unknown')
    
    do i = 1,val_iMesh%nDoma
        if(val_iMesh%iDoma(i)%nElem <= 0) cycle
        
        write(ioPort_FULL,*) trim(adjustl(string_varList))
        if(i == 1) then
            write(ioPort_FULL,*)'ZONE DATAPACKING = POINT, NODES =', val_iMesh%nPoin,   &
                                    ', ELEMENTS =', val_iMesh%iDoma(i)%nElem,           &
                                    ', ZONETYPE = ',trim(adjustl(string_ZoneType))
            
            do j=1,val_iMesh%nPoin
                write(ioPort_FULL,*) val_iMesh%iCoor_Poin(:,j),j,val_iMesh%iPart_Poin(j)
            enddo
            
        else
            write(ioPort_FULL,*)'ZONE DATAPACKING = POINT, NODES =', val_iMesh%nPoin,   &
                                    ', ELEMENTS =', val_iMesh%iDoma(i)%nElem,           &
                                    ', ZONETYPE = ',trim(adjustl(string_ZoneType)),     &
                                    ', VARSHARELIST=',trim(adjustl(string_VarShare))
        endif
        
        do j=1,val_iMesh%iDoma(i)%nElem
            elemID = val_iMesh%iDoma(i)%iElem(j)
            
            write(ioPort_FULL,*) val_iMesh%iElem(:,elemID)
        enddo
    enddo
    
    close(ioPort_FULL)
    
endsubroutine
!