!
subroutine bldMeshDeformZone(val_iMesh)
    use mod_TypeDef_Mesh
    use mod_Mesh_Deform
    implicit none
    type(typ_GlobalMesh),intent(in):: val_iMesh
    !
    integer:: i,j,k,s
    integer:: iCount,jCount,kCount
    integer:: regnID,markID,domaID,poinID
    integer:: iFlag_Mark(val_imesh%nMark)
    integer:: iFlag_Doma(val_iMesh%nDoma)
    integer:: iFlag_Poin(val_iMesh%nPoin)
    integer:: iMark_Poin(val_iMesh%nPoin)
    integer:: iMark_Mark(val_iMesh%nMark)
    
    
    iMark_Poin(:) = 0
    do i=1,val_iMesh%nMark
        do j=1,val_iMesh%iMark(i)%nPoin
            poinID = val_iMesh%iMark(i)%iPoin(j)
            
            iMark_Poin(poinID) = i
        enddo
    enddo
    
    
    do i=1,nDeformZone
        iFlag_Mark(:) = 0
        iFlag_Doma(:) = 0
        iFlag_Poin(:) = DEFORMPOINT_NONE  
        iMark_Mark(:) = 0
        
        do j=1,iDeformZone(i)%nRegn
            regnID = iDeformZone(i)%iRegnList(j)
            do k=1,val_iMesh%iRegn(regnID)%nMark
                markID = val_iMesh%iRegn(regnID)%markList(k)
                
                iFlag_Mark(markID) = 1
            enddo
            
            do k=1,val_iMesh%iRegn(regnID)%nDoma
                domaID = val_iMesh%iRegn(regnID)%domaList(k)
                
                iFlag_Doma(domaID) = 1
            enddo
        enddo
        
        do j=1,iDeformZone(i)%nMark
            markID = iDeformZone(i)%iMarkList(j)
            
            iFlag_Mark(markID) = 2
            iMark_Mark(markID) = j
        enddo
        
        do j=1,val_iMesh%nDoma
            if(iFlag_Doma(j) == 1) then
                do k=1,val_iMesh%iDoma(j)%nPoin
                    poinID = val_iMesh%iDoma(j)%iPoin(k)
                    
                    iFlag_Poin(poinID) = DEFORMPOINT_INTER
                enddo
            endif
        enddo
        
        do j=1,val_iMesh%nMark
            if(iFlag_Mark(j) == 1) then
                do k=1,val_iMesh%iMark(j)%nPoin
                    poinID = val_iMesh%iMark(j)%iPoin(k)
                    
                    iFlag_Poin(poinID) = DEFORMPOINT_STILL
                enddo
            elseif(iFlag_Mark(j) == 2) then
                do k=1,val_iMesh%iMark(j)%nPoin
                    poinID = val_iMesh%iMark(j)%iPoin(k)
                    
                    iFlag_Poin(poinID) = DEFORMPOINT_DEFOM
                enddo
            endif
        enddo
        
        iCount = 0
        jCount = 0
        do j=1,val_iMesh%nPoin
            if(iFlag_Poin(j) == DEFORMPOINT_INTER) then
                iCount = iCount + 1
            elseif(iFlag_Poin(j) == DEFORMPOINT_STILL) then
                jCount = jCount + 1
            elseif(iFlag_Poin(j) == DEFORMPOINT_DEFOM) then
                jCount = jCount + 1
            endif
        enddo
        
        
        iDeformZone(i)%nPoin   = iCount
        iDeformZone(i)%nPoinBC = jCount
        allocate(iDeformZone(i)%iPoinProp(iDeformZone(i)%nPoin,3))
        allocate(iDeformZone(i)%iPoinPropBC(iDeformZone(i)%nPoinBC,3))
        iDeformZone(i)%iPoinProp(:,:)   = 0
        iDeformZone(i)%iPoinPropBC(:,:) = 0
        
        
        iCount = 0
        jCount = 0
        do j=1,val_iMesh%nPoin
            if(iFlag_Poin(j) == DEFORMPOINT_INTER) then
                iCount = iCount + 1
                iDeformZone(i)%iPoinProp(iCount,1) = j
                iDeformZone(i)%iPoinProp(iCount,2) = DEFORMPOINT_INTER
                iDeformZone(i)%iPoinProp(iCount,3) = 0
                
            elseif(iFlag_Poin(j) == DEFORMPOINT_STILL) then
                jCount = jCount + 1
                iDeformZone(i)%iPoinPropBC(jCount,1) = j
                iDeformZone(i)%iPoinPropBC(jCount,2) = DEFORMPOINT_STILL
                iDeformZone(i)%iPoinPropBC(jCount,3) = 0
                
            elseif(iFlag_Poin(j) == DEFORMPOINT_DEFOM) then
                jCount = jCount + 1
                iDeformZone(i)%iPoinPropBC(jCount,1) = j
                iDeformZone(i)%iPoinPropBC(jCount,2) = DEFORMPOINT_DEFOM
                iDeformZone(i)%iPoinPropBC(jCount,3) = iMark_Mark(iMark_Poin(j))
                
            endif
        enddo
        
        
        SELECT CASE (iDeformZone(i)%DeformKind)
        CASE (DEFORMKIND_TRANS,DEFORMKIND_REGNT,DEFORMKIND_SOFTT)
            allocate(iDeformZone(i)%iDeltPoin(nDim,iDeformZone(i)%nPoin))
            allocate(iDeformZone(i)%iDeltPoinBC(nDim,iDeformZone(i)%nPoinBC))
            
        CASE (DEFORMKIND_ROTAT,DEFORMKIND_REGNR,DEFORMKIND_SOFTR)
            allocate(iDeformZone(i)%iDeltPoin(1,iDeformZone(i)%nPoin))
            allocate(iDeformZone(i)%iDeltPoinBC(1,iDeformZone(i)%nPoinBC))
            
            allocate(iDeformZone(i)%iOrgnAndR(iDeformZone(i)%nPoin,2))
            allocate(iDeformZone(i)%iOrgnAndRBC(iDeformZone(i)%nPoinBC,2))  
            
        END SELECT
        
    enddo
    
    
endsubroutine
!
    
    
