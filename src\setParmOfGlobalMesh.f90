
!---------------------------------------------
!
!---------------------------------------------
subroutine setParmOfGlobalMesh(val_iMesh)
    use mod_Config
    use mod_TypeDef_Mesh
    implicit none
    type(typ_GlobalMesh),intent(inout):: val_iMesh
    
    integer:: i
    
    
    !---------------------------------------------
    !---------------------------------------------
    !Step1: 默认初始化
    val_iMesh%isNeedMC          = .false.
    val_iMesh%isNeedOS          = .false.
    val_iMesh%isNeedSP          = .false.
    val_iMesh%isNeedPT          = .false.
    
    
    !---------------------------------------------
    !---------------------------------------------
    !分区设置;强制要求分区数上限为4
    if(NUMBER_PART > 4) then
        NUMBER_PART = 4
    endif
    
    
    !---------------------------------------------
    !---------------------------------------------
    !Step3: 根据Config进行配置
    if(TIME_DISCRE_FLOW /= _RUNGE_KUTTA_EXPLICIT) then
        val_iMesh%isNeedMC          = .true.
    endif
    
    do i=1,val_iMesh%nMark
        if(val_iMesh%iMark(i)%KIND == MARK_NONE) then
            write(*,*) "val_iMesh%iMark(i)%KIND == MARK_NONE",i
            val_iMesh%isNeedOS      = .true.
            exit
        endif
    enddo
    
    if(val_iMesh%nSlip > 0) then
        val_iMesh%isNeedSP      = .true.
    endif
    
    if(NUMBER_PART > 1) then
        val_iMesh%isNeedPT          = .true.
    endif
    
    
endsubroutine
!