module mod_GetSoluParm
    implicit none
    !
contains
    !
    subroutine getSoluParm_PhysicalTime(val_iParm, val_iIter, val_iTime)
        use mod_TypeDef_Parm
        implicit none
        type(typ_Parm    ),intent(in ):: val_iParm
        integer           ,intent(in ):: val_iIter
        real(kind=REALLEN),intent(out):: val_iTime
        !
        val_iTime = val_iParm%iTimeBegin + (val_iIter-1)*val_iParm%iTimeDelta
        !
    end subroutine
    !
    subroutine getSize_SoluPrim(val_nSoluX, val_nPrimX)
        use mod_PreDefine_IOPort
        use mod_PreDefine_Dimension
        use mod_Config, only: PHYSICAL_PROBLEM, KIND_TURB_MODEL
        use mod_Options
        implicit none
        integer,intent(inout):: val_nSoluX
        integer,intent(inout):: val_nPrimX

        if(PHYSICAL_PROBLEM == _EULER) then
            val_nSoluX = nDim+2
            val_nPrimX = nDim+2
        elseif(PHYSICAL_PROBLEM == _NS) then
            val_nSoluX = nDim+2
            val_nPrimX = nDim+3
        elseif(PHYSICAL_PROBLEM == _TURB) then
            select case(KIND_TURB_MODEL)
            case(_TURB_SA)
                val_nSoluX = nDim+3
                val_nPrimX = nDim+4
            case(_TURB_KE)
                val_nSoluX = nDim+4
                val_nPrimX = nDim+4
            case(_TURB_KW)
                val_nSoluX = nDim+4
                val_nPrimX = nDim+4
            case(_TURB_SST)
                val_nSoluX = nDim+4
                val_nPrimX = nDim+4
            case default
                write(ioPort_Out,*) 'Error of KIND_TURB_MODEL'
                stop
            end select
        else
            write(ioPort_Out,*) 'Error of PHYSICAL_PROBLEM'
            stop
        endif

    endsubroutine
    !
    subroutine getResuFileLabel(val_iStep,val_filename)
        use mod_PreDefine_Precision
        use mod_strOfNumber
        implicit none
        integer,intent(in):: val_iStep
        character(len=STRLEN),intent(inout):: val_filename
        !
        character(len=STRLEN):: strFlag

        if(val_iStep == 0) then
            strFlag = 'init'
        elseif(val_iStep == -99999) then
            strFlag = 'final'
        elseif(val_iStep == -8888) then
            strFlag = 'nan'
        elseif(val_iStep > 0) then
            strFlag = 'mid_'//trim(strSix_Number(val_iStep))
        else
            strFlag = 'test_'//trim(strSix_Number(-val_iStep))
        endif

        val_filename = trim(strFlag)

    endsubroutine
    !
    subroutine getAeroFMFileLabel(val_iStep,val_filename)
        use mod_PreDefine_Precision
        use mod_strOfNumber
        implicit none
        integer,intent(in):: val_iStep
        character(len=STRLEN),intent(inout):: val_filename
        !
        character(len=STRLEN):: strFlag

        if(val_iStep == 0) then
            strFlag = 'init'
        elseif(val_iStep == -99999) then
            strFlag = 'final'
        elseif(val_iStep == -8888) then
            strFlag = 'nan'
        elseif(val_iStep > 0) then
            strFlag = 'mid_'//strSix_Number(val_iStep)
        else
            strFlag = 'test_'
        endif

        val_filename = trim(strFlag)

    endsubroutine
    !
end module mod_GetSoluParm