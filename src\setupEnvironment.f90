!
!---------------------------------------------
!
!---------------------------------------------
subroutine setupProject
    use mod_PreDefine_Environment
    use mod_PreDefine_Precision
    use mod_PreDefine_IOPort
    use mod_MPIEnvironment
    use mod_WorkPath
    use mod_ErrMsg
    use mod_MPI_Exch
    implicit none
    
    
    !---------------------------------------------
    call mimd_init

    call setupLicense

    if(myID == MASTER_NODE) then
        call setWorkPath
    endif
    
    call mimd_Sync


#ifdef GFLOW_WITH_MPI
    !---------------------------------------------
    if(USING_MPI.and.(nProcess > 1)) then
        call MPI_BCAST(projName     ,STRLEN,MPI_CHARACTER,0,MPI_COMM_WORLD,iError)
        call MPI_BCAST(soluName     ,STRLEN,MPI_CHARACTER,0,MPI_COMM_WORLD,iError)
        call MPI_BCAST(cfigName     ,STRLEN,MPI_CHARACTER,0,MPI_COMM_WORLD,iError)
        call MPI_BCAST(rootPath     ,STRLEN,MPI_CHARACTER,0,MPI_COMM_WORLD,iError)
        call MPI_BCAST(workPath     ,STRLEN,MPI_CHARACTER,0,MPI_COMM_WORLD,iError)
        call MPI_BCAST(meshFullPath ,STRLEN,MPI_CHARACTER,0,MPI_COMM_WORLD,iError)
        call MPI_BCAST(soluFullPath ,STRLEN,MPI_CHARACTER,0,MPI_COMM_WORLD,iError)
        call MPI_BCAST(cfigFullPath ,STRLEN,MPI_CHARACTER,0,MPI_COMM_WORLD,iError)
        call MPI_BCAST(geomFullPath ,STRLEN,MPI_CHARACTER,0,MPI_COMM_WORLD,iError)
        call MPI_BCAST(resuFullPath ,STRLEN,MPI_CHARACTER,0,MPI_COMM_WORLD,iError)
        call MPI_BCAST(testFullPath ,STRLEN,MPI_CHARACTER,0,MPI_COMM_WORLD,iError)
        call MPI_BCAST(middFullPath ,STRLEN,MPI_CHARACTER,0,MPI_COMM_WORLD,iError)
        call MPI_BCAST(meshDynaPath ,STRLEN,MPI_CHARACTER,0,MPI_COMM_WORLD,iError)
        call MPI_BCAST(meshPartPath ,STRLEN,MPI_CHARACTER,0,MPI_COMM_WORLD,iError)
    endif
#endif
    
    !---------------------------------------------
    if((iErr /= 0).and.(myID == MASTER_NODE)) then
        write(ioPort_Out,*) trim(iErrMsg)
        stop
    endif
    
endsubroutine
!
subroutine setupLicense
    implicit none
    character*8 :: date
    character*5 :: zone
    !
    integer:: year_month

    call date_and_time(DATE=date, ZONE=zone)

    read(date(1:6),*) year_month

    !write(*,*) 'Year=',year
    !write(*,*) 'month=',month
    !write(*,*) 'day=',day

    if(year_month >= 202508) then
        write(*,*) "Invalid license."
        write(*,*) "Please contact email: <EMAIL>"
        stop
    end if

endsubroutine
!
!---------------------------------------------
!
!---------------------------------------------
subroutine setProjNameFromFile
    use mod_PreDefine_Precision
    use mod_PreDefine_IOPort
    use mod_WorkPath
    use mod_MPIEnvironment
    use mod_ErrMsg
    implicit none
    !namelist
    namelist/Project/projName,soluName
    !
    integer:: i,istat
    character(len = STRLEN):: iString


    !---------------------------------------------
    open(ioPort_FULL,file='current.dat',iostat=istat,status='old')
    if(istat /= 0) then
        write(ioPort_Out,*) 'ERROR: Open File failed (current.dat)'
        stop
    endif

    read(ioPort_FULL,nml=Project,iostat=istat,iomsg= iString)

    close(ioPort_FULL)

    if(istat /= 0) then
        iErr    = 1
        iErrMsg = trim(iString)
        return
    endif

endsubroutine
!
subroutine setProjNameFromCMD(currentPATH)
    use mod_PreDefine_Precision
    use mod_PreDefine_IOPort
    use mod_WorkPath
    use mod_MPIEnvironment
    use mod_GPUEnvironment
    use mod_ErrMsg
    implicit none
    character(len = STRLEN):: currentPATH
    !
    integer:: i,iargc
    character(len=256):: Argv

    do i=1,iargc()
        call getarg(i, Argv)

        Argv = adjustl(Argv)
        if(Argv(1:5) == "-wdir") then
            currentPATH = Argv(7:)
        elseif(Argv(1:5) == "-proj") then
            projName = adjustl(Argv(7:))
        elseif(Argv(1:5) == "-solu") then
            soluName = adjustl(Argv(7:))
        elseif(Argv(1:5) == "-nomp") then
            read(Argv(7:),*) nOMPThreads
        elseif(Argv(1:4) == "-gid") then
            read(Argv(6:),*) currentGPUID
        end if
    end do

    !write(*,*) trim(currentPATH)
    !write(*,*) trim(projName)
    !write(*,*) trim(soluName)
    !write(*,*) 'nOMPThreads =', nOMPThreads
    !write(*,*) 'currentGPUID =', currentGPUID
!pause

endsubroutine
!
subroutine setWorkPath
    use mod_PreDefine_Precision
    use mod_PreDefine_IOPort
    use mod_WorkPath
    use mod_MPIEnvironment
    use mod_GPUEnvironment
    use mod_ErrMsg
    implicit none
    integer:: iargc
    !logical function 
    logical :: isPathOrFileExist
    !namelist
    namelist/Project/projName,soluName,nOMPThreads,currentGPUID
    !
    integer:: i,istat,len_PATH,getcwd
    character(len = STRLEN):: currentPATH = ' '
    character(len = STRLEN):: iString


    !---------------------------------------------
    !istat = GetCurrentDirectory(STRLEN,currentPATH)
    istat = getcwd(currentPATH)

    if(iargc() >= 3) then
        write(IOPort_OUT,*) "Parse work path from command line.\n"
        call setProjNameFromCMD(currentPATH)
    else
        write(IOPort_OUT,*) "Parse work path from file current.dat.\n"
        call setProjNameFromFile(currentPATH)
    end if

    
    !---------------------------------------------
    !配置项目基本属性
    projName = adjustl(projName)
    soluName = adjustl(soluName)
    cfigName = 'parms.cfg'
    
    rootPath = adjustl(currentPATH)
    len_PATH = len_trim(rootPath)
    
    workPath = rootPath(1:len_PATH)//'/'//trim(projName)
    
    meshFullPath = trim(workPath)//'/'//trim(meshFolderName)
    soluFullPath = trim(workPath)//'/'//trim(soluFolderName)//'/'//trim(soluName)
    cfigFullPath = trim(soluFullPath)//'/parms.cfg'
    
    geomFullPath = trim(soluFullPath)//'/Geom'
    resuFullPath = trim(soluFullPath)//'/Resu'
    
    testFullPath = trim(soluFullPath)//'/Test'
    middFullPath = trim(soluFullPath)//'/Midd'
    meshDynaPath = trim(soluFullPath)//'/MESH_DYNA'
    meshPartPath = trim(soluFullPath)//'/MESH_PART'

    !write(*,*) trim(cfigFullPath)
    !if(.not.isPathOrFileExist(cfigFullPath)) then
    !    iErr    = 1
    !    iErrMsg = 'Error: The specified project or solution does not exist!'
    !    return
    !endif
    
    call createFolder(geomFullPath,iErr)
    call createFolder(resuFullPath,iErr)
    call createFolder(testFullPath,iErr)
    
    
endsubroutine
!
!---------------------------------------------
!
!---------------------------------------------
subroutine initGPUEnvironment
    use mod_PreDefine_IOPort
    use mod_GPUEnvironment
    use mod_MPIEnvironment
    use mod_MPI_Exch
    implicit none
    !
    integer:: i
    integer:: istat

    if(nProcess == 1 .and. currentGPUID == -1) then
        write(ioPort_Out,*) 'GPU not used'
        return
    end if

    !---------------------------------------------
    !获取GPU设备数
    !---------------------------------------------
    istat = cudaGetDeviceCount(deviceCount)

    if(deviceCount == 0) then
        write(ioPort_Out,*) 'Error: CUDA deviceCount = 0' 
        call mimd_Exit
        stop
    elseif(deviceCount < nProcess) then
        write(ioPort_Out,*) 'Error: deviceCount < nProcess' 
        call mimd_Exit
        stop
    endif

    
    !---------------------------------------------
    !获取各GPU属性
    !---------------------------------------------
    allocate(deviceProps(deviceCount))

    do i=1,deviceCount
        istat = cudaGetDeviceProperties(deviceProps(i),i-1)
    enddo
    
    if(myID == MASTER_NODE) then
        !write(ioPort_Out,*) ' '
        write(ioPort_Out,'(2X,A,I2)') '>> The CUDA deviceCount:',deviceCount
        do i=0,deviceCount-1
            write(ioPort_Out,'(7X,A,I2,A,A)') 'Device',i,' : ',trim(adjustl(deviceProps(i+1)%name))
        enddo
    endif
    
    
    !---------------------------------------------
    !为当前进程指定GPU
    !---------------------------------------------
    if(currentGPUID < 0) then
        currentGPUID = mod(myID,deviceCount)
    else
        if(currentGPUID >= deviceCount) then
            write(ioPort_Out,"(A,I2,A,I2,A)") 'currentGPUID(',currentGPUID,') >= deviceCount(',deviceCount,')'
            stop
        end if
    endif

    currentDeviceProp = deviceProps(currentGPUID + 1)
    
    istat = cudaSetDevice(currentGPUID)
    
    write(ioPort_Out,"(2X,A,I2,A,I2)") '>> The device',currentGPUID,' is sellected for Process',myID

endsubroutine
!

