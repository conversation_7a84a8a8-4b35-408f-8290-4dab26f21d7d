!
!---------------------------------------------
!
!---------------------------------------------
module mod_TypeDef_MeshComponent
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    use mod_PreDefine_Mark
    use mod_PreDefine_ExteKind
    use mod_PreDefine_ExchKind
    use mod_TypeDef_Division
    implicit none
    
    
    !---------------------------------------------
    !网格区块信息 （每个边界标记或每个网格块标记，为一网格区块）
    !---------------------------------------------
    type:: typ_Block
        integer:: ID                = 0         !ID
        character(len=STRLEN):: TAGS      !标签
        character(len=STRLEN):: NAME      !名称
        character(len=STRLEN):: KSTR      !类型
        integer:: KIND              = 0         !类型
        integer:: markID            = 0
        !
        integer:: nElem             = 0         !单元数目
        integer:: nValdElem         = 0         !有效单元数
        integer,allocatable:: iElem(:)          !单元编号
        !
        integer:: nPoin             = 0         !节点数目
        integer,allocatable:: iPoin(:)          !节点编号
    endtype
    
    
    !---------------------------------------------
    !网格区域信息 （由联通的网格块构成一个网格区域）
    !---------------------------------------------
    type:: typ_MeshRegn
        integer:: nDoma              = 0        !区域数目
        integer:: nMark              = 0        !Mark数目
        !
        integer,allocatable:: domaList(:)       !区域编号
        integer,allocatable:: markList(:)       !Mark编号
    endtype
    
    
    !---------------------------------------------
    !网格中需要插值的分区
    !---------------------------------------------
    type:: typ_ExteRegn
        integer:: KIND              = EXTE_NONE !插值类型
        !三种: 滑移、重叠、分区
        !
        integer:: bengRegnID        = 0         !所属网格区编号
        integer:: hostRegnID        = 0         !插值宿主网格区编号
        !
        integer:: nPoin             = 0
        integer:: nSate             = 0
        !
        integer,allocatable:: iPoinProp(:,:)    !(nPoin,3)
        real(kind=REALLEN),allocatable:: iCoor_Poin(:,:)
        !
        integer:: nOrgnPoin         = 0         !所属网格区原有节点数
        integer,allocatable:: kSate_Poin(:)     !(nOrgnPoin+1)
        integer,allocatable:: iSate_Poin(:,:)   !(nSate,2)
    endtype

    
    !---------------------------------------------
    !边界对：周期边界成对出现，滑移边界成对出现
    !---------------------------------------------
    type:: typ_BCPair
        integer:: KIND              = BCPAIR_NONE   !滑移，周期，交接
        character(len=STRLEN):: KSTR      !类型
        !
        integer:: nMarkL            = 0
        integer:: nMarkR            = 0
        integer,allocatable:: iMarkL(:)
        integer,allocatable:: iMarkR(:)
        !
        integer:: nPoinL            = 0         !nPoinL=nPoinR
        integer:: nPoinR            = 0         !nPoinR=nPoinL
        integer,allocatable:: iPoinL(:)
        integer,allocatable:: iPoinR(:)
        !
        type(typ_DivRegin):: iDivRegnL
        type(typ_DivRegin):: iDivRegnR
        !
        integer,allocatable:: iAlteSate_PoinL(:,:)
        integer,allocatable:: iAlteSate_PoinR(:,:)
        !
    endtype
    
    
    !---------------------------------------------
    !网格数据传输信息 （分区、重叠、滑移等均为传输的一种形式）
    !---------------------------------------------
    type:: typ_ExchRegn
        integer:: KIND              = EXCH_NONE !交换类型
        
        !分区数据传输相关数据
        integer:: EXID              = 0         !目标线程ID
        integer:: iExchTag          = 0         !执行状况
        !
        integer:: nDataPerNode      = 0         !每点包含数据数
        integer           ,allocatable:: iExchData(:,:) !整型数据
        real(kind=REALLEN),allocatable:: rExchData(:,:) !浮点数据
        
        !数据来源（分区交互）
        integer:: nPoin             = 0         !点数
        integer,allocatable:: iPoin(:)          !点编号:+Poin-Belv
    endtype
    
    
endmodule
!
