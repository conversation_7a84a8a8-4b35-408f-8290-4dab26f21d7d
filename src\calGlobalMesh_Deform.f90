!
subroutine calGlobalMesh_Deform(val_iMesh,val_iIter)
    use mod_TypeDef_Mesh
    use mod_Mesh_Deform
    use mod_calMesh_Overset
    use mod_calMesh_Slipping
    use mod_calMesh_Geometric
    implicit none
    type(typ_GlobalMesh),intent(inout):: val_iMesh
    integer,intent(in):: val_iIter
    !
    integer:: i,j
    
    
    call readMesh_DeformValu
    
    call calMeshDeform_OnBC
    
    do i=1,nDeformZone
        if(iDeformZone(i)%DeformMethod == DEFORMMETHOD_DWM) then !distance weighting
    
            call calMeshDeform_DWM(iDeformZone(i),val_iMesh)
        elseif(iDeformZone(i)%DeformMethod == DEFORMMETHOD_SM) then !spring method
        
        !    call calMeshDeform_SM(iDeformZone(i),val_iMesh)
        elseif(iDeformZone(i)%DeformMethod == DEFORMMETHOD_DM) then !delaunay mapping method
        
        !    call calMeshDeform_DM(iDeformZone(i),val_iMesh)
        elseif(iDeformZone(i)%DeformMethod == DEFORMMETHOD_RBF) then !radial based function
        
        !    call calMeshDeform_RBF(iDeformZone(i),val_iMesh)
        elseif(iDeformZone(i)%DeformMethod == DEFORMMETHOD_DMRBF) then !DM+RBF
        
        !    call calMeshDeform_DMRBF(iDeformZone(i),val_iMesh)
        endif
    enddo
    
    call updMeshDeform_Coor(val_iMesh)
    
    call calGlobalMesh_Geometric(val_iMesh)
    
    if(val_iMesh%isNeedOS) then
        call calMeshReform_Overset(val_iMesh)
    elseif(val_iMesh%isNeedSP) then
        call calMeshReform_Slipping(val_iMesh)
    endif
    
endsubroutine
!
