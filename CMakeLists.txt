cmake_minimum_required(VERSION 3.8 FATAL_ERROR)

set(CMAKE_C_COMPILER  gcc)
set(CMAKE_CXX_COMPILER  g++)
set(CMAKE_Fortran_COMPILER nvfortran)
set(CMAKE_CUDA_ARCHITECTURES 80)

string(TIMESTAMP DataTime "date +%Y-%m-%d_%H:%M")
string(TIMESTAMP GFLOW_VERSION "%Y.%m")
set(PROJECT_NAME "GFlow_v${GFLOW_VERSION}")

project(${PROJECT_NAME} LANGUAGES C Fortran CUDA)

include(GNUInstallDirs)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/${CMAKE_INSTALL_LIBDIR})
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/${CMAKE_INSTALL_LIBDIR})
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/${CMAKE_INSTALL_BINDIR})
set(${CMAKE_INSTALL_INCLUDEDIR} "${CMAKE_BINARY_DIR}/include")
set(${CMAKE_INSTALL_DATADIR} "${CMAKE_BINARY_DIR}/share")

message(STATUS "CMAKE_ARCHIVE_OUTPUT_DIRECTORY =: ${CMAKE_ARCHIVE_OUTPUT_DIRECTORY}")
message(STATUS "CMAKE_LIBRARY_OUTPUT_DIRECTORY =: ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}")
message(STATUS "CMAKE_RUNTIME_OUTPUT_DIRECTORY =: ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}")
message(STATUS "CMAKE_INSTALL_INCLUDEDIR =: ${CMAKE_INSTALL_INCLUDEDIR}")
message(STATUS "CMAKE_INSTALL_DATADIR =: ${CMAKE_INSTALL_DATADIR}")


if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release CACHE STRING "Build type" FORCE)
endif()

option(GFLOW_WITH_OMP "Build GFlow with OpenMP support" OFF)
option(GFLOW_WITH_MPI "Build GFlow with MPI support" OFF)
option(GFLOW_WITH_VTK "Build GFlow with VTK support" OFF)
option(GFLOW_WITH_CGNS "Build GFlow with CGNS support" OFF)

option(GFLOW_ENABLE_DP "Build GFlow in double precidion mode" ON)
option(GFLOW_ENABLE_3D "Build GFlow in 3D mode" OFF)

if(WIN32)
    set(CUDA_HOME "")

    if(GFLOW_WITH_MPI)
        set(MPI_HOME "")
    endif()
elseif(UNIX)
    if(APPLE)
    else()
        set(CUDA_HOME "/opt/nvidia/hpc_sdk/Linux_x86_64/23.5/compilers")

        if(GFLOW_WITH_MPI)
            set(MPI_HOME "/opt/nvidia/hpc_sdk/Linux_x86_64/23.5/comm_libs/mpi")
            find_package(MPI REQUIRED)
        endif()
    endif()
endif()

# find_package(MPI REQUIRED)
message(STATUS "CMAKE_Fortran_COMPILER_ID: ${CMAKE_Fortran_COMPILER_ID}")

set(CMAKE_Fortran_FLAGS "-cuda -O2 -fopenmp")
set(CMAKE_Fortran_FLAGS_DEBUG "-cuda -O0  -fopenmp -g")

message(STATUS "Fortran_Compile_Flags: ${CMAKE_Fortran_FLAGS}")
message(STATUS "Fortran_Compile_Flags_Debug: ${CMAKE_Fortran_FLAGS_DEBUG}")

# if(CMAKE_Fortran_COMPILER_ID MATCHES GNU)
# set(CMAKE_Fortran_FLAGS "${CMAKE_Fortran_FLAGS} -g -cpp -DLINUX_GNU -fdec-math -ffree-line-length-none -fallow-argument-mismatch -Wno-argument-mismatch")
# set(CMAKE_Fortran_FLAGS_DEBUG "${CMAKE_Fortran_FLAGS_DEBUG} -O0 -g -fbacktrace -fdec-math -ffree-line-length-none -Wall -fallow-argument-mismatch -Wno-argument-mismatch")
# elseif(CMAKE_Fortran_COMPILER_ID MATCHES Intel)
# message(STATUS "Fortran compiler ID is Intel")
# set(CMAKE_Fortran_FLAGS "${CMAKE_Fortran_FLAGS} -cpp -DINTEL -DLINUX_INTEL")
# set(CMAKE_Fortran_FLAGS_DEBUG "${CMAKE_Fortran_FLAGS} -O0 -g -traceback -DINTEL")
# message(STATUS "Fortran_Compile_Flags: ${CMAKE_Fortran_FLAGS}")
# message(STATUS "Fortran_Compile_Flags_Debug: ${CMAKE_Fortran_FLAGS_DEBUG}")
# else()
# message(FATAL_ERROR "CMake Error: Unsupported compiler ${CMAKE_Fortran_COMPILER_ID}")
# endif()

if(GFLOW_WITH_VTK)
    find_package(VTK)
    if(VTK_FOUND)
    else()
        add_subdirectory(extern/vtk)
        add_subdirectory(extern/VTKFortran)
        set(VTK_LIBRARIES "")
    endif()
endif()

if(GFLOW_WITH_CGNS)
    add_subdirectory(extern/CGNS)
endif()


#add_subdirectory(extern/su2_cgns)
#add_subdirectory(extern/metis)
add_subdirectory(intern/meshtool)

add_subdirectory(src)

