!
subroutine calMeshDeform_DWM(val_iDeformZone,val_iMesh)
    use mod_TypeDef_Mesh
    use mod_Mesh_Deform
    use mod_vectorAlgebra
    implicit none
    type(typ_DeformZone),intent(inout):: val_iDeformZone
    type(typ_GlobalMesh),intent(in):: val_iMesh
    !
    integer:: i,j
    real(kind=REALLEN):: iDeltCoor(nDim),iDeltAngle,iCoef,iDeno
    
    
    SELECT CASE (val_iDeformZone%DeformKind)
    CASE (DEFORMKIND_REGNT, DEFORMKIND_REGNR)
        !NONE
        do i=1,val_iDeformZone%nPoin
            val_iDeformZone%iDeltPoin(:,i) = val_iDeformZone%iDeltPoinBC(:,1)
        enddo
        
    CASE (DEFORMKIND_TRANS, DEFORMKIND_SOFTT)
           
        do i=1,val_iDeformZone%nPoin
            iDeltCoor(:) = 0.0
            iDeno        = 0.0
            do j=1,val_iDeformZone%nPoinBC
                iCoef = val_iDeformZone%iDeformCoef(j,i)
                    
                iDeltCoor(:) = iDeltCoor(:) + iCoef*val_iDeformZone%iDeltPoinBC(:,j)
                iDeno        = iDeno        + iCoef
            enddo
            val_iDeformZone%iDeltPoin(:,i) = iDeltCoor(:)/iDeno
        enddo
        
    CASE (DEFORMKIND_ROTAT, DEFORMKIND_SOFTR)
        do i=1,val_iDeformZone%nPoin
            iDeltAngle  = 0.0
            iDeno       = 0.0
            do j=1,val_iDeformZone%nPoinBC
                iCoef = val_iDeformZone%iDeformCoef(j,i)
                    
                iDeltAngle = iDeltAngle + iCoef*val_iDeformZone%iDeltPoinBC(1,j)
                iDeno      = iDeno      + iCoef
            enddo
            val_iDeformZone%iDeltPoin(1,i) = iDeltAngle/iDeno
        enddo
        
    END SELECT
    
    
endsubroutine
!
