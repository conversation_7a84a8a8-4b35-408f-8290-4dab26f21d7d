
!---------------------------------------------
!
!---------------------------------------------
subroutine initLocalMesh(val_iMesh)
    use mod_TypeDef_Mesh
    use mod_Config
    use mod_WorkPath
    use mod_MPIEnvironment
    use mod_PartMeshIO
    use mod_calLocalMesh_Topology
    use mod_calLocalMesh_Geometric
    use mod_Interface_AllocateArray
    implicit none
    type(typ_Mesh),intent(inout):: val_iMesh
    
    
    !---------------------------------------------
    !从网格子区读取网格
    call readPartMesh(val_iMesh, geomFullPath, myID)
    
    
    !---------------------------------------------
    !计算局部网格拓扑信息
    call calLocalMesh_Topology(val_iMesh)
    
    
    !---------------------------------------------
    !计算局部网格几何信息
    call calLocalMesh_Geometric(val_iMesh, 0)
    
    
    !---------------------------------------------
    !测试输出
    call testIO_LocalMesh(val_iMesh         , myID+1, 0)
    !call testIO_LocalMesh_Belv(val_iMesh    , myID+1, 0)
    !call testIO_LocalMesh_iCoef(val_iMesh   , myID+1, 0)
    !call testIO_LocalMesh_ROLayers(val_iMesh, myID+1, 0)
    !call testIO_LocalMesh_ReOrdering(val_iMesh, myID+1, 0)
    
endsubroutine
!
