!
module mod_matrixMultiplication
    use mod_PreDefine_Precision
    implicit none
    !
    contains
    !
    subroutine arrayLeftMultiplySquareMatrix(val_array,val_matrix,val_DimX)
        implicit none
        real(kind=REALLEN),intent(inout):: val_array(val_DimX)
        real(kind=REALLEN),intent(in   ):: val_matrix(val_DimX,val_DimX)
        integer           ,intent(in   ):: val_DimX
        !
        integer:: i,j
        real(kind=REALLEN):: iArray(val_DimX)
        
        
        do i=1,val_DimX
            iArray(i) = 0.0
            do j=1,val_DimX
                iArray(i) = iArray(i) + val_matrix(i,j)*val_array(j)
            enddo
        enddo
        
        val_array(:) = iArray(:)
        
    endsubroutine
    !
    subroutine arrayRightMultiplySquareMatrix(val_array,val_matrix,val_DimX)
        implicit none
        real(kind=REALLEN),intent(inout):: val_array(val_DimX)
        real(kind=REALLEN),intent(in   ):: val_matrix(val_DimX,val_DimX)
        integer           ,intent(in   ):: val_DimX
        !
        integer:: i,j
        real(kind=REALLEN):: iArray(val_DimX)
        
        
        do i=1,val_DimX
            iArray(i) = 0.0
            do j=1,val_DimX
                iArray(i) = iArray(i) + val_array(j)*val_matrix(j,i)
            enddo
        enddo
        
        val_array(:) = iArray(:)
        
    endsubroutine
    !
    subroutine arrayLeftMultiplyMatrix(val_arrayIn,val_matrix,val_arrayOut,val_DimX,val_DimY)
        implicit none
        real(kind=REALLEN),intent(in ):: val_arrayIn(val_DimY)
        real(kind=REALLEN),intent(in ):: val_matrix(val_DimX,val_DimY)
        real(kind=REALLEN),intent(out):: val_arrayOut(val_DimX)
        integer           ,intent(in):: val_DimX
        integer           ,intent(in):: val_DimY
        !
        integer:: i,j
        real(kind=REALLEN):: iArray(val_DimX)
        
        
        do i=1,val_DimX
            iArray(i) = 0.0
            do j=1,val_DimY
                iArray(i) = iArray(i) + val_matrix(i,j)*val_arrayIn(j)
            enddo
        enddo
        
        val_arrayOut(:) = iArray(:)
        
    endsubroutine
    !
    subroutine arrayRightMultiplyMatrix(val_arrayIn,val_matrix,val_arrayOut,val_DimX,val_DimY)
        implicit none
        real(kind=REALLEN),intent(in ):: val_arrayIn(val_DimX)
        real(kind=REALLEN),intent(in ):: val_matrix(val_DimX,val_DimY)
        real(kind=REALLEN),intent(out):: val_arrayOut(val_DimY)
        integer           ,intent(in):: val_DimX
        integer           ,intent(in):: val_DimY
        !
        integer:: i,j
        real(kind=REALLEN):: iArray(val_DimY)
        
        
        do i=1,val_DimY
            iArray(i) = 0.0
            do j=1,val_DimX
                iArray(i) = iArray(i) + val_arrayIn(j)*val_matrix(j,i)
            enddo
        enddo
        
        val_arrayOut(:) = iArray(:)
        
    endsubroutine
    !
    subroutine matrixLeftMultiplySquareMatrix
    endsubroutine
    !
    subroutine matrixRightMultiplySquareMatrix
    endsubroutine
    !
    subroutine matrixLeftMultiplyMatrix
    endsubroutine
    !
    subroutine matrixRightMultiplyMatrix
    endsubroutine
    !
endmodule
!
