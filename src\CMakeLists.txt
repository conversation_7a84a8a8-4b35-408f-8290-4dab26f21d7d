
set(Sources "")
file(GLOB_RECURSE Sources "${CMAKE_CURRENT_SOURCE_DIR}/*.f90")

# message(STATUS "Sources are: ${Sources}")

string(TIMESTAMP DataTime "date +%Y-%m-%d_%H:%M")
message(STATUS "Make Time: ${DataTime}")

string(TIMESTAMP Version "%Y.%m")

set(GFlow_Target "GFlow_v${Version}")

if(GFLOW_ENABLE_DP)
    if(GFLOW_ENABLE_3D)
        set(GFlow_Target "GFlow_3ddp_v${Version}")
    else()
        set(GFlow_Target "GFlow_2ddp_v${Version}")
    endif()
else()
    if(GFLOW_ENABLE_3D)
        set(GFlow_Target "GFlow_3dsp_v${Version}")
    else()
        set(GFlow_Target "GFlow_2dsp_v${Version}")
    endif()
endif()

message(STATUS "Build Target : ${GFlow_Target}")

add_executable(${GFlow_Target} ${Sources})
target_compile_options(${GFlow_Target} PRIVATE -DGFLOW_ENABLE_CUDA)


if(GFLOW_ENABLE_DP)
    target_compile_options(${GFlow_Target} PRIVATE -DGFLOW_PRECISION_DP)
else()
    target_compile_options(${GFlow_Target} PRIVATE -DGFLOW_PRECISION_SP)
endif()

if(GFLOW_ENABLE_3D)
    target_compile_options(${GFlow_Target} PRIVATE -DGFLOW_DIMENSION_3D)
else()
    target_compile_options(${GFlow_Target} PRIVATE -DGFLOW_DIMENSION_2D)
endif()

target_compile_options(${GFlow_Target} PRIVATE -Mpreprocess)

target_include_directories(${GFlow_Target} PRIVATE  ${CMAKE_INSTALL_INCLUDEDIR})
if(GFLOW_WITH_CGNS)
    target_compile_options(${GFlow_Target} PRIVATE -DGFLOW_WITH_CGNS)
    target_link_libraries(${GFlow_Target} PUBLIC cgns)
endif()

if(GFLOW_WITH_VTK)
    target_compile_options(${GFlow_Target} PRIVATE -DGFLOW_WITH_VTK)
    target_link_libraries(${GFlow_Target} PRIVATE ${VTK_LIBRARIES})
    message(STATUS "VTK_Fortran_LIBRARIES${VTK_LIBRARIES}")
endif()

if(GFLOW_WITH_MPI)
    message(STATUS "MPI_Fortran_INCLUDE_DIRS: ${MPI_Fortran_INCLUDE_DIRS}")
    message(STATUS "MPI_Fortran_LIBRARIES: ${MPI_Fortran_LIBRARIES}")

    target_compile_options(${GFlow_Target} PRIVATE -DGFLOW_ENABLE_MPI)
    target_include_directories(${GFlow_Target} PRIVATE ${MPI_Fortran_INCLUDE_DIRS})
    target_link_libraries(${GFlow_Target} PRIVATE ${MPI_Fortran_LIBRARIES})
endif()

if(GFLOW_WITH_OMP)
    target_compile_options(${GFlow_Target} PRIVATE -DGFLOW_ENABLE_OMP)
    target_compile_options(${GFlow_Target} PRIVATE -fopenmp)
    target_compile_options(${GFlow_Target} PRIVATE -mp=multicore)
endif()

if(CMAKE_BUILD_TYPE STREQUAL "Release")
    target_compile_options(${GFlow_Target} PRIVATE -fast)
endif()


