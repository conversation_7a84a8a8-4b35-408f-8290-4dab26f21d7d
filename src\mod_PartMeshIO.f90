!
module mod_PartMeshIO
    use mod_PreDefine_IOPort
    use mod_Interface_AllocateArray
    implicit none
    integer,parameter:: NUM_ENTITY          = 100
    integer,parameter:: NUM_ICOUNT          = 100
    
    !---------------------------------------------
    !Poin
    integer,parameter:: ENTITY_IPOIN        = 1
    integer,parameter:: ENTITY_IFLAG_POIN   = 2
    integer,parameter:: ENTITY_IPART_POIN   = 3
    integer,parameter:: ENTITY_IHOST_POIN   = 4
    integer,parameter:: ENTITY_ICLOR_POIN   = 5
    integer,parameter:: ENTITY_ICOOR_POIN   = 6
    integer,parameter:: ENTITY_IVOLU_POIN   = 7
    integer,parameter:: ENTITY_IWDIS_POIN   = 8
    integer,parameter:: ENTITY_DCOOR_POIN   = 9
    integer,parameter:: ENTITY_IDOMA_POIN   = 10
    !Sate
    integer,parameter:: ENTITY_KSATE_POIN   = 11
    integer,parameter:: ENTITY_ISATE_POIN   = 12
    integer,parameter:: ENTITY_ICOEF_SATE   = 13
    integer,parameter:: ENTITY_ILENS_SATE   = 14
    !Elem
    integer,parameter:: ENTITY_IELEM        = 21
    integer,parameter:: ENTITY_IDOMAPROP    = 22
    integer,parameter:: ENTITY_IELEMPROP    = 23
    !Bele
    integer,parameter:: ENTITY_IBELE        = 31
    integer,parameter:: ENTITY_IMARKPROP    = 32
    !Belv
    integer,parameter:: ENTITY_IBELVPROP    = 41
    integer,parameter:: ENTITY_ICOOR_BELV   = 44
    integer,parameter:: ENTITY_IAREA_BELV   = 45
    integer,parameter:: ENTITY_INVOR_BELV   = 46
    integer,parameter:: ENTITY_ICOEF_BELV   = 47
    integer,parameter:: ENTITY_IVELO_BELV   = 48
    !Exch
    integer,parameter:: ENTITY_IKIND_EXCH   = 51
    integer,parameter:: ENTITY_NPOIN_EXCH   = 52
    integer,parameter:: ENTITY_IPOIN_EXCH   = 53
    
    
    !---------------------------------------------
    integer,parameter:: LOCATION_REAL       = 1
    integer,parameter:: LOCATION_NDIME      = 2
    
    integer,parameter:: LOCATION_NDOMA      = 11
    integer,parameter:: LOCATION_NMARK      = 12
    integer,parameter:: LOCATION_NPOIN      = 13
    integer,parameter:: LOCATION_NELEM      = 14
    integer,parameter:: LOCATION_NCLOR      = 15
    
    integer,parameter:: LOCATION_NSATE      = 21
    integer,parameter:: LOCATION_NBELE      = 22
    integer,parameter:: LOCATION_NBELV      = 23
    integer,parameter:: LOCATION_NBELX      = 24
    
    integer,parameter:: LOCATION_NEXCH      = 31
    integer,parameter:: LOCATION_NEXPO      = 32
    
    !---------------------------------------------
    !---------------------------------------------
    integer:: entity_options(NUM_ENTITY)    = 0
    integer:: count_options(NUM_ICOUNT)     = 0
    !
contains
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getPartMeshFileName(val_ID,val_meshFileName)
        use mod_strOfNumber
        implicit none
        integer,intent(in):: val_ID
        character(len=100),intent(out):: val_meshFileName
        
    
        val_meshFileName = 'mesh.'//strTwo_Number(val_ID)//'.prt'
    
    endsubroutine
    !
    subroutine getPartMeshUpdFileName(val_ID,val_meshFileName)
        use mod_strOfNumber
        implicit none
        integer,intent(in):: val_ID
        character(len=100),intent(out):: val_meshFileName
        
    
        val_meshFileName = 'meshUpd.'//strTwo_Number(val_ID)//'.prt'
    
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine readPartMesh(val_iMesh,val_path,val_myID)
        use mod_TypeDef_Mesh
        use mod_PreDefine_IOPort
        use mod_Interface_allocateArray
        implicit none
        type(typ_Mesh),intent(inout):: val_iMesh
        character(len=*),intent(in):: val_path
        integer,intent(in):: val_myID
        !
        integer:: i,j,iCount
        character(len=100):: meshFileName
        !
        integer:: nExchPoin
        integer,allocatable:: iKind_Exch(:)
        integer,allocatable:: nPoin_Exch(:)
        integer,allocatable:: iPoin_Exch(:)
    
        !---------------------------------------------
        call getPartMeshFileName(val_myID+1,meshFileName) !partID = myID=i + 1
    
    
        !---------------------------------------------
        open(ioPort_FULL, file=trim(val_path)//'/'//trim(meshFileName), &
                          FORM='unformatted', status='old'              )
            !---------------------------------------------
            !读取数目类数据
            count_options(:)  = 0
            read(ioPort_FULL) count_options
        
            if(count_options(LOCATION_REAL) /= REALLEN) then
                write(ioPort_Out,*) 'count_options(LOCATION_REAL) /= REALLEN'
                stop
            endif
        
            if(count_options(LOCATION_NDIME) /= nDim) then
                write(ioPort_Out,*) 'count_options(LOCATION_NDIME) /= nDim'
                stop
            endif
        
            val_iMesh%nDoma = count_options(LOCATION_NDOMA)
            val_iMesh%nMark = count_options(LOCATION_NMARK)
            val_iMesh%nPoin = count_options(LOCATION_NPOIN)
            val_iMesh%nElem = count_options(LOCATION_NELEM)
            val_iMesh%nClor = count_options(LOCATION_NCLOR)
            
            val_iMesh%nSate = count_options(LOCATION_NSATE)
            val_iMesh%nBele = count_options(LOCATION_NBELE)
            val_iMesh%nBelv = count_options(LOCATION_NBELV)
            val_iMesh%nBelx = count_options(LOCATION_NBELX)
            
            val_iMesh%nExch = count_options(LOCATION_NEXCH)
            nExchPoin       = count_options(LOCATION_NEXPO)
            
            
            !---------------------------------------------
            !读取实体标记
            entity_options(:) = 0
            read(ioPort_FULL) entity_options
        
        
            !---------------------------------------------
            !申请并读取实体内容
            do i=1,NUM_ENTITY
                if(entity_options(i) /= 1) cycle
            
                !POIN---------------------------------------------
                if(i == ENTITY_IPOIN) then
                    !call allocateArray(val_iMesh%iPoin, val_iMesh%nPoin)
                    !read(ioPort_FULL) val_iMesh%iPoin
                elseif(i == ENTITY_IFLAG_POIN) then
                    call allocateArray(val_iMesh%iFlag_Poin, val_iMesh%nPoin)
                    read(ioPort_FULL) val_iMesh%iFlag_Poin
                elseif(i == ENTITY_IPART_POIN) then
                    call allocateArray(val_iMesh%iPart_Poin, val_iMesh%nPoin)
                    read(ioPort_FULL) val_iMesh%iPart_Poin
                elseif(i == ENTITY_IHOST_POIN) then
                    call allocateArray(val_iMesh%iHost_Poin, val_iMesh%nPoin)
                    read(ioPort_FULL) val_iMesh%iHost_Poin
                elseif(i == ENTITY_ICLOR_POIN) then
                    call allocateArray(val_iMesh%iClor_Poin, val_iMesh%nPoin)
                    read(ioPort_FULL) val_iMesh%iClor_Poin
                elseif(i == ENTITY_ICOOR_POIN) then
                    call allocateArray(val_iMesh%iCoor_Poin, val_iMesh%nPoin, nDim)
                    read(ioPort_FULL) val_iMesh%iCoor_Poin
                elseif(i == ENTITY_IVOLU_POIN) then
                    call allocateArray(val_iMesh%iVolu_Poin, val_iMesh%nPoin)
                    read(ioPort_FULL) val_iMesh%iVolu_Poin
                elseif(i == ENTITY_IWDIS_POIN) then
                    call allocateArray(val_iMesh%iWDis_Poin, val_iMesh%nPoin)
                    read(ioPort_FULL) val_iMesh%iWDis_Poin
                elseif(i == ENTITY_DCOOR_POIN) then
                    call allocateArray(val_iMesh%dCoor_Poin, val_iMesh%nPoin, nDim)
                    read(ioPort_FULL) val_iMesh%dCoor_Poin
                elseif(i == ENTITY_IDOMA_POIN) then
                    call allocateArray(val_iMesh%iDoma_Poin, val_iMesh%nPoin)
                    read(ioPort_FULL) val_iMesh%iDoma_Poin

                !SATE---------------------------------------------
                elseif(i == ENTITY_KSATE_POIN) then
                    call allocateArray(val_iMesh%kSate_Poin, val_iMesh%nPoin+1)
                    read(ioPort_FULL) val_iMesh%kSate_Poin
                elseif(i == ENTITY_ISATE_POIN) then
                    call allocateArray(val_iMesh%iSate_Poin, val_iMesh%nSate, 2)
                    read(ioPort_FULL) val_iMesh%iSate_Poin
                elseif(i == ENTITY_ICOEF_SATE) then
                    call allocateArray(val_iMesh%iCoef_Sate, val_iMesh%nSate, nDim)
                    read(ioPort_FULL) val_iMesh%iCoef_Sate
                elseif(i == ENTITY_ILENS_SATE) then
                    call allocateArray(val_iMesh%iLens_Sate, val_iMesh%nSate)
                    read(ioPort_FULL) val_iMesh%iLens_Sate
        
                !ELEM---------------------------------------------
                elseif(i == ENTITY_IELEM) then
                    call allocateArray(val_iMesh%iElem, 4*nDim-4, val_iMesh%nElem)
                    read(ioPort_FULL) val_iMesh%iElem
                elseif(i == ENTITY_IDOMAPROP) then
                    call allocateArray(val_iMesh%iDomaProp, val_iMesh%nDoma, 3)
                    read(ioPort_FULL) val_iMesh%iDomaProp
                elseif(i == ENTITY_IELEMPROP) then
                    call allocateArray(val_iMesh%iElemProp, val_iMesh%nElem, 1)
                    read(ioPort_FULL) val_iMesh%iElemProp
        
                !BELE---------------------------------------------
                elseif(i == ENTITY_IBELE) then
                    call allocateArray(val_iMesh%iBele, 2*nDim-2, val_iMesh%nBele)
                    read(ioPort_FULL) val_iMesh%iBele
                elseif(i == ENTITY_IMARKPROP) then
                    call allocateArray(val_iMesh%iMarkProp, val_iMesh%nMark, 2)
                    read(ioPort_FULL) val_iMesh%iMarkProp
                  
                !BELV---------------------------------------------
                elseif(i == ENTITY_IBELVPROP) then
                    call allocateArray(val_iMesh%iBelvProp, val_iMesh%nBelv, 2)
                    read(ioPort_FULL) val_iMesh%iBelvProp 
                elseif(i == ENTITY_ICOOR_BELV) then
                    call allocateArray(val_iMesh%iCoor_Belv, val_iMesh%nBelv, nDim)
                    read(ioPort_FULL) val_iMesh%iCoor_Belv 
                elseif(i == ENTITY_IAREA_BELV) then
                    call allocateArray(val_iMesh%iArea_Belv, val_iMesh%nBelv)
                    read(ioPort_FULL) val_iMesh%iArea_Belv 
                elseif(i == ENTITY_INVOR_BELV) then
                    call allocateArray(val_iMesh%iNvor_Belv, val_iMesh%nBelv, nDim)
                    read(ioPort_FULL) val_iMesh%iNvor_Belv 
                elseif(i == ENTITY_ICOEF_BELV) then
                    call allocateArray(val_iMesh%iCoef_Belv, val_iMesh%nBelv, nDim)
                    read(ioPort_FULL) val_iMesh%iCoef_Belv 
                elseif(i == ENTITY_IVELO_BELV) then
                    call allocateArray(val_iMesh%iVelo_Belv, val_iMesh%nBelv, nDim)
                    read(ioPort_FULL) val_iMesh%iVelo_Belv
                    
                !EXCH---------------------------------------------
                elseif(i == ENTITY_IKIND_EXCH) then
                    call allocateArray(iKind_Exch, val_iMesh%nExch)
                    read(ioPort_FULL) iKind_Exch
                elseif(i == ENTITY_NPOIN_EXCH) then
                    call allocateArray(nPoin_Exch, val_iMesh%nExch)
                    read(ioPort_FULL) nPoin_Exch
                elseif(i == ENTITY_IPOIN_EXCH) then
                    call allocateArray(iPoin_Exch, nExchPoin)
                    read(ioPort_FULL) iPoin_Exch
                endif
            enddo
    
        close(ioPort_FULL)
        
        
        if(val_iMesh%nExch > 0) then
            if(allocated(val_iMesh%iExch)) deallocate(val_iMesh%iExch)
            allocate(val_iMesh%iExch(val_iMesh%nExch))
            
            iCount = 0
            do i=1,val_iMesh%nExch
                val_iMesh%iExch(i)%KIND  = iKind_Exch(i)
                val_iMesh%iExch(i)%nPoin = nPoin_Exch(i)
                
                call allocateArray(val_iMesh%iExch(i)%iPoin, val_iMesh%iExch(i)%nPoin)
                
                do j=1,nPoin_Exch(i)
                    val_iMesh%iExch(i)%iPoin(j) = iPoin_Exch(iCount + j)
                enddo
                
                iCount = iCount + nPoin_Exch(i)
            enddo
        endif
    
    
    endsubroutine
    !
    subroutine savePartMesh(val_iMesh,val_path,val_ID)
        use mod_TypeDef_Mesh
        use mod_PreDefine_IOPort
        implicit none
        type(typ_Mesh),intent(in):: val_iMesh
        character(len=*),intent(in):: val_path
        integer,intent(in):: val_ID
        !
        integer:: i,j,iCount
        character(len=100):: meshFileName
        !
        integer:: nExchPoin
        integer,allocatable:: iKind_Exch(:)
        integer,allocatable:: nPoin_Exch(:)
        integer,allocatable:: iPoin_Exch(:)
        
        
        !---------------------------------------------
        if(val_iMesh%nExch > 0) then
            call allocateArray(iKind_Exch, val_iMesh%nExch)
            call allocateArray(nPoin_Exch, val_iMesh%nExch)
            
            iCount = 0
            do i=1,val_iMesh%nExch
                nPoin_Exch(i) = val_iMesh%iExch(i)%nPoin
                iCount = iCount + val_iMesh%iExch(i)%nPoin
            enddo
            nExchPoin = iCount
            
            if(nExchPoin > 0) then
                call allocateArray(iPoin_Exch, nExchPoin)
                
                iCount = 0
                do i=1,val_iMesh%nExch
                    do j=1,val_iMesh%iExch(i)%nPoin
                        iPoin_Exch(iCount+j) = val_iMesh%iExch(i)%iPoin(j)
                    enddo
                    
                    iCount = iCount + val_iMesh%iExch(i)%nPoin
                enddo
            endif
        endif
        
        
        !---------------------------------------------
        !配置文件标记
        count_options(:)  = 0
        entity_options(:) = 0
    
        count_options(LOCATION_REAL)  = REALLEN
        count_options(LOCATION_NDIME) = nDim
        
        count_options(LOCATION_NDOMA) = val_iMesh%nDoma
        count_options(LOCATION_NMARK) = val_iMesh%nMark
        count_options(LOCATION_NPOIN) = val_iMesh%nPoin
        count_options(LOCATION_NELEM) = val_iMesh%nElem
        count_options(LOCATION_NCLOR) = val_iMesh%nClor
        
        count_options(LOCATION_NSATE) = val_iMesh%nSate
        count_options(LOCATION_NBELE) = val_iMesh%nBele
        count_options(LOCATION_NBELV) = val_iMesh%nBelv
        count_options(LOCATION_NBELX) = val_iMesh%nBelx
        
        count_options(LOCATION_NEXCH) = val_iMesh%nExch
        count_options(LOCATION_NEXPO) = nExchPoin
    
        entity_options(ENTITY_IPOIN)      = 0 !$$!
        entity_options(ENTITY_IFLAG_POIN) = 1
        entity_options(ENTITY_IPART_POIN) = 1
        entity_options(ENTITY_IHOST_POIN) = 1
        entity_options(ENTITY_ICLOR_POIN) = 1
        entity_options(ENTITY_ICOOR_POIN) = 1
        entity_options(ENTITY_IVOLU_POIN) = 1
        if(allocated(val_iMesh%iWDis_Poin)) then
        entity_options(ENTITY_IWDIS_POIN) = 1 !$$!
        endif
        if(allocated(val_iMesh%dCoor_Poin)) then
        entity_options(ENTITY_DCOOR_POIN) = 1
        endif
        entity_options(ENTITY_IDOMA_POIN) = 1
    
        entity_options(ENTITY_KSATE_POIN) = 1
        entity_options(ENTITY_ISATE_POIN) = 1
        entity_options(ENTITY_ICOEF_SATE) = 1
        entity_options(ENTITY_ILENS_SATE) = 0
    
        entity_options(ENTITY_IELEM)      = 1
        entity_options(ENTITY_IDOMAPROP)  = 1
        entity_options(ENTITY_IELEMPROP)  = 1
    
        entity_options(ENTITY_IBELE)      = 1
        entity_options(ENTITY_IMARKPROP)  = 1
        
        entity_options(ENTITY_IBELVPROP)  = 1
        if(allocated(val_iMesh%iCoor_Belv)) then
        entity_options(ENTITY_ICOOR_BELV) = 1
        endif
        entity_options(ENTITY_IAREA_BELV) = 1
        entity_options(ENTITY_INVOR_BELV) = 1
        entity_options(ENTITY_ICOEF_BELV) = 1
        if(allocated(val_iMesh%iVelo_Belv)) then
        entity_options(ENTITY_IVELO_BELV) = 1
        endif
        
        if(val_iMesh%nExch > 0) then
        entity_options(ENTITY_IKIND_EXCH) = 1
        entity_options(ENTITY_NPOIN_EXCH) = 1
        endif
        if(nExchPoin > 0) then
        entity_options(ENTITY_IPOIN_EXCH) = 1
        endif
        
        
        !---------------------------------------------
        call getPartMeshFileName(val_ID,meshFileName) 
        
        
        !---------------------------------------------
        !保存节点网格文件
        open(ioPort_FULL, file=trim(val_path)//'/'//trim(meshFileName), &
                          FORM='unformatted', status='unknown'          )
            !---------------------------------------------
            !保存数目类数据 和 实体标记
            write(ioPort_FULL) count_options
            write(ioPort_FULL) entity_options
        
            !---------------------------------------------
            !保存实体内容
            do i=1,NUM_ENTITY
                if(entity_options(i) /= 1) cycle
            
                !POIN---------------------------------------------
                if(i == ENTITY_IPOIN) then
                    !write(ioPort_FULL) val_iMesh%iPoin
                elseif(i == ENTITY_IFLAG_POIN) then
                    write(ioPort_FULL) val_iMesh%iFlag_Poin
                elseif(i == ENTITY_IPART_POIN) then
                    write(ioPort_FULL) val_iMesh%iPart_Poin
                elseif(i == ENTITY_IHOST_POIN) then
                    write(ioPort_FULL) val_iMesh%iHost_Poin
                elseif(i == ENTITY_ICLOR_POIN) then
                    write(ioPort_FULL) val_iMesh%iClor_Poin
                elseif(i == ENTITY_ICOOR_POIN) then
                    write(ioPort_FULL) val_iMesh%iCoor_Poin
                elseif(i == ENTITY_IVOLU_POIN) then
                    write(ioPort_FULL) val_iMesh%iVolu_Poin
                elseif(i == ENTITY_IWDIS_POIN) then
                    write(ioPort_FULL) val_iMesh%iWDis_Poin
                elseif(i == ENTITY_dCoor_POIN) then
                    write(ioPort_FULL) val_iMesh%dCoor_Poin
                elseif(i == ENTITY_IDOMA_POIN) then
                    write(ioPort_FULL) val_iMesh%iDoma_Poin
        
                !SATE---------------------------------------------
                elseif(i == ENTITY_KSATE_POIN) then
                    write(ioPort_FULL) val_iMesh%kSate_Poin
                elseif(i == ENTITY_ISATE_POIN) then
                    write(ioPort_FULL) val_iMesh%iSate_Poin
                elseif(i == ENTITY_ICOEF_SATE) then
                    write(ioPort_FULL) val_iMesh%iCoef_Sate
                elseif(i == ENTITY_ILENS_SATE) then
                    write(ioPort_FULL) val_iMesh%iLens_Sate
        
                !ELEM---------------------------------------------
                elseif(i == ENTITY_IELEM) then
                    write(ioPort_FULL) val_iMesh%iElem
                elseif(i == ENTITY_IDOMAPROP) then
                    write(ioPort_FULL) val_iMesh%iDomaProp
                elseif(i == ENTITY_IELEMPROP) then
                    write(ioPort_FULL) val_iMesh%iElemProp
        
                !BELE---------------------------------------------
                elseif(i == ENTITY_IBELE) then
                    write(ioPort_FULL) val_iMesh%iBele
                elseif(i == ENTITY_IMARKPROP) then
                    write(ioPort_FULL) val_iMesh%iMarkProp
                    
                !BELV---------------------------------------------
                elseif(i == ENTITY_IBELVPROP) then
                    write(ioPort_FULL) val_iMesh%iBelvProp
                elseif(i == ENTITY_ICOOR_BELV) then
                    write(ioPort_FULL) val_iMesh%iCoor_Belv
                elseif(i == ENTITY_IAREA_BELV) then
                    write(ioPort_FULL) val_iMesh%iArea_Belv
                elseif(i == ENTITY_INVOR_BELV) then
                    write(ioPort_FULL) val_iMesh%iNvor_Belv
                elseif(i == ENTITY_ICOEF_BELV) then
                    write(ioPort_FULL) val_iMesh%iCoef_Belv
                elseif(i == ENTITY_IVELO_BELV) then
                    write(ioPort_FULL) val_iMesh%iVelo_Belv
                    
                !EXCH---------------------------------------------
                elseif(i == ENTITY_IKIND_EXCH) then
                    write(ioPort_FULL) iKind_Exch
                elseif(i == ENTITY_NPOIN_EXCH) then
                    write(ioPort_FULL) nPoin_Exch
                elseif(i == ENTITY_IPOIN_EXCH) then
                    write(ioPort_FULL) iPoin_Exch
                endif
            enddo
        close(ioPort_FULL)
    
        !call saveForRotFlow(val_iMesh,val_path,val_ID)
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getRotPartMeshFileName(val_ID,val_meshFileName)
        use mod_strOfNumber
        implicit none
        integer,intent(in):: val_ID
        character(len=100),intent(out):: val_meshFileName
        
    
        val_meshFileName = 'Part'//strTwo_Number(val_ID)//'.pmesh'
    
    endsubroutine
    !
    subroutine saveForRotFlow(val_iMesh,val_path,val_ID)
        use mod_TypeDef_Mesh
        use mod_PreDefine_IOPort
        use mod_PreDefine_Flag
        implicit none
        type(typ_Mesh),intent(in):: val_iMesh
        character(len=*),intent(in):: val_path
        integer,intent(in):: val_ID
        !
        integer:: i,j,iCount
        character(len=100):: meshFileName
        character(len=128):: aLine
        integer:: iSize(4)
        !
        
        
        !---------------------------------------------
        call getRotPartMeshFileName(val_ID,meshFileName) 
        
        
        !---------------------------------------------
        !保存节点网格文件
        open(ioPort_FULL, file=trim(val_path)//'/'//trim(meshFileName), status='unknown')
            !
            write(ioPort_FULL, "(A)"    ) '% solution method = Cell-Vertex FVM'
            write(ioPort_FULL, "(A,I12)") '# nDime = ', nDim
            write(ioPort_FULL, "(A,I12)") '# realW = ', REALLEN
            write(ioPort_FULL, "(A,I12)") '# nPoin = ', val_iMesh%nPoin
            write(ioPort_FULL, "(A,I12)") '# nElem = ', val_iMesh%nElem
            write(ioPort_FULL, "(A,I12)") '# nBele = ', val_iMesh%nBele
            write(ioPort_FULL, "(A,I12)") '# nBelv = ', val_iMesh%nBelv
            write(ioPort_FULL, "(A,I12)") '# nMark = ', val_iMesh%nMark
            write(ioPort_FULL, "(A,I12)") '# nInfc = ', val_iMesh%nSate
            write(ioPort_FULL, "(A)"        ) '# '
        
            !------------------------------------
            write(ioPort_FULL, "(A)") '% iCoor_Poin(nDime,nPoin)'
            do i=1,val_iMesh%nPoin
                write(ioPort_FULL, *) val_iMesh%iCoor_Poin(i,1:nDim)
            enddo
            
            !------------------------------------
            write(ioPort_FULL, "(A)") '% iFlag_Poin(nPoin)'
            write(ioPort_FULL, "(20I4)") val_iMesh%iFlag_Poin(1:val_iMesh%nPoin)
            
            !------------------------------------
            write(ioPort_FULL, "(A)") '% kSate_Poin(2,nPoin)'
            do i=1,val_iMesh%nPoin
                write(ioPort_FULL, "(2I12)") val_iMesh%kSate_Poin(i)+1,val_iMesh%kSate_Poin(i+1)
            enddo
            
            !------------------------------------
            write(ioPort_FULL, "(A)") '% iSate_Poin(3,nSate) %sateID, infcID, 0'
            do i=1,val_iMesh%nSate
                write(ioPort_FULL, "(3I12)") val_iMesh%iSate_Poin(i,1),val_iMesh%iSate_Poin(i,2),0
            enddo
            
            !------------------------------------
            write(ioPort_FULL, "(A)") '% iCoef_Sate(nDime,nSate)'
            do i=1,val_iMesh%nSate
                write(ioPort_FULL, "(3E15.7)") val_iMesh%iCoef_Sate(i,:)
            enddo
            
            !------------------------------------
            write(ioPort_FULL, "(A)") '% iElem(8,nElem)'
            do i=1,val_iMesh%nElem
                write(ioPort_FULL,"(8I12)") val_iMesh%iElem(1:4*nDim-4,i)
            enddo
            
            !------------------------------------
            write(ioPort_FULL, "(A)") '% iBele(4,nBele)'
            do i=1,val_iMesh%nBele
                write(ioPort_FULL,"(4I12)") val_iMesh%iBele(1:2*nDim-2,i)
            enddo
            
            !------------------------------------
            write(ioPort_FULL, "(A)") '% iBelv(2,nBelv) %poinID, markID'
            do i=1,val_iMesh%nBelv
                write(ioPort_FULL,"(2I12)") val_iMesh%iBelvProp(i,1),val_iMesh%iBelvProp(i,2)
            enddo
            
            !------------------------------------
            write(ioPort_FULL, "(A)") '% iCoef_Belv(nDime,nBelv)'
            do i=1,val_iMesh%nBelv
                write(ioPort_FULL,"(3E15.5)") val_iMesh%iCoef_Belv(i,1:nDim)
            enddo
            
            !------------------------------------
            write(ioPort_FULL, "(A)") '% iMark(nMark,2) %nBeleInMark, markKind'
            do i=1,val_iMesh%nMark
                write(ioPort_FULL,"(2I12)") val_iMesh%iMarkProp(i,1:2)
            enddo
            
            !------------------------------------
        close(ioPort_FULL)
    
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine readPartMesh_Updating(val_iMesh,val_path,val_myID)
        use mod_TypeDef_Mesh
        use mod_PreDefine_IOPort
        use mod_Interface_allocateArray
        implicit none
        type(typ_Mesh),intent(inout):: val_iMesh
        character(len=*),intent(in):: val_path
        integer,intent(in):: val_myID
        !
        integer:: i,j,iCount
        character(len=100):: meshFileName
        !
        integer:: nExchPoin
        integer,allocatable:: iKind_Exch(:)
        integer,allocatable:: nPoin_Exch(:)
        integer,allocatable:: iPoin_Exch(:)
    
    
        !---------------------------------------------
        call getPartMeshFileName(val_myID+1,meshFileName) !partID = myID=i + 1
    
    
        !---------------------------------------------
        open(ioPort_FULL, file=trim(val_path)//'/'//trim(meshFileName), &
                          FORM='unformatted', status='old'              )
            !---------------------------------------------
            !读取数目类数据
            count_options(:)  = 0
            read(ioPort_FULL) count_options
        
            if(count_options(LOCATION_REAL) /= REALLEN) then
                write(ioPort_Out,*) 'count_options(LOCATION_REAL) /= REALLEN'
                stop
            endif
        
            if(count_options(LOCATION_NDIME) /= nDim) then
                write(ioPort_Out,*) 'count_options(LOCATION_NDIME) /= nDim'
                stop
            endif
        
            if(val_iMesh%nDoma /= count_options(LOCATION_NDOMA)) pause 'E1'
            if(val_iMesh%nMark /= count_options(LOCATION_NMARK)) pause 'E1'
            if(val_iMesh%nPoin /= count_options(LOCATION_NPOIN)) pause 'E1'
            if(val_iMesh%nElem /= count_options(LOCATION_NELEM)) pause 'E1'
            if(val_iMesh%nClor /= count_options(LOCATION_NCLOR)) pause 'E1'
            
            if(val_iMesh%nSate /= count_options(LOCATION_NSATE)) pause 'E1'
            if(val_iMesh%nBele /= count_options(LOCATION_NBELE)) pause 'E1'
            
            val_iMesh%nBelv = count_options(LOCATION_NBELV)
            val_iMesh%nBelx = count_options(LOCATION_NBELX)
            
            val_iMesh%nExch = count_options(LOCATION_NEXCH)
            nExchPoin       = count_options(LOCATION_NEXPO)
        
        
            !---------------------------------------------
            !读取实体标记
            entity_options(:) = 0
            read(ioPort_FULL) entity_options
        
        
            !---------------------------------------------
            !申请并读取实体内容
            do i=1,NUM_ENTITY
                if(entity_options(i) /= 1) cycle
            
                !POIN---------------------------------------------
                if(i == ENTITY_IPOIN) then
                    !read(ioPort_FULL) val_iMesh%iPoin
                elseif(i == ENTITY_IFLAG_POIN) then
                    read(ioPort_FULL) val_iMesh%iFlag_Poin
                elseif(i == ENTITY_IPART_POIN) then
                    read(ioPort_FULL) val_iMesh%iPart_Poin
                elseif(i == ENTITY_IHOST_POIN) then
                    read(ioPort_FULL) val_iMesh%iHost_Poin
                elseif(i == ENTITY_ICLOR_POIN) then
                    read(ioPort_FULL) val_iMesh%iClor_Poin
                elseif(i == ENTITY_ICOOR_POIN) then
                    read(ioPort_FULL) val_iMesh%iCoor_Poin
                elseif(i == ENTITY_IVOLU_POIN) then
                    read(ioPort_FULL) val_iMesh%iVolu_Poin
                elseif(i == ENTITY_IWDIS_POIN) then
                    read(ioPort_FULL) val_iMesh%iWDis_Poin
                elseif(i == ENTITY_DCOOR_POIN) then
                    read(ioPort_FULL) val_iMesh%dCoor_Poin
        
                !SATE---------------------------------------------
                elseif(i == ENTITY_ICOEF_SATE) then
                    read(ioPort_FULL) val_iMesh%iCoef_Sate
                elseif(i == ENTITY_ILENS_SATE) then
                    read(ioPort_FULL) val_iMesh%iLens_Sate
        
                !ELEM---------------------------------------------
                !BELE---------------------------------------------
                !BELV---------------------------------------------
                elseif(i == ENTITY_IBELVPROP) then
                    call allocateArray(val_iMesh%iBelvProp, val_iMesh%nBelv, 2)
                    read(ioPort_FULL) val_iMesh%iBelvProp 
                elseif(i == ENTITY_ICOOR_BELV) then
                    call allocateArray(val_iMesh%iCoor_Belv, val_iMesh%nBelv, nDim)
                    read(ioPort_FULL) val_iMesh%iCoor_Belv 
                elseif(i == ENTITY_IAREA_BELV) then
                    call allocateArray(val_iMesh%iArea_Belv, val_iMesh%nBelv)
                    read(ioPort_FULL) val_iMesh%iArea_Belv 
                elseif(i == ENTITY_INVOR_BELV) then
                    call allocateArray(val_iMesh%iNvor_Belv, val_iMesh%nBelv, nDim)
                    read(ioPort_FULL) val_iMesh%iNvor_Belv 
                elseif(i == ENTITY_ICOEF_BELV) then
                    call allocateArray(val_iMesh%iCoef_Belv, val_iMesh%nBelv, nDim)
                    read(ioPort_FULL) val_iMesh%iCoef_Belv 
                elseif(i == ENTITY_IVELO_BELV) then
                    call allocateArray(val_iMesh%iVelo_Belv, val_iMesh%nBelv, nDim)
                    read(ioPort_FULL) val_iMesh%iVelo_Belv
                    
                !EXCH---------------------------------------------
                elseif(i == ENTITY_IKIND_EXCH) then
                    call allocateArray(iKind_Exch, val_iMesh%nExch)
                    read(ioPort_FULL) iKind_Exch
                elseif(i == ENTITY_NPOIN_EXCH) then
                    call allocateArray(nPoin_Exch, val_iMesh%nExch)
                    read(ioPort_FULL) nPoin_Exch
                elseif(i == ENTITY_IPOIN_EXCH) then
                    call allocateArray(iPoin_Exch, nExchPoin)
                    read(ioPort_FULL) iPoin_Exch
                endif
            enddo
        close(ioPort_FULL)
        
        
        !---------------------------------------------
        if(val_iMesh%nExch > 0) then
            if(allocated(val_iMesh%iExch)) deallocate(val_iMesh%iExch)
            allocate(val_iMesh%iExch(val_iMesh%nExch))
            
            iCount = 0
            do i=1,val_iMesh%nExch
                val_iMesh%iExch(i)%KIND  = iKind_Exch(i)
                val_iMesh%iExch(i)%nPoin = nPoin_Exch(i)
                
                call allocateArray(val_iMesh%iExch(i)%iPoin, val_iMesh%iExch(i)%nPoin)
                
                do j=1,nPoin_Exch(i)
                    val_iMesh%iExch(i)%iPoin(j) = iPoin_Exch(iCount + j)
                enddo
                
                iCount = iCount + nPoin_Exch(i)
            enddo
        endif
    
    endsubroutine
    !
    subroutine savePartMesh_Updating(val_iMesh,val_path,val_ID)
        use mod_TypeDef_Mesh
        use mod_PreDefine_IOPort
        implicit none
        type(typ_Mesh),intent(in):: val_iMesh
        character(len=*),intent(in):: val_path
        integer,intent(in):: val_ID
        !
        integer:: i,j,iCount
        character(len=100):: meshFileName
        !
        integer:: nExchPoin
        integer,allocatable:: iKind_Exch(:)
        integer,allocatable:: nPoin_Exch(:)
        integer,allocatable:: iPoin_Exch(:)
    
    
        !---------------------------------------------
        if(val_iMesh%nExch > 0) then
            call allocateArray(iKind_Exch, val_iMesh%nExch)
            call allocateArray(nPoin_Exch, val_iMesh%nExch)
            
            iCount = 0
            do i=1,val_iMesh%nExch
                nPoin_Exch(i) = val_iMesh%iExch(i)%nPoin
                iCount = iCount + val_iMesh%iExch(i)%nPoin
            enddo
            nExchPoin = iCount
            
            if(nExchPoin > 0) then
                call allocateArray(iPoin_Exch, nExchPoin)
                
                iCount = 0
                do i=1,val_iMesh%nExch
                    do j=1,val_iMesh%iExch(i)%nPoin
                        iPoin_Exch(iCount+j) = val_iMesh%iExch(i)%iPoin(j)
                    enddo
                    
                    iCount = iCount + val_iMesh%iExch(i)%nPoin
                enddo
            endif
        endif
        
        
        !---------------------------------------------
        !配置文件标记
        count_options(:)  = 0
        entity_options(:) = 0
    
        count_options(LOCATION_REAL)  = REALLEN
        count_options(LOCATION_NDIME) = nDim
        
        count_options(LOCATION_NDOMA) = val_iMesh%nDoma
        count_options(LOCATION_NMARK) = val_iMesh%nMark
        count_options(LOCATION_NPOIN) = val_iMesh%nPoin
        count_options(LOCATION_NELEM) = val_iMesh%nElem
        count_options(LOCATION_NCLOR) = val_iMesh%nClor
        
        count_options(LOCATION_NSATE) = val_iMesh%nSate
        count_options(LOCATION_NBELE) = val_iMesh%nBele
        count_options(LOCATION_NBELV) = val_iMesh%nBelv
        count_options(LOCATION_NBELX) = val_iMesh%nBelx
        
        count_options(LOCATION_NEXCH) = val_iMesh%nExch
        count_options(LOCATION_NEXPO) = nExchPoin
    
        entity_options(ENTITY_IPOIN)      = 0 !$$!
        entity_options(ENTITY_IFLAG_POIN) = 1
        entity_options(ENTITY_IPART_POIN) = 1
        entity_options(ENTITY_IHOST_POIN) = 1
        entity_options(ENTITY_ICLOR_POIN) = 1
        entity_options(ENTITY_ICOOR_POIN) = 1
        entity_options(ENTITY_IVOLU_POIN) = 1
        if(allocated(val_iMesh%iWDis_Poin)) then
        entity_options(ENTITY_IWDIS_POIN) = 1 !$$!
        endif
        if(allocated(val_iMesh%dCoor_Poin)) then
        entity_options(ENTITY_DCOOR_POIN) = 1
        endif
    
        entity_options(ENTITY_KSATE_POIN) = 0
        entity_options(ENTITY_ISATE_POIN) = 0
        entity_options(ENTITY_ICOEF_SATE) = 1
        entity_options(ENTITY_ILENS_SATE) = 0
    
        entity_options(ENTITY_IELEM)      = 0
        entity_options(ENTITY_IDOMAPROP)  = 0
    
        entity_options(ENTITY_IBELE)      = 0
        entity_options(ENTITY_IMARKPROP)  = 0
        
        entity_options(ENTITY_IBELVPROP)  = 1
        if(allocated(val_iMesh%iCoor_Belv)) then
        entity_options(ENTITY_ICOOR_BELV) = 1
        endif
        entity_options(ENTITY_IAREA_BELV) = 1
        entity_options(ENTITY_INVOR_BELV) = 1
        entity_options(ENTITY_ICOEF_BELV) = 1
        if(allocated(val_iMesh%iVelo_Belv)) then
        entity_options(ENTITY_IVELO_BELV) = 1
        endif
    
        if(val_iMesh%nExch > 0) then
        entity_options(ENTITY_IKIND_EXCH) = 1
        entity_options(ENTITY_NPOIN_EXCH) = 1
        endif
        if(nExchPoin > 0) then
        entity_options(ENTITY_IPOIN_EXCH) = 1
        endif
        
    
        !---------------------------------------------
        call getPartMeshFileName(val_ID,meshFileName) 
        
        
        !---------------------------------------------
        !保存节点网格文件
        open(ioPort_FULL, file=trim(val_path)//'/'//trim(meshFileName), &
                          FORM='unformatted', status='unknown'          )
            !---------------------------------------------
            !保存数目类数据 和 实体标记
            write(ioPort_FULL) count_options
            write(ioPort_FULL) entity_options
        
            !---------------------------------------------
            !保存实体内容
            do i=1,NUM_ENTITY
                if(entity_options(i) /= 1) cycle
            
                !POIN---------------------------------------------
                if(i == ENTITY_IPOIN) then
                    !write(ioPort_FULL) val_iMesh%iPoin
                elseif(i == ENTITY_IFLAG_POIN) then
                    write(ioPort_FULL) val_iMesh%iFlag_Poin
                elseif(i == ENTITY_IPART_POIN) then
                    write(ioPort_FULL) val_iMesh%iPart_Poin
                elseif(i == ENTITY_IHOST_POIN) then
                    write(ioPort_FULL) val_iMesh%iHost_Poin
                elseif(i == ENTITY_ICLOR_POIN) then
                    write(ioPort_FULL) val_iMesh%iClor_Poin
                elseif(i == ENTITY_ICOOR_POIN) then
                    write(ioPort_FULL) val_iMesh%iCoor_Poin
                elseif(i == ENTITY_IVOLU_POIN) then
                    write(ioPort_FULL) val_iMesh%iVolu_Poin
                elseif(i == ENTITY_IWDIS_POIN) then
                    write(ioPort_FULL) val_iMesh%iWDis_Poin
                elseif(i == ENTITY_DCOOR_POIN) then
                    write(ioPort_FULL) val_iMesh%dCoor_Poin
        
                !SATE---------------------------------------------
                elseif(i == ENTITY_ICOEF_SATE) then
                    write(ioPort_FULL) val_iMesh%iCoef_Sate
                elseif(i == ENTITY_ILENS_SATE) then
                    write(ioPort_FULL) val_iMesh%iLens_Sate
        
                !ELEM---------------------------------------------
                elseif(i == ENTITY_IELEM) then
                    write(ioPort_FULL) val_iMesh%iElem
                elseif(i == ENTITY_IDOMAPROP) then
                    write(ioPort_FULL) val_iMesh%iDomaProp
        
                !BELE---------------------------------------------
                !BELV---------------------------------------------
                elseif(i == ENTITY_IBELVPROP) then
                    write(ioPort_FULL) val_iMesh%iBelvProp
                elseif(i == ENTITY_ICOOR_BELV) then
                    write(ioPort_FULL) val_iMesh%iCoor_Belv
                elseif(i == ENTITY_IAREA_BELV) then
                    write(ioPort_FULL) val_iMesh%iArea_Belv
                elseif(i == ENTITY_INVOR_BELV) then
                    write(ioPort_FULL) val_iMesh%iNvor_Belv
                elseif(i == ENTITY_ICOEF_BELV) then
                    write(ioPort_FULL) val_iMesh%iCoef_Belv
                elseif(i == ENTITY_IVELO_BELV) then
                    write(ioPort_FULL) val_iMesh%iVelo_Belv
                
                !EXCH---------------------------------------------
                elseif(i == ENTITY_IKIND_EXCH) then
                    write(ioPort_FULL) iKind_Exch
                elseif(i == ENTITY_NPOIN_EXCH) then
                    write(ioPort_FULL) nPoin_Exch
                elseif(i == ENTITY_IPOIN_EXCH) then
                    write(ioPort_FULL) iPoin_Exch
                endif
            enddo
    
        close(ioPort_FULL)
    
    endsubroutine
    !

endmodule