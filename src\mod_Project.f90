!
!---------------------------------------------
!
!---------------------------------------------
module mod_TypeDef_Project
    use mod_TypeDef_Mesh
    use mod_TypeDef_Parm
    use mod_TypeDef_Solu
    implicit none
    
    type:: typ_Project
        !
        type(typ_Parm):: iParm  !参数类
        type(typ_Mesh):: iMesh  !网格类
        type(typ_Solu):: iSolu  !求解器
    endtype

endmodule
!
!---------------------------------------------
!
!---------------------------------------------
module mod_Project
    use mod_TypeDef_Mesh
    use mod_TypeDef_Project
    use mod_TypeDef_MeshMapping
    implicit none
    
    
    !---------------------------
    !全局网格和网格分区块
    type(typ_GlobalMesh):: iGlobalMesh
    !
    integer:: nMeshPart
    type(typ_Mesh       ),allocatable:: iPartMesh(:)
    type(typ_meshMapping),allocatable:: iMeshMapp(:)
    
    !---------------------------
    !当前节点项目工程
    type(typ_Project):: iProject
    
endmodule
!
