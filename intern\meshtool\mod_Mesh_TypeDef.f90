!
module mod_Mesh_TypeDef
    !
    type:: typ_zone
        character*255:: InfoLine
        integer:: zoneID
        character*255:: zoneName
        character*255:: zoneKind
        !
        integer:: nElem
        integer:: elemSize
        integer,allocatable:: iElem(:,:)
    endtype
    !
    type:: typ_Doma
        character*255:: DomaLine
        integer:: DomaID
        character*100:: DomaName
        character*4:: DomaKind
        integer:: nElem = 0
        integer,allocatable:: iElem(:,:)
        
    endtype
    !
    type:: typ_Mark
        character*255:: MarkLine
        integer:: MarkID
        character*100:: MarkName
        character*4:: MarkKind
        integer:: nElem = 0
        integer,allocatable:: iElem(:,:)
    endtype
    !
    type:: typ_Mesh
        integer:: nDime
        !
        integer:: nPoin
        integer:: nElem
        integer:: nBele
        integer:: nDoma
        integer:: nMark
        !
        real*8,allocatable:: iCoor(:,:)
        integer,allocatable:: iElem(:,:)
        integer,allocatable:: iBEle(:,:)
        integer,allocatable:: elemDoma(:)
        type(typ_Doma),allocatable:: iDoma(:)
        type(typ_mark),allocatable:: iMark(:)
        !
        integer,allocatable:: globalPoinID(:)
        integer,allocatable:: globalDomaID(:)
        integer,allocatable:: globalMarkID(:)
        
    endtype
    !
endmodule
!
