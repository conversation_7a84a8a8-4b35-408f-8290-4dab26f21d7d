!
!---------------------------------------------
!
!---------------------------------------------
subroutine bldPartMesh
    use mod_PreDefine_IOPort
    use mod_Project
    use mod_WorkPath
    use mod_Config
    use mod_PartMeshIO
    use mod_calMeshMapp, only:calMeshMapp
    use mod_calPartMesh
    use mod_addExteRegnToBelv, only:addExteRegnToBelv
    use mod_bldExchLink, only:bldExchLink
    implicit none
    !
    integer:: i
    
    
    !---------------------------------------------
    if(NUMBER_PART <= 0) then
        write(ioPort_Out,*) 'nMeshPart <= 0'
        stop
    endif
    
    nMeshPart = NUMBER_PART
    
    allocate(iPartMesh(nMeshPart))
    allocate(iMeshMapp(nMeshPart))
    
    !---------------------------------------------
    write(ioPort_Out,"(2X,A,I2,A)") ': <Total Mesh Parts =',nMeshPart,'>'
        
    !calPartMesh----------------------------------
    do i=1,nMeshPart
        call calMeshMapp(i)
        
        if(KIND_POINTREORDERING /= _NONE) then
            call calMeshMapp_Reordering(i)
        endif

        call iniPartMesh(i)

        call calPartMesh(i)

        if(iGlobalMesh%iExteRegnSANO%KIND /= EXTE_NONE) then
            call addExteRegnToBelv(i)
        endif
    enddo
    
    call bldExchLink
    
    do i=1,nMeshPart
        write(ioPort_Out,"(2X,A,I2,A,I10,A,I10)") 'iPart(',i, &
                            '): nPoin=',iPartMesh(i)%nPoin, &
                            ', nSate=',iPartMesh(i)%nSate
        
        call savePartMesh(iPartMesh(i),geomFullPath,i)
    enddo
    
endsubroutine
!

    