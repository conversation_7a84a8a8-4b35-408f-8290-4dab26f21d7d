
!---------------------------------------------
!
!---------------------------------------------
module mod_ElemProp
    use mod_PreDefine_Dimension
    implicit none
    
    integer:: ElemProp_nodeNum(nElemProp)         = 0  !包含顶点数
    integer:: ElemProp_nodeList(8,nElemProp)      = 0  !包含顶点列表
        
    integer:: ElemProp_edgeNum(nElemProp)         = 0  !包含通量面数
    integer:: ElemProp_edgeKind(6,nElemProp)      = 0  !包含通量面类型
    integer:: ElemProp_edgeList(4,6,nElemProp)    = 0  !包含通量面列表
        
    integer:: ElemProp_lineNum(nElemProp)         = 0  !包含线元数
    integer:: ElemProp_lineList(2,12,nElemProp)   = 0  !包含线元列表
    integer:: ElemProp_lineENUM(2,12,nElemProp)   = 0  !包含线所在该单元的通量面位置
    
    integer:: ElemProp_diviNum(nElemProp)         = 0  !包含分割数
    integer:: ElemProp_diviKind(nElemProp)        = 0  !包含分割类型
    integer:: ElemProp_diviList(4,6,nElemProp)    = 0  !包含分割列表
    
endmodule

    
!---------------------------------------------
!
!---------------------------------------------
module mod_ElemProp_GPU
    use cudafor
    use mod_PreDefine_Dimension
    implicit none
    
    integer,device:: ElemProp_nodeNum_d(nElemProp)       !包含顶点数
    integer,device:: ElemProp_nodeList_d(8,nElemProp)      !包含顶点列表
        
    integer,device:: ElemProp_edgeNum_d(nElemProp)         !包含通量面数
    integer,device:: ElemProp_edgeKind_d(6,nElemProp)      !包含通量面类型
    integer,device:: ElemProp_edgeList_d(4,6,nElemProp)    !包含通量面列表
        
    integer,device:: ElemProp_lineNum_d(nElemProp)         !包含线元数
    integer,device:: ElemProp_lineList_d(2,12,nElemProp)   !包含线元列表
    integer,device:: ElemProp_lineENUM_d(2,12,nElemProp)   !包含线所在该单元的通量面位置
        
    integer,device:: ElemProp_diviNum_d(nElemProp)         !包含分割数
    integer,device:: ElemProp_diviKind_d(nElemProp)        !包含分割类型
    integer,device:: ElemProp_diviList_d(4,6,nElemProp)    !包含分割列表
    
endmodule
!
