!
module mod_saveSoluUnst
    implicit none
    !
contains
    !
    subroutine saveSoluUnst(val_iParm,val_iMesh,val_iSolu,val_iIter,val_nVars)
        use mod_TypeDef_Project
        use mod_Config
        implicit none
        type(typ_Parm),intent(in   ):: val_iParm
        type(typ_Mesh),intent(in   ):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        integer,intent(in):: val_iIter
        integer,intent(in):: val_nVars

        if(KIND_HARDWARE == _CPU) then
            call saveSoluUnst_CPU(val_iParm,val_iMesh,val_iSolu,val_iIter,val_nVars)
        elseif(KIND_HARDWARE == _GPU) then
            call saveSoluUnst_GPU(val_iParm,val_iMesh,val_iSolu,val_iIter,val_nVars)
        end if

    end subroutine saveSoluUnst
    !
    subroutine saveSoluUnst_CPU(val_iParm,val_iMesh,val_iSolu,val_iIter,val_nVars)
        use mod_TypeDef_Project
        use mod_Config
        implicit none
        type(typ_Parm),intent(in   ):: val_iParm
        type(typ_Mesh),intent(in   ):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        integer,intent(in):: val_iIter
        integer,intent(in):: val_nVars
        !
        integer::i,j

        if(UNSTEADY_SIMULATION == _DUAL_TIME_STEPPING) then
            if((GRID_MOVEMENT    == _NO                ).or. &
               (NUMERICAL_METHOD == _NUMERICAL_MESHLESS) )then
                if(val_iIter == 1) then
                    val_iSolu%soluTimN(:,:) = val_iSolu%soluvar(:,:)
                    val_iSolu%soluTimX(:,:) = val_iSolu%soluvar(:,:)
                else
                    val_iSolu%soluTimX(:,:) = val_iSolu%soluTimN(:,:)
                    val_iSolu%soluTimN(:,:) = val_iSolu%soluvar(:,:)
                endif

            elseif(GRID_MOVEMENT == _YES) then
                if(val_iIter == 1) then
                    val_iSolu%soluTimN(:,:) = val_iSolu%soluvar(:,:)
                    val_iSolu%soluTimX(:,:) = val_iSolu%soluvar(:,:)
                else
                    do i=1,val_iMesh%nPoin
                        val_iSolu%soluTimX(i,:) = val_iSolu%soluTimN(i,:)* &
                                val_iMesh%iVolu_Poin_N(i)/val_iMesh%iVolu_Poin(i)
                        val_iSolu%soluTimN(:,:) = val_iSolu%soluvar(:,:)* &
                                val_iMesh%iVolu_Poin_N(i)/val_iMesh%iVolu_Poin(i)
                    end do
                endif
            endif
        endif

    endsubroutine
    !
    subroutine saveSoluUnst_GPU(val_iParm,val_iMesh,val_iSolu,val_iIter,val_nVars)
        use mod_TypeDef_Project
        use mod_Config
        use mod_GPUThreadDim
        use mod_Operation_GPUArrayCpy
        use cudafor
        implicit none
        type(typ_Parm),intent(in   ):: val_iParm
        type(typ_Mesh),intent(in   ):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        integer,intent(in):: val_iIter
        integer,intent(in):: val_nVars

    
        if(UNSTEADY_SIMULATION == _DUAL_TIME_STEPPING) then
        if((GRID_MOVEMENT    == _NO                ).or. &
           (NUMERICAL_METHOD == _NUMERICAL_MESHLESS) )then
            if(val_iIter == 1) then
                call GPURealArrayCpy2D1_Global<<<nThreadBlock(IP_BP,IP_NT26),64>>>  &
                            (val_iSolu%soluvar_d, val_iSolu%soluTimN_d, val_iMesh%nPoin, val_nVars)
                
                call GPURealArrayCpy2D1_Global<<<nThreadBlock(IP_BP,IP_NT26),64>>>  &
                            (val_iSolu%soluvar_d,  val_iSolu%soluTimX_d, val_iMesh%nPoin, val_nVars)
            
            else
                call GPURealArrayCpy2D1_Global<<<nThreadBlock(IP_BP,IP_NT26),64>>>  &
                            (val_iSolu%soluTimN_d, val_iSolu%soluTimX_d, val_iMesh%nPoin, val_nVars)
            
                call GPURealArrayCpy2D1_Global<<<nThreadBlock(IP_BP,IP_NT26),64>>>  &
                            (val_iSolu%soluvar_d, val_iSolu%soluTimN_d, val_iMesh%nPoin, val_nVars)
            
            endif
            
        elseif(GRID_MOVEMENT == _YES) then
            if(val_iIter == 1) then
                call GPURealArrayCpy2D1_Global<<<nThreadBlock(IP_BP,IP_NT26),64>>>  &
                            (val_iSolu%soluvar_d, val_iSolu%soluTimN_d, val_iMesh%nPoin, val_nVars)
                
                call GPURealArrayCpy2D1_Global<<<nThreadBlock(IP_BP,IP_NT26),64>>>  &
                            (val_iSolu%soluvar_d,  val_iSolu%soluTimX_d, val_iMesh%nPoin, val_nVars)
            
            else
                call saveSoluUnst_Global<<<nThreadBlock(IP_BP,IP_NT26),64>>>                    &
                            (val_iSolu%soluTimN_d, val_iSolu%soluTimX_d, val_iMesh%iVolu_Poin_d,&
                            val_iMesh%iVolu_Poin_N_d, val_iMesh%nPoin, val_nVars                )
            
                call saveSoluUnst_Global<<<nThreadBlock(IP_BP,IP_NT26),64>>>                    &
                            (val_iSolu%soluvar_d, val_iSolu%soluTimN_d, val_iMesh%iVolu_Poin_d, &
                            val_iMesh%iVolu_Poin_N_d, val_iMesh%nPoin, val_nVars                )
            
            endif
        endif
        endif
    
    endsubroutine
    !
    attributes(global) subroutine saveSoluUnst_Global(val_soluvar,val_soluOld,  &
                                    val_iVolu, val_voluOld, val_nPoin, val_nVar )
        use mod_PreDefine_Precision
        implicit none
        real(kind=REALLEN),device:: val_soluvar(val_nPoin, val_nVar)
        real(kind=REALLEN),device:: val_soluOld(val_nPoin, val_nVar)
        real(kind=REALLEN),device:: val_iVolu(val_nPoin)
        real(kind=REALLEN),device:: val_voluOld(val_nPoin)
        integer,value:: val_nPoin
        integer,value:: val_nVar
        !
        integer:: i,j
        
        i = (blockIdx%x-1)*blockDim%x+threadIdx%x
        
        if(i <= val_nPoin) then
            do j=1,val_nVar
                val_soluOld(i,j) = val_soluvar(i,j)*val_voluOld(i)/val_iVolu(i)
            enddo
        endif
        
    endsubroutine
    !
endmodule
    
    
    