!
subroutine getElemInDoma(val_iMesh)
    use mod_Mesh_TypeDef
    implicit none
    type(typ_Mesh),intent(inout):: val_iMesh
    
    integer:: i,j,k
    integer:: count
    
    write(*,*) val_iMesh%nDoma
    
    if(val_iMesh%nDoma == 1) then
        val_iMesh%iDoma(1)%nElem = val_iMesh%nElem
        
        if(val_iMesh%nDime == 2) then
            allocate(val_iMesh%iDoma(1)%iElem(4,val_iMesh%iDoma(1)%nElem))
        elseif(val_iMesh%nDime == 3) then
            allocate(val_iMesh%iDoma(1)%iElem(8,val_iMesh%iDoma(1)%nElem))
        endif
        
        do i=1,val_iMesh%nElem
            val_iMesh%iDoma(1)%iElem(:,i) = val_iMesh%iElem(:,i)
        enddo
        
        return
    endif
    
    do i=1,val_iMesh%nDoma
        count = 0
        do j=1,val_iMesh%nElem
            if(val_iMesh%elemDoma(j) == val_iMesh%iDoma(i)%DomaID) then
                count = count+1
            endif
        enddo
        
        val_iMesh%iDoma(i)%nElem = count
        
        if(val_iMesh%nDime == 2) then
            allocate(val_iMesh%iDoma(i)%iElem(4,val_iMesh%iDoma(i)%nElem))
        elseif(val_iMesh%nDime == 3) then
            allocate(val_iMesh%iDoma(i)%iElem(8,val_iMesh%iDoma(i)%nElem))
        endif
        
        count = 0
        do j=1,val_iMesh%nElem
            if(val_iMesh%elemDoma(j) == val_iMesh%iDoma(i)%DomaID) then
                count = count+1
                val_iMesh%iDoma(i)%iElem(:,count) = val_iMesh%iElem(:,j)
            endif
        enddo
        
    enddo
    
endsubroutine
!
