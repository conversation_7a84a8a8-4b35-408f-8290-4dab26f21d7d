!
!---------------------------------------------
!
!---------------------------------------------
program GFlow_main
    use mod_PreDefine_ALL
    use mod_MPIEnvironment
    use mod_WorkPath
    use mod_Config
    use mod_Project
    use mod_ErrMsg
    use mod_MPI_Exch
    use omp_lib
    implicit none
    integer:: i,nInter
    integer:: file_index, jerr

    !---------------------------------------------
    !Step 0: 初始化求解项目及其工作目录
    !---------------------------------------------
    call screenPrint(0) !Step 0-2: 主节点屏幕输出
    call setupProject

    call initGPUEnvironment

    call screenPrint(1) !Step 0-2: 主节点屏幕输出

    !---------------------------------------------
    !Step 1: 配置参数
    !---------------------------------------------
    call screenPrint(100)
    
    call setupConfig
    call screenPrint(-1)
    
    
    
    !---------------------------------------------
    !Step 2-1: 网格计算
    !---------------------------------------------
    
    !2-1: 网格基准
    call screenPrint(200)
    call initElemProp
    
    if(KIND_HARDWARE == _GPU) then
        call initElemProp_GPU
    endif
    
    
    !Step 2-2: 全局网格
    if(myID == MASTER_NODE) then
        call screenPrint(220)
        
        !2.2.1：全局网格文件读取
        call screenPrint(221)
        
        call readGlobalMesh(meshFullPath,GRID_FILENAME,iGlobalMesh)
        
        !2.2.2:全局网格基本计算
        call screenPrint(222)
        
        call initGlobalMesh(iGlobalMesh)
        
        !2.2.3:全局网格重构（网格染色、分区、排序等处理）
        call screenPrint(223)
          
        call calGlobalMesh_Reform(iGlobalMesh)
        
        !2.2.4:网格弹性变形
        if(GRID_MOVEMENT == _YES) then
            call screenPrint(224)
            
            call iniGlobalMesh_Deform(iGlobalMesh)
        endif
        
        call screenPrint(-1)
    endif    
    
    call mimd_Sync
    
    
    !Step 2-3: 分区网格
    if(myID == MASTER_NODE) then
        call screenPrint(230)
        call bldPartMesh
        
        call screenPrint(-1)
    endif
    
    call mimd_Sync
    
    
    !Step 2-4: 局部网格
    call screenPrint(240)
    
    !2.4.1：局部网格文件读取
    call screenPrint(241)
        
    call initLocalMesh(iProject%iMesh)
        
    !2.4.2:GPU端网格数据配置
    if(KIND_HARDWARE == _GPU) then
        call screenPrint(242)
        
        call initGPUMesh(iProject%iMesh)
    endif
    
    call screenPrint(-1)
    
    
    
    !---------------------------------------------
    !Step 3-1: 启动求解器,配置参数并解算流场
    !---------------------------------------------
    call screenPrint(300)

    call launchSolu(iProject)
    
    call screenPrint(-1)
    
    !---------------------------------------------
    !Step 4: 程序运行结束，并行环境关闭
    !---------------------------------------------
    call screenPrint(400)
    if(myID == MASTER_NODE) then
        endif

#ifdef GFLOW_WITH_MPI
    if(USING_MPI) then
        call MPI_FINALIZE(iError)
    endif
#endif
    return
end
!   
    
    