!
module mod_ParmIndex
    implicit none
    !
    integer,parameter:: NTotalParm          = 512 !Max Number of parameters
    !1~128 common parameters, 129~256 turbModelConstants, 257~512 not used
    !
    integer,parameter:: IP_GAMMA            = 1 !Gamma
    integer,parameter:: IP_GAMMO            = 2 !Gamma - 1.0
    integer,parameter:: IP_GASCO            = 3 !GasConstant
    integer,parameter:: IP_CP               = 4 !Cp
    integer,parameter:: IP_PrandtlL         = 5 !Prandtl_lam
    integer,parameter:: IP_PrandtlT         = 6 !Prandtl_Turb
    integer,parameter:: IP_PrecBelt         = 7 !Precondition belta2 min
    !
    integer,parameter:: IP_SutherlandT0     = 8
    integer,parameter:: IP_SutherlandTs     = 9
    integer,parameter:: IP_SutherlandM0     = 10
    !
    integer,parameter:: IP_ArtDissK2        = 11
    integer,parameter:: IP_ArtDissK4        = 12
    integer,parameter:: IP_UpWRelaxF        = 13 !upwind relaxing factor
    integer,parameter:: IP_ImplicitW        = 14 !implicit weight
    !
    integer,parameter:: IP_DTViscW          = 15
    integer,parameter:: IP_CFLOrgn          = 16 !CFL0
    integer,parameter:: IP_CFLCurt          = 17 !CFL
    integer,parameter:: IP_CFLTamp          = 18 !(size+3)
    integer,parameter:: IP_DTLIMIT          = 25
    !
    integer,parameter:: IP_RKAlpha          = 30 !(size+4)
    integer,parameter:: IP_RKBelta          = 35 !(size+4)
    !
    integer,parameter:: IP_InftyD           = 40
    integer,parameter:: IP_InftyP           = 41
    integer,parameter:: IP_InftyT           = 42
    integer,parameter:: IP_InftyM           = 43
    integer,parameter:: IP_InftyC           = 44
    integer,parameter:: IP_InftyMul         = 45
    integer,parameter:: IP_InftyMut         = 46
    integer,parameter:: IP_TotalD           = 47
    integer,parameter:: IP_TotalP           = 48
    integer,parameter:: IP_TotalT           = 49
    !
    integer,parameter:: IP_LimMinD          = 51
    integer,parameter:: IP_LimMaxD          = 52
    integer,parameter:: IP_LimMinP          = 53
    integer,parameter:: IP_LimMaxP          = 54
    integer,parameter:: IP_LimMinT          = 55
    integer,parameter:: IP_LimMaxT          = 56
    integer,parameter:: IP_LimMinX          = 57
    integer,parameter:: IP_LimMaxX          = 58
    integer,parameter:: IP_LimMinMuT        = 59
    integer,parameter:: IP_LimMaxMuT        = 60
    !
    integer,parameter:: IP_DDLIM            = 65
    integer,parameter:: IP_DULIM            = 66
    integer,parameter:: IP_DELIM            = 67
    integer,parameter:: IP_DMLIM            = 68
    !
    integer,parameter:: IP_DTViscCoef       = 73
    integer,parameter:: IP_RefMach          = 74
    !
    integer,parameter:: IP_OUTP2WALL        = 80
    !
    integer,parameter:: IP_PhysicalTime     = 96
    integer,parameter:: IP_AllowCHange      = 97

    ! shoule be less than 128, 129 to 256 are used for turbulence model

end module mod_ParmIndex