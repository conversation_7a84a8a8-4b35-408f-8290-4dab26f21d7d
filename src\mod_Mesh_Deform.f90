!
!---------------------------------------------
!
!---------------------------------------------
module mod_Mesh_Deform
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    use mod_PreDefine_MeshDeform
    use mod_TypeDef_Mesh
    implicit none
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    type:: typ_DeformZone
        
        !---------------------------------------------
        !---------------------------------------------
        ! Basic
        integer:: DeformKind    = DEFORMKIND_STILL      !运动类型
        integer:: DeformMethod  = DEFORMMETHOD_DWM      !动网格方法
    
        ! Info
        integer:: nRegn,nMark
        integer,allocatable:: iRegnList(:)              !(nRegn)
        integer,allocatable:: iMarkList(:)              !(nMark)
        
        ! Trans + Rotat
        real(kind=REALLEN),allocatable:: iTransDist(:,:)!(nDim,nMark)平移距离
        real(kind=REALLEN),allocatable:: iRotatAngle(:) !(nMark)旋转角度
        real(kind=REALLEN):: iRotatCent(nDim)           !旋转轴心
        real(kind=REALLEN):: iRotatDirs(nDim)           !转选轴方向（非轴向）
        integer:: iRotatAxle                            !旋转轴上(1,2,3为轴，0未指定方向)
        
        ! Soft
        character(len=STRLEN):: softDeformFileName!弹性运动文件
        
        !---------------------------------------------
        !---------------------------------------------
        ! Poin + BCPoin : BCPoin not in Poin
        integer:: nPoin,nPoinBC
        integer,allocatable:: iPoinProp(:,:)            !(nPoin,3)
        integer,allocatable:: iPoinPropBC(:,:)          !(nPoinBC,3)
        
        ! DeformValue
        real(kind=REALLEN),allocatable:: iDeltPoin(:,:)     !(nDim/1,nPoin)
        real(kind=REALLEN),allocatable:: iDeltPoinBC(:,:)   !(nDim/1,nPoinBC)
        
        ! Orgn A and R for Rotat
        real(kind=REALLEN),allocatable:: iOrgnAndR(:,:)     !(nPoin,2)
        real(kind=REALLEN),allocatable:: iOrgnAndRBC(:,:)   !(nPoinBC,2)
        !
        real(kind=REALLEN),allocatable:: iDeformCoef(:,:)   !(nPoinBC,nPoin) for DWM (nDim+1,nPoin) for DM
        
        
        !---------------------------------------------
        !---------------------------------------------
        ! DWM
        real(kind=REALLEN):: iDefoCoef_DWM_Moved = 1.0
        real(kind=REALLEN):: iDefoCoef_DWM_Still = 1.0
        ! DM
        type(typ_GlobalMesh):: iDelaunayMap
        integer,allocatable:: iDelaunayHostID(:)
        
    endtype
    !
    !---------------------------------------------
    !---------------------------------------------
    integer:: nDeformZone
    type(typ_DeformZone),allocatable:: iDeformZone(:)
    
endmodule
!  
    
