
!---------------------------------------------
!
!---------------------------------------------
subroutine calGlobalMesh_Reform(val_iMesh)
    use mod_PreDefine_IOPort
    use mod_PreDefine_Flag
    use mod_TypeDef_Mesh
    !
    use mod_calMesh_MultiClor
    use mod_calMesh_Overset
    use mod_calMesh_Slipping
    use mod_calMesh_Partition
    use mod_Interface_AllocateArray
    use mod_Statistics
    implicit none
    type(typ_GlobalMesh),intent(inout):: val_iMesh



    call beginTimer("calGlobalMesh_Reform")
    !---------------------------------
    !图层染色
    !---------------------------------
    if(val_iMesh%isNeedMC) then
        write(ioPort_Out,"(5X,A)") ':: MeshReforming_MultiClor...'
        call beginTimer("iniMesh_MultiClor")
        call iniMesh_MultiClor(val_iMesh)

        call beginNextTimer("calMesh_MultiClor")
        call calMesh_MultiClor(val_iMesh)
        call endTimer("calMesh_MultiClor")
    else
        call allocateArray(val_iMesh%iClor_Poin, val_iMesh%nPoin)
        
        val_iMesh%nClor         = 1
        val_iMesh%iClor_Poin(:) = 1
        
    endif 
    
    
    !---------------------------------
    !重叠网格
    !---------------------------------
    if(val_iMesh%isNeedOS) then
        write(ioPort_Out,"(5X,A)") ':: MeshReforming_Overset...'

        call beginTimer("calMeshReform_Overset")
        call calMeshReform_Overset(val_iMesh)
        call endTimer("calMeshReform_Overset")
        
    else
        call allocateArray(val_iMesh%iHost_Poin, val_iMesh%nPoin)
        
        val_iMesh%iHost_Poin(:) = FLAG_VALID
        
    endif
    
    
    !---------------------------------
    !滑移网格
    !---------------------------------
    if(val_iMesh%isNeedSP) then
        write(ioPort_Out,"(5X,A)") ':: MeshReforming_Sliping...'

        call beginTimer("calMeshReform_Slipping")
        call calMeshReform_Slipping(val_iMesh)
        call endTimer("calMeshReform_Slipping")
    endif
    
    
    !---------------------------------
    !网格分区 !初期按坐标分区，后期新增Metis支持
    !---------------------------------
    if(val_iMesh%isNeedPT) then
        write(ioPort_Out,"(5X,A)") ':: MeshReforming_Partition...'

        call beginTimer("calMesh_Partition")
        call calMesh_Partition(val_iMesh)
        call endTimer("calMesh_Partition")
        
    else
        call allocateArray(val_iMesh%iPart_Poin, val_iMesh%nPoin)
        
        val_iMesh%iPart_Poin(:) = 1
        
    endif

    call endTimer("calGlobalMesh_Reform")

   !call printTimeStatistics
    call clearTimeStatistics
    
    !call testIO_GlobalMesh(val_iMesh,-1)

endsubroutine
!
