!
!===================================!
!                                   !
!                                   !
!===================================!
module mod_Interface_AllocateArray
    implicit none
    !
    interface allocateArray
        MODULE PROCEDURE allocateIntegerArray1D
        MODULE PROCEDURE allocateIntegerArray2D
        MODULE PROCEDURE allocateIntegerArray3D
        MODULE PROCEDURE allocateIntegerArray4D
        
        MODULE PROCEDURE allocateRealArray1D
        MODULE PROCEDURE allocateRealArray2D
        MODULE PROCEDURE allocateRealArray3D
        MODULE PROCEDURE allocateRealArray4D
        
        MODULE PROCEDURE allocateDoubleArray1D
        MODULE PROCEDURE allocateDoubleArray2D
        MODULE PROCEDURE allocateDoubleArray3D
        MODULE PROCEDURE allocateDoubleArray4D
    endinterface
    !
    interface allocateGPUArray
        MODULE PROCEDURE allocateIntegerGPUArray1D
        MODULE PROCEDURE allocateIntegerGPUArray2D
        MODULE PROCEDURE allocateIntegerGPUArray3D
        MODULE PROCEDURE allocateIntegerGPUArray4D
        
        MODULE PROCEDURE allocateRealGPUArray1D
        MODULE PROCEDURE allocateRealGPUArray2D
        MODULE PROCEDURE allocateRealGPUArray3D
        MODULE PROCEDURE allocateRealGPUArray4D
        
        MODULE PROCEDURE allocateDoubleGPUArray1D
        MODULE PROCEDURE allocateDoubleGPUArray2D
        MODULE PROCEDURE allocateDoubleGPUArray3D
        MODULE PROCEDURE allocateDoubleGPUArray4D
    endinterface
    !
contains
    !
    subroutine allocateIntegerArray1D(array,isize)
        implicit none
        integer,allocatable:: array(:)
        integer:: isize
    
        if(isize <= 0) return
        
        if(.not.allocated(array)) then
            allocate(array(isize))
        else
            if(size(array) /= isize) then
                deallocate(array)
                allocate(array(isize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateIntegerArray2D(array,isize,jsize)
        implicit none
        integer,allocatable:: array(:,:)
        integer:: isize
        integer:: jsize
        
        if(isize <= 0) return
        if(jsize <= 0) return
    
        if(.not.allocated(array)) then
            allocate(array(isize,jsize))
        else
            if((size(array,1) /= isize).or. &
               (size(array,2) /= jsize)) then
                deallocate(array)
                allocate(array(isize,jsize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateIntegerArray3D(array,isize,jsize,ksize)
        implicit none
        integer,allocatable:: array(:,:,:)
        integer:: isize
        integer:: jsize
        integer:: ksize
        
        if(isize <= 0) return
        if(jsize <= 0) return
        if(ksize <= 0) return
    
        if(.not.allocated(array)) then
            allocate(array(isize,jsize,ksize))
        else
            if((size(array,1) /= isize).or. &
               (size(array,2) /= jsize).or. &
               (size(array,3) /= ksize)) then
                deallocate(array)
                allocate(array(isize,jsize,ksize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateIntegerArray4D(array,isize,jsize,ksize,lsize)
        implicit none
        integer,allocatable:: array(:,:,:,:)
        integer:: isize
        integer:: jsize
        integer:: ksize
        integer:: lsize
        
        if(isize <= 0) return
        if(jsize <= 0) return
        if(ksize <= 0) return
        if(lsize <= 0) return
    
        if(.not.allocated(array)) then
            allocate(array(isize,jsize,ksize,lsize))
        else
            if((size(array,1) /= isize).or. &
               (size(array,2) /= jsize).or. &
               (size(array,3) /= ksize).or. &
               (size(array,4) /= lsize)) then
                deallocate(array)
                allocate(array(isize,jsize,ksize,lsize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateRealArray1D(array,isize)
        implicit none
        real(kind=4),allocatable:: array(:)
        integer:: isize
        
        if(isize <= 0) return
    
        if(.not.allocated(array)) then
            allocate(array(isize))
        else
            if(size(array) /= isize) then
                deallocate(array)
                allocate(array(isize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateRealArray2D(array,isize,jsize)
        implicit none
        real(kind=4),allocatable:: array(:,:)
        integer:: isize
        integer:: jsize
        
        if(isize <= 0) return
        if(jsize <= 0) return
    
        if(.not.allocated(array)) then
            allocate(array(isize,jsize))
        else
            if((size(array,1) /= isize).or. &
               (size(array,2) /= jsize)) then
                deallocate(array)
                allocate(array(isize,jsize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateRealArray3D(array,isize,jsize,ksize)
        implicit none
        real(kind=4),allocatable:: array(:,:,:)
        integer:: isize
        integer:: jsize
        integer:: ksize
        
        if(isize <= 0) return
        if(jsize <= 0) return
        if(ksize <= 0) return
    
        if(.not.allocated(array)) then
            allocate(array(isize,jsize,ksize))
        else
            if((size(array,1) /= isize).or. &
               (size(array,2) /= jsize).or. &
               (size(array,3) /= ksize)) then
                deallocate(array)
                allocate(array(isize,jsize,ksize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateRealArray4D(array,isize,jsize,ksize,lsize)
        implicit none
        real(kind=4),allocatable:: array(:,:,:,:)
        integer:: isize
        integer:: jsize
        integer:: ksize
        integer:: lsize
    
        if(isize <= 0) return
        if(jsize <= 0) return
        if(ksize <= 0) return
        if(lsize <= 0) return
    
        if(.not.allocated(array)) then
            allocate(array(isize,jsize,ksize,lsize))
        else
            if((size(array,1) /= isize).or. &
               (size(array,2) /= jsize).or. &
               (size(array,3) /= ksize).or. &
               (size(array,4) /= lsize)) then
                deallocate(array)
                allocate(array(isize,jsize,ksize,lsize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateDoubleArray1D(array,isize)
        implicit none
        real(kind=8),allocatable:: array(:)
        integer:: isize
    
        if(isize <= 0) return
    
        if(.not.allocated(array)) then
            allocate(array(isize))
        else
            if(size(array) /= isize) then
                deallocate(array)
                allocate(array(isize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateDoubleArray2D(array,isize,jsize)
        implicit none
        real(kind=8),allocatable:: array(:,:)
        integer:: isize
        integer:: jsize
    
        if(isize <= 0) return
        if(jsize <= 0) return
    
        if(.not.allocated(array)) then
            allocate(array(isize,jsize))
        else
            if((size(array,1) /= isize).or. &
               (size(array,2) /= jsize)) then
                deallocate(array)
                allocate(array(isize,jsize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateDoubleArray3D(array,isize,jsize,ksize)
        implicit none
        real(kind=8),allocatable:: array(:,:,:)
        integer:: isize
        integer:: jsize
        integer:: ksize
    
        if(isize <= 0) return
        if(jsize <= 0) return
        if(ksize <= 0) return
    
        if(.not.allocated(array)) then
            allocate(array(isize,jsize,ksize))
        else
            if((size(array,1) /= isize).or. &
               (size(array,2) /= jsize).or. &
               (size(array,3) /= ksize)) then
                deallocate(array)
                allocate(array(isize,jsize,ksize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateDoubleArray4D(array,isize,jsize,ksize,lsize)
        implicit none
        real(kind=8),allocatable:: array(:,:,:,:)
        integer:: isize
        integer:: jsize
        integer:: ksize
        integer:: lsize
    
        if(isize <= 0) return
        if(jsize <= 0) return
        if(ksize <= 0) return
        if(lsize <= 0) return
    
        if(.not.allocated(array)) then
            allocate(array(isize,jsize,ksize,lsize))
        else
            if((size(array,1) /= isize).or. &
               (size(array,2) /= jsize).or. &
               (size(array,3) /= ksize).or. &
               (size(array,4) /= lsize)) then
                deallocate(array)
                allocate(array(isize,jsize,ksize,lsize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateIntegerGPUArray1D(array,isize)
        implicit none
        integer,device,allocatable:: array(:)
        integer:: isize
    
        if(isize <= 0) return
        
        if(.not.allocated(array)) then
            allocate(array(isize))
        else
            if(size(array) /= isize) then
                deallocate(array)
                allocate(array(isize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateIntegerGPUArray2D(array,isize,jsize)
        implicit none
        integer,device,allocatable:: array(:,:)
        integer:: isize
        integer:: jsize
        
        if(isize <= 0) return
        if(jsize <= 0) return
    
        if(.not.allocated(array)) then
            allocate(array(isize,jsize))
        else
            if((size(array,1) /= isize).or. &
               (size(array,2) /= jsize)) then
                deallocate(array)
                allocate(array(isize,jsize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateIntegerGPUArray3D(array,isize,jsize,ksize)
        implicit none
        integer,device,allocatable:: array(:,:,:)
        integer:: isize
        integer:: jsize
        integer:: ksize
        
        if(isize <= 0) return
        if(jsize <= 0) return
        if(ksize <= 0) return
    
        if(.not.allocated(array)) then
            allocate(array(isize,jsize,ksize))
        else
            if((size(array,1) /= isize).or. &
               (size(array,2) /= jsize).or. &
               (size(array,3) /= ksize)) then
                deallocate(array)
                allocate(array(isize,jsize,ksize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateIntegerGPUArray4D(array,isize,jsize,ksize,lsize)
        implicit none
        integer,device,allocatable:: array(:,:,:,:)
        integer:: isize
        integer:: jsize
        integer:: ksize
        integer:: lsize
        
        if(isize <= 0) return
        if(jsize <= 0) return
        if(ksize <= 0) return
        if(lsize <= 0) return
    
        if(.not.allocated(array)) then
            allocate(array(isize,jsize,ksize,lsize))
        else
            if((size(array,1) /= isize).or. &
               (size(array,2) /= jsize).or. &
               (size(array,3) /= ksize).or. &
               (size(array,4) /= lsize)) then
                deallocate(array)
                allocate(array(isize,jsize,ksize,lsize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateRealGPUArray1D(array,isize)
        implicit none
        real(kind=4),device,allocatable:: array(:)
        integer:: isize
        
        if(isize <= 0) return
    
        if(.not.allocated(array)) then
            allocate(array(isize))
        else
            if(size(array) /= isize) then
                deallocate(array)
                allocate(array(isize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateRealGPUArray2D(array,isize,jsize)
        implicit none
        real(kind=4),device,allocatable:: array(:,:)
        integer:: isize
        integer:: jsize
        
        if(isize <= 0) return
        if(jsize <= 0) return
    
        if(.not.allocated(array)) then
            allocate(array(isize,jsize))
        else
            if((size(array,1) /= isize).or. &
               (size(array,2) /= jsize)) then
                deallocate(array)
                allocate(array(isize,jsize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateRealGPUArray3D(array,isize,jsize,ksize)
        implicit none
        real(kind=4),device,allocatable:: array(:,:,:)
        integer:: isize
        integer:: jsize
        integer:: ksize
        
        if(isize <= 0) return
        if(jsize <= 0) return
        if(ksize <= 0) return
    
        if(.not.allocated(array)) then
            allocate(array(isize,jsize,ksize))
        else
            if((size(array,1) /= isize).or. &
               (size(array,2) /= jsize).or. &
               (size(array,3) /= ksize)) then
                deallocate(array)
                allocate(array(isize,jsize,ksize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateRealGPUArray4D(array,isize,jsize,ksize,lsize)
        implicit none
        real(kind=4),device,allocatable:: array(:,:,:,:)
        integer:: isize
        integer:: jsize
        integer:: ksize
        integer:: lsize
    
        if(isize <= 0) return
        if(jsize <= 0) return
        if(ksize <= 0) return
        if(lsize <= 0) return
    
        if(.not.allocated(array)) then
            allocate(array(isize,jsize,ksize,lsize))
        else
            if((size(array,1) /= isize).or. &
               (size(array,2) /= jsize).or. &
               (size(array,3) /= ksize).or. &
               (size(array,4) /= lsize)) then
                deallocate(array)
                allocate(array(isize,jsize,ksize,lsize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateDoubleGPUArray1D(array,isize)
        implicit none
        real(kind=8),device,allocatable:: array(:)
        integer:: isize
    
        if(isize <= 0) return
    
        if(.not.allocated(array)) then
            allocate(array(isize))
        else
            if(size(array) /= isize) then
                deallocate(array)
                allocate(array(isize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateDoubleGPUArray2D(array,isize,jsize)
        implicit none
        real(kind=8),device,allocatable:: array(:,:)
        integer:: isize
        integer:: jsize
    
        if(isize <= 0) return
        if(jsize <= 0) return
    
        if(.not.allocated(array)) then
            allocate(array(isize,jsize))
        else
            if((size(array,1) /= isize).or. &
               (size(array,2) /= jsize)) then
                deallocate(array)
                allocate(array(isize,jsize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateDoubleGPUArray3D(array,isize,jsize,ksize)
        implicit none
        real(kind=8),device,allocatable:: array(:,:,:)
        integer:: isize
        integer:: jsize
        integer:: ksize
    
        if(isize <= 0) return
        if(jsize <= 0) return
        if(ksize <= 0) return
    
        if(.not.allocated(array)) then
            allocate(array(isize,jsize,ksize))
        else
            if((size(array,1) /= isize).or. &
               (size(array,2) /= jsize).or. &
               (size(array,3) /= ksize)) then
                deallocate(array)
                allocate(array(isize,jsize,ksize))
            endif
        endif
    
    endsubroutine
    !
    subroutine allocateDoubleGPUArray4D(array,isize,jsize,ksize,lsize)
        implicit none
        real(kind=8),device,allocatable:: array(:,:,:,:)
        integer:: isize
        integer:: jsize
        integer:: ksize
        integer:: lsize
    
        if(isize <= 0) return
        if(jsize <= 0) return
        if(ksize <= 0) return
        if(lsize <= 0) return
    
        if(.not.allocated(array)) then
            allocate(array(isize,jsize,ksize,lsize))
        else
            if((size(array,1) /= isize).or. &
               (size(array,2) /= jsize).or. &
               (size(array,3) /= ksize).or. &
               (size(array,4) /= lsize)) then
                deallocate(array)
                allocate(array(isize,jsize,ksize,lsize))
            endif
        endif
    
    endsubroutine
    !
endmodule
!
