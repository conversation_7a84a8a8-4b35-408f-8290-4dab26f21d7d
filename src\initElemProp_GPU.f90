
!---------------------------------------------
!
!---------------------------------------------
subroutine initElemProp_GPU
    use mod_ElemProp
    use mod_ElemProp_GPU
    implicit none
    
    ElemProp_nodeNum_d  = ElemProp_nodeNum  !包含顶点数
    ElemProp_nodeList_d = ElemProp_nodeList !包含顶点列表
        
    ElemProp_edgeNum_d  = ElemProp_edgeNum  !包含通量面数
    ElemProp_edgeKind_d = ElemProp_edgeKind !包含通量面类型
    ElemProp_edgeList_d = ElemProp_edgeList !包含通量面列表
        
    ElemProp_lineNum_d  = ElemProp_lineNum  !包含线元数
    ElemProp_lineList_d = ElemProp_lineList !包含线元列表
    ElemProp_lineENUM_d = ElemProp_lineENUM !包含线所在该单元的通量面位置
        
    ElemProp_diviNum_d  = ElemProp_diviNum  !包含分割数
    ElemProp_diviKind_d = ElemProp_diviKind !包含分割类型
    ElemProp_diviList_d = ElemProp_diviList !包含分割列表
    
    
endsubroutine
!

