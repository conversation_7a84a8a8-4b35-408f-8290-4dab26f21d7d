!
!---------------------------------------------
!
!---------------------------------------------
module mod_calMesh_Partition
    implicit none
    !
    contains
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine calMesh_Partition(val_iMesh)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
        
        
        call partitionWithCoor(val_iMesh)
        
        !call partitionWithMetis(val_iMesh)
        
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine partitionWithCoor(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_PreDefine_IOPort
        use mod_Config
        use mod_Interface_AllocateArray
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
        !
        integer:: i
        integer:: iDivDim
        real(kind=REALLEN):: iBnd(nDim,2),iDivValue
        logical:: isOk
    
    
        !---------------------------------------------
        call allocateArray(val_iMesh%iPart_Poin, val_iMesh%nPoin)
        val_iMesh%iPart_Poin(:) = 1
        
        
        !---------------------------------------------
        if(NUMBER_PART /= 2) then
            write(ioPort_Out,*) 'Current nPart =',NUMBER_PART
            write(ioPort_Out,*) 'NUMBER_PART == 2 is only supported!'
            return
        endif
        
        
        !---------------------------------------------
        call getMeshBnd(val_iMesh, iBnd, iDivDim)
    
    
        !---------------------------------------------
        call getDivValue(val_iMesh%iCoor_Poin, val_iMesh%nPoin, &
                         iDivDim, iDivValue, isOk               )
    
        
        !---------------------------------------------
        call allocateArray(val_iMesh%iPart_Poin, val_iMesh%nPoin)
        
        val_iMesh%iPart_Poin(:) = 1
        
        do i=1,val_iMesh%nPoin
            if(val_iMesh%iCoor_Poin(iDivDim,i) > iDivValue) then
                val_iMesh%iPart_Poin(i) = 2
            endif
        enddo
    
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine partitionWithMetis(val_iMesh)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh):: val_iMesh
        !
        integer:: i
    
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getMeshBnd(val_iMesh,val_BndCoor,val_MainDir)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        real(kind=REALLEN),intent(out):: val_BndCoor(nDim,2)
        integer,intent(out):: val_MainDir
        !
        integer:: i,j,iDir
        real(kind=REALLEN):: minBnd(nDim),maxBnd(nDim)
    
        val_BndCoor(:,:) = 0.0
        val_MainDir      = 0
    
        minBnd(:) = val_iMesh%iCoor_Poin(:,1)
        maxBnd(:) = val_iMesh%iCoor_Poin(:,1)
    
        do i=2,val_iMesh%nPoin
            do j=1,nDim
                minBnd(j) = min(minBnd(j), val_iMesh%iCoor_Poin(j,i))
                maxBnd(j) = max(maxBnd(j), val_iMesh%iCoor_Poin(j,i))
            enddo
        enddo
    
        iDir = 1
        do j=2,nDim
            if(maxBnd(j) -minBnd(j) > maxBnd(iDir) -minBnd(iDir)) then
                iDir = j
            endif
        enddo
    
        val_BndCoor(:,1) = minBnd(:)
        val_BndCoor(:,2) = maxBnd(:)
    
        val_MainDir = iDir
    
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getDivValue(val_iCoor,val_nPoin, &
                val_iDivDim,val_iDivValue,val_isOk)
        use mod_PreDefine_Dimension
        use mod_PreDefine_Precision
        use mod_calDivRegin, only: sortPoinOfRagin
        use mod_Interface_SortArray
        implicit none
        real(kind=REALLEN),intent(in):: val_iCoor(nDim,val_nPoin)
        integer,intent(in):: val_nPoin
        integer,intent(in):: val_iDivDim
        real(kind=REALLEN),intent(out):: val_iDivValue
        logical,intent(out):: val_isOk
        !
        integer:: i
        integer:: iNumn(val_nPoin),iNeID(val_nPoin)
        real(kind=REALLEN):: iArray(val_nPoin),iDiv
    
    
        do i=1,val_nPoin
            iArray(i) = val_iCoor(val_iDivDim,i)
        enddo
        
        call sortArray1D(iArray, val_nPoin)
    
        iDiv = 0.5*(iArray(floor(val_nPoin/2.0)  ) + &
                    iArray(floor(val_nPoin/2.0)+1) )
    
        val_iDivValue = iDiv
        val_isOk = .true.
    
    endsubroutine
    !
endmodule
!
    
    
    