!
module mod_calMesh_Geometric
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    use mod_PreDefine_Elem
    use mod_PreDefine_IOPort
    use mod_ElemProp
    use mod_calMeshGeom_Func
    use mod_calMeshTopo_Func
    use mod_BelvType
    implicit none
    !
contains
    !
    subroutine calGlobalMesh_Geometric(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_Config
        use mod_Interface_AllocateArray
        use mod_Statistics
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
    
        
        !-------------------------------------------
        call beginTimer("calGlobalMesh_Geometric")
        call allocateArray(val_iMesh%iVolu_Poin, val_iMesh%nPoin)
        call allocateArray(val_iMesh%iCoor_Elem, nDim, val_iMesh%nElem)
        call allocateArray(val_iMesh%iCoor_Edge, nDim, val_iMesh%nEdge)
        call allocateArray(val_iMesh%iNvor_Edge, nDim, val_iMesh%nEdge)
        call allocateArray(val_iMesh%iArea_Bele, val_iMesh%nBele)
        call allocateArray(val_iMesh%iNvor_Bele, nDim, val_iMesh%nBele)
        call allocateArray(val_iMesh%iCoef_Sate, nDim, val_iMesh%nSate)
        call allocateArray(val_iMesh%iArea_Belv, val_iMesh%nBelv)
        call allocateArray(val_iMesh%iNvor_Belv, val_iMesh%nBelv, nDim)
        call allocateArray(val_iMesh%iCoef_Belv, val_iMesh%nBelv, nDim)
        call allocateArray(val_iMesh%iCoor_Belv, val_iMesh%nBelv, nDim)
    
        !write(*,*) "calICoor_Elem"
        call beginTimer("calICoor_Elem")
        call calICoor_Elem(val_iMesh, val_iMesh%iCoor_Elem)
        !write(*,*) "calICoor_Edge"
        call beginNextTimer("calICoor_Edge")
        call calICoor_Edge(val_iMesh, val_iMesh%iCoor_Edge)
        !write(*,*) "calINvor_Edge"
        call beginNextTimer("calINvor_Edge")
        call calINvor_Edge(val_iMesh, val_iMesh%iNvor_Edge)
        !write(*,*) "calIVolu_Poin"
        call beginNextTimer("calIVolu_Poin")
        call calIVolu_Poin(val_iMesh, val_iMesh%iVolu_Poin)

        !write(*,*) "calICoef_Sate"
        call beginNextTimer("calICoef_Sate")
        call calICoef_Sate(val_iMesh,val_iMesh%iCoef_Sate)

        !write(*,*) "calIAreaNvor_Bele"
        call beginNextTimer("calIAreaNvor_Bele")
        call calIAreaNvor_Bele(val_iMesh, val_iMesh%iArea_Bele, val_iMesh%iNvor_Bele)
        call endTimer("calIAreaNvor_Bele")
    
        if(iBelvType == BelvType_Merged) then
            !write(*,*) "calICoef_Belv"
            call beginTimer("calICoef_Belv")
            call calICoef_Belv(val_iMesh,val_iMesh%iArea_belv, val_iMesh%iNvor_Belv, val_iMesh%iCoef_Belv)
            call endTimer("calICoef_Belv")
        
        elseif(iBelvType == BelvType_Separate) then
            !write(*,*) "calICoef_BelvR"
            call beginTimer("calICoef_BelvR")
            call calICoef_BelvR(val_iMesh,val_iMesh%iArea_belv, val_iMesh%iNvor_Belv, val_iMesh%iCoef_Belv)
            call endTimer("calICoef_BelvR")
        endif
        val_iMesh%iCoor_Belv(:,:) = 0.0

        call endTimer("calGlobalMesh_Geometric")

        !call printTimeStatistics
        call clearTimeStatistics
        
    endsubroutine
    !
    !
    !---------------------------------------------
    !Part 1
    !---------------------------------------------
    subroutine calICoor_Elem(val_iMesh,val_iCoor_Elem)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        real(kind=REALLEN),intent(inout):: val_iCoor_Elem(nDim , val_iMesh%nElem)
        !
        integer:: i,j
        integer:: elemKind,nodeNum,nodeIP,nodeID
        integer:: icci(4*nDim-4)
        real(kind=8):: iCoor(nDim)
    
    
        val_iCoor_Elem(:,:) = 0.0

        !$omp parallel do private(elemKind, nodeNum, icci,iCoor,j,nodeIP,nodeID)
        do i=1,val_iMesh%nElem
            elemKind = val_iMesh%iElemKind(i)
        
            nodeNum = ElemProp_NodeNum(elemKind)
        
            icci(:) =  val_iMesh%iElem(:,i)
        
            iCoor(:) = 0.0
            do j=1,nodeNum
                nodeIP = ElemProp_NodeList(j,elemKind)
            
                nodeID = icci(nodeIP)
            
                iCoor(:) = iCoor(:) + val_iMesh%iCoor_Poin(:,nodeID)
            enddo
        
            val_iCoor_Elem(:,i) = iCoor(:)/nodeNum    
        enddo
        !$omp end parallel do
    
    endsubroutine
    !
    !---------------------------------------------
    subroutine calICoor_Edge(val_iMesh,val_iCoor_Edge)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        real(kind=REALLEN),intent(inout):: val_iCoor_Edge(nDim , val_iMesh%nEdge)
        !
        integer:: i,j
        integer:: edgeKind,nodeNum,nodeIP,nodeID
        integer:: ieei(2*nDim-2)
        real(kind=8):: iCoor(nDim)
    
    
        val_iCoor_Edge(:,:) = 0.0
        !$omp parallel do private(edgeKind, nodeNum,ieei,iCoor,j,nodeIP,nodeID)
        do i=1,val_iMesh%nEdge
            edgeKind = val_iMesh%iEdgeKind(i)
        
            nodeNum = ElemProp_NodeNum(edgeKind)
        
            ieei(:)  = val_iMesh%iEdge(1:2*nDim-2,i)
        
            iCoor(:) = 0.0
            do j=1,nodeNum
                nodeIP = ElemProp_NodeList(j,edgeKind)
            
                nodeID = ieei(nodeIP)
            
                iCoor(:) = iCoor(:) + val_iMesh%iCoor_Poin(:,nodeID)
            enddo
        
            val_iCoor_Edge(:,i) = iCoor(:)/nodeNum    
        enddo
        !$omp end parallel do
    
    
    endsubroutine
    !
    !---------------------------------------------
    subroutine calINvor_Edge(val_iMesh,val_iNvor_Edge)
        use mod_TypeDef_Mesh
        use mod_vectorAlgebra
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        real(kind=REALLEN),intent(inout):: val_iNvor_Edge(nDim , val_iMesh%nEdge)
        !
        integer:: i,j,k
        integer:: iek,el,ieei(2*nDim-2),kl,ek,pl
        real(kind=8):: ieec(nDim,2*nDim-2)
        real(kind=8):: nvi(nDim),elVector(nDim),iDotValue
    
    
        val_iNvor_Edge(:,:) = 0.0
        !$omp parallel do private(iek,ieei,j,ieec,nvi,el,kl,ek,pl,elVector,iDotValue)
        do i=1,val_iMesh%nEdge
            iek = val_iMesh%iEdgeKind(i)
        
            ieei(:) = val_iMesh%iEdge(1:2*nDim-2 , i)
        
            do j=1,2*nDim-2
                ieec(:,j) = val_iMesh%iCoor_Poin(:,ieei(j))
            enddo
        
            !getNormalVector(val_nDim,val_iek,val_size,val_ieec,val_normalVector)
            call getNormalVector(nDim, iek, 2*nDim-2, ieec, nvi)
        
            el = val_iMesh%iEdge(2*nDim-1,i)
            kl = val_iMesh%iEdge(2*nDim  ,i)
            ek = val_iMesh%iElemKind(el)
        
            call getLeftPoinID(ek,kl,pl)
            if(pl == 0) then
                write(ioPort_Out,*) 'pl==0:calINvor_Edge'
            endif
            pl = val_iMesh%iElem(pl,el)
        
            elVector(:) = val_iMesh%iCoor_Poin(:,pl) - ieec(:,1)
        
            call vector_scalarProduct(nDim, nvi, elVector, iDotValue)
        
            val_iNvor_Edge(:,i) = nvi
        enddo
        !$omp end parallel do
    
        return
        !
    contains
        !
        subroutine getLeftPoinID(val_elemKind,val_edgeID,val_poinID)
            use mod_PreDefine_Elem
            use mod_ElemProp
            implicit none
            integer,intent(in):: val_elemKind
            integer,intent(in):: val_edgeID
            integer,intent(out):: val_poinID
        
        
            val_poinID = 0
        
            if(val_elemKind == ELEM_TRIANGLE) then!三角形单元
                if(val_edgeID == 1) then
                    val_poinID = 3
                elseif(val_edgeID == 2) then
                    val_poinID = 1
                elseif(val_edgeID == 3) then
                    val_poinID = 2
                endif
            elseif(val_elemKind == ELEM_RECTANGLE) then!四边形单元
                if(val_edgeID ==1) then
                    val_poinID = 3
                elseif(val_edgeID ==2) then
                    val_poinID = 4
                elseif(val_edgeID ==3) then
                    val_poinID = 1
                elseif(val_edgeID ==4) then
                    val_poinID = 2
                endif
            elseif(val_elemKind == ELEM_TETRAHEDRAL) then!四面体单元
                if(val_edgeID ==1) then
                    val_poinID = 5
                elseif(val_edgeID ==2) then
                    val_poinID = 3
                elseif(val_edgeID ==3) then
                    val_poinID = 1
                elseif(val_edgeID ==4) then
                    val_poinID = 2
                endif
            elseif(val_elemKind == ELEM_HEXAHEDRAL) then!六面体单元
                if(val_edgeID ==1) then
                    val_poinID = 8
                elseif(val_edgeID ==2) then
                    val_poinID = 1
                elseif(val_edgeID ==3) then
                    val_poinID = 4
                elseif(val_edgeID ==4) then
                    val_poinID = 2
                elseif(val_edgeID ==5) then
                    val_poinID = 1
                elseif(val_edgeID ==6) then
                    val_poinID = 3
                endif
            elseif(val_elemKind == ELEM_WEDGE) then!三棱柱单元
                if(val_edgeID ==1) then
                    val_poinID = 6
                elseif(val_edgeID ==2) then
                    val_poinID = 1
                elseif(val_edgeID ==3) then
                    val_poinID = 3
                elseif(val_edgeID ==4) then
                    val_poinID = 1
                elseif(val_edgeID ==5) then
                    val_poinID = 2
                endif
            elseif(val_elemKind == ELEM_PYRAMID) then!金字塔单元
                if(val_edgeID ==1) then
                    val_poinID = 5
                elseif(val_edgeID ==2) then
                    val_poinID = 4
                elseif(val_edgeID ==3) then
                    val_poinID = 1
                elseif(val_edgeID ==4) then
                    val_poinID = 2
                elseif(val_edgeID ==5) then
                    val_poinID = 3
                endif
            endif
               
        endsubroutine
        !
    endsubroutine
    !
    !---------------------------------------------
    subroutine calIVolu_Poin(val_iMesh,val_iVolu_Poin)
        use mod_TypeDef_Mesh
        !use ieee_arithmetic
        use mod_MPIEnvironment
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        real(kind=REALLEN),intent(inout):: val_iVolu_Poin(val_iMesh%nPoin)
        !
        integer:: i,j,k,s
        integer:: sateID,lineID,elemID,edgeID,lineIP,edgeIP,elemKind
        real(kind=8):: poinA(nDim),poinB(nDim),poinC(nDim),poinD(nDim)
        real(kind=8):: iVolu,jVolu,kVolu
    
    
        val_iVolu_Poin(:) = 0.0

!$omp parallel do num_threads(nOMPThreads) private(poinA,iVolu,j,sateID,lineID,poinB,jVolu,k,elemID,lineIP,poinC,kVolu,elemKind,s,edgeIP,edgeID,poinD)
        do i=1,val_iMesh%nPoin
            poinA(:) = val_iMesh%iCoor_Poin(:,i)
        
            iVolu = 0.0
            do j=val_iMesh%kSate_Poin(i)+1,val_iMesh%kSate_Poin(i+1)
                sateID = val_iMesh%iSate_Poin(j,1)
                lineID = val_iMesh%iSate_Poin(j,2)
            
                poinB(:) = 0.5*(poinA(:) + val_iMesh%iCoor_Poin(:,sateID))
            
                jVolu = 0.0
                do k=val_iMesh%kElem_Line(lineID)+1,val_iMesh%kElem_Line(lineID+1)
                    elemID = val_iMesh%iElem_Line(1,k)
                    lineIP = val_iMesh%iElem_Line(2,k)
                
                    poinC(:) = val_iMesh%iCoor_Elem(:,elemID)
                
                    if(nDim == 2) then
                        call getVolumeOfTriangle(nDim,poinA,poinB,poinC,kVolu)
                    
                        jVolu = jVolu + kVolu
                    
                    elseif(nDim == 3) then
                        elemKind = val_iMesh%iElemKind(elemID)
                    
                        do s=1,2
                            edgeIP = ElemProp_lineENUM(s,lineIP,elemKind)
                        
                            edgeID = val_iMesh%iElemLink(elemID,edgeIP,3)
                        
                            poinD(:) = val_iMesh%iCoor_Edge(:,edgeID)
                        
                            call getVolumeOfTetrahedral(nDim,poinA,poinB,poinC,poinD,kVolu)
                        
                            jVolu = jVolu + kVolu
                        enddo
                    
                    endif
                enddo
            
                iVolu = iVolu +jVolu
            enddo
        
            val_iVolu_Poin(i) = iVolu
        enddo
!$omp end parallel do
    
    endsubroutine
    !
    !
    !---------------------------------------------
    !Part 2
    !---------------------------------------------
    subroutine calIAreaNvor_Bele(val_iMesh,val_iArea_Bele,val_iNvor_Bele)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        real(kind=REALLEN),intent(inout):: val_iArea_Bele(val_iMesh%nBele)
        real(kind=REALLEN),intent(inout):: val_iNvor_Bele(nDim , val_iMesh%nBele)
        !
        integer:: i,j
        integer:: elID,klID
        integer:: icci(4*nDim-4),ick,ieei(2*nDim-2),iek
        real(kind=8):: iCoor(nDim,2*nDim-2)
        real(kind=8):: iArea,Nvi(nDim)

        !$omp parallel do private(elID,klID,icci,ick,iek,ieei,iCoor,iArea,Nvi)
        do i=1,val_iMesh%nBele
            elID = val_iMesh%iBeleLink(i,1)
            klID = val_iMesh%iBeleLink(i,2)
            !
            icci(:) = val_iMesh%iElem(:,elID)
            ick     = val_iMesh%iElemKind(elID)
        
            call getEdgeOfElem(ick, 4*nDim-4, icci, klID, iek, 2*nDim-2, ieei )
        
            do j=1,2*nDim-2
                iCoor(:,j) = val_iMesh%iCoor_Poin(:,ieei(j))
            enddo
        
            call getVolumeOfElem(nDim, iek, 2*nDim-2, iCoor, iArea)
    
            call getNormalVector(nDim, iek, 2*nDim-2, iCoor, Nvi)
        
            val_iArea_Bele(i)   = iArea
            val_iNvor_Bele(:,i) = Nvi(:)
        enddo
        !$omp end parallel do
    
        return
    
    endsubroutine
    !
    !---------------------------------------------
    subroutine calICoef_Sate(val_iMesh,val_iCoef_Sate)
        use mod_TypeDef_Mesh
        use mod_vectorAlgebra
        use mod_Interface_Unitization
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        real(kind=REALLEN),intent(inout):: val_iCoef_Sate(nDim,val_iMesh%nSate)
        !
        integer:: i,j,k,s
        integer:: sateID,lineID,elemID,edgeID,lineIP,edgeIP,elemKind
        real(kind=8):: poinA(nDim),poinB(nDim),poinC(nDim),poinD(nDim),poinE(nDim)
        real(kind=8):: iArea,nvori(nDim),coefi(nDim)
        !
        integer:: iek
        real(kind=8):: iCoor(nDim,2*nDim-2)
        real(kind=8):: iVec(nDim),iValue
    
    
        val_iCoef_Sate(:,:) = 0.0

        !$omp parallel do private(j,k,s,sateID,lineID,elemID,edgeID,lineIP,edgeIP,elemKind,iArea,nvori,coefi,poinA,poinB,poinC,poinD,poinE,iek,iCoor,iVec,iValue)
        do i=1,val_iMesh%nPoin
            poinA(:) = val_iMesh%iCoor_Poin(:,i)
        
            do j=val_iMesh%kSate_Poin(i)+1,val_iMesh%kSate_Poin(i+1)
                sateID = val_iMesh%iSate_Poin(j,1)
                lineID = val_iMesh%iSate_Poin(j,2)
                
                if(lineID <= 0) then
                    val_iCoef_Sate(:,j) = 0.0
                    cycle
                endif
            
                poinB(:) = 0.5*(poinA(:) + val_iMesh%iCoor_Poin(:,sateID))
            
                coefi(:) = 0.0
            
                iVec(:) = val_iMesh%iCoor_Poin(:,sateID) - poinA(:)
                call unitization(nDim,iVec)
            
                do k=val_iMesh%kElem_Line(lineID)+1,val_iMesh%kElem_Line(lineID+1)
                    elemID = val_iMesh%iElem_Line(1,k)
                    lineIP = val_iMesh%iElem_Line(2,k)
                
                    poinC(:) = val_iMesh%iCoor_Elem(:,elemID)
                
                    if(nDim == 2) then
                        iek = ELEM_LINE
                        iCoor(:,1) = poinB(:)
                        iCoor(:,2) = poinC(:)
                    
                        !getAreaNvor_Edge(val_nDim,val_iek,val_size,val_ieec,val_areaFi,val_NvorFi)
                        call getAreaNvor_Edge(nDim, iek, 2*nDim-2, iCoor, iArea, nvori)
                    
                        call vector_scalarProduct(nDim, iVec,nvori,iValue)
                    
                        if(iValue >= 0) then
                            coefi(:) = coefi(:) + iArea*nvori(:)
                        else
                            coefi(:) = coefi(:) - iArea*nvori(:)
                        endif
                    
                    elseif(nDim == 3) then
                        elemKind = val_iMesh%iElemKind(elemID)
                    
                        edgeIP   = ElemProp_lineENUM(1,lineIP,elemKind)
                        edgeID   = val_iMesh%iElemLink(elemID,edgeIP,3)
                        poinD(:) = val_iMesh%iCoor_Edge(:,edgeID)
                        
                        edgeIP   = ElemProp_lineENUM(2,lineIP,elemKind)
                        edgeID   = val_iMesh%iElemLink(elemID,edgeIP,3)
                        poinE(:) = val_iMesh%iCoor_Edge(:,edgeID)
                    
                        iek = ELEM_RECTANGLE
                    
                        iCoor(:,1) = poinB(:)
                        iCoor(:,2) = poinD(:)
                        iCoor(:,3) = poinC(:)
                        iCoor(:,4) = poinE(:)
                    
                        !getAreaNvor_Edge(val_nDim,val_iek,val_size,val_ieec,val_areaFi,val_NvorFi)
                        call getAreaNvor_Edge(nDim, iek, 2*nDim-2, iCoor, iArea, nvori)
                    
                        if(i == val_iMesh%iElem(ElemProp_LineList(1,lineIP,elemKind),elemID)) then
                            coefi(:) = coefi(:) + iArea*nvori(:)
                        else
                            coefi(:) = coefi(:) - iArea*nvori(:)
                        endif
                    
                    endif
                
                enddo
            
                val_iCoef_Sate(:,j) = coefi(:)/val_iMesh%iVolu_Poin(i)
            enddo
        enddo
        !$omp end parallel do
    
        return
    
    endsubroutine
    !
    subroutine calICoef_Belv(val_iMesh,val_iArea,val_iNvor,val_iCoef)
        use mod_TypeDef_Mesh
        use mod_vectorAlgebra
        use mod_calMeshGeom_Func
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        real(kind=REALLEN),intent(inout):: val_iArea(val_iMesh%nBelv)
        real(kind=REALLEN),intent(inout):: val_iNvor(val_iMesh%nBelv, nDim)
        real(kind=REALLEN),intent(inout):: val_iCoef(val_iMesh%nBelv, nDim)
        !
        integer:: i,j
        integer:: poinID,beleID,beleIP,poinL,poinR,nodeNum
        real(kind=8):: iCoef(nDim),jCoef(nDim),iArea,iVolu
        real(kind=8):: iCoor(nDim,2*nDim-2),Nvi(nDim)
        real(kind=8):: ibeleCent(nDim,val_iMesh%nBele)
        
        
        !---------------------------------------------
        iBeleCent(:,:) = 0
        do i=1,val_iMesh%nBele
            nodeNum = val_iMesh%iBeleProp(i,1)
            
            iCoef(:) = 0.0
            do j=1,nodeNum
                poinID = val_iMesh%iBele(j,i)
                
                iCoef(:) = iCoef(:) + val_iMesh%iCoor_Poin(:,poinID)
            enddo
            iBeleCent(:,i) = iCoef(:)/nodeNum
        enddo
        
        
        !---------------------------------------------
        do i=1,val_iMesh%nBelv
            poinID = val_iMesh%iBelvProp(i,1)
            
            iCoef(:) = 0.0
            do j=val_iMesh%kBelx_Belv(i)+1,val_iMesh%kBelx_belv(i+1)
                beleID = val_iMesh%iBelx_Belv(j,1)
                
                nodeNum = val_iMesh%iBeleProp(beleID,1)
                
                if(nodeNum == 4) then
                    beleIP = val_iMesh%iBelx_Belv(j,2)
                    
                    if(poinID /= val_iMesh%iBele(beleIP,beleID)) then
                        pause 'poinID /= val_iMesh%iBele(beleIP,beleID)'
                    endif
                
                    poinL =val_iMesh%iBele(mod(beleIP+2,4)+1, beleID)
                    poinR =val_iMesh%iBele(mod(beleIP  ,4)+1, beleID)
                    
                    iCoor(:,1) = val_iMesh%iCoor_Poin(:,poinID)
                    iCoor(:,2) = 0.5*(iCoor(:,1) + val_iMesh%iCoor_Poin(:,poinR))
                    iCoor(:,3) = iBeleCent(:,beleID)
                    iCoor(:,4) = 0.5*(iCoor(:,1) + val_iMesh%iCoor_Poin(:,poinL))
                    
                    call getAreaNvor_Edge(nDim, ELEM_RECTANGLE, 2*nDim-2, iCoor, iArea, Nvi)
                    
                    jCoef(:) = iArea*Nvi(:)
                else
                    jCoef(:) = val_iMesh%iNvor_bele(:,beleID)*val_iMesh%iArea_bele(beleID)/nodeNum
                endif
                
                iCoef(:) = iCoef(:) + jCoef(:)
            enddo
            
            call vector_length(nDim, iCoef, iArea)
            
            val_iArea(i  ) = iArea
            val_iNvor(i,:) = iCoef(:)/iArea
            val_iCoef(i,:) = iCoef(:)/val_iMesh%iVolu_Poin(poinID)
        enddo
        
    endsubroutine
    !
    subroutine calICoef_BelvR(val_iMesh,val_iArea,val_iNvor,val_iCoef)
        use mod_TypeDef_Mesh
        use mod_vectorAlgebra
        use mod_calMeshGeom_Func
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        real(kind=REALLEN),intent(inout):: val_iArea(val_iMesh%nBelv)
        real(kind=REALLEN),intent(inout):: val_iNvor(val_iMesh%nBelv, nDim)
        real(kind=REALLEN),intent(inout):: val_iCoef(val_iMesh%nBelv, nDim)
        !
        integer:: i,j
        integer:: poinID,beleID,beleIP,poinL,poinR,nodeNum
        real(kind=8):: iCoef(nDim),jCoef(nDim),iArea,iVolu
        real(kind=8):: iCoor(nDim,2*nDim-2),Nvi(nDim)
        real(kind=8):: ibeleCent(nDim,val_iMesh%nBele)
        
        
        !---------------------------------------------
        iBeleCent(:,:) = 0
        do i=1,val_iMesh%nBele
            nodeNum = val_iMesh%iBeleProp(i,1)
            
            iCoef(:) = 0.0
            do j=1,nodeNum
                poinID = val_iMesh%iBele(j,i)
                
                iCoef(:) = iCoef(:) + val_iMesh%iCoor_Poin(:,poinID)
            enddo
            iBeleCent(:,i) = iCoef(:)/nodeNum
        enddo
        
        
        !---------------------------------------------
        do i=1,val_iMesh%nBelv
            poinID = val_iMesh%iBelvProp(i,1)
            beleID = val_iMesh%iBelx_Belv(i,1)
            
            nodeNum = val_iMesh%iBeleProp(beleID,1)
                
            if(nodeNum == 4) then
                beleIP = val_iMesh%iBelx_Belv(i,2)
                    
                if(poinID /= val_iMesh%iBele(beleIP,beleID)) then
                    pause 'poinID /= val_iMesh%iBele(beleIP,beleID)'
                endif
                
                poinL =val_iMesh%iBele(mod(beleIP+2,4)+1, beleID)
                poinR =val_iMesh%iBele(mod(beleIP  ,4)+1, beleID)
                    
                iCoor(:,1) = val_iMesh%iCoor_Poin(:,poinID)
                iCoor(:,2) = 0.5*(iCoor(:,1) + val_iMesh%iCoor_Poin(:,poinR))
                iCoor(:,3) = iBeleCent(:,beleID)
                iCoor(:,4) = 0.5*(iCoor(:,1) + val_iMesh%iCoor_Poin(:,poinL))
                    
                call getAreaNvor_Edge(nDim, ELEM_RECTANGLE, 2*nDim-2, iCoor, iArea, Nvi)
                    
                jCoef(:) = iArea*Nvi(:)
            else
                jCoef(:) = val_iMesh%iNvor_bele(:,beleID)*val_iMesh%iArea_bele(beleID)/nodeNum
            endif
                
            iCoef(:) = jCoef(:)
            
            call vector_length(nDim, iCoef, iArea)
            
            val_iArea(i  ) = iArea
            val_iNvor(i,:) = iCoef(:)/iArea
            val_iCoef(i,:) = iCoef(:)/val_iMesh%iVolu_Poin(poinID)
        enddo
        
    endsubroutine
    !
endmodule
!
    
    