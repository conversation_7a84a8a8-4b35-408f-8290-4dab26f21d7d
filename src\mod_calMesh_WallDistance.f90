!
module mod_calMesh_WallDistance
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    use cudafor
    implicit none
    !
    integer,parameter:: INT64 = 64
    !
    integer:: nWPoin
    integer,allocatable:: iWPoinList(:)  
    integer,allocatable:: iFlag_Poin(:)
    !
contains
    !
    subroutine calGlobalMesh_WallDistance(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_Config
        use mod_Interface_AllocateArray
        
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
        !
        
        !call calGlobalMesh_WallDistance_CPU(val_iMesh)
        
        call calGlobalMesh_WallDistance_GPU(val_iMesh)
        
    endsubroutine
    !
    subroutine calGlobalMesh_WallDistance_CPU(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_Config
        use mod_Interface_AllocateArray
        
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
        !
        
        !!CPU===
        integer:: i,j,k
        real(kind=REALLEN):: iDis,iMinDis
        
        
        call getWPoinList(val_iMesh)
            
        call allocateArray(val_iMesh%iWDis_Poin, val_iMesh%nPoin)
        val_iMesh%iWDis_Poin(:) = 0.0
            
        
        write(*,*) 'calWDis CPU Begin!'
        do i=1,val_iMesh%nPoin
            if(mod(i,10000) == 0) write(*,*) 'poinID =',i, val_iMesh%nPoin
            
            iMinDis = 1.0E+30
        
            do j=1,nWPoin
                iDis = 0.0
                do k=1,nDim
                    iDis = iDis + (val_iMesh%iCoor_Poin(k,iWPoinList(j)) - val_iMesh%iCoor_Poin(k,i))**2
                enddo
                
                iMinDis = min(iMinDis, iDis)
            enddo
            
            val_iMesh%iWDis_Poin(i) = sqrt(iMinDis)
        enddo
        write(*,*) 'calWDis CPU OK!'
        
        
        
    endsubroutine
    !
    subroutine calGlobalMesh_WallDistance_GPU(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_Config
        use mod_Interface_AllocateArray
        
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
        !
        integer:: nBlock,iStat,jBegin,jEnd,deltaJ,j
        integer           ,device,allocatable:: iFlag_Poin_d(:)
        integer           ,device,allocatable:: iWPoinList_d(:)
        real(kind=REALLEN),device,allocatable:: iCoor_Poin_d(:,:)
        real(kind=REALLEN),device,allocatable:: iWDis_Poin_d(:)
        integer           ,device,allocatable:: kWIdx_Poin_d(:)
    
     
            
        call allocateArray(val_iMesh%iWDis_Poin, val_iMesh%nPoin)
        val_iMesh%iWDis_Poin(:) = 0.0
        
        call getWPoinList(val_iMesh)
        if(nWPoin == 0) return
        
        !write(*,*) 'calWDis Begin!'
        !write(*,*) 'nPoin =',val_iMesh%nPoin
        !write(*,*) 'nWPoin =',nWPoin
        
        call allocateGPUArray(iFlag_Poin_d, val_iMesh%nPoin)
        call allocateGPUArray(iWPoinList_d, nWPoin)
        call allocateGPUArray(iCoor_Poin_d, nDim, val_iMesh%nPoin)
        call allocateGPUArray(iWDis_Poin_d, val_iMesh%nPoin)
        call allocateGPUArray(kWIdx_Poin_d, val_iMesh%nPoin)
        iFlag_Poin_d(:  ) = iFlag_Poin(:)
        iWPoinList_d(:  ) = iWPoinList(:)
        iCoor_Poin_d(:,:) = val_iMesh%iCoor_Poin(:,:)
        iWDis_Poin_d(:  ) = 1.0E+20
        
        nBlock = (val_iMesh%nPoin + INT64 - 1)/INT64
        
        !注意：单个Kernel中的计算代码展开长度不能过长
        !可能是由于架构底层设计的原因，使得单个kernel执行过多任务时出错
        deltaJ = 1000
        do j=1,nWPoin,deltaJ
            !write(*,*) 'j=',j,'%',nWPoin
            
            jBegin = j
            jEnd = min(j+deltaJ-1,nWPoin)
            
            istat = cudaDeviceSynchronize()
            call calIWDis_Poin_Global<<<nBlock,INT64>>>(iFlag_Poin_d,       &
                        iWPoinList_d, iCoor_Poin_d, iWDis_Poin_d,           &
                        kWIdx_Poin_d, val_iMesh%nPoin, nWPoin,jBegin,jEnd   )
            istat = cudaDeviceSynchronize()
        enddo
        
        val_iMesh%iWDis_Poin(:) = iWDis_Poin_d(:)
        istat = cudaDeviceSynchronize()
        
        deallocate(iFlag_Poin_d,iWPoinList_d,   &
                   iCoor_Poin_d,iWDis_Poin_d,   &
                   kWIdx_Poin_d                 )
        istat = cudaDeviceSynchronize()
        
        !write(*,*) 'calWDis OK!'
        
    endsubroutine
    !
    subroutine getWPoinList(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_Config
        use mod_Interface_AllocateArray
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        !
        integer:: i,j,iCount,poinID
        
        
        call allocateArray(iFlag_Poin, val_iMesh%nPoin)
        !---------------------------------------------
        iFlag_Poin(:) = 0
        do i=1,val_iMesh%nMark
            if(val_iMesh%iMark(i)%KIND /= MARK_WALL .and. &
               val_iMesh%iMark(i)%KIND /= MARK_EQTW .and. &
               val_iMesh%iMark(i)%KIND /= MARK_EULW .and. &
               val_iMesh%iMark(i)%KIND /= MARK_MOVW ) cycle
            
            do j=1,val_iMesh%iMark(i)%nPoin
                poinID = val_iMesh%iMark(i)%iPoin(j)
                
                iFlag_Poin(poinID) = 1
            enddo
        enddo
        
        !---------------------------------------------
        iCount = 0
        do i=1,val_iMesh%nPoin
            if(iFlag_Poin(i) == 1) then
                iCount = iCount + 1
            endif
        enddo
        nWPoin = iCount
        
        !---------------------------------------------
        call allocateArray(iWPoinList, nWPoin)
        
        iCount = 0
        do i=1,val_iMesh%nPoin
            if(iFlag_Poin(i) == 1) then
                iCount = iCount + 1
                
                iWPoinList(iCount) = i
            endif
        enddo
        
    endsubroutine
    !
    attributes(global) subroutine calIWDis_Poin_Global(val_iFlag_Poin,  &
                val_IWPoinList, val_iCoor_Poin, val_iWDis_Poin,         &
                val_kWIdx_Poin, val_nPoin, val_nWPoin,val_jBegin,val_jEnd)
        implicit none
        integer           ,device:: val_iFlag_Poin(val_nPoin)
        integer           ,device:: val_iWPoinList(val_nWPoin)
        real(kind=REALLEN),device:: val_iCoor_Poin(nDim, val_nPoin)
        real(kind=REALLEN),device:: val_iWDis_Poin(val_nPoin)
        integer           ,device:: val_kWIdx_Poin(val_nPoin)
        integer           ,value :: val_nPoin
        integer           ,value :: val_nWPoin
        integer           ,value :: val_jBegin
        integer           ,value :: val_jEnd
        !
        integer:: i,j,iID,jID,k,kMax,iDim,iMinIdx
        real(kind=REALLEN):: iCoor(nDim),dCoor(nDim),iDCoor,iDis,iMinDis
        !real(kind=REALLEN),shared:: iWCoor(nDim, INT64)
        
        
        i = (blockIdx%x-1)*blockDim%x+threadIdx%x
        iID = threadIdx%x
        
        if(i <= val_nPoin) then
            if(val_iFlag_Poin(i) == 1) then
                val_iWDis_Poin(i) = 0.0
            !    val_kWIdx_Poin(i) = i
                return
            endif
            
            do iDim=1,nDim
                iCoor(iDim) = val_iCoor_Poin(iDim,i)
            enddo
            
            iMinDis = val_iWDis_Poin(i)**2
            !iMinIdx = val_kWIdx_Poin(i)
            
            do j=val_jBegin,val_jEnd
                jID = val_iWPoinList(j)
                
                iDis = 0.0
                do k=1,nDim
                    dCoor(:) = abs(iCoor(k) - val_iCoor_Poin(k,jID))
                    iDis = iDis + dCoor(k)*dCoor(k)
                enddo
                
                if(iDis < iMinDis) then
                    iMinDis = iDis
                !    iMinIdx = j
                endif
            enddo
            
            val_iWDis_Poin(i) = sqrt(max(iMinDis,1.0E-30))
            !val_kWIdx_Poin(i) = val_iWPoinList(iMinIdx)
            
            
            
            !==method2, using shared memory====
            !do j=val_jBegin,val_jEnd,INT64Re
            !    jID = j+iID-1
            !    if(jID <= val_jEnd) then
            !        do iDim=1,nDim
            !            iWCoor(iDim,iID) = val_iCoor_Poin(iDim,val_iWPoinList(jID))
            !        enddo
            !    endif
            !    
            !    call syncthreads()
            !    
            !    kMax = val_nWPoin-j+1
            !    if(INT64 < kMax) kMax = INT64
            !    
            !    do k =1,kMax
            !        iDis = 0.0
            !        do iDim=1,nDim
            !            iDCoor = iCoor(iDim) - iWCoor(iDim,k)
            !            iDis = iDis + iDCoor*iDCoor
            !        enddo
            !    
            !        if(iDis < iMinDis) then
            !            iMinDis = iDis
            !            iMinIdx = jID
            !        endif
            !    enddo
            !enddo
            !
            !val_iWDis_Poin(i) = sqrt(iMinDis+1.0E-30)
            !val_kWIdx_Poin(i) = val_iWPoinList(iMinIdx)
        endif
        
    endsubroutine
    !
endmodule
!
