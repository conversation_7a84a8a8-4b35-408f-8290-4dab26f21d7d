!
!---------------------------------------------
!
!---------------------------------------------
module mod_Operation_GPUArraySub
    use cudafor
    use mod_PreDefine_Precision
    implicit none
    !
contains
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    attributes(global) subroutine GPURealArraySub1D1_Global(val_src,val_dst,val_dim1)
        implicit none
        real(kind=REALLEN),device:: val_src(val_dim1)
        real(kind=REALLEN),device:: val_dst(val_dim1)
        integer,value:: val_dim1
        !
        integer:: i
        
        i = (blockIdx%x-1)*blockDim%x+threadIdx%x
        
        if(i <= val_dim1) then
            val_dst(i) = val_dst(i) - val_src(i)
        endif
        
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    attributes(global) subroutine GPURealArraySub2D1_Global(val_src,val_dst,val_dim1,val_dim2)
        implicit none
        real(kind=REALLEN),device:: val_src(val_dim1,val_dim2)
        real(kind=REALLEN),device:: val_dst(val_dim1,val_dim2)
        integer,value:: val_dim1
        integer,value:: val_dim2
        !
        integer:: i,j
        
        i = (blockIdx%x-1)*blockDim%x+threadIdx%x
        
        if(i <= val_dim1) then
            do j=1,val_dim2
                val_dst(i,j) = val_dst(i,j) - val_src(i,j)
            enddo
        endif
        
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    attributes(global) subroutine GPURealArraySub2D2_Global(val_src,val_dst,val_dim1,val_dim2)
        implicit none
        real(kind=REALLEN),device:: val_src(val_dim1,val_dim2)
        real(kind=REALLEN),device:: val_dst(val_dim1,val_dim2)
        integer,value:: val_dim1
        integer,value:: val_dim2
        !
        integer:: i,j
        
        j = (blockIdx%x-1)*blockDim%x+threadIdx%x
        
        if(j <= val_dim2) then
            do i=1,val_dim1
                val_dst(i,j) = val_dst(i,j) - val_src(i,j)
            enddo
        endif
        
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    attributes(global) subroutine GPURealArraySub3D1_Global(val_src,val_dst,val_dim1,val_dim2,val_dim3)
        implicit none
        real(kind=REALLEN),device:: val_src(val_dim1,val_dim2,val_dim3)
        real(kind=REALLEN),device:: val_dst(val_dim1,val_dim2,val_dim3)
        integer,value:: val_dim1
        integer,value:: val_dim2
        integer,value:: val_dim3
        !
        integer:: i,j,k
        
        i = (blockIdx%x-1)*blockDim%x+threadIdx%x
        
        if(i <= val_dim1) then
            do j=1,val_dim2
                do k=1,val_dim3
                    val_dst(i,j,k) = val_dst(i,j,k) - val_src(i,j,k)
                enddo
            enddo
        endif
        
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    attributes(global) subroutine GPURealArraySub3D2_Global(val_src,val_dst,val_dim1,val_dim2,val_dim3)
        implicit none
        real(kind=REALLEN),device:: val_src(val_dim1,val_dim2,val_dim3)
        real(kind=REALLEN),device:: val_dst(val_dim1,val_dim2,val_dim3)
        integer,value:: val_dim1
        integer,value:: val_dim2
        integer,value:: val_dim3
        !
        integer:: i,j,k
        
        j = (blockIdx%x-1)*blockDim%x+threadIdx%x
        
        if(j <= val_dim2) then
            do i=1,val_dim1
                do k=1,val_dim3
                    val_dst(i,j,k) = val_dst(i,j,k) - val_src(i,j,k)
                enddo
            enddo
        endif
        
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    attributes(global) subroutine GPURealArraySub3D3_Global(val_src,val_dst,val_dim1,val_dim2,val_dim3)
        implicit none
        real(kind=REALLEN),device:: val_src(val_dim1,val_dim2,val_dim3)
        real(kind=REALLEN),device:: val_dst(val_dim1,val_dim2,val_dim3)
        integer,value:: val_dim1
        integer,value:: val_dim2
        integer,value:: val_dim3
        !
        integer:: i,j,k
        
        k = (blockIdx%x-1)*blockDim%x+threadIdx%x
        
        if(k <= val_dim3) then
            do i=1,val_dim1
                do j=1,val_dim2
                    val_dst(i,j,k) = val_dst(i,j,k) - val_src(i,j,k)
                enddo
            enddo
        endif
        
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    attributes(global) subroutine GPURealArraySub4D1_Global(val_src,val_dst,val_dim1,val_dim2,val_dim3,val_dim4)
        implicit none
        real(kind=REALLEN),device:: val_src(val_dim1,val_dim2,val_dim3,val_dim4)
        real(kind=REALLEN),device:: val_dst(val_dim1,val_dim2,val_dim3,val_dim4)
        integer,value:: val_dim1
        integer,value:: val_dim2
        integer,value:: val_dim3
        integer,value:: val_dim4
        !
        integer:: i,j,k,s
        
        i = (blockIdx%x-1)*blockDim%x+threadIdx%x
        
        if(i <= val_dim1) then
            do j=1,val_dim2
                do k=1,val_dim3
                    do s=1,val_dim4
                        val_dst(i,j,k,s) = val_dst(i,j,k,s) - val_src(i,j,k,s)
                    enddo
                enddo
            enddo
        endif
        
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    attributes(global) subroutine GPURealArraySub4D2_Global(val_src,val_dst,val_dim1,val_dim2,val_dim3,val_dim4)
        implicit none
        real(kind=REALLEN),device:: val_src(val_dim1,val_dim2,val_dim3,val_dim4)
        real(kind=REALLEN),device:: val_dst(val_dim1,val_dim2,val_dim3,val_dim4)
        integer,value:: val_dim1
        integer,value:: val_dim2
        integer,value:: val_dim3
        integer,value:: val_dim4
        !
        integer:: i,j,k,s
        
        j = (blockIdx%x-1)*blockDim%x+threadIdx%x
        
        if(j <= val_dim2) then
            do i=1,val_dim1
                do k=1,val_dim3
                    do s=1,val_dim4
                        val_dst(i,j,k,s) = val_dst(i,j,k,s) - val_src(i,j,k,s)
                    enddo
                enddo
            enddo
        endif
        
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    attributes(global) subroutine GPURealArraySub4D3_Global(val_src,val_dst,val_dim1,val_dim2,val_dim3,val_dim4)
        implicit none
        real(kind=REALLEN),device:: val_src(val_dim1,val_dim2,val_dim3,val_dim4)
        real(kind=REALLEN),device:: val_dst(val_dim1,val_dim2,val_dim3,val_dim4)
        integer,value:: val_dim1
        integer,value:: val_dim2
        integer,value:: val_dim3
        integer,value:: val_dim4
        !
        integer:: i,j,k,s
        
        k = (blockIdx%x-1)*blockDim%x+threadIdx%x
        
        if(k <= val_dim3) then
            do i=1,val_dim1
                do j=1,val_dim2
                    do s=1,val_dim4
                        val_dst(i,j,k,s) = val_dst(i,j,k,s) - val_src(i,j,k,s)
                    enddo
                enddo
            enddo
        endif
        
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    attributes(global) subroutine GPURealArraySub4D4_Global(val_src,val_dst,val_dim1,val_dim2,val_dim3,val_dim4)
        implicit none
        real(kind=REALLEN),device:: val_src(val_dim1,val_dim2,val_dim3,val_dim4)
        real(kind=REALLEN),device:: val_dst(val_dim1,val_dim2,val_dim3,val_dim4)
        integer,value:: val_dim1
        integer,value:: val_dim2
        integer,value:: val_dim3
        integer,value:: val_dim4
        !
        integer:: i,j,k,s
        
        s = (blockIdx%x-1)*blockDim%x+threadIdx%x
        
        if(s <= val_dim4) then
            do i=1,val_dim1
                do j=1,val_dim2
                    do k=1,val_dim3
                        val_dst(i,j,k,s) = val_dst(i,j,k,s) - val_src(i,j,k,s)
                    enddo
                enddo
            enddo
        endif
        
    endsubroutine
    !
endmodule
!
