!
subroutine testIO_LocalMesh(val_iMesh,val_iPart,val_iIter)
    use mod_PreDefine_Dimension
    use mod_PreDefine_IOPort
    use mod_PreDefine_Flag
    use mod_TypeDef_Mesh
    use mod_WorkPath
    use mod_Config
    use mod_strOfNumber
    implicit none
    type(typ_Mesh),intent(in):: val_iMesh
    integer,intent(in):: val_iPart
    integer,intent(in):: val_iIter
    !
    integer:: ii,i,j,elemID,poinID,beleID
    character(len=STRLEN):: string_varList
    character(len=STRLEN):: string_ZoneType
    character(len=STRLEN):: string_MarkType
    character(len=STRLEN):: string_VarShare
    character(len=STRLEN):: fileName
    integer:: blkID,bgnID,endID
    
    
    if(nDim == 2) then
        string_varList  = 'VARIABLES="X","Y","iFlag","iHost","iClor","iIner" '
        string_ZoneType = 'FEQUADRILATERAL'
        string_MarkType = 'FELINESEG'
        string_VarShare = '([1-6]=1)'
    elseif(nDim == 3) then
        string_varList  = 'VARIABLES="X","Y","Z","iFlag","iHost","iClor","iIner" '
        string_ZoneType = 'FEBRICK'
        string_MarkType = 'FEQUADRILATERAL'
        string_VarShare = '([1-7]=1)'
    else
        return
    endif
    
    fileName = strTwo_Number(val_iPart)//'_'//strSix_Number(val_iIter)//'.plt'
    
    !=================================
    !=================================
    open(ioPort_FULL,file=trim(testFullPath)//'/LocalMesh'//trim(fileName),asynchronous='yes',status='unknown')
    
    do i = 1,val_iMesh%nDoma
        if(val_iMesh%iDoma(i)%nValdElem <= 0) cycle
        
        write(ioPort_FULL,*) trim(adjustl(string_varList))
        if(i == 1) then
            write(ioPort_FULL,*)'ZONE DATAPACKING = POINT, NODES =', val_iMesh%nPoin,   &
                                    ', ELEMENTS =', val_iMesh%iDoma(i)%nValdElem,       &
                                    ', ZONETYPE = ',trim(adjustl(string_ZoneType))
            
            do j=1,val_iMesh%nPoin
                if(nDim == 2) then
                write(ioPort_FULL,"(2E14.5,4I5)") val_iMesh%iCoor_Poin(j,:),val_iMesh%iFlag_Poin(j), &
                                                  val_iMesh%iHost_Poin(j),val_iMesh%iClor_Poin(j),   &
                                                  val_iMesh%iIner_Poin(j)
                elseif(nDim == 3) then
                write(ioPort_FULL,"(3E14.5,4I5)") val_iMesh%iCoor_Poin(j,:),val_iMesh%iFlag_Poin(j), &
                                                  val_iMesh%iHost_Poin(j),val_iMesh%iClor_Poin(j),   &
                                                  val_iMesh%iIner_Poin(j)
                endif
            enddo
            
        else
            write(ioPort_FULL,*)'ZONE DATAPACKING = POINT, NODES =', val_iMesh%nPoin,   &
                                    ', ELEMENTS =', val_iMesh%iDoma(i)%nValdElem,       &
                                    ', ZONETYPE = ',trim(adjustl(string_ZoneType)),     &
                                    ', VARSHARELIST=',trim(adjustl(string_VarShare))
        endif
        
        do j=1,val_iMesh%iDoma(i)%nElem
            elemID = val_iMesh%iDoma(i)%iElem(j)
            
            if(val_iMesh%iFlag_Elem(elemID) /= FLAG_VALID) cycle
            
            write(ioPort_FULL,*) val_iMesh%iElem(:,elemID)
        enddo
    enddo
    
    close(ioPort_FULL)
    
    
    !=================================
    !=================================
    blkID = val_iMesh%nPoin/32+1
    bgnID = 16*(blkID-1)+1
    endID = 16*blkID
    
    open(ioPort_FULL,file=trim(testFullPath)//'/LocalCloud_'//strTen_Number(blkID)//'.dat',asynchronous='yes',status='unknown')
        do i=bgnID,endID
            write(ioPort_FULL,"(15I8)") i, val_iMesh%iSate_Poin(val_iMesh%kSate_Poin(i)+1:val_iMesh%kSate_Poin(i+1), 1)
        enddo
    
    close(ioPort_FULL)
    
    
    !=================================
    !=================================
    open(ioPort_FULL,file=trim(testFullPath)//'/LocalBC_'//trim(fileName),asynchronous='yes',status='unknown')
    
    ii = 0
    do i = 1,val_iMesh%nMark
        if(val_iMesh%iMark(i)%nElem <= 0) cycle
        if(val_iMesh%iMarkProp(i,2) == MARK_NONE) cycle
        if(val_iMesh%iMarkProp(i,2) == MARK_ATTA) cycle
                
        ii = ii + 1
        write(ioPort_FULL,*) trim(adjustl(string_varList))
        if(i == 1) then
            write(ioPort_FULL,*)'ZONE DATAPACKING = POINT, NODES =', val_iMesh%nBCPoin,     &
                                    ', ELEMENTS =', val_iMesh%iMark(i)%nElem,               &
                                    ', ZONETYPE = ',trim(adjustl(string_MarkType))
            
        else
            write(ioPort_FULL,*)'ZONE DATAPACKING = POINT, NODES =', val_iMesh%nBCPoin,     &
                                    ', ELEMENTS =', val_iMesh%iMark(i)%nElem,               &
                                    ', ZONETYPE = ',trim(adjustl(string_MarkType)),         &
                                    ', VARSHARELIST=',trim(adjustl(string_VarShare))
        endif
        
        if(ii == 1) then
            do j=1,val_iMesh%nBCPoin
                poinID = val_iMesh%iMapBToG_Poin(j)
                if(nDim == 2) then
                write(ioPort_FULL,"(2E14.5,4I5)") val_iMesh%iCoor_Poin(poinID,:),val_iMesh%iFlag_Poin(poinID), &
                                                  val_iMesh%iHost_Poin(poinID),val_iMesh%iClor_Poin(poinID),   &
                                                  val_iMesh%iIner_Poin(poinID)
                elseif(nDim == 3) then
                write(ioPort_FULL,"(3E14.5,4I5)") val_iMesh%iCoor_Poin(poinID,:),val_iMesh%iFlag_Poin(poinID), &
                                                  val_iMesh%iHost_Poin(poinID),val_iMesh%iClor_Poin(poinID),   &
                                                  val_iMesh%iIner_Poin(poinID)
                endif
            enddo
            
        endif
        
        do j=1,val_iMesh%iMark(i)%nElem
            beleID = val_iMesh%iMark(i)%iElem(j)
            write(ioPort_FULL,*) val_iMesh%iMapGToB_Poin(val_iMesh%iBele(1:2*nDim-2,beleID))
        enddo
        
    enddo
    
    close(ioPort_FULL)
    
endsubroutine
!