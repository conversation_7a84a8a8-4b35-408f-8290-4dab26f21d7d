!
!---------------------------------------------
!
!---------------------------------------------
module mod_calMesh_MultiClor
    use mod_PreDefine_IOPort
    implicit none
    !
contains
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine iniMesh_MultiClor(val_iMesh)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
    
        !ÎÞÐè²Ù×÷
        allocate(val_iMesh%iClor_Poin(val_iMesh%nPoin))
        val_iMesh%iClor_Poin(:) = 0
    
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine calMesh_MultiClor(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_Config
        use mod_PreDefine_IOPort
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
    
    
        if(.not.val_iMesh%isNeedMC) then
            val_iMesh%iClor_Poin(:) = 0
            return
        endif
    
        call calIClor_MC(val_iMesh,val_iMesh%iClor_Poin,val_iMesh%nClor)
    
        !call saveIClor_Poin_2D(val_iMesh,"_0")
    
        call updIClor_MC(val_iMesh,val_iMesh%iClor_Poin,val_iMesh%nClor)
    
        write(ioPort_Out,"(7X,A,I2,A)") ': <Total Color Levels =',val_iMesh%nClor,'>'
    
        !call saveIClor_Poin_2D(val_iMesh)
    
        return
    
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine calIClor_MC(val_iMesh,val_iClor_Poin,val_nClor)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        integer,intent(inout):: val_iClor_Poin(val_iMesh%nPoin)
        integer,intent(inout):: val_nClor
        !
        integer,parameter:: nMax = 100
        integer:: i,j,k,s
        integer:: poinID,sateID,sateIDD,iID,domaID
        integer:: IDBegin,IDEnd,nLevel,iLevel
        integer:: iPoinFlag(val_iMesh%nPoin)
        integer:: iPoinList(val_iMesh%nPoin)
        integer:: iPoinLevl(val_iMesh%nPoin)
        integer:: iPoinClor(val_iMesh%nPoin)
        integer:: iClorred(nMax),iCurtClor,nClor
        integer:: nPoinUpd
        !
        iPoinFlag(:) = 0
        iPoinList(:) = 0
        iPoinLevl(:) = 0
        iPoinClor(:) = 0
        
        do i=1,val_iMesh%nRegn
            domaID = val_iMesh%iRegn(i)%domaList(1)
            poinID = val_iMesh%iDoma(domaID)%iPoin(1)
            
            iPoinList(i) = poinID
            iPoinFlag(poinID) = 1
            iPoinLevl(poinID) = 1
            iPoinClor(poinID) = 1
        enddo

        IDBegin = 1
        IDEnd   = val_iMesh%nRegn
    
        iLevel = 1
        do
            if(IDBegin > val_iMesh%nPoin) exit

            nPoinUpd = 0

            iID = iDEnd
        
            do i=IDBegin,IDEnd
                poinID = iPoinList(i)
            
                do j=val_iMesh%kSate_Poin(poinID)+1,val_iMesh%kSate_Poin(poinID+1)
                    sateID = val_iMesh%iSate_Poin(j,1)
                
                    if(iPoinFlag(sateID) == 0) then
                        iID = iID + 1
                    
                        iPoinList(   iID) = sateID
                        iPoinFlag(sateID) = iLevel + 1
                        iPoinLevl(sateID) = iLevel + 1
                    
                        iClorred(:) = 0
                        do k=val_iMesh%kSate_Poin(sateID)+1,val_iMesh%kSate_Poin(sateID+1)
                            sateIDD = val_iMesh%iSate_Poin(k,1)
                        
                            iCurtClor = iPoinClor(sateIDD)
                            if(iCurtClor > 0) then
                                iClorred(iCurtClor) = 1
                            endif
                        enddo
                    
                        iCurtClor = 0
                        do k=1,nMax
                            if(iClorred(k) == 0) then
                                iCurtClor = k
                                exit
                            endif
                        enddo
                    
                        iPoinClor(sateID) = iCurtClor
                    
                        if(iCurtClor == 0) then
                            write(ioPort_Out,*) 'Error: iCurtClor == 0'
                        endif
                        nPoinUpd = nPoinUpd + 1
                    endif
                enddo
            enddo

            if(nPoinUpd == 0) then
                iLevel = iLevel + 1
                IDBegin = IDEnd + 1
                IDEnd = IDBegin

                do i=1,val_iMesh%nPoin
                    if(iPoinClor(i) == 0) then
                        iPoinList(IDBegin) = i
                        iPoinClor(i) = 1
                        exit
                    end if
                end do
            else
                iLevel  = iLevel + 1
                IDBegin = IDEnd  + 1
                IDEnd   = iID
            end if
        enddo
    
        nClor = 0
        do i=1,val_iMesh%nPoin
            nClor = max(nClor,iPoinClor(i))
        enddo
    
        val_iClor_Poin(:) = iPoinClor(:)
        val_nClor = nClor
        !
    endsubroutine   
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine updIClor_MC(val_iMesh,val_iClor_Poin,val_nClor)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        integer,intent(inout):: val_iClor_Poin(val_iMesh%nPoin)
        integer,intent(inout):: val_nClor
        !
        integer:: iC,i,j,k
        integer:: sateID,iClor,jClor,mClor,temp
        integer:: nPoin_Clor(val_nClor)
        integer:: isDel_Clor(val_nClor)
        integer:: sPoin_Clor(val_nClor)
        real(kind=REALLEN):: MCLimtCoef = 0.0
    
        
        nPoin_Clor(:) = 0
        isDel_Clor(:) = 0
        do i=1,val_iMesh%nPoin
            iClor = val_iClor_Poin(i)
            nPoin_Clor(iClor) = nPoin_Clor(iClor) + 1
        enddo
        
        do iC=val_nClor,1,-1
            if(nPoin_Clor(iC) > MCLimtCoef*val_iMesh%nPoin/val_nClor) then
                isDel_Clor(iC) = 0
                exit
            endif
            isDel_Clor(iC) = 1
            
            do i=1,val_iMesh%nPoin
                if(val_iClor_Poin(i) == iC) then
                    sPoin_Clor(:) = 0
                    do j=val_iMesh%kSate_Poin(i)+1,val_iMesh%kSate_Poin(i+1)
                        sateID = val_iMesh%iSate_Poin(j,1)
                        
                        jClor = val_iClor_Poin(sateID)
                        
                        sPoin_Clor(jClor) = sPoin_Clor(jClor) + 1
                    enddo
                    
                    mClor = 1
                    temp  = sPoin_Clor(1)
                    do j=2,iC-1
                        if(temp > sPoin_Clor(j)) then
                            temp  = sPoin_Clor(j)
                            mClor = j
                        endif
                    enddo
                    
                    val_iClor_Poin(i) = mClor
                    nPoin_Clor(iC   ) = nPoin_Clor(iC   ) - 1
                    nPoin_Clor(mClor) = nPoin_Clor(mClor) - 1
                endif
            enddo
            
        enddo
        
        temp = 0
        do i=1,val_iMesh%nPoin
            temp = max(temp,val_iClor_Poin(i))
        enddo
        val_nClor = temp
        
    endsubroutine
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine saveIClor_Poin_2D(val_iMesh,val_iFlag)
        use mod_TypeDef_Mesh
        use mod_PreDefine_IOPort
        use mod_WorkPath
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
        character(len=*),optional:: val_iFlag
    
        integer:: i,j,nk
        integer:: nPoin_Clor(val_iMesh%nClor)
    
        
    
        if(.not.present(val_iFlag)) then
            open(ioPort_FULL,file=trim(geomFullPath)//'/iClor_Poin.plt')
        else
            open(ioPort_FULL,file=trim(geomFullPath)//'/iClor_Poin'//trim(val_iFlag)//'.plt')
        endif
        
            if(nDim == 2) then
                write(ioPort_FULL,*) 'VARIABLES="x","y","iColor"'
            elseif(nDim == 3) then
                write(ioPort_FULL,*) 'VARIABLES="x","y","z","iColor"'
            endif
        
            do i=1,val_iMesh%nDoma
                
                if(i == 1) then
                    if(nDim == 2) then
                        write(ioPort_FULL,*) 'ZONE DATAPACKING = BLOCK', &
                                                ', NODES=',val_iMesh%nPoin, &
                                                ', ELEMENTS =',val_iMesh%iDoma(i)%nElem, &
                                                ', ZONETYPE = FEQUADRILATERAL'
                    elseif(nDim == 3) then
                        write(ioPort_FULL,*) 'ZONE DATAPACKING = BLOCK', &
                                                ', NODES=',val_iMesh%nPoin, &
                                                ', ELEMENTS =',val_iMesh%iDoma(i)%nElem, &
                                                ', ZONETYPE = FEBRICK'
                    endif
                else
                    if(nDim == 2) then
                        write(ioPort_FULL,*) 'ZONE DATAPACKING = BLOCK', &
                                                ', NODES=',val_iMesh%nPoin, &
                                                ', ELEMENTS =',val_iMesh%iDoma(i)%nElem, &
                                                ', ZONETYPE = FEQUADRILATERAL, VARSHARELIST =([1-3]=1)'
                    elseif(nDim == 3) then
                        write(ioPort_FULL,*) 'ZONE DATAPACKING = BLOCK', &
                                                ', NODES=',val_iMesh%nPoin, &
                                                ', ELEMENTS =',val_iMesh%iDoma(i)%nElem, &
                                                ', ZONETYPE = FEBRICK, VARSHARELIST =([1-4]=1)'
                    endif
                endif
            
                if(i == 1) then
                    do j=1,nDim
                        write(ioPort_FULL,*) val_iMesh%iCoor_Poin(j,1:val_iMesh%nPoin)
                    enddo
                    write(ioPort_FULL,*) val_iMesh%iClor_Poin(1:val_iMesh%nPoin)
                
                endif
            
                do j=1,val_iMesh%iDoma(i)%nElem
                    nk = val_iMesh%iDoma(i)%iElem(j)
                    write(ioPort_FULL,*) val_iMesh%iElem(:,nk)
                enddo
            
            enddo
    
        close(ioPort_FULL)
        
        
        
        nPoin_Clor(:) = 0
        do i=1,val_iMesh%nPoin
            nPoin_Clor(val_iMesh%iClor_Poin(i)) = nPoin_Clor(val_iMesh%iClor_Poin(i)) + 1
        enddo
    
        if(.not.present(val_iFlag)) then
            open(ioPort_FULL,file=trim(geomFullPath)//'/iPoin_Clor.dat')
        else
            open(ioPort_FULL,file=trim(geomFullPath)//'/iPoin_Clor'//trim(val_iFlag)//'.dat')
        endif
        
        write(ioPort_FULL,"(A,I5,X,A)") 'nClor = ',val_iMesh%nClor,'they are:'
        write(ioPort_FULL,*) nPoin_Clor
        
        close(ioPort_FULL)
        
    endsubroutine
    !
endmodule
!
    
    
    