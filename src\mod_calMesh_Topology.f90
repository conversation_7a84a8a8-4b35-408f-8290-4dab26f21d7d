! 
module mod_calMesh_Topology
    use mod_PreDefine_Dimension
    use mod_PreDefine_IOPort
    use mod_ElemProp
    use mod_calMeshTopo_Func
#ifdef GFLOW_ENABLE_OMP
    use omp_lib
#endif
    implicit none
    !
contains
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine calGlobalMesh_Topology(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_Config
        use mod_updMeshlessClouds
        use mod_Statistics
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh


        !-------------------------------------------
        !网格块和网格区
        !-------------------------------------------

        call beginTimer("calGlobalMesh_Topology")

        call beginTimer("calIDomaInMesh")
        call calIDomaInMesh(val_iMesh)

        call beginNextTimer("calIMarkInMesh")
        call calIMarkInMesh(val_iMesh)

        call beginNextTimer("calIRegnInMesh")
        call calIRegnInMesh(val_iMesh)

        call beginNextTimer("calIRegin_Poin")
        call calIRegin_Poin(val_iMesh)

        !-------------------------------------------
        !+
        !-------------------------------------------
        allocate(val_iMesh%iElemKind(val_iMesh%nElem))
        allocate(val_iMesh%iElemLink(val_iMesh%nElem,2*nDim,3))
        call beginNextTimer("calIElemKind")
        call calIElemKind(val_iMesh)

        call beginNextTimer("trtRHandRule")
        call trtRHandRule(val_iMesh)

        call beginNextTimer("calIElemLink")
        call calIElemLink(val_iMesh)

        !右手定则重新修正,同时更新iElemLink
        call beginNextTimer("reRHandRule")
        call reRHandRule(val_iMesh)

        call beginNextTimer("chkIElemLink")
        call chkIElemLink(val_iMesh)

        !-------------------------------------------
        !Bele信息
        !-------------------------------------------
        allocate(val_iMesh%iBeleLink(val_iMesh%nBele,2))
        allocate(val_iMesh%iBeleProp(val_iMesh%nBele,2))
        call beginNextTimer("calIBeleLink")
        call calIBeleLink(val_iMesh)

        call beginNextTimer("calIBeleProp")
        call calIBeleProp(val_iMesh)

        
        !-------------------------------------------
        !Belv信息
        !-------------------------------------------
        !write(*,*) 'calIBelv'
        call beginNextTimer("calIBelv")
        call calIBelv(val_iMesh)


        !-------------------------------------------
        !Edge信息
        !-------------------------------------------
        !write(*,*) 'calIEdge'
        call beginNextTimer("calIEdge")
        call calIEdge(val_iMesh)


        !-------------------------------------------
        !Line信息
        !-------------------------------------------
        !write(*,*) 'calILine'
        call beginNextTimer("calILine")
        call calILine(val_iMesh)

        !-------------------------------------------
        !Poin信息
        !-------------------------------------------
        !write(*,*) 'calIPoin'
        call beginNextTimer("calIPoin")
        call calIPoin(val_iMesh)
        call endTimer("calIPoin")

        if((NUMERICAL_METHOD == _NUMERICAL_MESHLESS).and. &
           (IS_MESHLESSCLOUD_RECONS == _YES) )then
            write(*,*) 'updMeshlessClouds'
            call beginTimer("updMeshlessClouds")
            call updMeshlessClouds(val_iMesh)
            call endTimer("updMeshlessClouds")
        endif

        call endTimer("calGlobalMesh_Topology")

        !call printTimeStatistics
        call clearTimeStatistics

    endsubroutine
    
    
    !---------------------------------------------
    !Part1
    !---------------------------------------------
    subroutine calIDomaInMesh(val_iMesh)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
    
        integer:: i,j,k,nk
        integer:: iCount,jCount !计数器
        integer,allocatable:: poinFlag(:,:)


        allocate(poinFlag(val_iMesh%nPoin,val_iMesh%nDoma))
        poinFlag(:,:) = 0

        iCount = 0
        do i=1,val_iMesh%nDoma
        
            if(val_iMesh%iDoma(i)%nElem == 0) cycle
        
            allocate(val_iMesh%iDoma(i)%iElem(val_iMesh%iDoma(i)%nElem))
            val_iMesh%iDoma(i)%iElem(:) = 0

            do j=1,val_iMesh%iDoma(i)%nElem
                val_iMesh%iDoma(i)%iElem(j) = iCount+j !$$$$$
                poinFlag(val_iMesh%iElem(:,iCount+j),i) = 1
            enddo
        
            jCount = 0
            do j=1,val_iMesh%nPoin
                if(poinFlag(j,i) == 1) then
                    jCount = jCount+1
                endif
            enddo
        
            val_iMesh%iDoma(i)%nPoin = jCount !$$$$$
        
            if(val_iMesh%iDoma(i)%nPoin > 0) then
                allocate(val_iMesh%iDoma(i)%iPoin(val_iMesh%iDoma(i)%nPoin))
                val_iMesh%iDoma(i)%iPoin(:) = 0
        
                jCount = 0
                do j=1,val_iMesh%nPoin
                    if(poinFlag(j,i) == 1) then
                        jCount = jCount+1
                        val_iMesh%iDoma(i)%iPoin(jCount) = j !$$$$$
                    endif
                enddo
            
            endif
        
            iCount = iCount+val_iMesh%iDoma(i)%nElem
        
        enddo

        deallocate(poinFlag)
    
    endsubroutine
    !
    !---------------------------------------------
    subroutine calIMarkInMesh(val_iMesh)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
        !
        integer:: i,j,k,nk
        integer:: iCount,jCount
        integer,dimension(val_iMesh%nPoin):: poinFlag
    
        iCount = 0
        do i=1,val_iMesh%nMark
            if(val_iMesh%iMark(i)%nElem == 0) cycle
        
            allocate(val_iMesh%iMark(i)%iElem(val_iMesh%iMark(i)%nElem))
            val_iMesh%iMark(i)%iElem(:) = 0
        
            poinFlag(:) = 0
            do j=1,val_iMesh%iMark(i)%nElem
                val_iMesh%iMark(i)%iElem(j) = iCount+j !$$$$$
                poinFlag(val_iMesh%iBele(:,iCount+j)) = 1
            enddo
        
            jCount = 0
            do j=1,val_iMesh%nPoin
                if(poinFlag(j) == 1) then
                    jCount = jCount+1
                endif
            enddo
        
            val_iMesh%iMark(i)%nPoin = jCount !$$$$$
        
            if(val_iMesh%iMark(i)%nPoin > 0) then
                allocate(val_iMesh%iMark(i)%iPoin(val_iMesh%iMark(i)%nPoin))
                val_iMesh%iMark(i)%iPoin(:) = 0
        
                jCount = 0
                do j=1,val_iMesh%nPoin
                    if(poinFlag(j) == 1) then
                        jCount = jCount+1
                        val_iMesh%iMark(i)%iPoin(jCount) = j !$$$$$
                    endif
                enddo
        
            endif
        
            iCount = iCount+val_iMesh%iMark(i)%nElem
        
        enddo
    
    
    endsubroutine
    !
    !---------------------------------------------
    subroutine calIRegnInMesh(val_iMesh)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
    
        integer:: i,j
        integer:: iCount,jCount
    
        iCount = 0
        jCount = 0
    
        do i=1,val_iMesh%nRegn
            if(val_iMesh%iRegn(i)%nDoma > 0) then
                allocate(val_iMesh%iRegn(i)%domaList(val_iMesh%iRegn(i)%nDoma))
            
                do j=1,val_iMesh%iRegn(i)%nDoma
                    val_iMesh%iRegn(i)%domaList(j) = iCount+j
                enddo
            
                iCount = iCount+val_iMesh%iRegn(i)%nDoma
            endif
        
            if(val_iMesh%iRegn(i)%nMark > 0) then
                allocate(val_iMesh%iRegn(i)%markList(val_iMesh%iRegn(i)%nMark))
            
                do j=1,val_iMesh%iRegn(i)%nMark
                    val_iMesh%iRegn(i)%markList(j) = jCount+j
                enddo
            
                jCount = jCount+val_iMesh%iRegn(i)%nMark
            endif
        
        enddo
    
    endsubroutine
    !
    !---------------------------------------------
    subroutine calIRegin_Poin(val_iMesh)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
    
        integer:: i,j,k
        integer:: nd,np
    
        allocate(val_iMesh%iRegn_Poin(val_iMesh%nPoin))
        val_iMesh%iRegn_Poin(:) = 0
    
        do i=1,val_iMesh%nRegn
            do j=1,val_iMesh%iRegn(i)%nDoma
                nd = val_iMesh%iRegn(i)%domaList(j)
            
                do k = 1,val_iMesh%iDoma(nd)%nPoin
                    np = val_iMesh%iDoma(nd)%iPoin(k)
                
                    val_iMesh%iRegn_Poin(np) = i
                enddo
            enddo
        enddo
    
    endsubroutine
    
    
    !---------------------------------------------
    !Part2
    !---------------------------------------------
    subroutine calIElemKind(val_iMesh)
        use mod_TypeDef_Mesh
        use omp_lib
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
    
        integer:: i,ick
        integer:: icci(nDim*4-4)
    
    
        val_iMesh%iElemKind(:) = 0
#ifdef GFLOW_ENABLE_OMP
        !$omp parallel do private(icci,ick)
#endif
        do i=1,val_iMesh%nElem
            icci(:) = val_iMesh%iElem(:,i)
        
            call getElemKind(nDim*4-4 , icci , ick)
        
            val_iMesh%iElemKind(i) = ick
        enddo
#ifdef GFLOW_ENABLE_OMP
        !$omp end parallel do
#endif
    
    endsubroutine
    !
    !---------------------------------------------
    subroutine trtRHandRule(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_Interface_Unitization
        use mod_calMeshGeom_Func
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
    
        integer:: i,j,itemp
        integer:: icci(4*nDim-4),ick,maxID
        real(kind=8):: rccr(nDim,4*nDim-4),iCoor(nDim),iNv(nDim),iValue

        if(nDim==3) return
        ! not need to change the order of nodes in elem
        ! the following codes are not strict

#ifdef GFLOW_ENABLE_OMP
        !!!$omp parallel do private(j,itemp,icci,ick,maxID,rccr,iCoor,iNv,ivalue)
#endif
        do i=1,val_iMesh%nElem
            icci(:) = val_iMesh%iElem(:,i)
        
            call getElemKind(4*nDim-4, icci,ick)
        
            do j=1,4*nDim-4
                rccr(:,j) = val_iMesh%iCoor_Poin(:,icci(j))-val_iMesh%iCoor_Poin(:,icci(1))
            enddo
        
            call getMaxAreaEdgeID(nDim, ick, rccr, maxID, iNv)
        
            if(maxID == 0) then
                write(ioPort_Out,*) 'error','E2'
                write(ioPort_Out,*) 'icci:',icci
                write(ioPort_Out,*) 'rccr:'
                do j=1,4*nDim-4
                write(ioPort_Out,*) rccr(:,j)
                enddo
                stop
            endif
        
            iCoor(:) = 0.0
            do j=1,ElemProp_NodeNum(ick)
                iCoor(:) = iCoor(:) + rccr(:,ElemProp_NodeList(j,ick))
            enddo
            iCoor(:) = iCoor(:)/ElemProp_NodeNum(ick)
        
            iCoor(:) = iCoor(:) - rccr(:,maxID)
            call unitization(nDim,iCoor)
        
            iValue = 0.0
            do j=1,nDim
                iValue = iValue + iCoor(j)*iNv(j)
            enddo
        
            if(iValue > 0.0) then
                if(nDim == 2) then
                    itemp   = icci(1)
                    icci(1) = icci(2)
                    icci(2) = itemp
                    
                    itemp   = icci(3)
                    icci(3) = icci(4)
                    icci(4) = itemp
                elseif(nDim == 3) then
                    itemp   = icci(1)
                    icci(1) = icci(2)
                    icci(2) = itemp
                    
                    itemp   = icci(3)
                    icci(3) = icci(4)
                    icci(4) = itemp
            
                    itemp   = icci(5)
                    icci(5) = icci(6)
                    icci(6) = itemp
                    
                    itemp   = icci(7)
                    icci(7) = icci(8)
                    icci(8) = itemp
                endif
            
            endif
        
            val_iMesh%iElem(:,i) = icci(:)
        enddo
#ifdef GFLOW_ENABLE_OMP
        !!!$omp end parallel do
#endif
        return
    
    contains
        !---------------------------------------------
        !
        !---------------------------------------------
        subroutine getMaxAreaEdgeID(val_nDim,val_ick,val_iccc,val_maxID,val_NV)
            implicit none
            integer,intent(in):: val_nDim
            integer,intent(in):: val_ick
            real(kind=8),intent(in):: val_iccc(val_nDim,4*val_nDim-4)
            integer,intent(out):: val_maxID
            real(kind=8),intent(out):: val_NV(val_nDim)
            !
            integer:: i,j,k
            integer:: maxID
            real(kind=8):: maxArea,maxNv(val_nDim)
            integer:: iek,ieei(2*val_nDim-2)
            real(kind=8):: ieec(val_nDim,2*val_nDim-2),iArea,iNv(val_nDim)
        
            maxArea = 0.0
            maxID = 0
            maxNv(:) = 0.0
            do i=1,ElemProp_EdgeNum(val_ick)
                iek = ElemProp_EdgeKind(i,val_ick)
            
                do j=1,2*val_nDim-2
                    ieei(j) = ElemProp_EdgeList(j,i,val_ick)
                    ieec(:,j) = val_iccc(:,ieei(j))
                enddo
            
                call getVolumeOfElem(val_nDim,iek,2*nDim-2,ieec,iArea)
            
                if(iArea > maxArea) then
                    call getNormalVector(val_nDim,iek,2*nDim-2,ieec,iNv)
                
                    maxArea = iArea
                    maxID = ieei(1)
                    maxNv(:) = iNv(:)
                endif
            enddo
        
            val_maxID = maxID
            val_NV(:) = maxNV(:)
        
        endsubroutine
        !
        !
    endsubroutine
    !
    !-------------------------------------------------
    subroutine reRHandRule(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_calMeshGeom_Func
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
        !
        integer:: j,k,elemID
        integer:: iLevel,nLevel
        integer,allocatable:: kElem_Level(:)
        integer,allocatable:: iElem_Level(:)
        integer,allocatable:: iLevel_Elem(:)
        !
        integer:: er,kr
        integer:: icci(4*nDim-4),ieei(2*nDim-2),ick
        integer:: jccj(4*nDim-4),jeej(2*nDim-2),jck
    
    
        allocate(kElem_Level(val_iMesh%nElem+1))
        allocate(iElem_Level(val_iMesh%nElem))
        allocate(iLevel_Elem(val_iMesh%nElem))
        kElem_Level(:) = 0
        iElem_Level(:) = 0
        iLevel_Elem(:) = 0
    
        call getIElem_Level(val_iMesh,nLevel,kElem_Level,iElem_Level,iLevel_Elem)
    
        do iLevel = 1,nLevel-1
            !$omp parallel do private(j,k,elemID,er,kr,icci,ieei,ick,jccj,jeej,jck)
            do j=kElem_Level(iLevel)+1,kELem_Level(iLevel+1)
                elemID = iElem_Level(j)
            
                ick = val_imesh%iElemKind(elemID)
                icci(:) = val_iMesh%iELem(:,elemID)
            
                do k=1,ElemProp_EdgeNum(ick)
                    er = val_iMesh%iElemLink(elemID,k,1)
                    kr = val_imesh%iElemLink(elemID,k,2)
                
                    if(er <= 0) cycle
                    if(iLevel_Elem(er) <= iLevel) cycle
                
                    jck = val_iMesh%iElemKind(er)
                    jccj(:) = val_iMesh%iElem(:,er)
                
                    ieei(:) = icci(ElemProp_EdgeList(1:2*nDim-2,k ,ick))
                    jeej(:) = jccj(ElemProp_EdgeList(1:2*nDim-2,kr,jck))
                
                    if(getEqual_Edge(2*nDim-2,ieei,jeej) == 1) then
                        call reOneElem(val_iMesh,er)
                    endif
                enddo
            enddo
            !$omp end parallel do
        enddo
    
        deallocate(kElem_Level,iElem_Level,iLevel_Elem)
        !
    contains
        !
        subroutine getIElem_Level(val_iMesh,val_nLevel,val_kElem_Level,val_iElem_Level,val_iLevel_Elem)
            use mod_TypeDef_Mesh
            implicit none
            type(typ_GlobalMesh),intent(in):: val_iMesh
            integer,intent(inout):: val_nLevel
            integer,intent(inout):: val_kElem_Level(val_iMesh%nElem+1)
            integer,intent(inout):: val_iElem_Level(val_iMesh%nElem)
            integer,intent(inout):: val_iLevel_Elem(val_iMesh%nElem)
            !
            integer:: i,j,k,s,domaID,elemID
            integer:: iLevel,nLevel
            !
            integer:: maxID,iID
            real(kind=8):: maxVolu,iVolu
            real(kind=8):: iccc(nDim,4*nDim-4)
            integer:: el,kl,er,kr,ick,jck
        
        
        
            !init
            val_nLevel         = 0
            val_kElem_Level(:) = 0
            val_iElem_Level(:) = 0
            val_iLevel_Elem(:) = 0
        
            !root elem
            val_kElem_Level(1) = 0
            val_kElem_Level(2) = val_iMesh%nRegn
            do i=1,val_iMesh%nRegn
                maxID = 0
                maxVolu = 0.0
        
                do j=1,val_iMesh%iRegn(i)%nDoma
                    domaID = val_iMesh%iRegn(i)%domaList(j)
                    do k=1,val_iMesh%iDoma(domaID)%nElem
                        elemID = val_iMesh%iDoma(domaID)%iElem(k)
                
                        ick = val_iMesh%iElemKind(elemID)
                        do s=1,4*nDim-4
                            iccc(:,s) = val_iMesh%iCoor_Poin(:,val_iMesh%iElem(s,elemID))
                        enddo
                
                        call getVolumeOfElem(nDim, ick, 4*nDim-4, iccc, iVolu)
                
                        if(iVolu > maxVolu) then
                            maxID = elemID
                            maxVolu = iVolu
                        endif
                    enddo
                enddo
        
                val_iElem_Level(i) = maxID
                val_iLevel_Elem(maxID) = 1
            enddo
    
        
            !links
            do iLevel = 1,val_iMesh%nElem
                val_kElem_Level(iLevel+1) = val_kElem_Level(iLevel) + val_kElem_Level(iLevel+1)
        
                if(val_kElem_Level(iLevel+1) > val_iMesh%nElem) then
                    write(ioPort_Out,*) 'error','E1'
                    stop
                endif
        
                if(val_kElem_Level(iLevel+1) == val_iMesh%nElem) then
                    val_nLevel = iLevel
                    exit
                endif
        
                do i=val_kElem_Level(iLevel)+1, val_kElem_Level(iLevel+1)
                    elemID = val_iElem_Level(i)
            
                    ick = val_iMesh%iElemKind(elemID)
                    do j=1,ElemProp_EdgeNum(ick)
                        el = i
                        kl = j
                        er = val_imesh%iElemLink(elemID,j,1)
                        kr = val_imesh%iElemLink(elemID,j,2)
                    
                        if(er > 0) then
                            if(val_iLevel_Elem(er) == 0) then
                                val_kElem_Level(iLevel+2) = val_kElem_Level(iLevel+2) + 1
                            
                                iID = val_kElem_Level(iLevel+1) + val_kElem_Level(iLevel+2)
                            
                                val_iELem_Level(iID) = er
                                val_iLevel_Elem(er ) = iLevel + 1
                            endif
                        endif
            
                    enddo
                enddo
            enddo
        
        endsubroutine
        !
        subroutine reOneElem(val_iMesh,val_ID)
            use mod_TypeDef_Mesh
            implicit none
            type(typ_GlobalMesh),intent(inout):: val_iMesh
            integer,intent(in):: val_ID
            !
            integer:: i,j,k,ick,jck
            integer:: el,er,kl,kr
            integer:: icci(4*nDim-4),itemp
            integer:: ieei(2*nDim-2),jeej(2*nDim-2)
            integer:: oldLink(2*nDim,3),oldieei(2*nDim,2*nDim-2)
    
            !
            ick = val_iMesh%iElemKind(val_ID)
            icci(:) = val_iMesh%iElem(:,val_ID)
        
            oldLink(:,1) = val_iMesh%iElemLink(val_ID,:,1)
            oldLink(:,2) = val_iMesh%iElemLink(val_ID,:,2)
        
            oldieei(:,:) = 0
            do i=1,ElemProp_EdgeNum(ick)
                oldieei(i,:) = icci(ElemProp_EdgeList(1:2*nDim-2,i,ick))
            enddo
    
            if(nDim == 2) then
                itemp   = icci(1)
                icci(1) = icci(2)
                icci(2) = itemp
                    
                itemp   = icci(3)
                icci(3) = icci(4)
                icci(4) = itemp
            elseif(nDim == 3) then
                itemp   = icci(1)
                icci(1) = icci(2)
                icci(2) = itemp
                    
                itemp   = icci(3)
                icci(3) = icci(4)
                icci(4) = itemp
            
                itemp   = icci(5)
                icci(5) = icci(6)
                icci(6) = itemp
                    
                itemp   = icci(7)
                icci(7) = icci(8)
                icci(8) = itemp
            endif
        
            val_iMesh%iElem(:,val_ID) = icci(:)
        
            do i=1,ElemProp_EdgeNum(ick)
                ieei(:) = icci(ElemProp_EdgeList(1:2*nDim-2,i,ick))
            
                do j=1,ElemProp_EdgeNum(ick)
                    jeej(:) = oldieei(j,:)
                
                    if(isEqual_Edge(2*nDim-2,ieei,jeej)) then
                        er = oldLink(j,1)
                        kr = oldLink(j,2)
                    
                        val_iMesh%iElemLink(val_ID,i,1) = er
                        val_iMesh%iElemLink(val_ID,i,2) = kr
                    
                        if(er > 0) then
                            val_iMesh%iElemLink(er,kr,1) = val_ID
                            val_iMesh%iElemLink(er,kr,2) = i
                        endif
                    
                        exit
                    endif
                enddo
            
            enddo
        
        endsubroutine
        !
        !
    endsubroutine
    
    
    !---------------------------------------------
    !Part3
    !---------------------------------------------
    subroutine calIBeleLink(val_iMesh)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
        !
        integer:: i,j
        integer:: nodeID,elemID,elemIP,nBeee_Poin
        integer:: ick,icci(4*nDim-4),ieei(2*nDim-2),jeej(2*nDim-2),jek
        integer,allocatable:: kBeee_Poin(:),iBeee_Poin(:,:)
    
    
        allocate(kBeee_Poin(val_iMesh%nPoin+1))
    
        call getKBeee_Poin(val_iMesh,kBeee_Poin,nBeee_Poin)
    
        allocate(iBeee_Poin(nBeee_Poin,2))
    
        call getIBeee_Poin(val_iMesh,kBeee_Poin,iBeee_Poin,nBeee_Poin)
    
        !$omp parallel do private(ieei,nodeID,j,elemID,elemIP,ick,icci,jek,jeej)
        do i=1,val_iMesh%nBele
            ieei(:) = val_iMesh%iBele(:,i)
        
            call getMinValueOfIntArray(2*nDim-2,ieei,nodeID)
        
            do j=kBeee_Poin(nodeID)+1,kBeee_Poin(nodeID+1)
                elemID = iBeee_Poin(j,1)
                elemIP = iBeee_Poin(j,2)
            
                ick = val_iMesh%iElemKind(elemID)
                icci(:) = val_iMesh%iElem(:,elemID)
            
                call getEdgeOfElem(ick,4*nDim-4,icci,elemIP,jek,2*nDim-2,jeej)
            
                if(isEqual_Edge(2*nDim-2,ieei,jeej)) then
                    val_iMesh%iBeleLink(i,1) = elemID
                    val_iMesh%iBeleLink(i,2) = elemIP
                
                    val_iMesh%iBele(:,i) = jeej(:)
                
                    exit
                endif
            
            enddo
        enddo
        !$omp end parallel do
    
        return
        !
    contains
    
        !---------------------------------------------
        !
        !---------------------------------------------
        subroutine getKBeee_Poin(val_iMesh,val_kBeee_Poin,val_nBeee_Poin)
            use mod_TypeDef_Mesh
            use mod_PreDefine_Dimension
            use mod_PreDefine_IOPort
            implicit none
            type(typ_GlobalMesh),intent(in):: val_iMesh
            integer,intent(inout):: val_kBeee_Poin(val_iMesh%nPoin+1)
            integer,intent(inout):: val_nBeee_Poin
            !
            integer:: i,j
            integer:: nodeID
            integer:: ick,icci(4*nDim-4)
            integer:: iek,ieei(2*nDim-2)
        
        
            val_kBeee_Poin(:) = 0
            do i=1,val_iMesh%nElem
                ick     = val_iMesh%iElemKind(i)
                icci(:) = val_iMesh%iElem(:,i)
                !
                do j=1,2*nDim
                    if(val_iMesh%iElemLink(i,j,1) < 0) then
                        call getEdgeOfElem(ick,4*nDim-4,icci,j,iek,2*nDim-2,ieei)
                    
                        call getMinValueOfIntArray(2*nDim-2,ieei,nodeID)
                    
                        val_kBeee_Poin(nodeID+1) = val_kBeee_Poin(nodeID+1) + 1
                    endif
                enddo
            enddo
        
            do i=2,val_iMesh%nPoin+1
                val_kBeee_Poin(i) = val_kBeee_Poin(i-1) + val_kBeee_Poin(i)
            enddo
        
            val_nBeee_Poin = val_kBeee_Poin(val_iMesh%nPoin+1)
        
        endsubroutine
    
    
        !---------------------------------------------
        !
        !---------------------------------------------
        subroutine getIBeee_Poin(val_iMesh,val_kBeee_Poin,val_iBeee_Poin,val_nBeee_Poin)
            use mod_TypeDef_Mesh
            use mod_PreDefine_Dimension
            use mod_PreDefine_IOPort
            implicit none
            type(typ_GlobalMesh),intent(in):: val_iMesh
            integer,intent(inout):: val_kBeee_Poin(val_iMesh%nPoin+1)
            integer,intent(inout):: val_iBeee_Poin(val_nBeee_Poin,2)
            integer,intent(inout):: val_nBeee_Poin
            !
            integer:: i,j
            integer:: nodeID
            integer:: ick,icci(4*nDim-4)
            integer:: iek,ieei(2*nDim-2)

            do i=1,val_iMesh%nElem
                ick     = val_iMesh%iElemKind(i)
                icci(:) = val_iMesh%iElem(:,i)
                !
                do j=1,2*nDim
                    if(val_iMesh%iElemLink(i,j,1) < 0) then
                        call getEdgeOfElem(ick,4*nDim-4,icci,j,iek,2*nDim-2,ieei)
                    
                        call getMinValueOfIntArray(2*nDim-2,ieei,nodeID)
                    
                        val_kBeee_Poin(nodeID) = val_kBeee_Poin(nodeID) + 1
                    
                        val_iBeee_Poin(val_kBeee_Poin(nodeID),1) = i
                        val_iBeee_Poin(val_kBeee_Poin(nodeID),2) = j
                    endif
                enddo
            enddo
        
            do i=val_iMesh%nPoin,1,-1
                val_kBeee_Poin(i+1) = val_kBeee_Poin(i)
            enddo
            val_kBeee_Poin(1) = 0
        
        endsubroutine
        !
    
    endsubroutine
    !
    !---------------------------------------------
    subroutine calIBeleProp(val_iMesh)
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
        !
        integer:: i,j,beleID
        integer:: ieei(2*nDim-2),iek
    
    
        val_iMesh%iBeleProp(:,:) = 0
        !$omp parallel do private(ieei,iek)
        do i=1,val_iMesh%nBele
            ieei(:) = val_iMesh%iBele(:,i)
        
            call getElemKind(2*nDim-2 , ieei , iek)
        
            val_iMesh%iBeleProp(i,1) = ElemProp_NodeNum(iek)
        enddo
        !$omp end parallel do
    
    
        beleID = 0
        do i=1,val_iMesh%nMark
            !$omp parallel do private(beleID)
            do j=1,val_iMesh%iMark(i)%nElem
                beleID = val_iMesh%iMark(i)%iElem(j)
            
                val_iMesh%iBeleProp(beleID,2) = val_iMesh%iMark(i)%KIND
            enddo
            !$omp end parallel do
        enddo
     
    
    endsubroutine
    !
endmodule
!
