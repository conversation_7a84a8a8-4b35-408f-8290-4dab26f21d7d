module mod_SoluIO
    use mod_PreDefine_Dimension
    use mod_PreDefine_Precision
    implicit none
    !
contains
    !
    subroutine IO_SaveSolu(val_iParm, val_iMesh, val_iSolu,val_iStep)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_Config, only: RESU_FILETYPE
        use mod_Options
        use mod_GetSoluParm, only: getSize_SoluPrim, getResuFileLabel, getAeroFMFileLabel
        use mod_calAeroFM, only: calAeroFM,calIPresCoef
        use mod_saveAeroFM, only: saveAeroFM
        use mod_RestartIO
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        integer,intent(in):: val_iStep
        !
        integer:: nSoluX, nPrimX
        character(len=STRLEN):: strFlag, strFla<PERSON>

        call getSize_SoluPrim(nSoluX, nPrimX)


        !---------------------------------------------
        call getResuFileLabel(val_iStep, strFlag)
        call getAeroFMFileLabel(val_iStep , strFlaA)

        !---------------------------------------------
        call calAeroFM(val_iMesh, val_iSolu, val_iParm, val_iParm%iIter, val_iParm%isViscFlow)

        call saveForRestart(val_iParm, val_iSolu)

        call calIPresCoef(val_iSolu%primvar, val_iSolu%iPresCoef, nPrimX, val_iMesh%nPoin)

        call saveAeroFM(val_iParm, strFlaA, val_iStep, val_iParm%isViscFlow)

        !---------------------------------------------
        if(RESU_FILETYPE == _TEC) then
            call saveSolu_TEC(val_iParm, val_iMesh, val_iSolu, val_iStep, strFlag)
        elseif(RESU_FILETYPE == _VTK) then
            call saveSolu_VTK(val_iParm, val_iMesh, val_iSolu, val_iStep, strFlag)
        elseif(RESU_FILETYPE == _CGNS) then
            call saveSolu_CGNS(val_iParm, val_iMesh, val_iSolu, val_iStep, strFlag)
        end if

    end subroutine IO_SaveSolu
    !
    subroutine saveSolu_TEC(val_iParm, val_iMesh, val_iSolu,val_iStep,val_NameFlag)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        integer,intent(in):: val_iStep
        character(len=STRLEN),intent(in ):: val_NameFlag

        call TECIO_SoluField_Binary(val_iParm, val_iMesh, val_iSolu, val_iStep, val_NameFlag)

        call TECIO_SoluBound_ASCII(val_iParm, val_iMesh, val_iSolu, val_NameFlag)

    end subroutine saveSolu_TEC
    !
    subroutine saveSolu_VTK(val_iParm, val_iMesh, val_iSolu,val_iStep,val_NameFlag)
        use mod_TypeDef_Project
        use mod_FreeStream
        use mod_WorkPath
        use mod_PreDefine_IOPort
#ifdef GFLOW_WITH_CGNS
        use vtk
#endif
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        integer,intent(in):: val_iStep
        character(len=*),intent(in ):: val_NameFlag
        !
        integer:: i,ii,j,istat,elemID,beleID,nBCPoin
        character(len=STRLEN):: strFlag,strFlaA
        character(len=STRLEN):: string_VarShare
#ifdef GFLOW_WITH_VTK
        integer:: index_file, index_base, index_zone, index_coord, index_flow, index_field, index_section, icgerr
        real*8,allocatable:: rdata(:)
        integer(cgsize_t):: isize(1,3)
        integer:: icelldim,iphysdim
        integer(cgsize_t),allocatable:: ielem(:,:),jelem(:,:)
#endif
        !
#ifdef GFLOW_WITH_VTK

#endif

    end subroutine saveSolu_VTK
    !
    subroutine saveSolu_CGNS(val_iParm, val_iMesh, val_iSolu,val_iStep,val_NameFlag)
        use mod_TypeDef_Project
        use mod_FreeStream
        use mod_WorkPath
        use mod_PreDefine_IOPort
#ifdef GFLOW_WITH_CGNS
        use cgns
#endif
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        integer,intent(in):: val_iStep
        character(len=*),intent(in ):: val_NameFlag
        !
        integer:: i,ii,j,istat,elemID,beleID,nBCPoin
        character(len=STRLEN):: strFlag,strFlaA
        character(len=STRLEN):: string_VarShare
#ifdef GFLOW_WITH_CGNS
        integer:: index_file, index_base, index_zone, index_coord, index_flow, index_field, index_section, icgerr
        real*4,allocatable:: rdata(:)
        integer(cgsize_t):: isize(1,3)
        integer:: icelldim,iphysdim
        integer(cgsize_t),allocatable:: ielem(:,:),jelem(:,:)
#endif
        !
#ifdef GFLOW_WITH_CGNS
        allocate(rdata(val_iMesh%nPoin))
        call cg_open_f(trim(resuFullPath)//'/'//trim(strFlag)//'.cgns', CG_MODE_WRITE, index_file, icgerr)

        if(icgerr /= CG_OK) call cg_error_exit_f

        icelldim = nDim
        iphysdim = nDim
        call cg_base_write_f(index_file, 'Base', icelldim, iphysdim, index_base, icgerr)

        isize(1,1) = val_iMesh%nPoin
        isize(1,2) = val_iMesh%nElem
        isize(1,3) = 0
        call cg_zone_write_f(index_file, index_base, 'Zone01', isize, Unstructured, index_zone, icgerr)
        rdata(:) = val_iMesh%iCoor_Poin(:,1)
        call cg_coord_write_f(index_file, index_base, index_zone, RealDouble, 'CoordinateX', rdata, index_coord, icgerr)
        rdata(:) = val_iMesh%iCoor_Poin(:,2)
        call cg_coord_write_f(index_file, index_base, index_zone, RealDouble, 'CoordinateY', rdata, index_coord, icgerr)
        if(nDim==3) then
            rdata(:) = val_iMesh%iCoor_Poin(:,nDim)
            call cg_coord_write_f(index_file, index_base, index_zone, RealDouble, 'CoordinateZ', rdata, index_coord, icgerr)
        endif

        allocate(ielem(4*nDim-4,val_iMesh%nElem))
        do i=1,val_iMesh%nElem
            ielem(1:4*nDim-4,i) = val_iMesh%iElem(1:4*nDim-4,i)
        enddo
        if(nDim == 2) then
            call cg_section_write_f(index_file, index_base, index_zone, 'Elem', QUAD_4, 1, isize(1,2), 0, ielem, index_section, icgerr)
        elseif(nDim==3) then
            call cg_section_write_f(index_file, index_base, index_zone, 'Elem', HEXA_8, 1, isize(1,2), 0, ielem, index_section, icgerr)
        endif
        deallocate(ielem)

        !do ii=1,val_iMesh%nMark
        !    allocate(jelem(2*nDim-2,val_iMesh%iMark(ii)%nElem))
        !    do i=1,val_iMesh%iMark(ii)%nElem
        !        beleID = val_iMesh%iMark(i)%iElem(j)
        !        jelem(1:2*nDim-2,i) = val_iMesh%iBele(1:2*nDim-2,beleID)
        !    enddo
        !    if(nDim == 2) then
        !    call cg_section_write_f(index_file, index_base, index_zone, 'Mark'//strTwo_Number(ii), BAR_2, 1, val_iMesh%iMark(ii)%nElem, 0, jelem, index_section, icgerr)
        !    elseif(nDim == 3) then
        !    call cg_section_write_f(index_file, index_base, index_zone, 'Mark'//strTwo_Number(ii), QUAD_4, 1, val_iMesh%iMark(ii)%nElem, 0, jelem, index_section, icgerr)
        !    endif
        !    deallocate(jelem)
        !enddo

        call cg_sol_write_f(index_file, index_base, index_zone, 'FlowSolution', Vertex, index_flow, icgerr)

        !rdata(:) = val_iSolu%soluvar(:,1)
        !call cg_field_write_f(index_file, index_base, index_zone, index_flow, RealDouble, 'Density', rdata, index_field, icgerr)
        !rdata(:) = val_iSolu%soluvar(:,nDim+3)
        !call cg_field_write_f(index_file, index_base, index_zone, index_flow, RealDouble, 'VBar', rdata, index_field, icgerr)
        rdata(:) = val_iSolu%primvar(:,1)
        call cg_field_write_f(index_file, index_base, index_zone, index_flow, RealDouble, 'Pressure', rdata, index_field, icgerr)
        rdata(:) = val_iSolu%primvar(:,nDim+2)
        call cg_field_write_f(index_file, index_base, index_zone, index_flow, RealDouble, 'Temperature', rdata, index_field, icgerr)
        rdata(:) = val_iSolu%primvar(:,2)
        call cg_field_write_f(index_file, index_base, index_zone, index_flow, RealDouble, 'Vx', rdata, index_field, icgerr)
        rdata(:) = val_iSolu%primvar(:,3)
        call cg_field_write_f(index_file, index_base, index_zone, index_flow, RealDouble, 'Vy', rdata, index_field, icgerr)
        if(nDim==3) then
        rdata(:) = val_iSolu%primvar(:,nDim+1)
        call cg_field_write_f(index_file, index_base, index_zone, index_flow, RealDouble, 'Vz', rdata, index_field, icgerr)
        endif
        !rdata(:) = val_iSolu%primvar(:,nDim+3)
        !call cg_field_write_f(index_file, index_base, index_zone, index_flow, RealDouble, 'Mul', rdata, index_field, icgerr)
        rdata(:) = val_iSolu%primvar(:,nDim+4)
        !rdata(:) = val_iSolu%soluvar(:,nDim+3)
        call cg_field_write_f(index_file, index_base, index_zone, index_flow, RealDouble, 'Mut', rdata, index_field, icgerr)
        !rdata(:) = val_iSolu%iPresCoef(:)
        !call cg_field_write_f(index_file, index_base, index_zone, index_flow, RealDouble, 'Cp', rdata, index_field, icgerr)
        !rdata(:) = val_iSolu%iSurfCoef(:)
        !call cg_field_write_f(index_file, index_base, index_zone, index_flow, RealDouble, 'Cf', rdata, index_field, icgerr)

        call cg_close_f(index_file, icgerr)

        deallocate(rdata)

#endif
    end subroutine saveSolu_CGNS
    !
    !=============================================================
    !=============================================================
    !
    subroutine TECIO_SoluField_Binary(val_iParm, val_iMesh, val_iSolu,val_iStep,val_NameFlag)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_WorkPath
        use mod_TECIO
        use mod_PreDefine_IOPort
        use mod_GetSoluParm, only: getSize_SoluPrim
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        integer,intent(in):: val_iStep
        character(len=STRLEN):: val_NameFlag
        !
        integer(kind=4):: i,j,nOutVar
        real(kind=REALLEN)   :: minBnd(20),maxBnd(20)
        character(256) :: varnames
        type(typ_TecFile):: tecfile
        !
        integer:: nSoluX,nPrimX

        call getSize_SoluPrim(nSoluX, nPrimX)

        nOutVar = nDim + nPrimX
        if(nDim == 2) then
            varnames = "X,Y,Pressure,Vx,Vy,Temperature"
        else if(nDim==3) then
            varnames = "X,Y,Z,Pressure,Vx,Vy,Vz,Temperature"
        end if
        if(val_iParm%isTurbFlow) then
            varnames = trim(varnames)//",Mul,Mut"
        elseif(val_iParm%isViscFlow) then
            varnames = trim(varnames)//",Mul"
        else
            !do nothing
        end if

        ! compressed list of the min/max bounds for each non-shared and non-passive variable
        !"X Y Z Density Pressure Vx Vy Vz Temperature (Mul Mut)"
        minBnd(1:20) =  1.0E+10
        maxBnd(1:20) = -1.0E+10
        do i=1,val_iMesh%nPoin
            do j=1,nDim
                minBnd(j) = min(minBnd(j), val_iMesh%iCoor_Poin(i,j))
                maxBnd(j) = max(maxBnd(j), val_iMesh%iCoor_Poin(i,j))
            end do
            do j=1,nPrimX
                minBnd(nDim+j) = min(minBnd(nDim+j), val_iSolu%primvar(i,j))
                maxBnd(nDim+j) = max(maxBnd(nDim+j), val_iSolu%primvar(i,j))
            end do
        end do

        call plt_open_file(tecfile, ioPort_FULL, trim(resuFullPath)//'/RSU_'//trim(val_NameFlag)//'.plt')

        call plt_zone_init(tecfile, nDim, nOutVar, val_iMesh%nPoin, val_iMesh%nElem, varnames, minBnd, maxBnd, val_iStep)

        call plt_write_header(tecfile)

        do j=1,nDim
            call plt_write_variable(tecfile, val_iMesh%iCoor_Poin(:,j))
        end do
        do j=1,nPrimX
            call plt_write_variable(tecfile, val_iSolu%primvar(:,j))
        end do

        call plt_write_elements(tecfile, val_iMesh%iElem, 4*nDim-4, tecfile%nelements)

        call plt_close_file(tecfile)

    end subroutine TECIO_SoluField_Binary
    !
    subroutine TECIO_SoluBound_ASCII(val_iParm,val_iMesh,val_iSolu,val_NameFlag)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_WorkPath
        use mod_GetSoluParm, only: getSize_SoluPrim
        use mod_PreDefine_IOPort
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        character(len=*),intent(in ):: val_NameFlag
        !
        integer:: ii,i,j,nBCPoin,beleID
        integer:: nSOluX, nPrimx
        character(len=20):: VShareStr
        character(len=32):: MarkTypeStr

        if(nDim == 2) then
            MarkTypeStr = "FELINESEG"
        elseif(nDim == 3) then
            MarkTypeStr = "FEQUADRILATERAL"
        end if


        call getSize_SoluPrim(nSoluX, nPrimX)

        !---------------------------------------------
        open(ioPort_FULL,file = trim(resuFullPath)//'/BC_'//trim(val_NameFlag)//'.plt',asynchronous='yes',status='unknown')

        if(val_iParm%isTurbFlow) then
            if(nDim == 2) then
                write(ioPort_FULL,*) 'VARIABLES = "X","Y","Density","Pressure","Vx","Vy","Temperature","Mul","Mut","Cp","Cf","Cfx","Cfy"'
                VShareStr = '([1-13]=1)'
            elseif(nDim == 3) then
                write(ioPort_FULL,*) 'VARIABLES = "X","Y","Z","Density","Pressure","Vx","Vy","Vz","Temperature","Mul","Mut","Cp","Cf","Cfx","Cfy","Cfz"'
                VShareStr = '([1-16]=1)'
            endif
        elseif(val_iParm%isViscFlow) then
            if(nDim == 2) then
                write(ioPort_FULL,*) 'VARIABLES = "X","Y","Density","Pressure","Vx","Vy","Temperature","Mul","Cp","Cf","Cfx","Cfy"'
                VShareStr = '([1-12]=1)'
            elseif(nDim == 3) then
                write(ioPort_FULL,*) 'VARIABLES = "X","Y","Z","Density","Pressure","Vx","Vy","Vz","Temperature","Mul","Cp","Cf","Cfx","Cfy","Cfz"'
                VShareStr = '([1-15]=1)'
            endif
        else
            if(nDim == 2) then
                write(ioPort_FULL,*) 'VARIABLES = "X","Y","Density","Pressure","Vx","Vy","Temperature","Cp"'
                VShareStr = '([1-8]=1)'
            elseif(nDim == 3) then
                write(ioPort_FULL,*) 'VARIABLES = "X","Y","Z","Density","Pressure","Vx","Vy","Vz","Temperature","Cp"'
                VShareStr = '([1-10]=1)'
            endif
        end if

        ii = 0
        do i=1,val_iMesh%nMark
            if(val_iMesh%iMark(i)%nElem == 0) cycle
            if(val_iMesh%iMarkProp(i,2) == MARK_NONE) cycle
            if(val_iMesh%iMarkProp(i,2) == MARK_ATTA) cycle

            ii = ii + 1
            if(ii == 1) then
                write(ioPort_FULL,"(A,A,I6,A,I6,A,A)") 'ZONE DATAPACKING = BLOCK', &
                        ', NODES=',val_iMesh%nBCPoin, &
                        ', ELEMENTS=',val_iMesh%iMark(i)%nElem, &
                        ', ZONETYPE=',trim(adjustl(MarkTypeStr))
            else
                write(ioPort_FULL,"(A,A,I6,A,I6,A,A,A,A)") 'ZONE DATAPACKING = BLOCK', &
                        ', NODES=',val_iMesh%nBCPoin, &
                        ', ELEMENTS=',val_iMesh%iMark(i)%nElem, &
                        ', ZONETYPE=',trim(adjustl(MarkTypeStr)), &
                        ', VARSHARELIST=',trim(adjustl(VShareStr))
            endif

            if(ii == 1) then
                nBCPoin = val_iMesh%nBCPoin

                do j=1,nDim
                    write(ioPort_FULL,"(8E13.5)") val_iMesh%iCoor_Poin(val_iMesh%iMapBToG_Poin(1:nBCPoin),j)
                enddo

                write(ioPort_FULL,"(8E13.5)") val_iSolu%soluvar(val_iMesh%iMapBToG_Poin(1:nBCPoin),1)

                do j=1,nPrimX
                    write(ioPort_FULL,"(8E13.5)") val_iSolu%primvar(val_iMesh%iMapBToG_Poin(1:nBCPoin),j)
                enddo

                write(ioPort_FULL,"(8E13.5)") val_iSolu%iPresCoef(val_iMesh%iMapBToG_Poin(1:nBCPoin))

                if(val_iParm%isViscFlow) then
                    write(ioPort_FULL,"(8E13.5)") val_iSolu%iSurfCoef(val_iMesh%iMapBToG_Poin(1:nBCPoin))

                    do j=1,nDim
                        write(ioPort_FULL,"(8E13.5)") val_iSolu%iSurfTau(val_iMesh%iMapBToG_Poin(1:nBCPoin),j)
                    end do
                end if

            endif

            do j=1,val_iMesh%iMark(i)%nElem
                beleID = val_iMesh%iMark(i)%iElem(j)
                write(ioPort_FULL,"(4I10)") val_iMesh%iMapGToB_Poin(val_iMesh%iBele(1:2*nDim-2,beleID))
            enddo

        enddo

        close(ioPort_FULL)

    end subroutine TECIO_SoluBound_ASCII
    !
end module mod_SoluIO