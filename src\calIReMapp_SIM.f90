!
module mod_calIReMapp_SIM
    use mod_MeshReordering
    implicit none
    !
contains
    !
    !---------------------------------------------
    !---------------------------------------------
    subroutine calIReMapp_Poin_SIM(val_iPart,val_isOk)
        implicit none
        integer,intent(in):: val_iPart
        logical,intent(out):: val_isOk
        !
        integer:: i,j,k,iCont
        integer:: iClor,nTPoin
        integer,allocatable:: kPoin_Clor(:)
        
        
        !---------------------------------------------
        !---------------------------------------------
        allocate(kPoin_Clor(iReMesh%nClor+1))
        kPoin_Clor(:) = 0
        
        do i=1,iReMesh%nPoin
            iClor = iReMesh%iClor_Poin(j)
            
            kPoin_Clor(iClor+1) = kPoin_Clor(iClor+1) + 1
        enddo
        
        do i=2,iReMesh%nClor+1
            if(kPoin_Clor(i) > 0) then
                j = (kPoin_Clor(i) + nPoinPerAccess - 1)/nPoinPerAccess
                
                kPoin_Clor(i) = j*nPoinPerAccess
            endif
            
            kPoin_Clor(i) = kPoin_Clor(i) + kPoin_Clor(i-1)
        enddo
        
        !---------------------------------------------
        !---------------------------------------------
        iReMapp%nGPoin = iReMesh%nPoin
        iReMapp%nLPoin = kPoin_Clor(iReMesh%nClor+1)
        
        allocate(iReMapp%iMapGToL_Poin(iReMapp%nGPoin))
        allocate(iReMapp%iMapLToG_Poin(iReMapp%nLPoin))
        iReMapp%iMapGToL_Poin(:) = 0
        iReMapp%iMapLToG_Poin(:) = 0
        
        !---------------------------------------------
        !---------------------------------------------
        do i=1,iReMesh%nClor
            iCont = 0
            
            do j=1,iReMesh%nPoin
                if(iReMesh%iClor_Poin(j) == i) then
                    iCont = iCont + 1
                    
                    iReMapp%iMapGToL_Poin(j) = iCont + kPoin_Clor(i)
                    iReMapp%iMapLToG_Poin(iCont + kPoin_Clor(i)) = j
                endif
            enddo
        enddo
        
        !---------------------------------------------
        !---------------------------------------------
        val_isOk = .true.
        return
        
    endsubroutine
    !
    !---------------------------------------------
    subroutine calIReMapp_Sate_SIM(val_iPart,val_isOk)
        implicit none
        integer,intent(in):: val_iPart
        logical,intent(out):: val_isOk
        !
        !---------------------------------------------
        !---------------------------------------------
        val_isOk = .true.
        return
            
    endsubroutine
    !
endmodule
! 
  