
!---------------------------------------------
!
!---------------------------------------------
module mod_SoluTurbSST
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    use mod_PreDefine_Flag
    use mod_PreDefine_Mark
    use mod_TypeDef_Parm
    use mod_Interface_AllocateArray
    use mod_GPUThreadDim
    use mod_TurbSST_ParmIndx
    use mod_Options
    use cudafor
    implicit none
    !
contains
    !
    subroutine setTurbModelConstants_TurbSST(val_iParm)
        use mod_TypeDef_Parm
        use mod_Config, only: MESH_DYFIRST, MESH_DOMALEN
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        !
        real(kind=REALLEN):: turbSST_kInf       = 9.0E-9
        real(kind=REALLEN):: turbSST_wInf       = 1.0E-6
        real(kind=REALLEN):: turbSST_kWall      = 0.0
        real(kind=REALLEN):: turbSST_wWall      = 1.0
        !
        real(kind=REALLEN):: turbSST_MutInf     = 9.0E-3
        real(kind=REALLEN):: turbSST_MutWall    = 0.0
        real(kind=REALLEN):: turbSST_DY1        = 1.0E-5
        real(kind=REALLEN):: turbSST_DomLen     = 10
        !
        real(kind=REALLEN):: turbSST_sigmaK1    = 0.85000
        real(kind=REALLEN):: turbSST_sigmaK2    = 1.00000
        real(kind=REALLEN):: turbSST_sigmaW1    = 0.50000
        real(kind=REALLEN):: turbSST_sigmaW2    = 0.85600
        real(kind=REALLEN):: turbSST_belta1     = 0.07500
        real(kind=REALLEN):: turbSST_belta2     = 0.08280
        real(kind=REALLEN):: turbSST_Cw1        = 0.53300
        real(kind=REALLEN):: turbSST_Cw2        = 0.44000
        real(kind=REALLEN):: turbSST_gamma1     = 0.55500
        real(kind=REALLEN):: turbSST_gamma2     = 0.44000
        real(kind=REALLEN):: turbSST_bStar      = 0.09000
        real(kind=REALLEN):: turbSST_kappa      = 0.41000
        real(kind=REALLEN):: turbSST_a1         = 0.31000
        real(kind=REALLEN):: turbSST_cmu        = 0.08950

        val_iParm%iHostParm(IP_TurbSST_SigmaK1) = turbSST_sigmaK1
        val_iParm%iHostParm(IP_TurbSST_SigmaK2) = turbSST_sigmaK2
        val_iParm%iHostParm(IP_TurbSST_SigmaW1) = turbSST_sigmaW1
        val_iParm%iHostParm(IP_TurbSST_SigmaW2) = turbSST_sigmaW2
        val_iParm%iHostParm(IP_TurbSST_Belta1 ) = turbSST_belta1
        val_iParm%iHostParm(IP_TurbSST_Belta2 ) = turbSST_belta2
        val_iParm%iHostParm(IP_TurbSST_CW1    ) = turbSST_Cw1
        val_iParm%iHostParm(IP_TurbSST_CW2    ) = turbSST_Cw2
        val_iParm%iHostParm(IP_TurbSST_Gamma1 ) = turbSST_gamma1
        val_iParm%iHostParm(IP_TurbSST_Gamma2 ) = turbSST_gamma2
        val_iParm%iHostParm(IP_TurbSST_BStar  ) = turbSST_bStar
        val_iParm%iHostParm(IP_TurbSST_Kappa  ) = turbSST_kappa
        val_iParm%iHostParm(IP_TurbSST_A1     ) = turbSST_a1
        val_iParm%iHostParm(IP_TurbSST_Cmu    ) = turbSST_cmu


        turbSST_DY1     = MESH_DYFIRST
        turbSST_DomLen  = MESH_DOMALEN

        turbSST_kInf    = 9.0E-9*val_iParm%iHostParm(IP_InftyC)**2
        turbSST_wInf    = 1.0E-6*val_iParm%iHostParm(IP_InftyC)**2/val_iParm%iHostParm(IP_InftyMul)
        turbSST_MutInf  = val_iParm%iHostParm(IP_InftyD)*turbSST_kInf/turbSST_wInf

        turbSST_kWall   = 0.0
        turbSST_wWall   = 60.0*val_iParm%iHostParm(IP_InftyMul) / (val_iParm%iHostParm(IP_InftyD)*turbSST_belta1*turbSST_DY1**2)
        turbSST_MutWall = 0.0

        val_iParm%iHostParm(IP_TurbSST_kInf   ) = turbSST_kInf
        val_iParm%iHostParm(IP_TurbSST_wInf   ) = turbSST_wInf
        val_iParm%iHostParm(IP_TurbSST_kWall  ) = turbSST_kWall
        val_iParm%iHostParm(IP_TurbSST_wWall  ) = turbSST_wWall
        val_iParm%iHostParm(IP_TurbSST_MutInf ) = turbSST_MutInf
        val_iParm%iHostParm(IP_TurbSST_DY1    ) = turbSST_DY1
        val_iParm%iHostParm(IP_TurbSST_DomLen ) = turbSST_DomLen

    endsubroutine
    !
    subroutine soluMalloc_TurbSST(val_iSolu)
        use mod_TypeDef_Solu
        use mod_Interface_AllocateArray
        implicit none
        type(typ_Solu),intent(inout):: val_iSolu
        !
        !
        val_iSolu%nNodeAuxv = 5
        val_iSolu%nSateAuxv = 0
        val_iSolu%nBelvAuxv = 0
        !
        call allocateGPUArray(val_iSolu%nodeAuxv_d, val_iSolu%nNode, val_iSolu%nNodeAuxv)
        call allocateGPUArray(val_iSolu%sateAuvx_d, val_iSolu%nNode, val_iSolu%nSateAuxv)
        call allocateGPUArray(val_iSolu%belvAuvx_d, val_iSolu%nNode, val_iSolu%nBelvAuxv)
        !
    endsubroutine
    !
    subroutine updFieldWithBC_TurbSST(val_iParm,val_iMesh,val_iSolu)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_TurbSST_Def
        use mod_TurbSST_GPUFunc
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        !
        integer:: i,istat

        call bldBC_Global<<<nGridDim(IP_BB), nBlockDim>>>                               &
                (val_iMesh%iCalc_Poin_d, val_iMesh%iBelvProp_d, val_iMesh%iMarkProp_d,  &
                val_iMesh%iWDis_Poin_d, val_iMesh%iCoor_Poin_d, val_iMesh%iNvor_Belv_d, &
                val_iMesh%iVelo_Poin_d, val_iMesh%iVelo_Belv_d,                         &
                val_iSolu%soluvar_d, val_iSolu%primvar_d, val_iSolu%soluBelv_d,         &
                val_iSolu%primBelv_d, val_iSolu%soluMark_d, val_iSolu%primMark_d,       &
                val_iParm%iGPUSParm, val_iParm%isWithPrec, val_iMesh%nBelv,             &
                val_iMesh%nMark, val_iMesh%nPoin, val_iMesh%nBelv, val_iParm%iIter      )

    endsubroutine
    !
    subroutine updFlowInftyInSolu_TurbSST(val_iParm,val_iSolu)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_FreeStream
        use mod_Config
        implicit none
        type(typ_Parm),intent(in   ):: val_iParm
        type(typ_Solu),intent(inout):: val_iSolu
        !
        integer:: i
        real(kind=REALLEN):: sqFi,gammo,cFi,kFi,wFi,mutFi


        gammo = val_iParm%iHostParm(IP_GAMMO)
        sqFi  = flowInfty_Velocity(1)**2 + flowInfty_Velocity(2)**2 + flowInfty_Velocity(nDim)**2*(nDim-2)
        cFi   = val_iParm%iHostParm(IP_InftyC)

        kFi   = 9.0E-9*cFi*cFi
        wFi   = 1.0E-6*cFi*cFi/val_iParm%iHostParm(IP_InftyMul)
        mutFi = flowInfty_Density*kFi/wFi

        val_iSolu%soluInfty(       1) = flowInfty_Density
        val_iSolu%soluInfty(2:nDim+1) = flowInfty_Density*flowInfty_Velocity(:)
        val_iSolu%soluInfty(  nDim+2) = flowInfty_Pressure/gammo + 0.50*flowInfty_Density*sqFi
        val_iSolu%soluInfty(  nDim+3) = kFi
        val_iSolu%soluInfty(  nDim+4) = wFi

        val_iSolu%primInfty(       1) = flowInfty_Pressure
        val_iSolu%primInfty(2:nDim+1) = flowInfty_Velocity(:)
        val_iSolu%primInfty(  nDim+2) = flowInfty_Temperature
        val_iSolu%primInfty(  nDim+3) = flowInfty_Viscosity
        val_iSolu%primInfty(  nDim+4) = mutFi

    endsubroutine
    !
    subroutine getMarkSoluAndPrim_TurbSST(val_iParm,val_iSolu,val_markInfo,val_iStep,val_soluMi,val_primMi)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_TurbSST_Def
        use mod_Config, only: typ_MarkInfo
        use mod_parmDimLess
        use mod_getSoluParm, only: getSoluParm_PhysicalTime
        use mod_TurbSST_GPUFunc, only: getPrimBySolu_TurbSST
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Solu),intent(inout):: val_iSolu
        type(typ_MarkInfo),intent(in):: val_markInfo
        integer       ,intent(in   ):: val_iStep
        real(kind=REALLEN),intent(inout):: val_soluMi(nDim+4)
        real(kind=REALLEN),intent(inout):: val_primMi(nDim+4)
        !
        integer:: istat,i
        real*8 :: dEi,vEi(nDim),qEi
        real(kind=REALLEN):: iTime = 0.0
        real(kind=REALLEN):: zero = 0.0


        call getSoluParm_PhysicalTime(val_iParm, val_iStep, iTime)

        val_soluMi(:) = 0.0
        val_primMi(:) = 0.0

        if(val_markInfo%markKD == MARK_WALL) then
            !do nothing
        elseif(val_markInfo%markKD == MARK_EQTW) then
            val_soluMi(1     ) = val_markInfo%surfTemp

        elseif(val_markInfo%markKD == MARK_EULW) then
            !do nothing
        elseif(val_markInfo%markKD == MARK_MOVW) then
            val_soluMi(1     :nDim  ) = val_markInfo%transVelo(1:nDim)
            val_soluMi(nDim+1       ) = val_markInfo%rotatOmga
            val_primMi(1     :nDim  ) = val_markInfo%rotatOrgn(1:nDim)
            val_primMi(nDim+1:2*nDim) = val_markInfo%rotatAxis(1:nDim)

        elseif(val_markInfo%markKD == MARK_INLE) then
            dEi = val_markInfo%surfPres/(val_markInfo%surfTemp*val_iParm%iHostParm(IP_GASCO))
            vEi(1:nDim) = val_markInfo%surfVelo*val_markInfo%surfNorm(1:nDim)
            qEi = 0.5*dEi*(vEi(1)**2+vEi(2)**2+vEi(nDim)**2*(nDim-2))

            val_soluMi(1       ) = dEi
            val_soluMi(2:nDim+1) = dEi*vEi(1:nDim)
            val_soluMi(nDim+2  ) = val_markInfo%surfPres/(val_iParm%iHostParm(IP_GAMMA) - 1) + qEi
            val_soluMi(nDim+3  ) = val_iSolu%soluInfty(nDim+3)
            val_soluMi(nDim+4  ) = val_iSolu%soluInfty(nDim+4)

            write(*,*) 'to be finished.'
            call getPrimBySolu_TurbSST(                   &
                    val_soluMi, val_primMi,               &
                    val_iParm%iHostParm(IP_GAMMO),        &
                    val_iParm%iHostParm(IP_GASCO),        &
                    val_iParm%iHostParm(IP_SutherlandT0), &
                    val_iParm%iHostParm(IP_SutherlandTs), &
                    val_iParm%iHostParm(IP_SutherlandM0), &
                    val_iParm%iHostParm(IP_TurbSST_A1),   &
                    val_iParm%iHostParm(IP_LIMMINP),      &
                    val_iParm%iHostParm(IP_LIMMAXP),      &
                    val_iParm%iHostParm(IP_LIMMAXMuT),    &
                    zero, zero, zero)

        elseif(val_markInfo%markKD == MARK_INLM) then
            dEi = val_markInfo%surfPres/(val_markInfo%surfTemp*val_iParm%iHostParm(IP_GASCO))
            vEi(1:nDim) = val_markInfo%surfMass/(dEi*val_markInfo%surfArea)*val_markInfo%surfNorm(1:nDim)
            qEi = 0.5*dEi*(vEi(1)**2+vEi(2)**2+vEi(nDim)**2*(nDim-2))

            val_soluMi(1       ) = dEi
            val_soluMi(2:nDim+1) = dEi*vEi(1:nDim)
            val_soluMi(nDim+2  ) = val_markInfo%surfPres/(val_iParm%iHostParm(IP_GAMMA) - 1) + qEi
            val_soluMi(nDim+3  ) = val_iSolu%soluInfty(nDim+3)
            val_soluMi(nDim+4  ) = val_iSolu%soluInfty(nDim+4)

            write(*,*) 'to be finished.'
            call getPrimBySolu_TurbSST(                   &
                    val_soluMi, val_primMi,               &
                    val_iParm%iHostParm(IP_GAMMO),        &
                    val_iParm%iHostParm(IP_GASCO),        &
                    val_iParm%iHostParm(IP_SutherlandT0), &
                    val_iParm%iHostParm(IP_SutherlandTs), &
                    val_iParm%iHostParm(IP_SutherlandM0), &
                    val_iParm%iHostParm(IP_TurbSST_A1),   &
                    val_iParm%iHostParm(IP_LIMMINP),      &
                    val_iParm%iHostParm(IP_LIMMAXP),      &
                    val_iParm%iHostParm(IP_LIMMAXMuT),    &
                    zero, zero, zero)

        elseif(val_markInfo%markKD == MARK_OUTL) then
            !do nothing
        elseif(val_markInfo%markKD == MARK_OUTP) then
            val_soluMi(1) = val_markInfo%surfPres * (val_markInfo%BackPresFact &
                            + val_markInfo%BackPrecIncr*max(0, val_iStep - 1) )

            val_soluMi(2) = val_markInfo%surfTtot / DLRef_temperature
            val_soluMi(3) = val_markInfo%surfPtot / DLRef_pressure

        elseif(val_markInfo%markKD == MARK_OUTM) then
            !do nothing

        elseif(val_markInfo%markKD == MARK_PFAR) then
            val_soluMi(:) = val_iSolu%soluInfty(:)
            val_primMi(:) = val_iSolu%primInfty(:)
        else
            val_soluMi(:) = val_iSolu%soluInfty(:)
            val_primMi(:) = val_iSolu%primInfty(:)
        end if

    end subroutine
    !
    subroutine RunOneIteration_UPW_GPU_TurbSST(val_iParm,val_iMesh,val_iSolu)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_Config, only: TIME_DISCRE_FLOW, UPWIND_SCHEME, NITER_O1TUNING, UNSTEADY_SIMULATION, UPWIND_LIMITER, UPWIND_ORDER
        use mod_Operation_GPUArrayCpy
        use mod_TurbSST_Def
        use mod_TurbSST_GPUFunc
        use mod_Unsteady, only: calDualTerm_Global
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        !
        integer:: i, j, iIter, iClor, iStat, iRK
        logical:: is2ndOrder,relFlag

        is2ndOrder = (val_iParm%iIter > NITER_O1TUNING).and.(UPWIND_ORDER == 2)


        !---------------------------------------------
        !隐式LU-SGS迭代
        if(TIME_DISCRE_FLOW == _MCLUSGS_IMPLICIT) then
            call GPURealArrayCpy2D1_Global<<<nGridDim(IP_BP), nBlockDim>>>              &
                    (val_iSolu%soluvar_d, val_iSolu%soluOld_d,val_iMesh%nPoin, nSoluEx  )

            !do i=1,3
            !    call GPURealArrayCpy1D1_Global<<<nGridDim(IP_BP), nBlockDim>>>          &
            !            (val_iSolu%DTCoef_d, val_iSolu%DTCoef2_d,val_iMesh%nPoin        )
            !
            !    call tuningDTCoef_Global<<<nGridDim(IP_BP), nBlockDim>>>                        &
            !            (val_iMesh%iCalc_Poin_d, val_iMesh%kSate_Poin_d, val_iMesh%iSate_Poin_d,&
            !            val_iSolu%DTCoef2_d, val_iSolu%DTCoef_d, val_iMesh%nPoin,               &
            !            val_iMesh%nSate,val_iMesh%nPoin                                         )
            !end do

            if(val_iParm%isWithPrec ) then
                call calPrecCoefMatx_Global<<<nGridDim(IP_BP), nBlockDim>>>             &
                        (val_iMesh%iCalc_Poin_d,val_iSolu%soluvar_d,val_iSolu%primvar_d,&
                        val_iSolu%preCoef_d, val_iSolu%pMatrix_d, val_iParm%iGPUSParm,  &
                        val_iMesh%nPoin, val_iMesh%nPoin, val_iParm%isWithPrec          )
            end if

            if(val_iParm%isUnstDual) then
                call calDualTerm_Global<<<nGridDim(IP_BP), nBlockDim>>>                 &
                        (val_iMesh%iCalc_Poin_d,val_iSolu%soluvar_d,val_iSolu%soluTimN_d,&
                        val_iSolu%soluTimX_d, val_iSolu%UnstTerm_d, val_iParm%iGPUSParm,&
                        val_iMesh%nPoin, nSoluEx, val_iMesh%nPoin                       )
            endif

            call calSpecRaduAndDeltTime_Global<<<nGridDim(IP_BP), nBlockDim>>>              &
                    (val_iMesh%iCalc_Poin_d, val_iMesh%kSate_Poin_d, val_iMesh%iSate_Poin_d,&
                    val_iMesh%iCoef_Sate_d, val_iMesh%kBelv_Poin_d, val_iMesh%iBelv_Poin_d, &
                    val_iMesh%iCoef_Belv_d, val_iMesh%iVelo_Poin_d, val_iMesh%iVelo_Belv_d, &
                    val_iSolu%primvar_d, val_iSolu%primBelv_d, val_iSolu%preCoef_d,         &
                    val_iSolu%SRconv_d, val_iSolu%SRvisc_d, val_iSolu%SRcNode_d,            &
                    val_iSolu%SRvNode_d, val_iSolu%SRcBelv_d, val_iSolu%SRvBelv_d,          &
                    val_iSolu%DT_d, val_iSolu%DTCoef_d, val_iParm%iGPUSParm,                &
                    val_iMesh%nPoin, val_iMesh%nSate, val_iMesh%nBelv,                      &
                    val_iMesh%nPoin, val_iParm%isWithPrec, val_iParm%isUnstDual             )

            call calSoluGradWithLimt_Global<<<nGridDim(IP_BP),nBlockDim>>>                  &
                    (val_iMesh%iCalc_Poin_d, val_iMesh%kSate_Poin_d,val_iMesh%iSate_Poin_d, &
                    val_iMesh%iCoef_Sate_d, val_iMesh%iCoor_Poin_d,val_iMesh%iRadu_Poin_d,  &
                    val_iMesh%kBelv_Poin_d, val_iMesh%iBelv_Poin_d, val_iMesh%iCoef_Belv_d, &
                    val_iSolu%soluvar_d, val_iSolu%primvar_d, val_iSolu%soluBelv_d,         &
                    val_iSolu%primBelv_d, val_iSolu%gradSolu_d, val_iSolu%limiter_d,        &
                    val_iSolu%soluUCRef_d, val_iParm%iGPUSParm, val_iMesh%nPoin,            &
                    val_iMesh%nSate, val_iMesh%nBelv,val_iMesh%nPoin,UPWIND_LIMITER         )

            call calFluxConvViscSour_UPW_Global<<<nGridDim(IP_BP),nBlockDim>>>              &
                    (val_iMesh%iCalc_Poin_d, val_iMesh%iIner_Poin_d, val_iMesh%kSate_Poin_d,&
                    val_iMesh%iSate_Poin_d, val_iMesh%iCoef_Sate_d, val_iMesh%iCoor_Poin_d, &
                    val_iMesh%kBelv_Poin_d, val_iMesh%iBelv_Poin_d, val_iMesh%iCoef_Belv_d, &
                    val_iMesh%iVelo_Poin_d, val_iMesh%iVelo_Belv_d, val_iMesh%iWDis_Poin_d, &
                    val_iSolu%soluvar_d, val_iSolu%primvar_d, val_iSolu%soluBelv_d,         &
                    val_iSolu%primBelv_d, val_iSolu%gradSolu_d, val_iSolu%limiter_d,        &
                    val_iSolu%pMatrix_d, val_iSolu%UnstTerm_d, val_iSolu%nodeAuxv_d,        &
                    val_iSolu%Res_All_d, val_iparm%iGPUSParm, val_iMesh%iMarkProp_d,        &
                    val_iMesh%nPoin, val_iMesh%nSate, val_iMesh%nBelv, val_iMesh%nMark,     &
                    val_iSolu%nNodeAuxv,val_iMesh%nPoin, is2ndOrder, UPWIND_SCHEME,         &
                    val_iParm%isWithPrec,val_iParm%isUnsteady,UPWIND_LIMITER                )

            do iClor=1,val_iMesh%nClor
            call loopLUSGS_Global<<<nGridDim(IP_BP),nBlockDim>>>                            &
                    (val_iMesh%iCalc_Poin_d, val_iMesh%iClor_Poin_d, val_iMesh%kSate_Poin_d,&
                    val_iMesh%iSate_Poin_d, val_iMesh%iCoef_Sate_d, val_iMesh%iVelo_Poin_d, &
                    val_iSolu%soluvar_d, val_iSolu%deltSolu_d, val_iSolu%SRconv_d,          &
                    val_iSolu%SRvisc_d, val_iSolu%SRcNode_d, val_iSolu%SRvNode_d,           &
                    val_iSolu%Res_All_d, val_iSolu%DT_d, val_iParm%iGPUSParm,               &
                    iClor, Dir_Forward, val_iMesh%nPoin, val_iMesh%nSate, val_iMesh%nPoin   )

            istat = cudaDeviceSynchronize()
            end do

            do iClor=val_iMesh%nClor,1,-1
            call loopLUSGS_Global<<<nGridDim(IP_BP),nBlockDim>>>                            &
                    (val_iMesh%iCalc_Poin_d, val_iMesh%iClor_Poin_d, val_iMesh%kSate_Poin_d,&
                    val_iMesh%iSate_Poin_d, val_iMesh%iCoef_Sate_d, val_iMesh%iVelo_Poin_d, &
                    val_iSolu%soluvar_d, val_iSolu%deltSolu_d, val_iSolu%SRconv_d,          &
                    val_iSolu%SRvisc_d, val_iSolu%SRcNode_d, val_iSolu%SRvNode_d,           &
                    val_iSolu%Res_All_d, val_iSolu%DT_d, val_iParm%iGPUSParm,               &
                    iClor, Dir_Backward, val_iMesh%nPoin, val_iMesh%nSate, val_iMesh%nPoin  )

            istat = cudaDeviceSynchronize()
            end do

            istat = cudaDeviceSynchronize()

            call updSoluAndDTCoef_Global<<<nGridDim(IP_BP),nBlockDim>>>                 &
                    (val_iMesh%iCalc_Poin_d, val_iSolu%soluvar_d, val_iSolu%primvar_d,  &
                    val_iSolu%soluOld_d, val_iSolu%deltSolu_d, val_iSolu%DTCoef_d,      &
                    val_iSolu%nodeAuxv_d, val_iParm%iGPUSParm, val_iMesh%nPoin,         &
                    val_iSolu%nNodeAuxv, val_iMesh%nPoin                                )

            call bldBC_Global<<<nGridDim(IP_BB), nBlockDim>>>                               &
                    (val_iMesh%iCalc_Poin_d, val_iMesh%iBelvProp_d, val_iMesh%iMarkProp_d,  &
                    val_iMesh%iWDis_Poin_d, val_iMesh%iCoor_Poin_d, val_iMesh%iNvor_Belv_d, &
                    val_iMesh%iVelo_Poin_d, val_iMesh%iVelo_Belv_d,                         &
                    val_iSolu%soluvar_d, val_iSolu%primvar_d, val_iSolu%soluBelv_d,         &
                    val_iSolu%primBelv_d, val_iSolu%soluMark_d, val_iSolu%primMark_d,       &
                    val_iParm%iGPUSParm, val_iParm%isWithPrec, val_iMesh%nBelv,             &
                    val_iMesh%nMark, val_iMesh%nPoin, val_iMesh%nBelv, val_iParm%iIter      )

        else if(TIME_DISCRE_FLOW == _RUNGE_KUTTA_EXPLICIT) then
            call GPURealArrayCpy2D1_Global<<<nGridDim(IP_BP),nBlockDim>>>               &
                    (val_iSolu%soluvar_d, val_iSolu%soluOld_d,val_iMesh%nPoin, nSoluEx  )

            if(val_iParm%isWithPrec ) then
                call calPrecCoefMatx_Global<<<nGridDim(IP_BP), nBlockDim>>>             &
                        (val_iMesh%iCalc_Poin_d,val_iSolu%soluvar_d,val_iSolu%primvar_d,&
                        val_iSolu%preCoef_d, val_iSolu%pMatrix_d, val_iParm%iGPUSParm,  &
                        val_iMesh%nPoin, val_iMesh%nPoin, val_iParm%isWithPrec          )
            end if

            if(val_iParm%isUnstDual) then
                call calDualTerm_Global<<<nGridDim(IP_BP), nBlockDim>>>                 &
                        (val_iMesh%iCalc_Poin_d,val_iSolu%soluvar_d,val_iSolu%soluTimN_d,&
                        val_iSolu%soluTimX_d, val_iSolu%UnstTerm_d, val_iParm%iGPUSParm,&
                        val_iMesh%nPoin, nSoluEx, val_iMesh%nPoin                       )
            endif

            do iRK=1,4
                if(iRK == 1) then
                call calSpecRaduAndDeltTime_Global<<<nGridDim(IP_BP), nBlockDim>>>              &
                        (val_iMesh%iCalc_Poin_d, val_iMesh%kSate_Poin_d, val_iMesh%iSate_Poin_d,&
                        val_iMesh%iCoef_Sate_d, val_iMesh%kBelv_Poin_d, val_iMesh%iBelv_Poin_d, &
                        val_iMesh%iCoef_Belv_d, val_iMesh%iVelo_Poin_d, val_iMesh%iVelo_Belv_d, &
                        val_iSolu%primvar_d, val_iSolu%primBelv_d, val_iSolu%preCoef_d,         &
                        val_iSolu%SRconv_d, val_iSolu%SRvisc_d, val_iSolu%SRcNode_d,            &
                        val_iSolu%SRvNode_d, val_iSolu%SRcBelv_d, val_iSolu%SRvBelv_d,          &
                        val_iSolu%DT_d, val_iSolu%DTCoef_d, val_iParm%iGPUSParm,                &
                        val_iMesh%nPoin, val_iMesh%nSate, val_iMesh%nBelv,                      &
                        val_iMesh%nPoin, val_iParm%isWithPrec, val_iParm%isUnstDual             )

                call calSoluGradWithLimt_Global<<<nGridDim(IP_BP),nBlockDim>>>                  &
                        (val_iMesh%iCalc_Poin_d, val_iMesh%kSate_Poin_d,val_iMesh%iSate_Poin_d, &
                        val_iMesh%iCoef_Sate_d, val_iMesh%iCoor_Poin_d,val_iMesh%iRadu_Poin_d,  &
                        val_iMesh%kBelv_Poin_d, val_iMesh%iBelv_Poin_d, val_iMesh%iCoef_Belv_d, &
                        val_iSolu%soluvar_d, val_iSolu%primvar_d, val_iSolu%soluBelv_d,         &
                        val_iSolu%primBelv_d, val_iSolu%gradSolu_d, val_iSolu%limiter_d,        &
                        val_iSolu%soluUCRef_d, val_iParm%iGPUSParm, val_iMesh%nPoin,            &
                        val_iMesh%nSate, val_iMesh%nBelv,val_iMesh%nPoin,UPWIND_LIMITER         )
                endif

                call calFluxConvViscSour_UPW_Global<<<nGridDim(IP_BP),nBlockDim>>>              &
                        (val_iMesh%iCalc_Poin_d, val_iMesh%iIner_Poin_d, val_iMesh%kSate_Poin_d,&
                        val_iMesh%iSate_Poin_d, val_iMesh%iCoef_Sate_d, val_iMesh%iCoor_Poin_d, &
                        val_iMesh%kBelv_Poin_d, val_iMesh%iBelv_Poin_d, val_iMesh%iCoef_Belv_d, &
                        val_iMesh%iVelo_Poin_d, val_iMesh%iVelo_Belv_d, val_iMesh%iWDis_Poin_d, &
                        val_iSolu%soluvar_d, val_iSolu%primvar_d, val_iSolu%soluBelv_d,         &
                        val_iSolu%primBelv_d, val_iSolu%gradSolu_d, val_iSolu%limiter_d,        &
                        val_iSolu%pMatrix_d, val_iSolu%UnstTerm_d, val_iSolu%nodeAuxv_d,        &
                        val_iSolu%Res_All_d, val_iparm%iGPUSParm, val_iMesh%iMarkProp_d,        &
                        val_iMesh%nPoin, val_iMesh%nSate, val_iMesh%nBelv, val_iMesh%nMark,     &
                        val_iSolu%nNodeAuxv,val_iMesh%nPoin, is2ndOrder, UPWIND_SCHEME,         &
                        val_iParm%isWithPrec,val_iParm%isUnsteady,UPWIND_LIMITER                )

                call updSolu_RK_Global<<<nGridDim(IP_BP),nBlockDim>>>                       &
                        (val_iMesh%iCalc_Poin_d, val_iSolu%soluOld_d, val_iSolu%soluvar_d,  &
                        val_iSolu%primvar_d,  val_iSolu%nodeAuxv_d,val_iSolu%Res_All_d,     &
                        val_iSolu%DT_d, val_iSolu%DTCoef_d, val_iParm%iGPUSParm,            &
                        val_iMesh%nPoin, val_iSolu%nNodeAuxv,val_iMesh%nPoin, iRK           )

                call bldBC_Global<<<nGridDim(IP_BB), nBlockDim>>>                               &
                        (val_iMesh%iCalc_Poin_d, val_iMesh%iBelvProp_d, val_iMesh%iMarkProp_d,  &
                        val_iMesh%iWDis_Poin_d, val_iMesh%iCoor_Poin_d, val_iMesh%iNvor_Belv_d, &
                        val_iMesh%iVelo_Poin_d, val_iMesh%iVelo_Belv_d,                         &
                        val_iSolu%soluvar_d, val_iSolu%primvar_d, val_iSolu%soluBelv_d,         &
                        val_iSolu%primBelv_d, val_iSolu%soluMark_d, val_iSolu%primMark_d,       &
                        val_iParm%iGPUSParm, val_iParm%isWithPrec, val_iMesh%nBelv,             &
                        val_iMesh%nMark, val_iMesh%nPoin, val_iMesh%nBelv, val_iParm%iIter      )

                istat = cudaDeviceSynchronize()
            end do
        end if

    endsubroutine
    !
endmodule
!
