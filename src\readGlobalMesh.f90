

!---------------------------------------------
!
!---------------------------------------------
subroutine readGlobalMesh(val_path,val_meshName,val_iMesh)
    use mod_TypeDef_Mesh
    use mod_Interface_AllocateArray
    implicit none
    character(len=*),intent(in):: val_path
    character(len=*),intent(in):: val_meshName
    type(typ_GlobalMesh),intent(inout):: val_iMesh
    
    
    call readMesh_InfoFile(val_path,val_meshName,val_iMesh)
    
    call readMesh_MeshFile(val_path,val_meshName,val_iMesh)
    
    return
    
contains
    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine readMesh_InfoFile(val_path,val_meshName,val_iMesh)
        use mod_TypeDef_Mesh
        use mod_Config, only: MarkInfos
        use mod_PreDefine_IOPort
        implicit none
        character(len=*),intent(in):: val_path
        character(len=*),intent(in):: val_meshName
        type(typ_GlobalMesh),intent(inout):: val_iMesh
    
        integer:: i,j,ic,tempInt,istat
        logical:: isOk
        character(len=STRLEN):: tempStr,aLine,bLine
    
        logical:: isEqual_String
    
        open(ioPort_FULL,file=trim(val_path)//'/'//trim(val_meshName)//'.info',status='old')
            read(ioPort_FULL,*)
            read(ioPort_FULL,*) val_iMesh%nPoin,val_iMesh%nElem,val_iMesh%nBEle
            read(ioPort_FULL,*)
            !read(ioPort_FULL,*) val_iMesh%nDime,val_iMesh%nDoma,val_iMesh%nMark,val_iMesh%nRegn
            
            !======替换上一行读取，以支持滑移边界对
            read(ioPort_FULL,'(A100)') aLine
            
            if(len_trim(aLine) <= 24) then
                read(aLine,*) val_iMesh%nDime,val_iMesh%nDoma,val_iMesh%nMark,val_iMesh%nRegn
            else
                read(aLine,*) val_iMesh%nDime,val_iMesh%nDoma,val_iMesh%nMark,val_iMesh%nRegn,val_iMesh%nSlip
            endif
            !======替换上一行读取，以支持滑移边界对
        
            if(val_iMesh%nDime /= nDim) then
                write(ioPort_Out,*) 'nDim Error!'
                stop
            endif
        
            call allocateArray(val_iMesh%iCoor_Poin, nDim    , val_iMesh%nPoin)
            call allocateArray(val_iMesh%iElem     , nDim*4-4, val_iMesh%nElem)
            call allocateArray(val_iMesh%iBele     , nDim*2-2, val_iMesh%nBEle)
            call allocateArray(val_iMesh%iElemProp , val_iMesh%nElem, 1       )
        
            allocate(val_iMesh%iDoma(val_iMesh%nDoma))
            allocate(val_iMesh%iMark(val_iMesh%nMark))
            allocate(val_iMesh%iRegn(val_iMesh%nRegn))
        
            read(ioPort_FULL,*)
            do i=1,val_iMesh%nDoma
                read(ioPort_FULL,'(A4,I5,5X,A4,I10,5X,A)')tempStr,tempInt,val_iMesh%iDoma(i)%KSTR,val_iMesh%iDoma(i)%nElem,val_iMesh%iDoma(i)%NAME
            enddo

            ic = 0
            do i=1,val_iMesh%nDoma
                do j=1,val_iMesh%iDoma(i)%nElem
                    ic = ic+ 1
                    val_iMesh%iElemProp(ic,1) = i
                end do
            end do
        
            read(ioPort_FULL,*)
            do i=1,val_iMesh%nMark
                read(ioPort_FULL,'(A4,I5,5X,A4,I10,5X,A)')tempStr,tempInt,val_iMesh%iMark(i)%KSTR,val_iMesh%iMark(i)%nElem,val_iMesh%iMark(i)%NAME
                if(isEqual_String(val_iMesh%iMark(i)%KSTR,'WALL')) then
                    val_iMesh%iMark(i)%KIND = MARK_WALL
                elseif(isEqual_String(val_iMesh%iMark(i)%KSTR,'EULW')) then
                    val_iMesh%iMark(i)%KIND = MARK_EULW
                elseif(isEqual_String(val_iMesh%iMark(i)%KSTR,'MOVW')) then
                    val_iMesh%iMark(i)%KIND = MARK_MOVW
                elseif(isEqual_String(val_iMesh%iMark(i)%KSTR,'SYMM')) then
                    val_iMesh%iMark(i)%KIND = MARK_SYMM
                elseif(isEqual_String(val_iMesh%iMark(i)%KSTR,'SYMP')) then
                    val_iMesh%iMark(i)%KIND = MARK_SYMM
                elseif(isEqual_String(val_iMesh%iMark(i)%KSTR,'PRES')) then
                    val_iMesh%iMark(i)%KIND = MARK_PFAR
                elseif(isEqual_String(val_iMesh%iMark(i)%KSTR,'PFAR')) then
                    val_iMesh%iMark(i)%KIND = MARK_PFAR
                elseif(isEqual_String(val_iMesh%iMark(i)%KSTR,'ATTA')) then
                    val_iMesh%iMark(i)%KIND = MARK_ATTA
                elseif(isEqual_String(val_iMesh%iMark(i)%KSTR,'CYCL')) then
                    val_iMesh%iMark(i)%KIND = MARK_CPRD
                elseif(isEqual_String(val_iMesh%iMark(i)%KSTR,'PROD')) then
                    val_iMesh%iMark(i)%KIND = MARK_LPRD
                elseif(isEqual_String(val_iMesh%iMark(i)%KSTR,'OVST')) then
                    val_iMesh%iMark(i)%KIND = MARK_OVST

                elseif(isEqual_String(val_iMesh%iMark(i)%KSTR,'INLE')) then
                    val_iMesh%iMark(i)%KIND = MARK_INLE
                elseif(isEqual_String(val_iMesh%iMark(i)%KSTR,'INLM')) then
                    val_iMesh%iMark(i)%KIND = MARK_INLM

                elseif(isEqual_String(val_iMesh%iMark(i)%KSTR,'OUTL')) then
                    val_iMesh%iMark(i)%KIND = MARK_OUTL
                elseif(isEqual_String(val_iMesh%iMark(i)%KSTR,'OUTP')) then
                    val_iMesh%iMark(i)%KIND = MARK_OUTP
                elseif(isEqual_String(val_iMesh%iMark(i)%KSTR,'OUTM')) then
                    val_iMesh%iMark(i)%KIND = MARK_OUTM

                elseif(isEqual_String(val_iMesh%iMark(i)%KSTR,'UDBC')) then
                    val_iMesh%iMark(i)%KIND = MARK_UDBC
                else
                    val_iMesh%iMark(i)%KIND = MARK_NONE
                endif
            enddo

            do i=1,val_iMesh%nMark
                if(MarkInfos(i)%markID /= 0) then
                    val_iMesh%iMark(i)%KIND = MarkInfos(i)%markKD
                end if
            end do
        
            read(ioPort_FULL,*)
            do i=1,val_iMesh%nRegn
                read(ioPort_FULL,'(A4,3I5)') tempStr,tempInt,val_iMesh%iRegn(i)%nDoma,val_iMesh%iRegn(i)%nMark
            enddo
        
            !================新增滑移边界对
            if(val_iMesh%nSlip > 0) then
                allocate(val_iMesh%iSlip(val_iMesh%nSlip))
                
                read(ioPort_FULL,*)
                do i=1,val_iMesh%nSlip
                    read(ioPort_FULL,'(A4,2I5,5X,A4)') tempStr,val_iMesh%iSlip(i)%nMarkL,val_iMesh%iSlip(i)%nMarkR,val_iMesh%iSlip(i)%KSTR
                    
                    if(isEqual_String(val_iMesh%iSlip(i)%KSTR,'NONE')) then
                        val_iMesh%iSlip(i)%KIND = BCPAIR_NONE
                    elseif(isEqual_String(val_iMesh%iSlip(i)%KSTR,'SLIP')) then
                        val_iMesh%iSlip(i)%KIND = BCPAIR_SLIP
                    elseif(isEqual_String(val_iMesh%iSlip(i)%KSTR,'PRDL')) then
                        val_iMesh%iSlip(i)%KIND = BCPAIR_PRDL
                    elseif(isEqual_String(val_iMesh%iSlip(i)%KSTR,'PRDR')) then
                        val_iMesh%iSlip(i)%KIND = BCPAIR_PRDR
                    elseif(isEqual_String(val_iMesh%iSlip(i)%KSTR,'INFC')) then
                        val_iMesh%iSlip(i)%KIND = BCPAIR_INFC
                    else
                        val_iMesh%iSlip(i)%KIND = BCPAIR_NONE
                    endif
                    
                    allocate(val_iMesh%iSlip(i)%iMarkL(val_iMesh%iSlip(i)%nMarkL))
                    allocate(val_iMesh%iSlip(i)%iMarkR(val_iMesh%iSlip(i)%nMarkR))
                    
                    read(ioPort_FULL,*) val_iMesh%iSlip(i)%iMarkL
                    read(ioPort_FULL,*) val_iMesh%iSlip(i)%iMarkR
                enddo
            endif
            !================新增滑移边界对
            
        close(ioPort_FULL)
        
        !===========检查滑移边界对
        call chkMesh_MeshInfo(val_iMesh,isOk)
        
        if(.not.isOk) then
            write(ioPort_Out,*) 'chkMesh_MeshInfo not Ok.'
            stop
        endif
    
    endsubroutine
    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine chkMesh_MeshInfo(val_iMesh,val_isOk)
        use mod_TypeDef_Mesh
        use mod_PreDefine_IOPort
        use mod_Config
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        logical,intent(out):: val_isOk
        !
        integer:: i,j,markID,iCount
        integer:: iFlag_Mark(val_iMesh%nMark)
        integer:: iRegn_Mark(val_iMesh%nMark),RegnLD,RegnRD
        
        
        markID = 0
        do i=1,val_iMesh%nRegn
            do j=1,val_imesh%iRegn(i)%nMark
                markID = markID + 1
                iRegn_Mark(markID) = i
            enddo
        enddo
        
        
        iFlag_Mark(:) = 0
        do i=1,val_iMesh%nSlip
            markID = val_iMesh%iSlip(i)%iMarkL(1)
            regnLD = iRegn_Mark(markID)
            
            markID = val_iMesh%iSlip(i)%iMarkR(1)
            regnRD = iRegn_Mark(markID)
            
            if(regnLD == regnRD) then
                val_isOk = .false.
                return
            endif
                
            do j=1,val_iMesh%iSlip(i)%nMarkL
                markID = val_iMesh%iSlip(i)%iMarkL(j)
                
                if(iRegn_Mark(markID) /= regnLD) then
                    val_isOk = .false.
                    return
                endif
                
                if(iFlag_Mark(markID) /= 0) then
                    val_isOk = .false.
                    return
                endif
                
                if(val_iMesh%iMark(markID)%KIND /= MARK_ATTA) then
                    val_isOk = .false.
                    return
                endif
                
                iFlag_Mark(markID) = i
            enddo
            
            do j=1,val_iMesh%iSlip(i)%nMarkR
                markID = val_iMesh%iSlip(i)%iMarkR(j)
                
                if(iRegn_Mark(markID) /= regnRD) then
                    val_isOk = .false.
                    return
                endif
                
                if(iFlag_Mark(markID) /= 0) then
                    val_isOk = .false.
                    return
                endif
                
                if(val_iMesh%iMark(markID)%KIND /= MARK_ATTA) then
                    val_isOk = .false.
                    return
                endif
                
                iFlag_Mark(markID) = i
            enddo
        enddo
        
        val_isOk = .true.
        return
        
    endsubroutine
            
    
    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine readMesh_MeshFile(val_path,val_meshName,val_iMesh)
        use mod_TypeDef_Mesh
        use mod_PreDefine_IOPort
        use mod_Config
        implicit none
        character(len=*),intent(in):: val_path
        character(len=*),intent(in):: val_meshName
        type(typ_GlobalMesh),intent(inout):: val_iMesh
    
        real*8:: iPoinCoor(val_iMesh%nDime,val_iMesh%nPoin)
    
        integer:: i,j
    
        open(ioPort_FULL,file=trim(val_path)//'/'//trim(val_meshName)//'.mesh',FORM='unformatted',status='old')
            do i=1,val_iMesh%nPoin
                read(ioPort_FULL) (iPoinCoor(j,i),j=1,val_iMesh%nDime)
            enddo
            
            do i=1,val_iMesh%nDime
                val_iMesh%iCoor_Poin(i,:) = iPoinCoor(i,:)*GRID_SCALE_FACTOR(i)
            enddo
        
            read(ioPort_FULL) val_iMesh%iElem
        
            read(ioPort_FULL) val_iMesh%iBele
        
        close(ioPort_FULL)

    
    endsubroutine

    
    !---------------------------------------------
    !
    !---------------------------------------------
    logical function isEqual_String(val_str1,val_str2)
        implicit none
        character(len=*),intent(in):: val_str1
        character(len=*),intent(in):: val_str2
    
        character(len=len_trim(val_str1)):: str1
        character(len=len_trim(val_str2)):: str2
        integer:: i
    
        str1 = adjustl(val_str1)
        str2 = adjustl(val_str2)
    
        call toUpperCase(str1)
        call toUpperCase(str2)
    
        if(len_trim(str1) /= len_trim(str2)) then
            isEqual_String = .false. 
            return
        else
            do i=1,len_trim(str1)
                if(str1(i:i) /= str2(i:i)) then
                    isEqual_String = .false.
                    return
                endif
            enddo
        
            isEqual_String = .true.
            return
        endif
    
    endfunction

    
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine toUpperCase(val_string)
        implicit none
        character(len=*),intent(inout):: val_string
    
        integer:: i
        character::chr
    
        do i=1,len_trim(val_string)
            chr = val_string(i:i)
            if(chr >= 'a'.and.chr <= 'z') then
                val_string(i:i)= char(ichar(chr)+ichar('A')-ichar('a'))
            endif
        enddo
    
    endsubroutine
    !
    !
endsubroutine
!
