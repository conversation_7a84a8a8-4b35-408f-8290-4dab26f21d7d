
!---------------------------------------------
!参数选项预定义
!---------------------------------------------
module mod_Options
    !
    !---------------------------------------------
    !求解器定义
    !---------------------------------------------
    !Options of NUMERICAL_METHOD
    integer,parameter:: _NUMERICAL_NONE                 = 0     !未定义
    integer,parameter:: _NUMERICAL_FVM                  = 1     !格点格式有限体积法
    integer,parameter:: _NUMERICAL_MESHLESS             = 2     !无网格方法
    integer,parameter:: _NUMERICAL_FEM                  = 11    !有限元方法（不支持）
    integer,parameter:: _NUMERICAL_FDM                  = 12    !有限差分法（不支持）

    !Options of KIND_HARDWARE
    integer,parameter:: _CPU                            = 0     !CPU通用
    integer,parameter:: _GPU                            = 1     !GPU通用

    !Options of KIND_POINTREORDERING
    !integer,parameter:: _NONE                           = 0     !不排序
    integer,parameter:: _REORDERING_SIM                 = 1     !简单排序
    integer,parameter:: _REORDERING_LBL                 = 2     !逐层排序
    integer,parameter:: _REORDERING_SFC                 = 3     !SFC排序
    integer,parameter:: _REORDERING_KDT                 = 4     !k-d Tree排序
    integer,parameter:: _REORDERING_REC                 = 10    !重构排序

    !---------------------------------------------
    !Options of PHYSICAL_PROBLEM
    integer,parameter:: _NONE                           = 0     !未定义
    integer,parameter:: _EULER                          = 1     !Euler无粘流
    integer,parameter:: _NS                             = 2     !层流NS
    integer,parameter:: _TURB                           = 3     !湍流RANS
    integer,parameter:: _DES                            = 4     !混合RANS+LES
    integer,parameter:: _LES                            = 5     !大涡模拟
    integer,parameter:: _DNS                            = 6     !直接数值模拟

    !Options of KIND_TURB_MODEL
    !$$!integer,parameter:: _NONE                       = 0     !无
    integer,parameter:: _TURB_BL                        = 1     !BL零方程模型
    integer,parameter:: _TURB_SA                        = 2     !SA一方程模型
    integer,parameter:: _TURB_KE                        = 3     !KE两方程模型
    integer,parameter:: _TURB_KW                        = 4     !KW两方程模型
    integer,parameter:: _TURB_SST                       = 5     !SST两方程模型
    integer,parameter:: _TURB_SAS                       = 6
    integer,parameter:: _TURB_RSM                       = 7

    !Options of KIND_TURBSA
    integer,parameter:: _SA_STANDARD                    = 10    !
    integer,parameter:: _SA_NEG                         = 11    !SA一方
    integer,parameter:: _SA_NOFT2                       = 12    !SA一方
    integer,parameter:: _SA_NOFT2_S                     = 13    !SA一方

    !Options of KIND_TURBSST
    integer,parameter:: _SST_STANDARD                   = 20    !
    integer,parameter:: _SST_V                          = 21    !SA一方
    integer,parameter:: _SST_KL                         = 22    !SA一方
    integer,parameter:: _SST_2003                       = 23    !SA一方
    integer,parameter:: _SST_SUST                       = 24    !SA一方
    integer,parameter:: _SST_VSUST                      = 25    !SA一方
    integer,parameter:: _SST_RC                         = 26    !SA一方
    integer,parameter:: _SST_RC_HELLSTEN                = 27    !SA一方

    !Options of KIND_TURBRSM
    integer,parameter:: _RSM_WILCOX_W2006               = 60    !
    integer,parameter:: _RSM_V                          = 21    !SA一方
    integer,parameter:: _RSM_KL                         = 22    !SA一方
    integer,parameter:: _RSM_2003                       = 23    !SA一方
    integer,parameter:: _RSM_SUST                       = 24    !SA一方
    integer,parameter:: _RSM_VSUST                      = 25    !SA一方
    integer,parameter:: _RSM_RC                         = 26    !SA一方
    integer,parameter:: _RSM_RC_HELLSTEN                = 27    !SA一方

    !Options of IS_INCOMPRESSIBLE
    integer,parameter:: _NO                             = 0     !不带低速预处理
    integer,parameter:: _YES                            = 1     !带低速预处理

    !Options of RESTART_SOL
    !$$!integer,parameter:: _NO                         = 0     !非续算
    !$$!integer,parameter:: _YES                        = 1     !续算

    !Options of IS_DIMENSIONLESS
    !integer,parameter:: _NO                            = 0     !有量纲
    !integer,parameter:: _YES                           = 1     !无量纲

    !---------------------------------------------
    !Options of FLOW_PARMTYPE
    integer,parameter:: _NON_DIMENSIONAL                = 0     !来流为无量纲参数
    integer,parameter:: _DIMENSIONAL                    = 1     !来流为有量纲参数

    !---------------------------------------------
    !Options of FLOW_LVPLANE
    integer,parameter:: _XZ                             = 0
    integer,parameter:: _XY                             = 1


    !---------------------------------------------
    !Options of WRITE_CONVERTED_MESH
    !$$!integer,parameter:: _NO                         = 0     !无
    !$$!integer,parameter:: _YES                        = 1     !保存修正网格


    !---------------------------------------------
    !Options of SPACE_DISCRE_FLOW
    integer,parameter:: _JST                            = 0     !中心格式
    integer,parameter:: _UPW                            = 1     !迎风格式

    !Options of UPWIND_ORDER
    !$$!integer,parameter:: _NONE                       = 0     !无
    integer,parameter:: _ORDER_1ST                      = 1     !一阶
    integer,parameter:: _ORDER_2ND                      = 2     !二阶
    !Options of UPWIND_SCHEME
    !$$!integer,parameter:: _NONE                       = 0     !无
    integer,parameter:: _ROE                            = 1     !Roe格式
    integer,parameter:: _HLL                            = 11    !HLL格式系列
    integer,parameter:: _HLLC                           = 12    !HLLC格式系列
    integer,parameter:: _AUSM                           = 21    !AUSM格式系列
    integer,parameter:: _AUSMP                          = 22
    integer,parameter:: _AUSMDV                         = 23
    integer,parameter:: _AUSMPW                         = 24
    integer,parameter:: _AUSMPWP                        = 25
    integer,parameter:: _SLAU                           = 31
    integer,parameter:: _SLAU2                          = 32
    integer,parameter:: _SLAU2R1                        = 33
    integer,parameter:: _SLAU2R2                        = 34
    integer,parameter:: _SLAU2VL                        = 35

    !Options of UPWIND_LIMITER !https://www.cnblogs.com/li12242/p/5062354.html
    !$$!integer,parameter:: _NONE                       = 0     !无
    integer,parameter:: _MINMOD                         = 1     !
    integer,parameter:: _KOREN                          = 2
    integer,parameter:: _Superbee                       = 3
    integer,parameter:: _VanAlbada                      = 4
    integer,parameter:: _VANLEER                        = 5
    integer,parameter:: _VANLEERAVG                     = 6
    integer,parameter:: _VENKATAKRISHNAN                = 7


    !Options of TIME_DISCRE_FLOW and TIME_DISCRE_TURB
    integer,parameter:: _RUNGE_KUTTA_EXPLICIT           = 0     !RK迭代
    integer,parameter:: _LUSGS_IMPLICIT                 = 1     !LUSGS迭代
    integer,parameter:: _JACOB_IMPLICIT                 = 2     !Jacob迭代
    integer,parameter:: _MCGS_IMPLICIT                  = 10    !Multicolored GS隐式
    integer,parameter:: _MCLUSGS_IMPLICIT               = 11    !Multicolored LU-SGS格式

    !Options of CFL_RAMP_KIND
    !$$!integer,parameter:: _NONE                       = 0     !无
    integer,parameter:: _CFL_RAMP_LINEAR                = 1     !线性增长的
    integer,parameter:: _CFL_RAMP_EXP                   = 2     !指数增长的
    integer,parameter:: _CFL_RAMP_LSTAIR                = 3     !线性阶梯增长的
    integer,parameter:: _CFL_RAMP_ESTAIR                = 4     !指数阶梯增长的

    !---------------------------------------------
    !Options of UNSTEADY_SIMULATION
    !$$!integer,parameter:: _NONE                       = 0     !无
    integer,parameter:: _TIME_STEPPING                  = 1     !单时间步
    integer,parameter:: _DUAL_TIME_STEPPING             = 2     !双时间步
    integer,parameter:: _MULTI_STEADY_SIMULATION        = 3     !准定常多状态仿真

    !Options of MESH_DEFORM_METHOD
    !integer,parameter:: _NONE                           = 0     !无
    integer,parameter:: _DEFORMKIND_DWM                 = 1     !Distance weight method
    integer,parameter:: _DEFORMKIND_SM                  = 2     !Spring method
    integer,parameter:: _DEFORMKIND_DM                  = 3     !Delaunay mapping
    integer,parameter:: _DEFORMKIND_RBF                 = 4     !Radial basis function
    integer,parameter:: _DEFORMKIND_DMRBF               = 5     !DM+RBF
    integer,parameter:: _DEFORMKINF_CUSTOM              = 11    !自定义


    !Options of IMPLICIT_TREATMENT
    !$$!integer,parameter:: _NONE                       = 0     !无
    integer,parameter:: _SIMPLE_SR                      = 1     !简化的
    integer,parameter:: _ALL_JACOB                      = 2     !简化的
    !integer,parameter:: _LSR_RJACOB                     = 3     !简化的3
    !integer,parameter:: _LJACOB_RJACOB                  = 4     !全Jacob的
    !integer,parameter:: _MATRIX_FREE                    = 10    !matrix-free的

    !Options of RESU_FILETYPE
    integer,parameter:: _TEC                            = 0
    integer,parameter:: _VTK                            = 1
    integer,parameter:: _CGNS                           = 2

endmodule
