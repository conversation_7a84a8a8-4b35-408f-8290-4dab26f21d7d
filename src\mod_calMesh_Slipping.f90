!
!---------------------------------------------
!
!---------------------------------------------
module mod_calMesh_Slipping
    use mod_Interface_AllocateArray
    use mod_PreDefine_IOPort
    implicit none
    integer,parameter:: nAlteSize = 10
    integer,parameter:: maxNAlte  = 10 !maxNAlte >= nAlteSize
    !
contains
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine calMeshReform_Slipping(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_Interface_AllocateArray
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
        !
        integer:: i,j
        
        
        !---------------------------------------------
        if(val_iMesh%nSlip <= 0) then
            return
        endif
        
        
        !---------------------------------------------
        do i=1,val_iMesh%nSlip
            call getNPoinLR_BCPair(val_iMesh,       &
                        val_iMesh%iSlip(i)%nMarkL,  &
                        val_iMesh%iSlip(i)%iMarkL,  &
                        val_iMesh%iSlip(i)%nMarkR,  &
                        val_iMesh%iSlip(i)%iMarkR,  &
                        val_iMesh%iSlip(i)%nPoinL,  &
                        val_iMesh%iSlip(i)%nPoinR   )
            
            call allocateArray(val_iMesh%iSlip(i)%iPoinL, val_iMesh%iSlip(i)%nPoinL)
            call allocateArray(val_iMesh%iSlip(i)%iPoinR, val_iMesh%iSlip(i)%nPoinR)
            val_iMesh%iSlip(i)%iPoinL(:) = 0
            val_iMesh%iSlip(i)%iPoinR(:) = 0
            
            call getIPoinLR_BCPair(val_iMesh,       &
                        val_iMesh%iSlip(i)%nMarkL,  &
                        val_iMesh%iSlip(i)%iMarkL,  &
                        val_iMesh%iSlip(i)%nMarkR,  &
                        val_iMesh%iSlip(i)%iMarkR,  &
                        val_iMesh%iSlip(i)%nPoinL,  &
                        val_iMesh%iSlip(i)%nPoinR,  &
                        val_iMesh%iSlip(i)%iPoinL,  &
                        val_iMesh%iSlip(i)%iPoinR   )
            
            call calDivRegnForSlip(val_iMesh,       &
                        val_iMesh%iSlip(i)%nPoinL,  &
                        val_iMesh%iSlip(i)%iPoinL,  &
                        val_iMesh%iSlip(i)%iDivRegnL)
            
            call calDivRegnForSlip(val_iMesh,       &
                        val_iMesh%iSlip(i)%nPoinR,  &
                        val_iMesh%iSlip(i)%iPoinR,  &
                        val_iMesh%iSlip(i)%iDivRegnR)
               
            call allocateArray(val_iMesh%iSlip(i)%iAlteSate_PoinL, val_iMesh%iSlip(i)%nPoinL, nAlteSize)
            call allocateArray(val_iMesh%iSlip(i)%iAlteSate_PoinR, val_iMesh%iSlip(i)%nPoinR, nAlteSize)
            val_iMesh%iSlip(i)%iAlteSate_PoinL(:,:) = 0
            val_iMesh%iSlip(i)%iAlteSate_PoinR(:,:) = 0
            
            call calIAlteSate_Poin(val_iMesh,               &
                        val_iMesh%iSlip(i)%nPoinL,          &
                        val_iMesh%iSlip(i)%iPoinL,          &
                        val_iMesh%iSlip(i)%iDivRegnR,       &
                        val_iMesh%iSlip(i)%iAlteSate_PoinL  )
            
            call calIAlteSate_Poin(val_iMesh,               &
                        val_iMesh%iSlip(i)%nPoinR,          &
                        val_iMesh%iSlip(i)%iPoinR,          &
                        val_iMesh%iSlip(i)%iDivRegnL,       &
                        val_iMesh%iSlip(i)%iAlteSate_PoinR  )
            
        enddo
        
        call allocateArray(val_iMesh%iHostAlte_Poin, val_iMesh%nPoin, nAlteSize)
        
        call calIHostAlte_Poin(val_iMesh)
        
        call calIExteRegnSlip(val_iMesh, val_iMesh%iExteRegnSANO)
        
        call testIO_GlobalMesh_Valid(val_iMesh,'Slip')
        
        return
        
    endsubroutine
    !  
    subroutine getNPoinLR_BCPair(val_iMesh,val_nMarkL,  &
                val_iMarkL,val_nMarkR,val_iMarkR,       &
                val_nPoinL,val_nPoinR                   )
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        integer,intent(in):: val_nMarkL
        integer,intent(in):: val_iMarkL(val_nMarkL)
        integer,intent(in):: val_nMarkR
        integer,intent(in):: val_iMarkR(val_nMarkR)
        integer,intent(out):: val_nPoinL
        integer,intent(out):: val_nPoinR
        !
        integer:: i,j,iCount
        integer:: markID,poinID
        integer:: iFlag_Poin(val_iMesh%nPoin)
    
    
        !---------------------------------------------
        !---------------------------------------------
        iFlag_Poin(:) = 0
        do i=1,val_nMarkL
            markID = val_iMarkL(i)
            do j=1,val_iMesh%iMark(markID)%nPoin
                poinID = val_iMesh%iMark(markID)%iPoin(j)
            
                iFlag_Poin(poinID) = 1
            enddo
        enddo
    
        iCount = 0
        do i=1,val_iMesh%nPoin
            if(iFlag_Poin(i) == 1) then
                iCount = iCount + 1
            endif
        enddo
    
        val_nPoinL = iCount
    
    
        !---------------------------------------------
        !---------------------------------------------
        iFlag_Poin(:) = 0
        do i=1,val_nMarkR
            markID = val_iMarkR(i)
            do j=1,val_iMesh%iMark(markID)%nPoin
                poinID = val_iMesh%iMark(markID)%iPoin(j)
            
                iFlag_Poin(poinID) = 1
            enddo
        enddo
    
        iCount = 0
        do i=1,val_iMesh%nPoin
            if(iFlag_Poin(i) == 1) then
                iCount = iCount + 1
            endif
        enddo
    
        val_nPoinR = iCount

    endsubroutine
    !
    subroutine getIPoinLR_BCPair(val_iMesh,val_nMarkL,  &
                val_iMarkL,val_nMarkR,val_iMarkR,val_nPoinL,&
                val_nPoinR,val_iPoinL,val_iPoinR            )
        use mod_TypeDef_Mesh
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        integer,intent(in):: val_nMarkL
        integer,intent(in):: val_iMarkL(val_nMarkL)
        integer,intent(in):: val_nMarkR
        integer,intent(in):: val_iMarkR(val_nMarkR)
        integer,intent(in):: val_nPoinL
        integer,intent(in):: val_nPoinR
        integer,intent(out):: val_iPoinL(val_nPoinL)
        integer,intent(out):: val_iPoinR(val_nPoinR)
        !
        integer:: i,j,iCount
        integer:: markID,poinID
        integer:: iFlag_Poin(val_iMesh%nPoin)
    
    
        !---------------------------------------------
        iFlag_Poin(:) = 0
        do i=1,val_nMarkL
            markID = val_iMarkL(i)
            do j=1,val_iMesh%iMark(markID)%nPoin
                poinID = val_iMesh%iMark(markID)%iPoin(j)
            
                iFlag_Poin(poinID) = 1
            enddo
        enddo
    
        iCount = 0
        do i=1,val_iMesh%nPoin
            if(iFlag_Poin(i) == 1) then
                iCount = iCount + 1
            
                val_iPoinL(iCount) = i
            endif
        enddo
    
    
        !---------------------------------------------
        iFlag_Poin(:) = 0
        do i=1,val_nMarkR
            markID = val_iMarkR(i)
            do j=1,val_iMesh%iMark(markID)%nPoin
                poinID = val_iMesh%iMark(markID)%iPoin(j)
            
                iFlag_Poin(poinID) = 1
            enddo
        enddo
    
        iCount = 0
        do i=1,val_iMesh%nPoin
            if(iFlag_Poin(i) == 1) then
                iCount = iCount + 1
            
                val_iPoinR(iCount) = i
            endif
        enddo

                endsubroutine
    !          
    subroutine calDivRegnForSlip(val_iMesh,val_nPoin,   &
                val_iPoin,val_iDivRegn                  )
        use mod_TypeDef_Mesh
        use mod_TypeDef_Division
        use mod_calDivRegin
        use mod_Interface_AllocateArray
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        integer,intent(in):: val_nPoin
        integer,intent(in):: val_iPoin(val_nPoin)
        type(typ_DivRegin),intent(inout):: val_iDivRegn
        !
        integer:: i,j,iCount
        integer:: poinID,sateID,nPoin,nPoinPerDivBlock
        integer:: iTempFlag(val_iMesh%nPoin)
        logical:: isOk
        
        
        !---------------------------------------------
        iTempFlag(:) = 0
        
        do i=1,val_nPoin
            poinID = val_iPoin(i)
            do j=val_iMesh%kSate_Poin(poinID)+1,val_iMesh%kSate_Poin(poinID+1)
                sateID = val_iMesh%iSate_Poin(j,1)
                iTempFlag(sateID) = 1
            enddo
        enddo
        
        do i=1,val_nPoin
            poinID = val_iPoin(i)
            iTempFlag(poinID) = 0
        enddo
        
        iCount = 0
        do i=1,val_iMesh%nPoin
            if(iTempFlag(i) == 1) then
                iCount = iCount + 1
            endif
        enddo
        
        nPoin = iCount
        
        
        !---------------------------------------------
        val_iDivRegn%nPoin = nPoin
        
        call allocateArray(val_iDivRegn%iPoin, val_iDivRegn%nPoin)
        
        iCount = 0
        do i=1,val_iMesh%nPoin
            if(iTempFlag(i) == 1) then
                iCount = iCount + 1
                
                val_iDivRegn%iPoin(iCount) = i
            endif
        enddo
        
        
        !---------------------------------------------
        call getNPoinPerDivBlock(val_iDivRegn%nPoin, nPoinPerDivBlock)
        
        call initDivRegin(val_iDivRegn, val_iMesh%iCoor_Poin,&
                          val_nPoin, nPoinPerDivBlock       )
        
        call calDivRegin(val_iDivRegn, isOk)
        
        if(.not.isOk) then
            write(ioPort_Out,*) 'calDivRegin(val_iDivRegn, isOk) is not OK. in mod_calMesh_Slipping.f90'
            stop
        endif
    
    endsubroutine
    !      
    subroutine calIAlteSate_Poin(val_iMesh,val_nPoin,   &
                val_iPoin,val_iDivRegn,val_iAlteSate    )
        use mod_TypeDef_Mesh
        use mod_TypeDef_Division
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        integer,intent(in):: val_nPoin
        integer,intent(in):: val_iPoin(val_nPoin)
        type(typ_DivRegin),intent(in):: val_iDivRegn
        integer,intent(inout):: val_iAlteSate(val_nPoin,nAlteSize)
        !
        integer:: i,j
        integer:: iTempFlag(val_iMesh%nPoin)
        real(kind=REALLEN):: iDis,jDis,iCoor(nDim)
        integer:: nearID,nAlte,iAltePoin(maxNAlte)
        real(kind=REALLEN):: iAlteDist(maxNAlte)
        
        
        !---------------------------------------------
        iTempFlag(:) = 0
        do i=1,val_iDivRegn%nPoin
            iTempFlag(val_iDivRegn%iPoin(i)) = 1
        enddo
        
        
        !---------------------------------------------
        val_iAlteSate(:,:) = 0
        
        do i=1,val_nPoin
            iCoor(:) = val_iMesh%iCoor_Poin(:,val_iPoin(i))
            
            call getNearPoinIDInDivRegn(iCoor, val_iDivRegn, nearID, iDis)
                
            call getAltePoin(val_iMesh, iTempFlag, val_iPoin(i),&
                        nearID, iAltePoin, iAlteDist, nAlte     )
                
            val_iAlteSate(i,1) = min(9,nAlte)
                
            do j=1,min(9,nAlte)
                val_iAlteSate(i,j+1) = iAltePoin(j)
            enddo
        enddo
    
    endsubroutine
    !
    subroutine calIHostAlte_Poin(val_iMesh)
        use mod_TypeDef_Mesh
        use mod_Config
        use mod_Interface_AllocateArray
        implicit none
        type(typ_GlobalMesh),intent(inout):: val_iMesh
        !
        integer:: i,j,poinID
        
        
        val_iMesh%iHostAlte_Poin(:,:) = 0
        
        do i=1,val_iMesh%nSlip
            do j=1,val_iMesh%iSlip(i)%nPoinL
                poinID = val_iMesh%iSlip(i)%iPoinL(j)
                
                val_iMesh%iHostAlte_Poin(poinID,:) = val_iMesh%iSlip(i)%iAlteSate_PoinL(j,:)
            enddo
            
            do j=1,val_iMesh%iSlip(i)%nPoinR
                poinID = val_iMesh%iSlip(i)%iPoinR(j)
                
                val_iMesh%iHostAlte_Poin(poinID,:) = val_iMesh%iSlip(i)%iAlteSate_PoinR(j,:)
            enddo
        enddo
        
    endsubroutine
    !
    subroutine calIExteRegnSlip(val_iMesh, val_iExteRegn)
        use mod_TypeDef_Mesh
        use mod_Config
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        type(typ_ExteRegn),intent(inout):: val_iExteRegn
        !
        integer:: i,j
        integer:: sateID,nSate
        integer:: nPoinForReplace = 3
        
        
        !---------------------------------------------
        val_iExteRegn%KIND    = EXTE_SLIP
        val_iExteRegn%nPoin   = 0
        
        val_iExteRegn%nOrgnPoin = val_iMesh%nPoin
        call allocateArray(val_iExteRegn%kSate_Poin, val_iExteRegn%nOrgnPoin+1)
        
        
        !---------------------------------------------
        val_iExteRegn%kSate_Poin(:) = 0
        do i=1,val_iMesh%nPoin
            if(val_iMesh%iHostAlte_Poin(i,1) > 0) then
                nSate = min(val_iMesh%iHostAlte_Poin(i,1), nPoinForReplace)
                
                val_iExteRegn%kSate_Poin(i+1) = nSate
            endif
            val_iExteRegn%kSate_Poin(i+1) = val_iExteRegn%kSate_Poin(i) + &
                                            val_iExteRegn%kSate_Poin(i+1)
        enddo
        
        
        !---------------------------------------------
        nSate = val_iExteRegn%kSate_Poin(val_iMesh%nPoin+1)
        
        val_iExteRegn%nSate = nSate
        call allocateArray(val_iExteRegn%iSate_Poin, val_iExteRegn%nSate, 2)
        
        
        !---------------------------------------------
        do i=1,val_iMesh%nPoin
            if(val_iMesh%iHostAlte_Poin(i,1) > 0) then
                nSate = min(val_iMesh%iHostAlte_Poin(i,1), nPoinForReplace)
                
                do j=1,nSate
                    sateID = val_iExteRegn%kSate_Poin(i) + j
                    
                    val_iExteRegn%iSate_Poin(sateID,1) = val_iMesh%iHostAlte_Poin(i,j+1)
                    val_iExteRegn%iSate_Poin(sateID,2) = i
                enddo
            endif
        enddo
        
    endsubroutine
    !
    !================================
    !
    subroutine getNearPoinIDInDivRegn(val_iCoor,    &
                val_iDivRegn, val_nearID, val_iDis  )
        use mod_TypeDef_Division
        use mod_vectorAlgebra
        implicit none
        real(kind=REALLEN),intent(in):: val_iCoor(nDim)
        type(typ_DivRegin),intent(in):: val_iDivRegn
        integer,intent(out):: val_nearID
        real(kind=REALLEN),intent(out):: val_iDis
        !
        integer:: i,j
        integer:: nearBD,blockID,poinID,minID
        real(kind=REALLEN):: minDis,iDis,jDis
        
        
        !====OLD METHOD
        !real(kind=REALLEN):: iVector(nDim)
        !
        !do i=1,val_iDivRegn%nPoin
        !    poinID = val_iDivRegn%iPoin(i)
        !    
        !    iVector(:) = val_iDivRegn%iCoor_Orgn(i,:) - val_iCoor(:)
        !    
        !    call vector_length(nDim, iVector, iDis)
        !    
        !    if(i == 1) then
        !        minID = poinID
        !        minDis = iDis
        !    elseif(iDis < minDis) then
        !        minDis = iDis
        !        minID = poinID
        !    endif
        !enddo
        !
        !val_nearID = minID
        !val_iDis   = minDis
        !
        !return
        
        
        call getNearestBlockID(val_iCoor,val_iDivRegn, nearBD)
        call getIDisInBlock(val_iCoor, val_iDivRegn, nearBD, iDis, poinID)
        minDis = iDis
        minID  = poinID
        
        do i=1,val_iDivRegn%nFinalBlock
            blockID = val_iDivRegn%iFinalBlock(i)
            if(blockID == nearBD) cycle
                
            call getIDisToBlock(val_iCoor, val_iDivRegn, blockID, iDis)
                
            if(iDis < minDis) then
                call getIDisInBlock(val_iCoor, val_iDivRegn, blockID, jDis, poinID)
                    
                if(jDis < minDis) then
                    minDis = jDis
                    minID  = poinID
                endif
            endif
        enddo
            
        val_nearID = val_iDivRegn%iPoin(minID)
        val_iDis   = minDis
        
    endsubroutine
    !
    subroutine getAltePoin(val_iMesh,val_iFlag,     &
                val_poinID,val_nearID,val_iAltePoin,&
                val_iAlteDist,val_nAlte             )
        use mod_TypeDef_Mesh
        use mod_vectorAlgebra
        implicit none
        type(typ_GlobalMesh),intent(in):: val_iMesh
        integer,intent(in):: val_iFlag(val_iMesh%nPoin)
        integer,intent(in):: val_poinID
        integer,intent(in):: val_nearID
        integer,intent(out):: val_iAltePoin(maxNAlte)
        real(kind=REALLEN),intent(out):: val_iAlteDist(maxNAlte)
        integer,intent(out):: val_nAlte
        !
        integer:: i,j,k,iCount
        integer:: sateID,sateJD,sateKD
        integer:: iAltePoin(maxNAlte)
        real(kind=REALLEN):: iAlteDist(maxNAlte)
        real(kind=REALLEN):: iVector(nDim),iDis
        logical:: isOk
        
        
        iAltePoin(:) = 0
        iAlteDist(:) = 0.0
        
        
        iVector(:) = val_iMesh%iCoor_Poin(:,val_nearID) - &
                     val_iMesh%iCoor_Poin(:,val_poinID)
        call vector_length(nDim, iVector, iDis)
        
        iAltePoin(1) = val_nearID 
        iAlteDist(1) = iDis
        
        
        do i=val_iMesh%kSate_Poin(val_nearID)+1,val_iMesh%kSate_Poin(val_nearID+1)
            sateID = val_iMesh%iSate_Poin(i,1)
            if(val_iFlag(sateID) == 0) cycle
            
            call insertPoin(val_iMesh, val_poinID, sateID, isOk)
            
            do j=val_iMesh%kSate_Poin(sateID)+1,val_iMesh%kSate_Poin(sateID+1)
                sateJD = val_iMesh%iSate_Poin(j,1)
                if(val_iFlag(sateJD) == 0) cycle
                
                call insertPoin(val_iMesh, val_poinID, sateJD, isOk)
                
                do k=val_iMesh%kSate_Poin(sateJD)+1,val_iMesh%kSate_Poin(sateJD+1)
                    sateKD = val_iMesh%iSate_Poin(k,1)
                    if(val_iFlag(sateKD) == 0) cycle
                    
                    call insertPoin(val_iMesh, val_poinID, sateKD, isOk)
                enddo
            enddo
        enddo
        
            
        iCount = 0
        do i=1,maxNAlte
            if(iAltePoin(i) > 0) then
                iCount = iCount + 1
            endif
        enddo
        val_nAlte = iCount
        
        val_iAltePoin(:) = iAltePoin(:)
        val_iAlteDist(:) = iAlteDist(:)
        
        return
        
    contains
        !
        subroutine insertPoin(val_iMesh,val_poinID,val_instID,val_isOk)
            implicit none
            type(typ_GlobalMesh),intent(in):: val_iMesh
            integer,intent(in):: val_poinID
            integer,intent(in):: val_instID
            logical,intent(out):: val_isOk
            !
            integer:: ii,jj
            
            
            iVector(:) = val_iMesh%iCoor_Poin(:,val_poinID) - &
                         val_iMesh%iCoor_Poin(:,val_instID)
            
            call vector_length(nDim, iVector, iDis) 
            
            do ii=1,maxNAlte
                if(val_instID == iAltePoin(ii)) then
                    val_isOk = .false.
                    return
                endif
            enddo
            
            do ii=1,maxNAlte
                if(iAltePoin(ii) == 0) then
                    iAltePoin(ii) = val_instID
                    iAlteDist(ii) = iDis
                    
                    val_isOk = .true.
                    return
                elseif(iDis < iAlteDist(ii)) then
                    do jj=maxNAlte,ii+1,-1
                        iAltePoin(jj) = iAltePoin(jj-1)
                        iAlteDist(jj) = iAlteDist(jj-1)
                    enddo
                    
                    iAltePoin(ii) = val_instID
                    iAlteDist(ii) = iDis
                
                    val_isOk = .true.
                    return
                endif
            enddo
            
            val_isOk = .false.
            return
            
        endsubroutine
        !  
    endsubroutine
    !
endmodule
!
    
    
    
    
    
    
    
    
    
    
    
    
    