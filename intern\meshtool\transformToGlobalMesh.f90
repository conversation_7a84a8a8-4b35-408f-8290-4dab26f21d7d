!
subroutine transformToGlobalMesh
    use mod_Mesh
    implicit none
    integer:: i,j,k
    integer:: count
    
    count = 0
    do i=1,nMesh
        allocate(iInMesh(i)%globalPoinID(iInMesh(i)%nPoin))
        do j=1,iInMesh(i)%nPoin
            count = count+1
            iInMesh(i)%globalPoinID(j) = count
        enddo
    enddo
    
    count = 0
    do i=1,nMesh
        allocate(iInMesh(i)%globalDomaID(iInMesh(i)%nDoma))
        do j=1,iInMesh(i)%nDoma
            count = count+1
            iInMesh(i)%globalDomaID(j) = count
        enddo
    enddo
    
    count = 0
    do i=1,nMesh
        allocate(iInMesh(i)%globalMarkID(iInMesh(i)%nMark))
        do j=1,iInMesh(i)%nMark
            count = count+1
            iInMesh(i)%globalMarkID(j) = count
        enddo
    enddo
    
endsubroutine
!
