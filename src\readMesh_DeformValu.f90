!
subroutine readMesh_DeformValu
    use mod_PreDefine_Dimension
    use mod_PreDefine_IOPort
    use mod_Mesh_Deform
    use mod_WorkPath
    implicit none
    !
    integer:: i,j,istat,iFlag
    real(kind=REALLEN):: iDeltValueIn(nDim)
    character(len= 255):: aLine,bLine
    
    open(ioPort_FULL,file=trim(soluFullPath)//'/meshDeform_Valu.dat',status='OLD',iostat=istat)

    if(istat /= 0) then
        write(ioPort_Out,*) 'readMeshDeform_Valu: open File error!'
        stop
    endif
    
    do
        read(ioPort_FULL,'(A)',iostat=istat) aLine
        if(istat /= 0) exit
        
        bLine = adjustl(aLine)
        
        if(bLine(1:12) == '#DEFORMVALUE') then
            read(ioPort_FULL,*) 
            do i=1,nDeformZone
                read(ioPort_FULL,*) !'#ZONE ID'
                read(ioPort_FULL,*) iFlag
                
                if((iFlag /= 0).and.(iFlag /= 1)) then
                    write(ioPort_Out,*) 'ERROR!'
                    stop
                endif
                
                SELECT CASE (iDeformZone(i)%DeformKind)
                CASE (DEFORMKIND_TRANS)
                    if(iFlag == 0) then
                        read(ioPort_FULL,*) iDeltValueIn
                            
                        do j=1,iDeformZone(i)%nMark
                            iDeformZone(i)%iTransDist(1:nDim,j) = iDeltValueIn(1:nDim)
                        enddo
                    elseif(iFlag == 1) then
                        do j=1,iDeformZone(i)%nMark
                            read(ioPort_FULL,*) iDeltValueIn
                                
                            iDeformZone(i)%iTransDist(1:nDim,j) = iDeltValueIn(1:nDim)
                        enddo
                    endif
                    
                CASE (DEFORMKIND_ROTAT)
                    if(iFlag == 0) then
                        read(ioPort_FULL,*) iDeltValueIn(1)
                            
                        do j=1,iDeformZone(i)%nMark
                            iDeformZone(i)%iRotatAngle(j) = iDeltValueIn(1)
                        enddo
                    elseif(iFlag == 1) then
                        do j=1,iDeformZone(i)%nMark
                            read(ioPort_FULL,*) iDeltValueIn(1)
                                
                            iDeformZone(i)%iRotatAngle(j) = iDeltValueIn(1)
                        enddo
                    endif
                    
                CASE (DEFORMKIND_REGNT)
                        read(ioPort_FULL,*) iDeltValueIn
                            
                        do j=1,iDeformZone(i)%nMark
                            iDeformZone(i)%iTransDist(1:nDim,j) = iDeltValueIn(1:nDim)
                        enddo
                        
                CASE (DEFORMKIND_REGNR)
                        read(ioPort_FULL,*) iDeltValueIn(1)
                            
                        do j=1,iDeformZone(i)%nMark
                            iDeformZone(i)%iRotatAngle(j) = iDeltValueIn(1)
                        enddo
                        
                CASE (DEFORMKIND_SOFTT, DEFORMKIND_SOFTR)
                    read(ioPort_FULL,*) iDeformZone(i)%softDeformFileName
                    
                END SELECT
                
                read(ioPort_FULL,*) !Black Line
            enddo
            
            exit
        endif
    enddo
    
    close(ioPort_FULL)
    
    
endsubroutine
!
