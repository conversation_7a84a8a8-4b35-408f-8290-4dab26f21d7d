!
module mod_TypeDef_Parm
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    use mod_ParmIndex
    use cudafor
    implicit none
    !
    type Typ_AeroFM
        real(kind=REALLEN),allocatable:: surfaceForce_pres(:,:) !压力-表面力
        real(kind=REALLEN),allocatable:: surfaceMomnt_pres(:,:) !压力-力矩
        real(kind=REALLEN),allocatable:: surfaceCForce_pres(:,:) !压力-表面力
        real(kind=REALLEN),allocatable:: surfaceCMomnt_pres(:,:) !压力矩系数
        !
        real(kind=REALLEN),allocatable:: surfaceForce_visc(:,:) !粘性力-表面力
        real(kind=REALLEN),allocatable:: surfaceMomnt_visc(:,:) !粘性力-力矩
        real(kind=REALLEN),allocatable:: surfaceCForce_visc(:,:) !粘性力-表面力
        real(kind=REALLEN),allocatable:: surfaceCMomnt_visc(:,:) !粘性力矩系数
        !
        real(kind=REALLEN),allocatable:: surfaceForce_total(:,:) !总力-表面力
        real(kind=REALLEN),allocatable:: surfaceMomnt_total(:,:) !总力-力矩
        real(kind=REALLEN),allocatable:: surfaceCForce_total(:,:) !总力-表面力
        real(kind=REALLEN),allocatable:: surfaceCMomnt_total(:,:) !总力矩系数
        !
        real(kind=REALLEN):: totalForce(nDim) !总力-表面力
        real(kind=REALLEN):: totalMomnt(nDim) !总力-力矩
        real(kind=REALLEN):: totalCForce(nDim) !总力-表面力
        real(kind=REALLEN):: totalCMomnt(nDim) !总力矩系数
    endtype
    !
    type:: typ_Parm
        !---------------------------------------------
        !解算器分支控制类
        logical:: isMeshMove    = .false.
        logical:: isViscFlow    = .false.
        logical:: isTurbFlow    = .false.
        logical:: isImplicit    = .false.
        logical:: isLocalTim    = .true.
        logical:: isWithPrec    = .false.
        logical:: isUnsteady    = .false.
        logical:: isUnstUnit    = .false.
        logical:: isUnstDual    = .false.
        logical:: isDualTime    = .false.
        logical:: isNeedExch    = .false.
        integer:: nTurbVar      = 0
        !
        !---------------------------------------------
        !Solution parameters
        real(kind=REALLEN)       ,allocatable:: iHostParm(:) !nTotalParm
        real(kind=REALLEN),device,allocatable:: iGPUSParm(:) !nTotalParm
        !
        !real(kind=REALLEN)       ,allocatable:: soluMark(:,:)
        !real(kind=REALLEN),device,allocatable:: soluMark_d(:,:)
        !real(kind=REALLEN)       ,allocatable:: primMark(:,:)
        !real(kind=REALLEN),device,allocatable:: primMark_d(:,:)
        
        !---------------------------------------------
        !Iteration parameters
        integer:: nTotalIter                = 1
        integer:: nIter_Steady              = 0
        integer:: nUnstOuterIter            = 0
        integer:: nUnstInnerIter            = 0
        integer:: iIter                     = 0
        integer:: iOuterIterBegin           = 0
        integer:: iInnerIterBegin           = 1
        integer:: iCurrOuterIter            = 1
        integer:: iCurrInnerIter            = 1
        integer:: nMCIter                   = 10
        integer:: nIterHG                   = 100
        integer:: nIterFMAvg                = 1

        !
        real(kind=REALLEN):: iTimeBegin     = 0.0
        real(kind=REALLEN):: iTimeFinal     = 1.0
        real(kind=REALLEN):: iTimeDelta     = 2.0
        real(kind=REALLEN):: iTimeCurrt     = 0.0
        !
        integer:: nIter_ResultSaveBgn       = 0
        integer:: nIter_ResultSave          = 1
        integer:: nIter_ResErrCalc          = 1
        integer:: nIter_ResErrShow          = 1
        !
        integer:: nResErrVar                = 0
        logical:: isResErrRedCalculated     = .false.
        real(kind=8)       ,allocatable:: ResErrLim(:)
        real(kind=8)       ,allocatable:: ResErrRef(:)
        real(kind=8)       ,allocatable:: ResErrAbs(:)
        real(kind=8)       ,allocatable:: ResErrMax(:)
        real(kind=8)       ,allocatable:: ResErrLog(:)
        real(kind=8),device,allocatable:: ResErr_d(:)
        real(kind=8),device,allocatable:: ResErrMax_d(:)
        !
        integer:: nMark
        logical,allocatable:: isMarkWall(:)
        real(kind=REALLEN):: aeroFM_RefQ
        real(kind=REALLEN):: aeroFM_RefS
        real(kind=REALLEN):: aeroFM_RefL
        real(kind=REALLEN):: aeroFM_RefO(nDim)
        logical,allocatable:: aeroFM_MarkWallFlag(:)
        type(Typ_AeroFM):: currAeroFM
        type(Typ_AeroFM):: avgAeroFM
        type(Typ_AeroFM),allocatable:: aeroFMHist(:) !Dim(nTotalIter)
        !
        !---------------------------------------------
        character(len=STRLEN):: ConsVarNames = ''
        character(len=STRLEN):: PrimVarNames = ''
        character(len=STRLEN):: TurbVarNames = ''
        !
        !---------------------------------------------
        !Time measurement
        real(kind=4):: iCPUTimeBegin
        real(kind=4):: iCPUTimeFinal
        real(kind=4):: nSecondPerIter
        real(kind=4):: nIterPerSecond
    endtype
    !
endmodule
!
