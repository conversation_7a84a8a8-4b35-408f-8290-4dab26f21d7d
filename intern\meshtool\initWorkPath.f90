!
subroutine initWorkPath
    use mod_Mesh
    implicit none
    integer:: i
    
    integer:: commandCount
    character(len=255):: argvi
    
    commandCount = command_argument_count()
    
    !open(44,file='arg.dat')
    !    do i=1,commandCount
    !        call GET_COMMAND_ARGUMENT(i,argvi)
    !        write(44,*) argvi
    !    enddo
    !close(44)
    !pause
    
    if(commandCount < 5) then
        open(22,file='mesh.trn',status='old')
            read(22,*) workDir
        
            read(22,*) nDime
            read(22,*) nMesh
            allocate(iMeshNameList(nMesh))
        
            do i=1,nMesh
                read(22,*) iMeshNameList(i)
            enddo
        
            read(22,*) goal
        
        close(22)
    else
        call GET_COMMAND_ARGUMENT(1,argvi)
        workDir = trim(argvi)
        
        call GET_COMMAND_ARGUMENT(2,argvi)
        read(argvi,*) nDime
        
        call GET_COMMAND_ARGUMENT(3,argvi)
        read(argvi,*) nMesh
        allocate(iMeshNameList(nMesh))
        
        do i=1,nMesh
            call GET_COMMAND_ARGUMENT(i+3,argvi)
            iMeshNameList(i) = trim(argvi)
        enddo
        
        call GET_COMMAND_ARGUMENT(nMesh+4,argvi)
        goal = trim(argvi)
        
    endif
    
    write(*,*) 'The working dir is: '
    write(*,*) '    ',trim(workDir)
    write(*,*) 'nDime =',nDime
    write(*,*) 'nMesh =',nMesh
    write(*,*) 'The input mesh name list :'
    do i=1,nMesh
        write(*,*) '    ',trim(iMeshNameList(i))
    enddo
    write(*,*) 'The out mesh goal is :'
    write(*,*) '    ',trim(goal)
    write(*,*) 
    
endsubroutine
!
