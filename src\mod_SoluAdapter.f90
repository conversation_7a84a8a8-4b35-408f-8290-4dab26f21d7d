module mod_SoluAdapter
    use mod_PreDefine_IOPort
    use mod_PreDefine_Dimension
    use mod_Config, only: PHYSICAL_PROBLEM, KIND_TURB_MODEL
    use mod_Options
    !
contains
    !
    subroutine setSoluParm_Turb(val_iParm)
        use mod_TypeDef_Parm
        use mod_Config, only: KIND_TURB_MODEL
        use mod_Options
        !use mod_SoluTurbBL, only: setTurbModelConstants_TurbBL
        use mod_SoluTurbSA, only: setTurbModelConstants_TurbSA
        use mod_SoluTurbSST, only: setTurbModelConstants_TurbSST
        implicit none
        type(typ_Parm),intent(inout):: val_iParm

        if(val_iParm%isTurbFlow) then
            if(KIND_TURB_MODEL == _TURB_BL) then
            !    call setTurbModelConstants_TurbBL(val_iParm)
            elseif(KIND_TURB_MODEL == _TURB_SA) then
                call setTurbModelConstants_TurbSA(val_iParm)
            elseif(KIND_TURB_MODEL == _TURB_SST) then
                call setTurbModelConstants_TurbSST(val_iParm)
            end if
        end if

    end subroutine setSoluParm_Turb
    !
    subroutine soluMalloc_TurbModel(val_iSolu)
        use mod_TypeDef_Solu
        use mod_SoluTurbSA, only: soluMalloc_TurbSA
        use mod_SoluTurbSST, only: soluMalloc_TurbSST
        implicit none
        type(typ_Solu),intent(inout):: val_iSolu

        if(PHYSICAL_PROBLEM == _EULER) then
            ! do nothing
        elseif(PHYSICAL_PROBLEM == _NS) then
            ! do nothing
        elseif(PHYSICAL_PROBLEM == _TURB) then
            select case(KIND_TURB_MODEL)
            case(_TURB_SA)
                call soluMalloc_TurbSA(val_iSolu)
            case(_TURB_SST)
                call soluMalloc_TurbSST(val_iSolu)
            case default
                write(ioPort_Out,*) 'Error of KIND_TURB_MODEL'
                stop
            end select
        endif

    endsubroutine
    !
    subroutine updFlowInftyInSolu(val_iParm, val_iSolu)
        use mod_TypeDef_Parm
        use mod_TypeDef_Solu
        use mod_SoluEuler, only: updFlowInftyInSolu_Euler
        use mod_SoluLamNS, only: updFlowInftyInSolu_LamNS
        use mod_SoluTurbSA, only: updFlowInftyInSolu_TurbSA
        use mod_SoluTurbSST, only: updFlowInftyInSolu_TurbSST
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Solu),intent(inout):: val_iSolu

        if(PHYSICAL_PROBLEM == _EULER) then
            call updFlowInftyInSolu_Euler(val_iParm, val_iSolu)
        elseif(PHYSICAL_PROBLEM == _NS) then
            call updFlowInftyInSolu_LamNS(val_iParm, val_iSolu)
        elseif(PHYSICAL_PROBLEM == _TURB) then
            select case(KIND_TURB_MODEL)
            case(_TURB_SA)
                call updFlowInftyInSolu_TurbSA(val_iParm, val_iSolu)
            case(_TURB_SST)
                call updFlowInftyInSolu_TurbSST(val_iParm, val_iSolu)
            case default
                write(ioPort_Out,*) 'Error of KIND_TURB_MODEL'
                stop
            end select
        endif

    endsubroutine
    !
    subroutine updMarkSoluAndPrim(val_iParm,val_iMesh,val_iSolu,val_iStep)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_Config, only: MarkInfos
        use mod_SoluEuler, only: getMarkSoluAndPrim_Euler
        use mod_SoluLamNS, only: getMarkSoluAndPrim_LamNS
        use mod_SoluTurbSA, only: getMarkSoluAndPrim_TurbSA
        use mod_SoluTurbSST, only: getMarkSoluAndPrim_TurbSST
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(in   ):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        integer       ,intent(in   ):: val_iStep
        !
        integer:: i

        do i=1,val_iMesh%nMark
            if(PHYSICAL_PROBLEM == _EULER) then
                call getMarkSoluAndPrim_Euler(val_iParm, val_iSolu, MarkInfos(i), val_iStep, &
                        val_iSolu%soluMark(i,:),val_iSolu%primMark(i,:))
            elseif(PHYSICAL_PROBLEM == _NS) then
                call getMarkSoluAndPrim_LamNS(val_iParm,val_iSolu,MarkInfos(i), val_iStep, &
                                    val_iSolu%soluMark(i,:),val_iSolu%primMark(i,:))
            elseif(PHYSICAL_PROBLEM == _TURB) then
                select case(KIND_TURB_MODEL)
                case(_TURB_SA)
                    call getMarkSoluAndPrim_TurbSA(val_iParm,val_iSolu,MarkInfos(i), val_iStep, &
                            val_iSolu%soluMark(i,:),val_iSolu%primMark(i,:))
                case(_TURB_SST)
                    call getMarkSoluAndPrim_TurbSST(val_iParm,val_iSolu,MarkInfos(i), val_iStep, &
                                        val_iSolu%soluMark(i,:),val_iSolu%primMark(i,:))
                case default
                    write(ioPort_Out,*) 'Error of KIND_TURB_MODEL'
                    stop
                end select
            endif
        enddo

    endsubroutine
    !
    subroutine updFieldWithBCond(val_iParm, val_iMesh, val_iSolu)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_SoluEuler, only: updFieldWithBC_Euler
        use mod_SoluLamNS, only: updFieldWithBC_LamNS
        use mod_SoluTurbSA, only: updFieldWithBC_TurbSA
        use mod_SoluTurbSST, only: updFieldWithBC_TurbSST
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu


        if(PHYSICAL_PROBLEM == _EULER) then
            call updFieldWithBC_Euler(val_iParm, val_iMesh, val_iSolu)
        elseif(PHYSICAL_PROBLEM == _NS) then
            call updFieldWithBC_LamNS(val_iParm, val_iMesh, val_iSolu)
        elseif(PHYSICAL_PROBLEM == _TURB) then
            select case(KIND_TURB_MODEL)
            case(_TURB_SA)
                call updFieldWithBC_TurbSA(val_iParm, val_iMesh, val_iSolu)
            case(_TURB_SST)
                call updFieldWithBC_TurbSST(val_iParm, val_iMesh, val_iSolu)
            case default
                write(ioPort_Out,*) 'Error of KIND_TURB_MODEL'
                stop
            end select
        endif

    endsubroutine
    !
    subroutine SaveSolu(val_iParm, val_iMesh, val_iSolu, val_iIter)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_SoluIO, only: IO_SaveSolu
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        integer       ,intent(in   ):: val_iIter

        write(ioPort_OUT,"(1X,A,I8)") 'Saving......'

        call IO_SaveSolu(val_iParm, val_iMesh, val_iSolu, val_iIter)

    endsubroutine SaveSolu
    !
    subroutine RunOneIteration_UPW_GPU(val_iParm, val_iMesh, val_iSolu)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_SoluEuler, only: RunOneIteration_UPW_GPU_Euler
        use mod_SoluLamNS, only: RunOneIteration_UPW_GPU_LamNS
        use mod_SoluTurbSA, only: RunOneIteration_UPW_GPU_TurbSA
        use mod_SoluTurbSST, only: RunOneIteration_UPW_GPU_TurbSST
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu

        if(PHYSICAL_PROBLEM == _EULER) then
            call RunOneIteration_UPW_GPU_Euler(val_iParm, val_iMesh, val_iSolu)
        elseif(PHYSICAL_PROBLEM == _NS) then
            call RunOneIteration_UPW_GPU_LamNS(val_iParm, val_iMesh, val_iSolu)
        elseif(PHYSICAL_PROBLEM == _TURB) then
            select case(KIND_TURB_MODEL)
            case(_TURB_SA)
                call RunOneIteration_UPW_GPU_TurbSA(val_iParm, val_iMesh, val_iSolu)
            case(_TURB_SST)
                call RunOneIteration_UPW_GPU_TurbSST(val_iParm, val_iMesh, val_iSolu)
            case default
                write(ioPort_Out,*) 'Error of KIND_TURB_MODEL'
                stop
            end select
        endif

    endsubroutine
    !
    subroutine CalResErrAndSave(val_iParm, val_iMesh, val_iSolu,val_iInnerIter, val_isNAN)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_Interface_ArrayNAN
        use mod_Config, only: KIND_HARDWARE
        use mod_GetSoluParm, only: getSize_SoluPrim
        use mod_calResErr, only: calResErr
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        integer       ,intent(in   ):: val_iInnerIter
        logical       ,intent(inout):: val_isNAN
        !
        integer:: nSoluX,nPrimX


        call getSize_SoluPrim(nSoluX, nPrimX)

        call calResErr(val_iParm, val_iMesh, val_iSolu, nSoluX, val_iParm%iIter, val_isNAN)

        write(ioPort_RES,'(I8,6E15.7)') val_iParm%iIter, val_iParm%ResErrLog(:)

        if(mod(val_iInnerIter,val_iParm%nIter_ResErrShow) == 0) then
            write(ioPort_Out,'(I8,E15.5, 6F15.10)') val_iInnerIter, &
                    val_iParm%iHostParm(IP_CFLCurt), val_iParm%ResErrLog(:)
        endif

        if(val_isNAN) then
            write(ioPort_OUT,*) 'The Residual Error is NAN.'
        !    stop
        end if

    end subroutine CalResErrAndSave
    !
    subroutine CalAeroFMAndSave(val_iParm, val_iMesh, val_iSolu, val_iIter)
        use mod_TypeDef_Parm
        use mod_TypeDef_Mesh
        use mod_TypeDef_Solu
        use mod_calAeroFM, only: calAeroFM
        use mod_HistoryIO, only: History_DetailedAeroFMFile_WriteData
        implicit none
        type(typ_Parm),intent(inout):: val_iParm
        type(typ_Mesh),intent(inout):: val_iMesh
        type(typ_Solu),intent(inout):: val_iSolu
        integer       ,intent(in   ):: val_iIter


        call calAeroFM(val_iMesh, val_iSolu, val_iParm, val_iParm%iIter, val_iParm%isViscFlow )

        write(ioPort_RES+1,'(I8,6E15.7)') val_iParm%iIter, &
                val_iParm%currAeroFM%totalCForce(1:nDim),  &
                val_iParm%currAeroFM%totalCMomnt(1:2*nDim-3)

        call History_DetailedAeroFMFile_WriteData(ioPort_RES+2, val_iParm%iIter, &
                val_iParm%nMark, val_iParm%isViscFlow, val_iParm%currAeroFM      )

    end subroutine CalAeroFMAndSave
    !
end module mod_SoluAdapter