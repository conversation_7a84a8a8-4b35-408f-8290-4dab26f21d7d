! 
module mod_strOfNumber
    implicit none
    !
contains
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    character(len=2) function strTwo_Number(val_iNum)
        implicit none
        integer,intent(in):: val_iNum
        !
        integer:: i
        character(len=2):: iStr
    
        write(iStr,"(I2)") val_iNum
    
        do i=1,2
            if(iStr(i:i) == ' ') then
                iStr(i:i) = '0'
            endif
        enddo
    
        strTwo_Number = iStr
    
    endfunction
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    character(len=5) function strFiv_Number(val_iNum)
        implicit none
        integer,intent(in):: val_iNum
        !
        integer:: i
        character(len=5):: iStr
    
        write(iStr,"(I5)") val_iNum
    
        do i=1,5
            if(iStr(i:i) == ' ') then
                iStr(i:i) = '0'
            endif
        enddo
    
        strFiv_Number = iStr
    
    endfunction
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    character(len=6) function strSix_Number(val_iNum)
        implicit none
        integer,intent(in):: val_iNum
        !
        integer:: i
        character(len=6):: iStr
    
        write(iStr,"(I6)") val_iNum
    
        do i=1,6
            if(iStr(i:i) == ' ') then
                iStr(i:i) = '0'
            endif
        enddo
    
        strSix_Number = iStr
    
    endfunction
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    character(len=8) function strEight_Number(val_iNum)
        implicit none
        integer,intent(in):: val_iNum
        !
        integer:: i
        character(len=8):: iStr
    
        write(iStr,"(I6)") val_iNum
    
        do i=1,8
            if(iStr(i:i) == ' ') then
                iStr(i:i) = '0'
            endif
        enddo
    
        strEight_Number = iStr
    
    endfunction
    !
    !---------------------------------------------
    !
    !---------------------------------------------
    character(len=10) function strTen_Number(val_iNum)
        implicit none
        integer,intent(in):: val_iNum
        !
        integer:: i
        character(len=10):: iStr
    
        write(iStr,"(I10)") val_iNum
    
        do i=1,10
            if(iStr(i:i) == ' ') then
                iStr(i:i) = '0'
            endif
        enddo
    
        strTen_Number = iStr
    
    endfunction
    !
endmodule
!
    
    