
subroutine calMeshUpdating(val_iParm,val_iMesh,val_iIter)
    use mod_Project
    use mod_Config
    use mod_WorkPath
    use mod_PartMeshIO
    use mod_MPIEnvironment
    !use mod_MPI_Exch
    implicit none
    type(typ_Parm),intent(in   ):: val_iParm
    type(typ_Mesh),intent(inout):: val_iMesh
    integer,intent(in):: val_iIter
        
        
    !---------------------------------------------
    if(GRID_MOVEMENT == _NO) return
        
        
    !---------------------------------------------
    if(myID == MASTER_NODE) then
        call calGlobalMesh_Deform(iGlobalMesh, val_iIter)
                
        call updPartMesh(val_iIter)
    endif
    
    !call mimd_Sync
        
    !---------------------------------------------
    call updLocalMesh(val_iMesh, val_iIter)
    
    call updGPUMesh(val_iMesh)
        
    write(ioPort_Out,"(A,I4,A,I4,A,10I8)") 'iIter =',val_iIter, &
                                          '  MyID =',myID,':',  &
                                        val_iMesh%iExch(:)%nPoin
    
    !---------------------------------------------
        
endsubroutine
!

