!
!---------------------------------------------
!
!---------------------------------------------
subroutine getNPoinPerDivBlock(val_nPoin, val_nPoinPerBlock)
    implicit none
    integer,intent(in):: val_nPoin
    integer,intent(out):: val_nPoinPerBlock
    
    val_nPoinPerBlock = ceiling(sqrt(val_nPoin+0.0))
    
endsubroutine
!
!---------------------------------------------
!
!---------------------------------------------
subroutine getNearestBlockID(val_iCoor,val_iDivRegin,val_blockID)
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    use mod_TypeDef_Division
    implicit none
    real(kind=REALLEN),intent(in):: val_iCoor(nDim)
    type(typ_DivRegin),intent(in):: val_iDivRegin
    integer,intent(inout):: val_blockID
    !
    integer:: i
    integer:: iID,iDim
    
    
    iID = 1
    do i=1,val_iDivRegin%nDivLevel
        iDim = val_iDivRegin%iBlock(iID)%iDivDim
        
        if(.not.val_iDivRegin%iBlock(iID)%isNeedDiv) exit
        
        if(val_iCoor(iDim) <= val_iDivRegin%iBlock(iID)%iDivValue) then
            iID = val_iDivRegin%iBlock(iID)%sonID(1)
        else
            iID = val_iDivRegin%iBlock(iID)%sonID(2)
        endif
        
    enddo
    
    val_blockID = iID
    !
endsubroutine
! 
!---------------------------------------------
!
!---------------------------------------------
subroutine getIDisInBlock(val_iCoor,val_iDivRegin,val_blockID,val_iDis,val_poinID)
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    use mod_TypeDef_Division
    implicit none
    real(kind=REALLEN),intent(in):: val_iCoor(nDim)
    type(typ_DivRegin),intent(in):: val_iDivRegin
    integer,intent(in):: val_blockID
    real(kind=REALLEN),intent(inout):: val_iDis
    integer,intent(inout):: val_poinID
    !
    integer:: i,j
    integer:: poinID,minID
    real(kind=REALLEN):: iDis,minDis
    real(kind=REALLEN):: dCoor(nDim)
    
    minDis = 1.0E10
    minID  = 0
    
    do i=1,val_iDivRegin%iBlock(val_blockID)%nPoin
        poinID = val_iDivRegin%iBlock(val_blockID)%iPoin(i)
        
        dCoor(:) = val_iDivRegin%iCoor_Orgn(poinID,:) - val_iCoor(:)
        
        iDis = 0.0
        do j=1,nDim
            iDis = iDis + dCoor(j)*dCoor(j)
        enddo
        iDis = sqrt(iDis)
        
        if(iDis < minDis) then
            minDis = iDis
            minID  = poinID
        endif
    enddo
    
    val_iDis   = minDis
    val_poinID = minID
    
endsubroutine
!
!---------------------------------------------
!
!---------------------------------------------
subroutine getIDisToBlock(val_iCoor,val_iDivRegin,val_blockID,val_iDis)
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    use mod_TypeDef_Division
    implicit none
    real(kind=REALLEN),intent(in):: val_iCoor(nDim)
    type(typ_DivRegin),intent(in):: val_iDivRegin
    integer,intent(in):: val_blockID
    real(kind=REALLEN),intent(inout):: val_iDis
    !
    integer:: i
    real(kind=REALLEN):: iDis, dCoor(nDim)
    
    
    do i=1,nDim
        if(val_iCoor(i) < val_iDivRegin%iBlock(val_blockID)%binBnd(i)) then
            dCoor(i) = val_iDivRegin%iBlock(val_blockID)%binBnd(i) - val_iCoor(i)
        elseif(val_iCoor(i) > val_iDivRegin%iBlock(val_blockID)%baxBnd(i)) then
            dCoor(i) = val_iDivRegin%iBlock(val_blockID)%baxBnd(i) - val_iCoor(i)
        else
            dCoor(i) = 0.0
        endif
    enddo
    
    
    iDis = 0.0
    do i=1,nDim
        iDis = iDis + dCoor(i)*dCoor(i)
    enddo
    iDis = sqrt(iDis)
    
    val_iDis = iDis
    
endsubroutine
!
    
    
