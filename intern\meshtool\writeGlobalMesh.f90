!
subroutine writeGlobalMesh
    implicit none
    
    call writeGlobalMesh_info
    
    call writeGlobalMesh_mesh
    
    !call writeGlobalMesh_node
    
    !call writeGlobalMesh_elem
    
    !call writeGlobalMesh_mark
    
endsubroutine
!
subroutine writeGlobalMesh_mesh
    use mod_Mesh
    implicit none
    integer:: i,j,k
    integer:: ielemsize
    !integer,allocatable:: ieei(:,:)
    !real*8,allocatable:: icci(:,:)
    
    open(33,file=trim(workDir)//'/'//trim(goal)//'.mesh',FORM='unformatted')
        
        do i=1,iGlobalMesh%nPoin
            write(33) (iGlobalMesh%iCoor(j,i),j=1,iGlobalMesh%nDime)
        enddo
        ielemsize = 4*nDime-4
        write(33) iGlobalMesh%iElem !((iGlobalMesh%iElem(j,i),j=1,ielemsize),i=1,iGlobalMesh%nElem)
        !do i=1,iGlobalMesh%nDoma
        !    write(33) (((iGlobalMesh%iDoma(i)%iElem(k,j)),k=1,ielemsize),j=1,iGlobalMesh%iDoma(i)%nElem)
        !enddo
        ielemsize = 2*nDime-2
        write(33) iGlobalMesh%iBEle !((iGlobalMesh%iBEle(j,i),j=1,ielemsize),i=1,iGlobalMesh%nBEle)
        !do i=1,iGlobalMesh%nMark
        !    write(33) (((iGlobalMesh%iMark(i)%iElem(k,j)),k=1,ielemsize),j=1,iGlobalMesh%iMark(i)%nElem) 
        !enddo
        
    close(33)
    
    ! open(33,file=trim(workDir)//'/'//trim(goal)//'.mesh_text')
    !
    !     do i=1,iGlobalMesh%nPoin
    !         write(33,*) (iGlobalMesh%iCoor(j,i),j=1,iGlobalMesh%nDime)
    !     enddo
    !
    !     write(33,'(8I10)') iGlobalMesh%iElem
    !
    !     write(33,'(8I10)') iGlobalMesh%iBEle
    !
    ! close(33)
    
endsubroutine
!
subroutine writeGlobalMesh_info
    use mod_Mesh
    implicit none
    integer:: i,j,k
    
    !open(33,file=trim(workDir)//'/'//trim(goal)//'.info',status='unknown')
    !    write(33,*) iGlobalMesh%nDime,' %nDime'
    !    write(33,*) iGlobalMesh%nDoma,' %nDoma'
    !    write(33,'(A)') "DOMA DomaID DomaKind Doma_nElem DomaName"
    !    do i=1,iGlobalMesh%nDoma
    !        write(33,'(A,I5,5X,A,I10,5X,A)') "DOMA",i,trim(iGlobalMesh%iDoma(i)%DomaKind),iGlobalMesh%iDoma(i)%nElem,trim(iGlobalMesh%iDoma(i)%DomaName)
    !    enddo
    !    write(33,*)
    !    
    !    write(33,*) iGlobalMesh%nMark,' %nMark'
    !    write(33,'(A)') "MARK MarkID MarkKind Mark_nElem MarkName"
    !    do i=1,iGlobalMesh%nMark
    !        write(33,'(A,I5,5X,A,I10,5X,A)') "MARK",i,trim(iGlobalMesh%iMark(i)%MarkKind),iGlobalMesh%iMark(i)%nElem,trim(iGlobalMesh%iMark(i)%MarkName)
    !    enddo
    !    write(33,*)
    !
    !    write(33,*) nMesh,' nMesh'
    !    write(33,'(A)') "MESH MeshID Mesh_nDoma Mesh_nMark"
    !    do i=1,nMesh
    !        write(33,'(A,3I5)') "MESH",i,iInMesh(i)%nDoma,iInMesh(i)%nMark
    !    enddo
    !    
    !close(33)
    
    open(33,file=trim(workDir)//'/'//trim(goal)//'.info',status='unknown')   
        write(33,'(A)') '     nPoin     nElem     nBEle'
        write(33,'(3I10)') iGlobalMesh%nPoin,iGlobalMesh%nElem,iGlobalMesh%nBEle
        write(33,'(A)') '  nDime nDoma nMark nMesh'
        write(33,'(4I6)') iGlobalMesh%nDime,iGlobalMesh%nDoma,iGlobalMesh%nMark,nMesh

        write(33,*)
        do i=1,iGlobalMesh%nDoma
            write(33,'(A,I5,5X,A,I10,5X,A)') "DOMA",i,trim(iGlobalMesh%iDoma(i)%DomaKind),iGlobalMesh%iDoma(i)%nElem,trim(iGlobalMesh%iDoma(i)%DomaName)
        enddo
        
        write(33,*)
        do i=1,iGlobalMesh%nMark
            write(33,'(A,I5,5X,A,I10,5X,A)') "MARK",i,trim(iGlobalMesh%iMark(i)%MarkKind),iGlobalMesh%iMark(i)%nElem,trim(iGlobalMesh%iMark(i)%MarkName)
        enddo
    
        write(33,*)
        do i=1,nMesh
            write(33,'(A,3I5)') "MESH",i,iInMesh(i)%nDoma,iInMesh(i)%nMark
        enddo
        
    close(33)
    
    
endsubroutine
!
subroutine writeGlobalMesh_node
    use mod_Mesh
    implicit none
    integer:: i,j,k
    
    open(33,file=trim(workDir)//'/'//trim(goal)//'.node',status='unknown',FORM='unformatted')
    do i=1,iGlobalMesh%nPoin
        write(33) (iGlobalMesh%iCoor(j,i),j=1,iGlobalMesh%nDime)
    enddo
    close(33)
    
    open(33,file=trim(workDir)//'/'//trim(goal)//'.node_text',status='unknown')
    do i=1,iGlobalMesh%nPoin
        write(33,*) (iGlobalMesh%iCoor(j,i),j=1,iGlobalMesh%nDime)
    enddo
    close(33)
    
endsubroutine
!
subroutine writeGlobalMesh_elem
    use mod_Mesh
    implicit none
    integer:: i,j,k,l
    integer:: ielemsize
    
    ielemsize = 4*nDime-4
    
    open(33,file=trim(workDir)//'/'//trim(goal)//'.elem',status='unknown',FORM='unformatted')
    do i=1,iGlobalMesh%nDoma
        write(33) (((iGlobalMesh%iDoma(i)%iElem(k,j)),k=1,ielemsize),j=1,iGlobalMesh%iDoma(i)%nElem)
    enddo
    close(33)
    
    open(33,file=trim(workDir)//'/'//trim(goal)//'.elem_text',status='unknown')
    do i=1,iGlobalMesh%nDoma
        write(33,"(8I10)") (((iGlobalMesh%iDoma(i)%iElem(k,j)),k=1,ielemsize),j=1,iGlobalMesh%iDoma(i)%nElem)
    enddo
    close(33)
    
endsubroutine
!
subroutine writeGlobalMesh_mark
    use mod_Mesh
    implicit none
    integer:: i,j,k
    integer:: ielemsize
    
    ielemsize = 2*nDime-2
    
    open(33,file=trim(workDir)//'/'//trim(goal)//'.mark',status='unknown',FORM='unformatted')
    do i=1,iGlobalMesh%nMark
        write(33) (((iGlobalMesh%iMark(i)%iElem(k,j)),k=1,ielemsize),j=1,iGlobalMesh%iMark(i)%nElem) 
    enddo
    close(33)
    
    open(33,file=trim(workDir)//'/'//trim(goal)//'.mark_text',status='unknown')
    do i=1,iGlobalMesh%nMark
        write(33,"(8I10)") (((iGlobalMesh%iMark(i)%iElem(k,j)),k=1,ielemsize),j=1,iGlobalMesh%iMark(i)%nElem) 
    enddo
    close(33)
    
endsubroutine
!