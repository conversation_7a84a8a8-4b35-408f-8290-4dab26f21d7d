!
subroutine calMeshDeform_OrgnAndR(val_iDeformZone,val_iMesh)
    use mod_TypeDef_Mesh
    use mod_Mesh_Deform
    use mod_vectorAlgebra
    implicit none
    type(typ_DeformZone),intent(inout):: val_iDeformZone
    type(typ_GlobalMesh),intent(in):: val_iMesh
    !
    integer:: i,j,poinID,regnID
    real(kind=REALLEN):: iCoor(nDim),iVec(nDim),iDist,iCoef
    real(kind=REALLEN):: iAngle,iRadiu
    
    
    if((val_iDeformZone%DeformKind /= DEFORMKIND_ROTAT).and. &
       (val_iDeformZone%DeformKind /= DEFORMKIND_REGNR).and. &
       (val_iDeformZone%DeformKind /= DEFORMKIND_SOFTR)) then
        return
    endif
        
    do i=1,val_iDeformZone%nPoin
        poinID = val_iDeformZone%iPoinProp(i,1)
            
        iCoor(:) = val_iMesh%iCoor_Poin(:,poinID)
            
        if(nDim == 2) then
            call getAndR_2D(iCoor,                  &
                        val_iDeformZone%iRotatCent, &
                        iAngle, iRadiu              )
                    
        elseif(nDim == 3) then
            call getAndR_3D(iCoor,                  &
                        val_iDeformZone%iRotatCent, &
                        val_iDeformZone%iRotatAxle, &
                        val_iDeformZone%iRotatDirs, &
                        iAngle, iRadiu              )
        endif
                
        val_iDeformZone%iOrgnAndR(i,1) = iAngle
        val_iDeformZone%iOrgnAndR(i,2) = iRadiu
    enddo
    
    do i=1,val_iDeformZone%nPoinBC
        poinID = val_iDeformZone%iPoinPropBC(i,1)
            
        iCoor(:) = val_iMesh%iCoor_Poin(:,poinID)
            
        if(nDim == 2) then
            call getAndR_2D(iCoor,                  &
                        val_iDeformZone%iRotatCent, &
                        iAngle, iRadiu              )
                    
        elseif(nDim == 3) then
            call getAndR_3D(iCoor,                  &
                        val_iDeformZone%iRotatCent, &
                        val_iDeformZone%iRotatAxle, &
                        val_iDeformZone%iRotatDirs, &
                        iAngle, iRadiu              )
        endif
                
        val_iDeformZone%iOrgnAndRBC(i,1) = iAngle
        val_iDeformZone%iOrgnAndRBC(i,2) = iRadiu
    enddo
    
    
contains
    !
    subroutine getAndR_2D(val_iCoor, val_iCent, val_iAngle, val_iRadiu)
        use mod_vectorAlgebra
        implicit none
        real(kind=REALLEN),intent(in):: val_iCoor(2)
        real(kind=REALLEN),intent(in):: val_iCent(2)
        real(kind=REALLEN),intent(out):: val_iAngle
        real(kind=REALLEN),intent(out):: val_iRadiu
        !
        integer:: i
        real(kind=8):: iVec(2),iRadiu,iAngle
        
        iVec = val_iCoor(:) - val_iCent(:)
        
        call vector_length(2, iVec, iRadiu)
        
        iAngle = acosd(iVec(1)/iRadiu)
        if(iVec(2) < 0.0) then
            iAngle = - iAngle
        endif
        
        val_iAngle = iAngle
        val_iRadiu = iRadiu
        
    endsubroutine
    !
    subroutine getAndR_3D(val_iCoor, val_iCent,val_iAxle,&
                val_iDir, val_iAngle, val_iRadiu         )
        use mod_PreDefine_IOPort
        implicit none
        real(kind=REALLEN),intent(in):: val_iCoor(3)
        real(kind=REALLEN),intent(in):: val_iCent(3)
        integer,intent(in):: val_iAxle
        real(kind=REALLEN),intent(in):: val_iDir(3)
        real(kind=REALLEN),intent(out):: val_iAngle
        real(kind=REALLEN),intent(out):: val_iRadiu
        
        write(ioPort_Out,*) "Empty in getAndR_3D in iniMeshDeform_DWM"
        stop
        
    endsubroutine
    !
endsubroutine
!
