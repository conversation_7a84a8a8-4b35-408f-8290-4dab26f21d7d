
!---------------------------------------------
!
!---------------------------------------------
module mod_TurbSA_ParmIndx
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    implicit none
    ! 129~256 are used to store turb constants
    integer,parameter:: IP_TurbSA_Cb1   = 129
    integer,parameter:: IP_TurbSA_Cb2   = 130
    integer,parameter:: IP_TurbSA_Cv1   = 131
    integer,parameter:: IP_TurbSA_Cv2   = 132
    integer,parameter:: IP_TurbSA_Cw1   = 133
    integer,parameter:: IP_TurbSA_Cw2   = 134
    integer,parameter:: IP_TurbSA_Cw3   = 135
    integer,parameter:: IP_TurbSA_Ct1   = 136
    integer,parameter:: IP_TurbSA_Ct2   = 137
    integer,parameter:: IP_TurbSA_Ct3   = 138
    integer,parameter:: IP_TurbSA_Ct4   = 139
    integer,parameter:: IP_TurbSA_Sigma = 140
    integer,parameter:: IP_TurbSA_Kappa = 141
    !
    ! ==== index of turbAuxv on node ====
    integer,parameter:: IP_SA_Fv1       = 1
    integer,parameter:: IP_SA_Fv2       = 2
    integer,parameter:: IP_SA_OMEGA     = 3
    integer,parameter:: IP_SA_SIGMA     = 4
    ! ==== index of turbAuxv on sate ====
    ! ==== index of turbAuxv on belv ====

endmodule
!
module mod_TurbSA_Def
    use mod_PreDefine_Precision
    use mod_PreDefine_Dimension
    implicit none
    !
    integer,parameter:: nTurbEx = 1
    integer,parameter:: nConsEx = nDim + 2
    integer,parameter:: nPrimEx = nDim + 4 !+mul+mut
    integer,parameter:: nSoluEx = nDim + 3 !nCons+nTurb
    !
endmodule
!
