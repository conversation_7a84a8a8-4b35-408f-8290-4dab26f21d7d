!
!---------------------------------------------
!
!---------------------------------------------
subroutine saveComputingTime(val_iParm,val_iTimeBegin,val_iTimeFinal,val_nPoin)
    use mod_TypeDef_Parm
    use mod_PreDefine_IOPort
    use mod_PreDefine_Precision
    use mod_WorkPath
    use mod_Config
    use mod_strOfNumber
    implicit none
    type(typ_Parm),intent(inout):: val_iParm
    real(kind=4  ),intent(in   ):: val_iTimeBegin
    real(kind=4  ),intent(in   ):: val_iTimeFinal
    integer       ,intent(in   ):: val_nPoin
    character(len=10):: NPString
    character(len=100):: fileName
    !
    
    val_iParm%iCPUTimeBegin = val_iTimeBegin
    val_iParm%iCPUTimeFinal = val_iTimeFinal
    
    val_iParm%nSecondPerIter = (val_iTimeFinal - val_iTimeBegin)/val_iParm%iIter
    val_iParm%nIterPerSecond = val_iParm%iIter/(val_iTimeFinal - val_iTimeBegin)
    
    !write(ioPort_Out,"(A,E12.5)") 'CPUTimePerIter =',val_iParm%nSecondPerIter
    !write(ioPort_Out,"(A,E12.5)") 'iterPerCPUTime =',val_iParm%nIterPerSecond
    
    fileName = 'testCost'
    
    if(KIND_HARDWARE == _CPU) then
        fileName = trim(fileName)//'_CPU'
    elseif(KIND_HARDWARE == _GPU) then
        fileName = trim(fileName)//'_GPU'
    endif
    
    if(REALLEN == 4) then
        fileName = trim(fileName)//'_SP'
    elseif(REALLEN == 8) then
        fileName = trim(fileName)//'_DP'
    endif
    
    
    open(ioPort_FULL,file=trim(adjustl(soluFullPath))//'/'//trim(fileName)//'.dat')
    
    write(ioPort_FULL,"(A,I10)"  ) 'numberOfPoints  =', val_nPoin
    write(ioPort_FULL,"(A,E12.5)") 'nSecondPerIter  =', val_iParm%nSecondPerIter
    write(ioPort_FULL,"(A,E12.5)") 'nIterPerSecond  =', val_iParm%nIterPerSecond
    !
    write(ioPort_FULL,"(A,E12.5)")
    write(ioPort_FULL,"(A,E12.5)") 'UnifiedTimeCost =', val_iParm%nSecondPerIter*1.0E+6/val_nPoin
    
    close(ioPort_FULL)
    !
    
    
    NPString = strTen_Number(val_nPoin)
    
    open(ioPort_FULL,file=trim(adjustl(soluFullPath))//'/'//trim(fileName)//trim(NPString)//'.dat')
    
    write(ioPort_FULL,"(A,I10)"  ) 'numberOfPoints  =', val_nPoin
    write(ioPort_FULL,"(A,E12.5)") 'nSecondPerIter  =', val_iParm%nSecondPerIter
    write(ioPort_FULL,"(A,E12.5)") 'nIterPerSecond  =', val_iParm%nIterPerSecond
    !
    write(ioPort_FULL,"(A,E12.5)")
    write(ioPort_FULL,"(A,E12.5)") 'UnifiedTimeCost =', val_iParm%nSecondPerIter*1.0E+6/val_nPoin
    
    close(ioPort_FULL)
    !
endsubroutine
!
    
    