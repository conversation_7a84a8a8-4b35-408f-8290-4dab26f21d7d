!
module mod_StringOP
    !
contains
    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getSubString(val_string,val_div,val_num,val_sub)
        implicit none
        character(len=*),intent(in ):: val_string
        character       ,intent(in ):: val_div
        integer         ,intent(in ):: val_num
        character(len=*),intent(out):: val_sub

        integer:: i,ibegin,iend
        integer:: subCount

        if(val_num == 0) then
            val_sub = val_string
            return
        endif

        subCount = 1
        do i=2,len_trim(val_string)-1
            if((val_string(i:i) == val_div).and.(val_string(i-1:i-1) /= val_div)) then
                subCount = subCount+1
            endif
        enddo

        if(abs(val_num) > subCount) then
            val_sub = ' '
            return
        endif

        if(val_num > 0 ) then
            ibegin = 1

            subCount = 0
            do i=2,len_trim(val_string)-1
                if((val_string(i:i) == val_div).and.(val_string(i-1:i-1) /= val_div)) then
                    subCount = subCount+1

                    if(subCount == val_num-1) then
                        ibegin = i+1
                    endif

                    if(subCount == val_num) then
                        iend = i-1
                        val_sub = adjustl(val_string(ibegin:iend))
                        return
                    endif

                endif
            enddo

            iend = len_trim(val_string)

        elseif(val_num<0) then
            iend = len_trim(val_string)

            subCount = 0
            do i=len_trim(val_string)-1,2,-1
                if((val_string(i:i) == val_div).and.(val_string(i-1:i-1) /= val_div)) then
                    subCount = subCount+1

                    if(subCount == -val_num-1) then
                        iend = i+1
                    endif

                    if(subCount == -val_num) then
                        ibegin = i+1
                        val_sub = adjustl(val_string(ibegin:iend))
                        return
                    endif

                endif
            enddo

            ibegin = 1

        endif

        val_sub = adjustl(val_string(ibegin:iend))

    endsubroutine

    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getSubStringData(val_string,val_div,val_sub,val_isExist)
        use mod_PreDefine_Precision
        implicit none
        character(len=*),intent(in ):: val_string
        character(len=*),intent(in ):: val_div
        character(len=*),intent(out):: val_sub
        logical         ,intent(out):: val_isExist
        !
        integer:: i,j,iSize,IP,bgnIP,endIP
        character(len=999):: iStr


        iStr = trim(adjustl(val_string))

        !call toUpperCase(iStr)

        iSize = len_trim(val_div)

        do i=0,len_trim(iStr)-iSize
            if(iStr(i+1:i+iSize) == val_div(1:iSize)) then
                IP = i+iSize

                bgnIP = 0
                do j=IP+1,len_trim(iStr)
                    if(iStr(j:j) == '(') then
                        bgnIP = j
                        exit
                    endif
                enddo
                if(bgnIP == 0) exit

                endIP = 0
                do j=bgnIP+1,len_trim(iStr)
                    if(iStr(j:j) == ')') then
                        endIP = j
                        exit
                    endif
                enddo
                if(endIP == 0) exit
                if(endIP-bgnIP <= 1) exit

                val_sub = iStr(bgnIP+1:endIP-1)
                val_isExist = .true.
                return
            endif
        enddo

        val_isExist = .false.
        return

    endsubroutine

    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine replaceInString(val_string,val_oldChar,val_newChar)
        implicit none
        character(len=*),intent(inout):: val_string
        character       ,intent(in   ):: val_oldChar
        character       ,intent(in   ):: val_newChar
        !
        integer:: i

        do i=1,len_trim(val_string)
            if(val_string(i:i) == val_oldChar) then
                val_string(i:i) = val_newChar
            endif
        enddo

    endsubroutine

    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine getNDivInString(val_string,val_div,val_nDiv)
        implicit none
        character(len=*),intent(in ):: val_string
        character       ,intent(in ):: val_div
        integer         ,intent(out):: val_nDiv

        integer:: i,icount

        icount = 0
        do i=1,len_trim(val_string)
            if(val_string(i:i) == val_div) then
                icount = icount + 1
            endif
        enddo
        val_nDiv = icount

    endsubroutine

    !---------------------------------------------
    !
    !---------------------------------------------
    logical function isEqual_String(val_str1,val_str2)
        implicit none
        character(len=*),intent(in):: val_str1
        character(len=*),intent(in):: val_str2

        character(len=len_trim(val_str1)):: str1
        character(len=len_trim(val_str2)):: str2
        integer:: i

        str1 = adjustl(val_str1)
        str2 = adjustl(val_str2)

        call toUpperCase(str1)
        call toUpperCase(str2)

        if(len_trim(str1) /= len_trim(str2)) then
            isEqual_String = .false.
            return
        else
            do i=1,len_trim(str1)
                if(str1(i:i) /= str2(i:i)) then
                    isEqual_String = .false.
                    return
                endif
            enddo

            isEqual_String = .true.
            return
        endif

    endfunction


    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine toUpperCase(val_string)
        implicit none
        character(len=*),intent(inout):: val_string

        integer:: i
        character::chr

        do i=1,len_trim(val_string)
            chr = val_string(i:i)
            if(chr >= 'a'.and.chr <= 'z') then
                val_string(i:i)= char(ichar(chr)+ichar('A')-ichar('a'))
            endif
        enddo

    endsubroutine


    !---------------------------------------------
    !
    !---------------------------------------------
    subroutine toLowerCase(val_string)
        implicit none
        character(len=*),intent(inout):: val_string

        integer:: i
        character::chr

        do i=1,len_trim(val_string)
            chr = val_string(i:i)
            if(chr >= 'A'.and.chr <= 'Z') then
                val_string(i:i)= char(ichar(chr)+ichar('a')-ichar('A'))
            endif
        enddo

    endsubroutine
    !
endmodule
!

