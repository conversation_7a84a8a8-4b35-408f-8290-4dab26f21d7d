module mod_BC
    use cudafor
    use mod_PreDefine_Dimension
    use mod_PreDefine_Precision
    implicit none
    !
contains
    !
    attributes(host,device) subroutine BC_Wall_NS(soluEl,nvorFi,vgFi,soluFi,Gamma,GasCo)
        implicit none
        real(kind=REALLEN):: soluEl(nDim+2)
        real(kind=REALLEN):: nvorFi(nDim)
        real(kind=REALLEN):: vgFi(nDim)
        real(kind=REALLEN):: soluFi(nDim+2)
        real(kind=REALLEN):: Gamma,GasCo
        !
        real(kind=REALLEN):: qFi,pFi

        qFi = 0.5*(soluEl(2)**2+soluEl(3)**2+soluEl(4)**2*(nDim-2))/soluEl(1)
        pFi = (Gamma-1.0)*(soluEl(nDim+2) - qFi)
        qFi = 0.5*soluEl(1)*(vgFi(1)**2+vgFi(2)**2+vgFi(nDim)**2*(nDim-2))

        soluFi(     1) = soluEl(     1)
        soluFi(     2) = soluEl(     1)*vgFi(   1)
        soluFi(     3) = soluEl(     1)*vgFi(   2)
        soluFi(nDim+1) = soluEl(     1)*vgFi(nDim)
        soluFi(nDim+2) = soluEl(nDim+2) !pFi/(Gamma-1.0) + qFi

    endsubroutine
    !
    attributes(host,device) subroutine BC_Wall_Euler(soluEl,nvorFi,VgFi,soluFi)
        implicit none
        real(kind=REALLEN):: soluEl(nDim+2)
        real(kind=REALLEN):: nvorFi(nDim)
        real(kind=REALLEN):: vgFi(nDim)
        real(kind=REALLEN):: soluFi(nDim+2)
        !
        real(kind=REALLEN):: VnFi

        VnFi = soluEl(2)*nvorFi(1) + soluEl(3)*nvorFi(2) + soluEl(4)*nvorFi(nDim)*(nDim-2)
        VnFi = VnFi - soluEl(1)*(vgFi(1)*nvorFi(1) + vgFi(2)*nvorFi(2) + vgFi(nDim)*nvorFi(nDim)*(nDim-2))

        soluFi(     1) = soluEl(     1)
        soluFi(     2) = soluEl(     2) - VnFi*nvorFi(   1)
        soluFi(     3) = soluEl(     3) - VnFi*nvorFi(   2)
        soluFi(nDim+1) = soluEl(nDim+1) - VnFi*nvorFi(nDim)
        soluFi(nDim+2) = soluEl(nDim+2) !- 0.5*VnFi*VnFi/soluEl(1)

    end subroutine
    !
    attributes(host,device) subroutine BC_MovingWall_NS(soluEl,nvorFi,veloFi,Gamma,soluFi)
        implicit none
        real(kind=REALLEN):: soluEl(nDim+2)
        real(kind=REALLEN):: nvorFi(nDim)
        real(kind=REALLEN):: veloFi(nDim)
        real(kind=REALLEN):: Gamma
        real(kind=REALLEN):: soluFi(nDim+2)
        !
        real(kind=REALLEN):: pFi,qFi

        qFi = 0.5*(soluEl(2)**2+soluEl(3)**2+soluEl(4)**2*(nDim-2))/soluEl(1)
        pFi = (Gamma-1.0)*(soluEl(nDim+2) - qFi)
        qFi = 0.5*soluEl(1)*(veloFi(1)**2+veloFi(2)**2+veloFi(nDim)**2*(nDim-2))

        soluFi(     1) = soluEl(     1)
        soluFi(     2) = soluEl(     1)*veloFi(   1)
        soluFi(     3) = soluEl(     1)*veloFi(   2)
        soluFi(nDim+1) = soluEl(     1)*veloFi(nDim)
        soluFi(nDim+2) = pFi/(Gamma-1.0) + qFi
        soluFi(nDim+2) = soluEl(nDim+2)

    end subroutine
    !
    !
    attributes(host,device) subroutine BC_MovingWall_Euler(soluEl,nvorFi,vgFi,Gamma,soluFi)
        implicit none
        real(kind=REALLEN):: soluEl(nDim+2)
        real(kind=REALLEN):: nvorFi(nDim)
        real(kind=REALLEN):: vgFi(nDim)
        real(kind=REALLEN):: Gamma
        real(kind=REALLEN):: soluFi(nDim+2)
        !
        real(kind=REALLEN):: pFi,qFi,vnFi

        VnFi = soluEl(2)*nvorFi(1) + soluEl(3)*nvorFi(2) + soluEl(4)*nvorFi(nDim)*(nDim-2)
        VnFi = VnFi - soluEl(1)*(vgFi(1)*nvorFi(1) + vgFi(2)*nvorFi(2) + vgFi(nDim)*nvorFi(nDim)*(nDim-2))

        soluFi(     1) = soluEl(     1)
        soluFi(     2) = soluEl(     2) - VnFi*nvorFi(   1)
        soluFi(     3) = soluEl(     3) - VnFi*nvorFi(   2)
        soluFi(nDim+1) = soluEl(nDim+1) - VnFi*nvorFi(nDim)
        soluFi(nDim+2) = soluEl(nDim+2) !- 0.5*VnFi*VnFi/soluEl(1)

    end subroutine
    !
    attributes(host,device) subroutine BC_Symm(soluEl,nvorFi,soluFi)
        implicit none
        real(kind=REALLEN):: soluEl(nDim+2)
        real(kind=REALLEN):: nvorFi(nDim)
        real(kind=REALLEN):: soluFi(nDim+2)
        !
        real(kind=REALLEN):: VnFi

        VnFi = soluEl(2)*nvorFi(1) + soluEl(3)*nvorFi(2) + soluEl(nDim+1)*nvorFi(nDim)*(nDim-2)

        soluFi(     1) = soluEl(     1)
        soluFi(     2) = soluEl(     2) - VnFi*nvorFi(   1)
        soluFi(     3) = soluEl(     3) - VnFi*nvorFi(   2)
        soluFi(nDim+1) = soluEl(nDim+1) - VnFi*nvorFi(nDim)
        soluFi(nDim+2) = soluEl(nDim+2) - 0.5*VnFi*VnFi/soluEl(1)

    end subroutine
    !
    attributes(host,device) subroutine BC_PFar(soluEl,soluEr,nvorFi,soluFi,Gamma,GasCo,isPrec,iDir)
        implicit none
        real(kind=REALLEN):: soluEl(nDim+2)
        real(kind=REALLEN):: soluEr(nDim+2)
        real(kind=REALLEN):: nvorFi(nDim)
        real(kind=REALLEN):: soluFi(nDim+2)
        real(kind=REALLEN):: Gamma,GasCo
        logical:: isPrec
        integer:: iDir
        !
        integer:: s
        real(kind=REALLEN):: Gammo
        real(kind=REALLEN):: DInvEl,VnEl,qEl,pEl,cEl
        real(kind=REALLEN):: DInvEr,VnEr,qEr,pEr,cEr,VnFi,cFi,sFi
        real(kind=REALLEN):: RP,RM,MFi,DFi,PFi,VFi(nDim),sqFi,deltP,MXi

        Gammo = Gamma - 1.0

        DInvEl = 1.0/soluEl(1)
        DInvEr = 1.0/soluEr(1)

        VnEl = (soluEl(2)*nvorFi(1) + soluEl(3)*nvorFi(2) + soluEl(4)*nvorFi(nDim)*(nDim-2))*DInvEl
        VnEr = (soluEr(2)*nvorFi(1) + soluEr(3)*nvorFi(2) + soluEr(4)*nvorFi(nDim)*(nDim-2))*DInvEr
        qEl = 0.5*(soluEl(2)*soluEl(2) + soluEl(3)*soluEl(3) + soluEl(4)*soluEl(4)*(nDim-2))*DInvEl
        qEr = 0.5*(soluEr(2)*soluEr(2) + soluEr(3)*soluEr(3) + soluEr(4)*soluEr(4)*(nDim-2))*DInvEr

        pEl = Gammo*(soluEl(nDim+2) - qEl)
        pEr = Gammo*(soluEr(nDim+2) - qEr)
        cEl = sqrt(Gamma*pEl*DInvEl)
        cEr = sqrt(Gamma*pEr*DInvEr)

        RP = VnEl + 2.0*cEl/Gammo
        RM = VnEr - 2.0*cEr/Gammo

        if(VnEr > cEr) then
          RM = VnEl - 2.0*cEl/Gammo
        endif
        if(VnEr < -cEr) then
            RP = VnEr + 2.0*CEr/Gammo
        endif


        VnFi = 0.5*(RP+RM)
        cFi  = 0.25*Gammo*(RP-RM)
        MFi  = VnFi/cFi

        MXi = sqrt(2.0*qEr*DInvEr)/cEr

        if(isPrec) then
            if(VnEr >= 0) then
                do s=1,nCons
                    soluFi(s) = soluEl(s)
                end do
            else
                do s=1,nCons
                    soluFi(s) = soluEr(s)
                end do
            endif

        else
            if(VnEr >= cEr) then !supersonic outflow
                do s=1,nCons
                    soluFi(s) = soluEl(s)
                 end do
            elseif(VnEr <= -cEr) then !supersonic inflow
                do s=1,nCons
                    soluFi(s) = soluEr(s)
                end do
            else
                if(VnFi >= 0.0) then !subsonic outflow
                    pFi       = pEr
                    deltP     = pFi - pEl
                    DFi       = soluEl(     1)        + deltP/(cEr*cEl)
                    VFi(   1) = soluEl(     2)*DInvEl - deltP*nvorFi(   1)/(cEl*soluEl(1))
                    VFi(   2) = soluEl(     3)*DInvEl - deltP*nvorFi(   2)/(cEl*soluEl(1))
                    VFi(nDim) = soluEl(nDim+1)*DInvEl - deltP*nvorFi(nDim)/(cEl*soluEl(1))
                else !subsonic inflow
                    pFi       = 0.5*(pEl+pEr-soluEl(1)*cEl*(VnEr-VnEl))
                    deltP     = pFi - pEr
                    DFi       = soluEr(     1) + deltP/(cEl*cEl)
                    VFi(   1) = soluEr(     2)*DInvEr + deltP*nvorFi(   1)/(cEl*soluEl(1))
                    VFi(   2) = soluEr(     3)*DInvEr + deltP*nvorFi(   2)/(cEl*soluEl(1))
                    VFi(nDim) = soluEr(nDim+1)*DInvEr + deltP*nvorFi(nDim)/(cEl*soluEl(1))
                endif

                sqFi = VFi(1)*VFi(1) + VFi(2)*VFi(2) + VFi(nDim)*VFi(nDim)*(nDim-2)

                soluFi(     1) = DFi
                soluFi(     2) = DFi*VFi(   1)
                soluFi(     3) = DFi*VFi(   2)
                soluFi(nDim+1) = DFi*VFi(nDim)
                soluFi(nDim+2) = PFi/Gammo + 0.5*DFi*sqFi
            endif
        endif

        if(VnFi <= 0.0) then
            iDir = -1
        else
            iDir = 1
        endif

    end subroutine
    !
    attributes(host,device) subroutine BC_OutL(soluEl, soluEr, nvorFi, soluFi, Gamma, GasCo)
        implicit none
        real(kind=REALLEN):: soluEl(nDim+2)
        real(kind=REALLEN):: soluEr(nDim+2)
        real(kind=REALLEN):: nvorFi(nDim)
        real(kind=REALLEN):: soluFi(nDim+2)
        real(kind=REALLEN):: Gamma,GasCo
        !
        integer:: i
        real(kind=REALLEN):: qFl,nFl,pFl


        qFl = 0.5*soluEl(1)*(soluEl(2)**2 + soluEl(3)**2 + soluEl(4)**2*(nDim-2))
        nFl = soluEl(2)*nvorFi(1) + soluEl(3)*nvorFi(2) + soluEl(4)*nvorFi(nDim)*(nDim-2)
        pFl = (Gamma-1.0)*(soluEl(nDim+2) - qFl)

        if(nFl >= 0.0) then
            do i=1,nCons
                soluFi(i) = soluEl(i)
            enddo
        else
            soluFi(1     ) = soluEl(1)
            soluFi(2     ) = 0.0
            soluFi(3     ) = 0.0
            soluFi(nDim+1) = 0.0
            soluFi(nDim+2) = pFl/(Gamma-1.0)
        endif

    endsubroutine
    !
    attributes(host,device) subroutine BC_OutP(soluEl, soluEr, nvorFi, soluFi, Gamma, GasCo, iDir)
        implicit none
        real(kind=REALLEN):: soluEl(nDim+2)
        real(kind=REALLEN):: soluEr(nDim+2)
        real(kind=REALLEN):: nvorFi(nDim)
        real(kind=REALLEN):: soluFi(nDim+2)
        real(kind=REALLEN):: Gamma,GasCo
        integer:: iDir
        !
        real(kind=REALLEN):: qFl,pFl,factA,vnFi,cFi,mFi,dFi,vFi(nDim),pFi,tFi

        vFi(   1) = soluEl(     2)/soluEl(1)
        vFi(   2) = soluEl(     3)/soluEl(1)
        vFi(nDim) = soluEl(nDim+1)/soluEl(1)

        qFl  = 0.5 * soluEl(1) * (vFi(1)**2 + vFi(2)**2 + vFi(nDim)**2*(nDim-2))
        pFl  = (Gamma-1.0) * (soluEl(nDim+2) - qFl)

        cFi = sqrt(Gamma*pFl/soluEl(1))
        mFi = sqrt(2.0*qFl/soluEl(1))/cFi

        vnFi = vFi(1)*nvorFi(1) + vFi(2)*nvorFi(2) + vFi(nDim)*nvorFi(nDim)*(nDim-2)

        if(soluEl(nDim+2) <= soluEr(1)/(Gamma-1.0)) then
            pFi = soluEr(1)
            tFi = soluEr(2)
            dFi = pFi/(GasCo*tFi)
            !dFi = soluEl(1)*pFi/pFl

            vFi(   1) = 0.0
            vFi(   2) = 0.0
            vFi(nDim) = 0.0
            iDir = -1
        elseif(vnFi < 0.0) then ! total pressure
            !pFi = soluEr(1)
            !factA = max(1.0, soluEr(3)/soluEr(1))
            !mFi = min(mFi, sqrt((factA**((Gamma-1.0)/Gamma)-1)*2.0/(Gamma-1.0)))

            mFi = min(mFi, 0.8)
            factA = 1 + 0.5*(Gamma-1.0)*mFi*mFi

            pFi = soluEr(1)/factA**(Gamma/(Gamma-1.0))
            tFi = soluEr(2)/(1+0.5*(Gamma-1.0)*mFi*mFi)
            dFi = pFi/(GasCo*tFi)

            factA = sqrt(Gamma*GasCo*tFi)*mFi

            vFi(   1) = factA*nvorFi(   1)
            vFi(   2) = factA*nvorFi(   2)
            vFi(nDim) = factA*nvorFi(nDim)
            iDir = -1
        else
            pFi = soluEr(1)
            dFi = soluEl(1)*pFi/pFl

            factA = min(min(mFi, 0.98), sqrt((soluEl(nDim+2)-pFi/(Gamma-1.0))*2.0/dFi))

            factA = mFi/factA
            !vFi(   1) = vnFi*nvorFi(   1) / factA
            vFi(   1) = vFi(   1) / factA
            vFi(   2) = vFi(   2) / factA
            if(nDim == 3) then
            vFi(nDim) = vFi(nDim) / factA
            endif
            iDir = 1
        endif

        soluFi(     1) = dFi
        soluFi(     2) = dFi*vFi(   1)
        soluFi(     3) = dFi*vFi(   2)
        soluFi(nDim+1) = dFi*vFi(nDim)
        soluFi(nDim+2) = min(soluEl(nDim+2), pFi/(Gamma-1.0) + 0.5*dFi*(vFi(1)**2+vFi(2)**2+vFi(nDim)**2*(nDim-2)))
        soluFi(nDim+2) = pFi/(Gamma-1.0) + 0.5*dFi*(vFi(1)**2+vFi(2)**2+vFi(nDim)**2*(nDim-2))

    endsubroutine
    !
    attributes(host,device) subroutine BC_Inlet(soluEl, soluEr, nvorFi, soluFi, Gamma, GasCo, iWDis)
        implicit none
        real(kind=REALLEN):: soluEl(nDim+2)
        real(kind=REALLEN):: soluEr(nDim+2)
        real(kind=REALLEN):: nvorFi(nDim)
        real(kind=REALLEN):: soluFi(nDim+2)
        real(kind=REALLEN):: Gamma,GasCo,iWDis
        !
        real(kind=REALLEN):: qEl, fFi

        fFi = min(1.0, (iWDis/0.002)**2)
        qEl = 0.5*(soluEr(2)**2+soluEr(3)**2+soluEr(nDim+1)**2*(nDim-2))/soluEr(1)

        soluFi(     1) = soluEr(     1)
        soluFi(     2) = soluEr(     2)*fFi
        soluFi(     3) = soluEr(     3)*fFi
        soluFi(nDim+1) = soluEr(nDim+1)*fFi
        soluFi(nDim+2) = soluEr(nDim+2) - qEl*(1-fFi**2)

    endsubroutine
    !
end module mod_BC